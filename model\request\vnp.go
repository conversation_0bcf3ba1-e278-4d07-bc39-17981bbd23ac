package request

type BookVNPost struct {
	SenderTel             string        `json:"SenderTel"`
	SenderFullName        string        `json:"SenderFullname"`
	SenderAddress         string        `json:"SenderAddress"`
	SenderWardId          string        `json:"SenderWardId"`
	SenderDistrictId      string        `json:"SenderDistrictId"`
	SenderProvinceId      string        `json:"SenderProvinceId"`
	ReceiverTel           string        `json:"ReceiverTel"`
	ReceiverFullName      string        `json:"ReceiverFullname"`
	ReceiverAddress       string        `json:"ReceiverAddress"`
	ReceiverWardId        string        `json:"ReceiverWardId"`
	ReceiverDistrictId    string        `json:"ReceiverDistrictId"`
	ReceiverProvinceId    string        `json:"ReceiverProvinceId"`
	ServiceName           string        `json:"ServiceName"`
	OrderCode             string        `json:"OrderCode"`
	PackageContent        string        `json:"PackageContent"`    // Nội dung hàng hóa
	WeightEvaluation      float64       `json:"WeightEvaluation"`  //gram
	IsPackageViewable     bool          `json:"IsPackageViewable"` // Có cho xem hàng hay không
	PickupType            int           `json:"PickupType"`
	CodAmountEvaluation   float64       `json:"CodAmountEvaluation"`   //COD
	OrderAmountEvaluation float64       `json:"OrderAmountEvaluation"` //Giá trị đơn hàng
	PickupPoscode         string        `json:"PickupPoscode"`
	OrderCodeInBatch      string        `json:"OrderCodeInBatch"`      // đơn nhiều kiện
	LstOrderDetailRequest []*VNPItem    `json:"LstOrderDetailRequest"` // đơn nhiều kiện
	ServiceId             int           `json:"ServiceId"`
	CodAmount             float64       `json:"CodAmount"`
	AdditionServices      []int `json:"LstDichVuCongThem"`
}

type CancelVNPost struct {
	OrderId string `json:"OrderId"`
}

type VNPItem struct {
	CodAmount int64   `json:"CodAmount"`
	Height    float64 `json:"Height"`
	Length    float64 `json:"Length"`
	Weight    float64 `json:"Weight"`
	Width     float64 `json:"Width"`
}
