package nt

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathBookNhatTinService   = "/v1/bill/create"
	pathCancelNhatTinService = "/v1/bill/destroy"
	pathGetProvince          = "/v1/loc/provinces"
	pathGetDistrict          = "/v1/loc/districts"
	pathGetWard              = "/v1/loc/wards"
	pathGetDetailOrder       = "/v1/bill/tracking"
)

type Client struct {
	svc     *client.RestClient
	headers map[string]string
}

func NewServiceClient(apiHost, logName string, session *mongo.Database) *Client {
	if apiHost == "" {
		return nil
	}
	newClient := &Client{
		svc: client.NewRESTClient(
			apiHost,
			logName,
			180*time.Second,
			0,
			180*time.Second,
		),
		headers: map[string]string{},
	}
	newClient.svc.SetDBLog(session)
	return newClient
}

func (cli *Client) ChangeInformation(carrierInfo *model.Carrier) {
	cli.headers = map[string]string{
		"username": carrierInfo.ExtraData.ClientKey,
		"password": carrierInfo.ExtraData.SecretKey,
	}
}

func (cli *Client) CreateTrackingNhatTin(createTrackingRequest request.BookNTRequest) (trackingInfo *model.ShippingInfo, err error) {
	type myResponse struct {
		Success bool   `json:"success"`
		Message string `json:"message"`
		Data    struct {
			BillId    int64   `json:"bill_id"`
			BillCode  string  `json:"bill_code"`
			RefCode   string  `json:"ref_code"`
			StatusId  int64   `json:"status_id"`
			CodAmount float64 `json:"cod_amount"`
			TotalFee  float64 `json:"total_fee"`
		} `json:"data"`
	}

	query := map[string]string{}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, query, createTrackingRequest, pathBookNhatTinService, nil)

	if err != nil {
		return
	}
	resBody := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &resBody)

	if err != nil {
		return
	}

	if !resBody.Success {
		err = fmt.Errorf("%v", resBody.Message)
		return
	}

	trackingInfo = &model.ShippingInfo{
		CODAmount:      resBody.Data.CodAmount,
		TrackingNumber: resBody.Data.BillCode,
		FeeAmount:      resBody.Data.TotalFee,
	}

	return
}

func (cli *Client) CancelTrackingNhatTin(cancelTrackingNhatTin request.CancelNTRequest) (err error) {
	type myResponse struct {
		Success bool   `json:"success"`
		Message string `json:"message"`
	}

	query := map[string]string{}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, query, cancelTrackingNhatTin, pathCancelNhatTinService, nil)
	if err != nil {
		return
	}

	resBody := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return
	}

	if !resBody.Success {
		err = fmt.Errorf("%v", resBody)
		return
	}

	return
}

func (cli *Client) GetProvince() (result []*model.ProvinceNhatTin, err error) {
	type myResponse struct {
		Success bool                     `json:"success"`
		Message string                   `json:"message"`
		Data    []*model.ProvinceNhatTin `json:"data"`
	}

	query := map[string]string{}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, query, nil, pathGetProvince, nil)
	if err != nil {
		return
	}
	resBody := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return
	}

	if !resBody.Success {
		err = fmt.Errorf("%v", resBody)
		return
	}

	if len(resBody.Data) > 0 {
		result = resBody.Data
	}

	return
}

func (cli *Client) GetDistrict(provinceId int64) (result []*model.DistrictNhatTin, err error) {
	type myResponse struct {
		Success bool                     `json:"success"`
		Message string                   `json:"message"`
		Data    []*model.DistrictNhatTin `json:"data"`
	}

	query := map[string]string{
		"province_id": strconv.Itoa(int(provinceId)),
	}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, query, nil, pathGetDistrict, nil)
	if err != nil {
		return
	}

	resBody := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return
	}

	if !resBody.Success {
		err = fmt.Errorf("%v", resBody)
		return
	}

	if len(resBody.Data) > 0 {
		result = resBody.Data
	}

	return
}

func (cli *Client) GetWard(districtId int64) (result []*model.WardNhatTin, err error) {
	type myResponse struct {
		Success bool                 `json:"success"`
		Message string               `json:"message"`
		Data    []*model.WardNhatTin `json:"data"`
	}

	query := map[string]string{
		"district_id": strconv.Itoa(int(districtId)),
	}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, query, nil, pathGetWard, nil)
	if err != nil {
		return
	}

	resBody := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return
	}

	if !resBody.Success {
		err = fmt.Errorf("%v", resBody)
		return
	}

	if len(resBody.Data) > 0 {
		result = resBody.Data
	}

	return
}

func (cli *Client) GetOrderDetail(trackingCode string) (result *model.NTOrder, err error) {
	type myResponse struct {
		Success bool             `json:"success"`
		Message string           `json:"message"`
		Data    []*model.NTOrder `json:"data"`
	}

	query := map[string]string{
		"bill_code": trackingCode,
	}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, query, nil, pathGetDetailOrder, nil)
	if err != nil {
		return
	}
	resBody := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return
	}

	if !resBody.Success {
		err = fmt.Errorf("%v", resBody)
		return
	}

	if len(resBody.Data) > 0 {
		result = resBody.Data[0]
	}

	return
}
