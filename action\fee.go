package action

import (
	"errors"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/utils"
	"go.mongodb.org/mongo-driver/bson"
)

func GetConfigFee(query model.ConfigFee, offset int64, limit int64, getTotal bool) *common.APIResponse {
	filter := bson.M{
		"is_deleted": false,
	}
	if query.ConfigId != 0 {
		filter["config_id"] = query.ConfigId
	}
	if query.Name != "" {
		filter["config_name"] = query.Name
	}
	if query.ShippingOrderTypes != nil && len(*query.ShippingOrderTypes) > 0 {
		filter["shipping_order_types"] = bson.M{
			"$in": query.ShippingOrderTypes,
		}
	}
	if query.Keyword != "" {
		filter["keyword"] = bson.M{
			"$regex": utils.ToRawText(query.Keyword),
		}
	}

	if len(query.ConfigFeeTypes) > 0 {
		filter["config_fee_types"] = bson.M{
			"$in": query.ConfigFeeTypes,
		}
	}

	if query.IsApplyToArea != nil {
		if *query.IsApplyToArea {
			filter["is_apply_to_area"] = *query.IsApplyToArea
		} else {
			filter["is_apply_to_area"] = bson.M{
				"$ne": true,
			}
		}
	}

	result := model.ConfigFeeDB.Query(filter, offset, limit, nil)
	if getTotal {
		countResult := model.ConfigFeeDB.Count(filter)
		result.Total = countResult.Total
	}

	return result
}

func CreateConfigFee(newConfig *model.ConfigFee) *common.APIResponse {
	if newConfig.Name == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Tên biểu phí không được để trống",
		}
	}

	if newConfig.Code == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã biểu phí không được để trống",
		}
	}

	if newConfig.IsApplyToArea == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Vui lòng chọn áp dụng khu vực",
		}
	}

	if !*newConfig.IsApplyToArea && len(newConfig.FeeStructures) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Biểu phí không được để trống",
		}
	}

	err := validateFeeStructure(*newConfig)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Error(),
		}
	}

	buildErr := buildFeeStructure(newConfig)
	if buildErr != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: buildErr.Error(),
		}
	}

	checkDupName := model.ConfigFeeDB.QueryOne(bson.M{
		"$or": []interface{}{
			bson.M{"config_name": newConfig.Name},
			bson.M{"code": newConfig.Code},
		},
		"is_deleted": false,
	})

	if checkDupName.Status == common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Tên biểu phí hoặc mã biểu phí đã tồn tại.",
		}
	}

	newConfig.ConfigId = model.GenId("FEE_CONFIG_ID")

	newKeyword, err := utils.GenKeyword(newConfig.Name, newConfig.Code)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không thể tạo từ khóa cho carrier",
		}
	}
	newConfig.Keyword = newKeyword

	// Validate fee structure
	newConfig.IsDeleted = &enum.False
	createResult := model.ConfigFeeDB.Create(newConfig)
	if createResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: createResult.Message,
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Tạo biểu phí thành công",
	}
}

func UpdateConfigFee(updateConfig *model.ConfigFee) *common.APIResponse {
	if updateConfig.ConfigId == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Id biểu phí không được để trống",
		}
	}

	err := validateFeeStructure(*updateConfig)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Error(),
		}
	}

	buildErr := buildFeeStructure(updateConfig)
	if buildErr != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: buildErr.Error(),
		}
	}

	// Không query theo deleted, để case vận hành xóa nhầm thì dùng api này bật lại được
	storedConfigFee := model.ConfigFeeDB.QueryOne(bson.M{
		"config_id": updateConfig.ConfigId,
	})
	if storedConfigFee.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không tìm thấy biểu phí",
		}
	}
	configFee := storedConfigFee.Data.([]*model.ConfigFee)[0]

	// Nếu muốn xóa biểu phí thì xem biểu phí có được gán cho khách hàng nào không
	if updateConfig.IsDeleted != nil &&
		*updateConfig.IsDeleted {
		customerRaw := model.CustomerDB.QueryOne(bson.M{
			"applied_fees.config_fee_id": configFee.ConfigId,
		})

		// Không được xóa biểu phí mặc định
		if configFee.Code == conf.Config.DefaultFMDropOffFeeCode ||
			configFee.Code == conf.Config.DefaultFMPickUpFeeCode ||
			configFee.Code == conf.Config.DefaultCrossRegionDropOffFee {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không được xóa biểu phí mặc định",
			}
		}

		if customerRaw.Status == common.APIStatus.Ok {
			customer := customerRaw.Data.([]*model.Customer)[0]
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Tồn tại khách hàng: " + customer.Name + " hiện đang sử dụng biểu phí.",
			}
		}
	}

	newKeyword, err := utils.GenKeyword(updateConfig.Name, updateConfig.Code)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không thể tạo từ khóa cho carrier",
		}
	}
	updateConfig.Keyword = newKeyword
	return model.ConfigFeeDB.UpdateOne(bson.M{
		"config_id": updateConfig.ConfigId,
	}, updateConfig)

}

func validateFeeStructure(configFee model.ConfigFee) error {
	feeStructures := configFee.FeeStructures
	if configFee.IsApplyToArea != nil && *configFee.IsApplyToArea {
		for _, cf := range configFee.FeeAreaList {
			feeStructures = append(feeStructures, cf.FeeStructures...)
		}
	}
	for _, feeStructure := range feeStructures {
		for key, _ := range feeStructure.Fees {
			if !utils.CheckExistInEnum(key, *enum.FeeType) {
				return errors.New("Loại phí không hợp lệ")
			}
		}
		// Chỉ có chính xác 2 phép so sánh <, > đứng một mình ở chặn trên và chặn dưới
		for _, condition := range feeStructure.FeeConditions {
			if !utils.CheckExistInEnum(condition.ConditionType, *enum.FeeConditionType) {
				return errors.New("Loại điều kiện phí không hợp lệ")
			}
			if !utils.CheckExistInEnum(condition.Method, *enum.ComparableMethod) {
				return errors.New("Phép so sánh không hợp lệ")
			}
		}
	}
	if configFee.AdditionalFees != nil {
		for _, additionalFee := range *configFee.AdditionalFees {
			if !utils.CheckExistInEnum(additionalFee.AdditionalFeeType, *enum.AdditionalFeeType) {
				return errors.New("Loại phụ phí không hợp lệ")
			}
		}
	}

	return nil
}

func MigrateConfigFeeKeyWord() *common.APIResponse {
	configsRaw := model.ConfigFeeDB.QueryAll()
	configs := configsRaw.Data.([]*model.ConfigFee)

	for _, config := range configs {
		newKeyword, err := utils.GenKeyword(config.Name, config.Code)
		if err != nil {
			continue
		}
		config.Keyword = newKeyword
		model.ConfigFeeDB.UpdateOne(bson.M{
			"config_id": config.ConfigId,
		}, config)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Ok",
	}
}

func buildFeeStructure(configFee *model.ConfigFee) error {
	if configFee.IsApplyToArea != nil && *configFee.IsApplyToArea {
		if len(configFee.FeeAreaList) == 0 {
			return errors.New("Không có khu vực nào được chọn")
		}

		for _, cf := range configFee.FeeAreaList {
			if cf.FeeArea == nil {
				return errors.New("Không có khu vực nào được chọn")
			}
			if cf.FeeArea.Type == enum.FeeArea.HUB_TO_WH ||
				cf.FeeArea.Type == enum.FeeArea.WH_TO_HUB {
				hubRaw := model.HubDB.QueryOne(bson.M{
					"warehouse_reference_code": cf.FeeArea.WarehouseCode,
				})
				if hubRaw.Status != common.APIStatus.Ok {
					return errors.New("Không tìm thấy hub gán với kho: " + cf.FeeArea.WarehouseCode)
				}
				hub := hubRaw.Data.([]*model.Hub)[0]

				if cf.FeeArea.Type == enum.FeeArea.HUB_TO_WH {
					if len(cf.FeeArea.FromAreaIdentifiers) == 0 {
						return errors.New("Vui lòng chọn khu vực gửi hàng")
					}
					cf.FeeArea.ToAreaIdentifiers = []model.AreaIdentifier{
						{
							Code: hub.Code,
							Name: hub.Name,
						},
					}
				}

				if cf.FeeArea.Type == enum.FeeArea.WH_TO_HUB {
					if len(cf.FeeArea.ToAreaIdentifiers) == 0 {
						return errors.New("Vui lòng chọn khu vực nhận hàng")
					}
					cf.FeeArea.FromAreaIdentifiers = []model.AreaIdentifier{
						{
							Code: hub.Code,
							Name: hub.Name,
						},
					}
				}
			}

			if cf.FeeArea.Type == enum.FeeArea.WH_TO_ZONE ||
				cf.FeeArea.Type == enum.FeeArea.ZONE_TO_WH {
				hubRaw := model.HubDB.QueryOne(bson.M{
					"warehouse_reference_code": cf.FeeArea.WarehouseCode,
				})
				if hubRaw.Status != common.APIStatus.Ok {
					return errors.New("Không tìm thấy hub gán với kho: " + cf.FeeArea.WarehouseCode)
				}
				hub := hubRaw.Data.([]*model.Hub)[0]

				if cf.FeeArea.Type == enum.FeeArea.WH_TO_ZONE {
					if len(cf.FeeArea.ToAreaIdentifiers) == 0 {
						return errors.New("Vui lòng chọn khu vực nhận hàng")
					}
					cf.FeeArea.FromAreaIdentifiers = []model.AreaIdentifier{
						{
							Code: hub.Code,
							Name: hub.Name,
						},
					}
				}
				if cf.FeeArea.Type == enum.FeeArea.ZONE_TO_WH {
					if len(cf.FeeArea.FromAreaIdentifiers) == 0 {
						return errors.New("Vui lòng chọn khu vực gửi hàng")
					}
					cf.FeeArea.ToAreaIdentifiers = []model.AreaIdentifier{
						{
							Code: hub.Code,
							Name: hub.Name,
						},
					}
				}
			}

			if cf.FeeArea.Type == enum.FeeArea.PROVINCE_TO_WH ||
				cf.FeeArea.Type == enum.FeeArea.WH_TO_PROVINCE {
				hubRaw := model.HubDB.QueryOne(bson.M{
					"warehouse_reference_code": cf.FeeArea.WarehouseCode,
				})
				if hubRaw.Status != common.APIStatus.Ok {
					return errors.New("Không tìm thấy hub gán với kho: " + cf.FeeArea.WarehouseCode)
				}
				hub := hubRaw.Data.([]*model.Hub)[0]
				if cf.FeeArea.Type == enum.FeeArea.PROVINCE_TO_WH {
					if len(cf.FeeArea.FromAreaIdentifiers) == 0 {
						return errors.New("Vui lòng chọn khu vực gửi hàng")
					}
					cf.FeeArea.ToAreaIdentifiers = []model.AreaIdentifier{
						{
							Code: hub.Code,
							Name: hub.Name,
						},
					}
				}

				if cf.FeeArea.Type == enum.FeeArea.WH_TO_PROVINCE {
					if len(cf.FeeArea.ToAreaIdentifiers) == 0 {
						return errors.New("Vui lòng chọn khu vực nhận hàng")
					}
					cf.FeeArea.FromAreaIdentifiers = []model.AreaIdentifier{
						{
							Code: hub.Code,
							Name: hub.Name,
						},
					}
				}
			}
		}
	}

	return nil
}

func GetDefaultConfigFee(shippingOrderType enum.ShippingOrderTypeValue) *common.APIResponse {
	if shippingOrderType == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Loại đơn không được để trống",
		}
	}
	if shippingOrderType == enum.ShippingOrderType.EO {
		configFeeRaw := model.ConfigFeeDB.Query(bson.M{
			"code": bson.M{
				"$in": []string{
					conf.Config.DefaultEOPickNDeliCode,
					conf.Config.DefaultEOTransportFeeCode,
					conf.Config.DefaultEOPickNTransFeeCode,
					conf.Config.DefaultEOTransNDeliCode,
				},
			},
		}, 0, 4, nil)
		if configFeeRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không tìm thấy biểu phí mặc định",
			}
		}
		configFees := configFeeRaw.Data.([]*model.ConfigFee)
		if len(configFees) < 4 {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không tìm thấy biểu phí mặc định",
			}
		}
		for i, configFee := range configFees {
			switch configFee.Code {
			case conf.Config.DefaultEOPickNDeliCode:
				configFees[i].DefaultConfigFeeType = enum.ConfigFeeType.PICK_N_DELIVERY
			case conf.Config.DefaultEOTransportFeeCode:
				configFees[i].DefaultConfigFeeType = enum.ConfigFeeType.TRANSPORT
			case conf.Config.DefaultEOPickNTransFeeCode:
				configFees[i].DefaultConfigFeeType = enum.ConfigFeeType.PICK_N_TRANSPORT
			case conf.Config.DefaultEOTransNDeliCode:
				configFees[i].DefaultConfigFeeType = enum.ConfigFeeType.TRANSPORT_N_DELIVERY
			}
		}
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Data:    configFees,
			Message: "Ok",
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Invalid,
		Message: "Không tìm thấy biểu phí mặc định",
	}
}
