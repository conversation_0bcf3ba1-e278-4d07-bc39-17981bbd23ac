package model

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"go.mongodb.org/mongo-driver/mongo"
)

type Config struct {
	ProvinceCode  string           `json:"provinceCode" bson:"province_code,omitempty"`
	ProvinceName  string           `json:"provinceName" bson:"province_name,omitempty"`
	DistrictRoute []*DistrictRoute `json:"districtRoute" bson:"district_route"`
	Carriers      []*Available     `json:"carriers" bson:"carriers"`
	WarehouseCode string           `json:"warehouseCode" bson:"warehouse_code,omitempty"`
}

type DistrictRoute struct {
	DistrictCode string       `json:"districtCode" bson:"district_code,omitempty"`
	DistrictName string       `json:"districtName" bson:"district_name,omitempty"`
	WardRoute    []*WardRoute `json:"wardRoute" bson:"ward_route"`
	Available    []*Available `json:"available" bson:"available,omitempty"`
}

type WardRoute struct {
	WardCode  string       `json:"wardCode" bson:"ward_code,omitempty"`
	WardName  string       `json:"wardName" bson:"ward_name,omitempty"`
	Available []*Available `json:"available" bson:"available,omitempty"`
}

type Available struct {
	CarrierId  int64       `json:"carrierId"  bson:"carrier_id,omitempty"`
	Conditions []Condition `json:"conditions" bson:"conditions,omitempty"`
}

type Condition struct {
	Method        string                   `json:"method" bson:"method,omitempty"`
	NumPackage    int64                    `json:"numPackage" bson:"num_package,omitempty"`
	TypeCondition *enum.TypeConditionValue `json:"typeCondition" bson:"type_condition,omitempty"`
	Weight        float64                  `json:"weight" bson:"weights,omitempty"`
	Price         float64                  `json:"price" bson:"price,omitempty"`
}

var ConfigDB = &db.Instance{
	ColName:        "config",
	TemplateObject: &Config{},
}

func InitConfigModel(s *mongo.Database) {
	ConfigDB.ApplyDatabase(s)
}
