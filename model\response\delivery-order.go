package response

import (
	"time"

	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type DeliveryOrder struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	VersionNo       string             `json:"versionNo,omitempty" bson:"version_no,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	CreatedBy       string             `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	UpdatedBy       string             `json:"updatedBy,omitempty" bson:"updated_by,omitempty"`
	WarehouseCode   string             `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`

	DeliveryOrderCode     string                     `json:"deliveryOrderCode,omitempty" bson:"delivery_order_code,omitempty"`
	SaleOrderCode         string                     `json:"saleOrderCode,omitempty" bson:"sale_order_code,omitempty"`
	ReferenceID           int64                      `json:"referenceID,omitempty" bson:"reference_id,omitempty"`
	OutboundTime          *time.Time                 `json:"outboundTime,omitempty" bson:"outbound_time,omitempty"`
	DeliveredTime         *time.Time                 `json:"deliveredTime,omitempty" bson:"delivered_time,omitempty"`
	CompletedTime         *time.Time                 `json:"completedTime,omitempty" bson:"completed_time,omitempty"`
	Status                *enum.SaleOrderStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	TotalAmount           float64                    `json:"totalAmount,omitempty" bson:"total_amount,omitempty"`
	DeliveryAmount        float64                    `json:"deliveryAmount,omitempty" bson:"delivery_amount,omitempty"`
	CODAmount             float64                    `json:"codAmount,omitempty" bson:"cod_amount,omitempty"`
	PaymentMethod         *enum.PaymentMethodValue   `json:"paymentMethod,omitempty" bson:"payment_method,omitempty"`
	PaymentMethodName     string                     `json:"paymentMethodName,omitempty" bson:"payment_method_name,omitempty"`
	CollectOnDelivery     *bool                      `json:"collectOnDelivery,omitempty" bson:"collect_on_delivery,omitempty"`
	TrackingCode          string                     `json:"trackingCode,omitempty" bson:"tracking_code,omitempty"`
	CarrierCode           string                     `json:"carrierCode,omitempty" bson:"carrier_code,omitempty"`
	CarrierName           string                     `json:"carrierName,omitempty" bson:"carrier_name,omitempty"`
	EstimateDeliveredTime *time.Time                 `json:"estimateDeliveredTime,omitempty" bson:"estimate_delivered_time,omitempty"` // Thời gian dự giao hàng thành công
	OrderNote             string                     `json:"orderNote,omitempty" bson:"order_note,omitempty"`
	FeeAmount             struct {
		Delivery float64 `json:"deliveryFee,omitempty" bson:"delivery_fee,omitempty"`
		Extra    float64 `json:"extraFee,omitempty" bson:"extra_fee,omitempty"`
	} `json:"fee,omitempty" bson:"fee,omitempty"`
	DiscountAmount struct {
		Voucher       float64 `json:"voucherAmount,omitempty" bson:"voucher_amount,omitempty"`
		PaymentMethod float64 `json:"paymentMethod,omitempty" bson:"payment_method,omitempty"`
	} `json:"discountAmount,omitempty" bson:"discount_amount,omitempty"`
	Tags []string `json:"tags,omitempty" bson:"tags,omitempty"`
}

type DeliveryOrderItem struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	VersionNo       string             `json:"versionNo,omitempty" bson:"version_no,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	CreatedBy       string             `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	UpdatedBy       string             `json:"updatedBy,omitempty" bson:"updated_by,omitempty"`

	DeliveryOrderCode string  `json:"deliveryOrderCode,omitempty" bson:"delivery_order_code,omitempty"`
	SaleOrderCode     string  `json:"saleOrderCode,omitempty" bson:"sale_order_code,omitempty"`
	SaleOrderLineID   int64   `json:"saleOrderLineID,omitempty" bson:"sale_order_line_id,omitempty"`
	ReferenceID       int64   `json:"referenceID,omitempty" bson:"reference_id,omitempty"`
	LineId            int64   `json:"lineId,omitempty" bson:"line_id,omitempty"`
	SKU               string  `json:"sku,omitempty" bson:"sku,omitempty"`
	Name              string  `json:"name,omitempty" bson:"name,omitempty"`
	ImageURL          string  `json:"imageUrl,omitempty" bson:"image_url,omitempty"`
	Packaging         string  `json:"packaging,omitempty" bson:"packaging,omitempty"`
	Weight            float64 `json:"weight,omitempty" bson:"weight,omitempty"`
	Unit              string  `json:"unit,omitempty" bson:"unit,omitempty"`
	OutboundQuantity  int64   `json:"outboundQuantity" bson:"outbound_quantity,omitempty"`
	DeliveredQuantity int64   `json:"deliveredQuantity" bson:"delivered_quantity,omitempty"`
	ReturnedQuantity  int64   `json:"returnedQuantity" bson:"returned_quantity,omitempty"`
	UnitPrice         float64 `json:"unitPrice,omitempty" bson:"unit_price,omitempty"`
	RateTax           int64   `json:"rateTax,omitempty" bson:"rateTax,omitempty"`

	// Tags
	Tags []string `json:"tags,omitempty"`
}
