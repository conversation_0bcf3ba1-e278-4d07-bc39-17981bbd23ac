package action

import (
	"errors"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/shipping"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"math"
)

type be struct{}

var Be = &be{}

// Rounding Logic:
// If the decimal part ≥ 500, round up to the nearest 1,000.
// If the decimal part < 500, round down to the nearest 1,000.
// Examples:
// 198,400 → 198,000 (since 400 < 500 → round down)
// 198,500 → 199,000 (since 500 ≥ 500 → round up)
func roundUpToMultipleOf1000(n int) int {
	if n%1000 == 0 {
		return n
	}
	return int(math.Round(float64(n)/1000)) * 1000
}

func (be) BookShippingOrder(
	input *request.BookShippingOrder,
	carrierModel *model.Carrier) (*model.ShippingInfo, error) {
	if shipping.TplShippingClient.BeClient == nil {
		err := errors.New("Dịch vụ book Ahamove chưa được khởi tạo")
		return nil, err
	}
	toAddress := input.To.Address + ", " +
		input.To.WardName + ", " +
		input.To.DistrictName + ", " +
		input.To.ProvinceName
	fromAddress := input.From.Address + ", " +
		input.From.WardName + ", " +
		input.From.DistrictName + ", " +
		input.From.ProvinceName

	paymentMode := 18
	cod := roundUpToMultipleOf1000(int(input.CODAmount))
	requestBook := request.BookBeRequest{
		ServiceType:   carrierModel.Service,
		PartnerUserId: input.ReferenceCode,
		OrderInfo: request.BeOrderInfo{
			ReferenceId: input.ReferenceCode,
			PaymentMode: paymentMode,
			Note:        input.Note,
		},
		Pickup: request.BePickup{
			Longitude:   input.From.Longitude,
			Latitude:    input.From.Latitude,
			FullAddress: fromAddress,
			PhoneNumber: input.From.Phone,
			Name:        input.From.Name,
		},
		Dropoff: []request.BeDropoff{
			{
				Longitude:   input.To.Longitude,
				Latitude:    input.To.Latitude,
				FullAddress: toAddress,
				Name:        input.To.Name,
				PhoneNumber: input.To.Phone,
				Cod:         float64(cod),
				Priority:    0,
			},
		},
	}
	shippingInfo, err := shipping.TplShippingClient.BeClient.CreateTrackingBe(requestBook, carrierModel)
	if err != nil {
		return nil, err
	}
	return shippingInfo, nil
}
