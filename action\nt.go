package action

import (
	"fmt"
	"strconv"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/shipping"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"go.mongodb.org/mongo-driver/bson"
)

type nt struct{}

var NT nt

func (nt) BookShipping(input *request.BookShipping, saleOrder *model.SaleOrder, carrierModel *model.Carrier, pickupAddress *model.Address) (result *model.ShippingInfo, err error) {
	if shipping.TplShippingClient.NhatTinClient == nil {
		err = fmt.Errorf("Dịch vụ book NHAT_TIN chưa được khởi tạo")
		return
	}

	if len(carrierModel.Service) == 0 {
		err = fmt.Errorf("Không tìm thấy dịch vụ vận chuyển phù hợp")
	}

	requestBooking := request.BookNTRequest{
		SName:         pickupAddress.Name,
		PartnerId:     carrierModel.ExtraData.PartnerId,
		SPhone:        pickupAddress.Phone,
		SAddress:      pickupAddress.Address,
		SAddressValue: pickupAddress.Address,
		RPhone:        saleOrder.CustomerInfos.Delivery.Phone,
		RAddress:      saleOrder.CustomerInfos.Delivery.Address,
		RName:         saleOrder.CustomerInfos.Delivery.Name,
		CODAmount:     saleOrder.CODAmount,
		Weight:        input.Weight,
		PackageNo:     input.NbOfPackages,
		Note:          input.DeliveryNote,
		CargoContent:  "Thuốc",
		RefCode:       input.SO,
		UTMSource:     "api_thuocsi",
	}

	if *saleOrder.PaymentMethod == enum.PaymentMethod.BANK {
		requestBooking.CODAmount = 0
	}

	paymentMethod, err := strconv.Atoi(carrierModel.PaymentMethod)

	if err != nil {
		err = fmt.Errorf("Phương thức thanh toán với nhà vận chuyển không hợp lệ")
		return
	}

	service, err := strconv.Atoi(carrierModel.Service)

	if err != nil {
		err = fmt.Errorf("Dịch vụ vận chuyển của ", carrierModel.CarrierName, " Không hợp lệ")
		return
	}

	requestBooking.ServiceId = int64(service)

	requestBooking.PaymentMethod = int64(paymentMethod)
	requestBooking.SProvinceId, requestBooking.SDistrictId, requestBooking.SWardId, err = compareMasterDataNT(pickupAddress.ProvinceName, pickupAddress.DistrictName, pickupAddress.WardName, input.WarehouseCode)
	if err != nil {
		return
	}

	requestBooking.RProvinceId, requestBooking.RDistrictId, requestBooking.RWardId, err = compareMasterDataNT(saleOrder.CustomerInfos.Delivery.Province, saleOrder.CustomerInfos.Delivery.District, saleOrder.CustomerInfos.Delivery.Ward, input.WarehouseCode)
	if err != nil {
		return
	}

	// Giá trị hàng hóa mặc định không quá 3 củ
	requestBooking.CargoValue = saleOrder.DeliveryAmount
	if requestBooking.CargoValue > carrierModel.InsuranceValue {
		requestBooking.CargoValue = carrierModel.InsuranceValue
	}

	result, err = shipping.TplShippingClient.NhatTinClient.CreateTrackingNhatTin(requestBooking)
	if err != nil {
		return
	}

	result.CODAmount = requestBooking.CODAmount

	return
}

func compareMasterDataNT(provinceName, districtName, wardName, warehouseCode string) (provinceId, districtId, wardId int64, err error) {
	provinceMappingNT := model.ProvinceMappingDB.QueryOne(bson.M{
		"dictionary": provinceName,
	})

	if provinceMappingNT.Status != common.APIStatus.Ok || provinceMappingNT.Data == nil {
		err = fmt.Errorf("Không tìm thấy thông tin ", provinceName)
		return
	}
	provinceMappingNTModel := provinceMappingNT.Data.([]*model.ProvinceMapping)[0]

	districtMappingNT := model.DistrictMappingDB.QueryOne(bson.M{
		"dictionary":    districtName,
		"province_code": provinceMappingNTModel.Code,
	})

	if districtMappingNT.Status != common.APIStatus.Ok || districtMappingNT.Data == nil {
		err = fmt.Errorf("Không tìm thấy thông tin ", districtName+", "+provinceName)
		return
	}

	districtMappingNTModel := districtMappingNT.Data.([]*model.DistrictMapping)[0]

	wardMappingNTModel := new(model.WardMapping)

	wardMappingNT := model.WardMappingDB.QueryOne(bson.M{
		"dictionary":    wardName,
		"district_code": districtMappingNTModel.Code,
	})

	if wardMappingNT.Status == common.APIStatus.Ok {
		wardMappingNTModel = wardMappingNT.Data.([]*model.WardMapping)[0]
	}

	return provinceMappingNTModel.NhatTinId, districtMappingNTModel.NhatTinId, wardMappingNTModel.NhatTinId, err
}
