package action

import (
	"fmt"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"go.mongodb.org/mongo-driver/bson"
)

func CreateReconcile(hubShippingOrder *model.HubShippingOrder, reconcileType string) (*common.APIResponse, bool) {
	carrierCode := string(*hubShippingOrder.TplCode)
	// Lấy carrier cha nếu có
	response := model.CarrierDB.QueryOne(&bson.M{"carrier_code": carrierCode})
	if response.Status == common.APIStatus.Ok {
		carrier := response.Data.([]*model.Carrier)[0]
		if carrier.ParentCode != nil && *carrier.ParentCode != "" {
			carrierCode = string(*carrier.ParentCode)
		}
	}

	createReconcileSession := &request.CreateReconcileSessionRequest{
		CarrierCode:   carrierCode,
		ReconcileType: reconcileType,
		HubCode:       hubShippingOrder.HUBCode,
		UserId:        int(hubShippingOrder.DriverID),
		Fullname:      hubShippingOrder.DriverName,
	}

	response = client.Services.AccountingClient.CreateReconcileSession(createReconcileSession)
	if response.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Tạo đối soát thất bại.",
		}, true
	} else {
		deliveredTime := time.Unix(hubShippingOrder.DeliveredTime, 0)

		paymentMethod := "COD"
		if hubShippingOrder.PaymentMethod != nil {
			paymentMethod = string(*hubShippingOrder.PaymentMethod)
		}
		createReconcileSessionOrder := &request.CreateReconcileSessionOrderRequest{
			HubCode:             hubShippingOrder.HUBCode,
			UserId:              int(hubShippingOrder.DriverID),
			ReferenceCode:       hubShippingOrder.ReferenceCode,
			TrackingCode:        hubShippingOrder.TrackingCode,
			ReconcileType:       reconcileType,
			CODAmount:           hubShippingOrder.CODAmount,
			TplCode:             hubShippingOrder.TplCode,
			PaymentMethod:       paymentMethod,
			CarrierCode:         string(*hubShippingOrder.TplCode),
			DeliveredTime:       &deliveredTime,
			DeliveryAmount:      hubShippingOrder.DeliveryAmount,
			ParentReferenceCode: hubShippingOrder.ParentReferenceCode,
			OrderType:           hubShippingOrder.SubType,
		}

		if hubShippingOrder.SubType != nil &&
			*hubShippingOrder.SubType == enum.SubType.EO {
			// Nếu là đơn lấy hàng VÀ thu phí người gửi thì tạo đối soát với phí lấy hàng
			// COD CHỈ thu ở nguời nhận nên COD sẽ bằng 0
			if hubShippingOrder.Status != nil &&
				(*hubShippingOrder.Status == enum.HubShippingOrderStatus.WAIT_TO_STORING ||
					*hubShippingOrder.Status == enum.HubShippingOrderStatus.STORING) &&
				hubShippingOrder.FeeSenderAmount != nil {
				if hubShippingOrder.FeeSenderAmount != nil {
					temp := int64(*hubShippingOrder.FeeSenderAmount)
					createReconcileSessionOrder.DeliveryFeeAmount = &temp
				}
				createReconcileSessionOrder.CODAmount = 0
			}

			if hubShippingOrder.Status != nil &&
				*hubShippingOrder.Status == enum.HubShippingOrderStatus.DELIVERED {
				createReconcileSessionOrder.CODAmount = hubShippingOrder.CODAmount
				if hubShippingOrder.FeeReceiverAmount != nil {
					if hubShippingOrder.FeeReceiverAmount != nil {
						temp := int64(*hubShippingOrder.FeeReceiverAmount)
						createReconcileSessionOrder.DeliveryFeeAmount = &temp

					}
				}
			}

			shippingOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
				"reference_code": hubShippingOrder.ReferenceCode,
			})
			if shippingOrderRaw.Status == common.APIStatus.Ok {
				shippingOrder := shippingOrderRaw.Data.([]*model.ShippingOrder)[0]
				if shippingOrder.FeeCollectMethod != nil &&
					*shippingOrder.FeeCollectMethod == enum.FeeCollectMethod.RECEIVER_PAY {
					createReconcileSessionOrder.IsMerged = &enum.True
				}
			}
		}

		response = client.Services.AccountingClient.CreateReconcileSessionOrder(createReconcileSessionOrder)
		if response.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Ok,
				Message: "Tạo đối soát thất bại.",
			}, true
		}
	}

	return nil, false
}

func ProcessReconcileNotify(input request.ProcessReconcileNotifyRequest, account *model.ActionSource) *common.APIResponse {

	if input.Action != "" {
		var notificationTitle string
		var notificationDescription string
		var hubNotificationTitle string
		var hubNotificationDescription string
		var userNames []string

		switch input.Action {
		case "CANCEL":
			notificationTitle = "Phiếu nộp tiền đã bị hủy"
			notificationDescription = fmt.Sprintf("Phiếu đối soát %s đã bị hủy, vui lòng kiểm tra", input.ShortCode)

			hubNotificationTitle = "Phiên đối soát đã bị hủy"
			hubNotificationDescription = fmt.Sprintf("Phiên đối soát %s đã bị hủy", input.ShortCode)
		case "DONE":
			notificationTitle = "Phiếu nộp tiền đã được thông qua"
			notificationDescription = fmt.Sprintf("Phiếu đối soát %s đã được thông qua, vui lòng kiểm tra", input.ShortCode)

			hubNotificationTitle = "Phiên đối soát đã được thông qua"
			hubNotificationDescription = fmt.Sprintf("Phiên đối soát %s đã được thông qua", input.ShortCode)
		case "WAIT_TO_APPROVE":
			notificationTitle = "Gửi yêu cầu nộp tiền thành công"
			notificationDescription = fmt.Sprintf("Bạn đã gửi yêu cầu nộp tiền thành công %s, vui lòng kiểm tra", input.ShortCode)

			hubNotificationTitle = "Bạn nhận được yêu cầu đối soát"
			hubNotificationDescription = fmt.Sprintf("Bạn có một phiên đối soát %s mới", input.ShortCode)
		}

		// Send notify theo quan hệ cha con
		// Lấy thu ngân và q.lý hub nếu k có q.lý hub -> lấy hub điều phối
		for roleCodeParent, roleCodeChild := range conf.Config.DefaultHubRoleNotify {
			userNames = addUserNameToSendNotification(input.HubCode, []string{roleCodeParent}, roleCodeChild, userNames)
		}

		// Thông báo riêng phiếu này cho tài xế nếu có
		if input.UserName != "" {
			_ = client.Services.NotificationClient.SendNotification(notificationTitle, notificationDescription, []string{input.UserName}, "ReconcileDetail", input.Params)
		}

		// Gửi theo danh sách tài khoản trong HUB cấp thu ngân/q.lý/đpgh theo ref id
		if len(userNames) > 0 {
			_ = client.Services.NotificationClient.SendNotification(hubNotificationTitle, hubNotificationDescription, userNames, "HubReconcileDetail", input.Params)
		}

		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "OK",
		}
	}
	for _, val := range input.ReferenceCodes {
		// Xác định đơn đã giao và được đối soát
		response := model.HUBShippingOrderDB.QueryOne(bson.M{
			"reference_code": val,
			"hub_code":       input.HubCode,
		})
		if response.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Không tìm thấy đơn hàng để xử lý",
			}
		}

		hubShippingOrder := response.Data.([]*model.HubShippingOrder)[0]
		// Update thành COD_COLLECTED
		switch input.ReconcileType {
		case string(enum.ReconcileTypeRequest.RIDER_HUB):
			// hub duyệt deliverd - cod
			hubShippingOrder.Status = &enum.HubShippingOrderStatus.COD_COLLECTED
			break
		case string(enum.ReconcileTypeRequest.HUB_COMP):
			// hub duyệt cod - completed
			hubShippingOrder.Status = &enum.HubShippingOrderStatus.COMPLETED
			break
		default:
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Không phải trạng thái hợp lệ để cập nhật",
			}
		}

		if strings.HasPrefix(val, "PO") ||
			strings.HasPrefix(val, "PGH") {
			continue
		}

		response = UpdateStatusHubShippingOrder(hubShippingOrder, account)
		if response.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Chuyển trạng thái thất bại",
			}
		}
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}
}

func addUserNameToSendNotification(hubCode string, roleCodeParent []string, roleCodeChild []string, userNames []string) []string {

	accountResponse := model.AccountDB.Query(bson.M{
		"hub_code":  hubCode,
		"role_code": bson.M{"$in": roleCodeParent},
		"status":    enum.AccountStatus.ACTIVE,
	}, 0, 0, nil)
	// Nếu tìm thấy tầng cao nhất thì đi tiếp - còn không thì tìm đến con
	if accountResponse.Status == common.APIStatus.Ok {
		logisticAccount := accountResponse.Data.([]*model.Account)
		for _, val := range logisticAccount {
			userNames = append(userNames, val.Username)
		}
		return userNames
	}
	if len(roleCodeChild) > 0 {
		userNames = append(userNames, addUserNameToSendNotification(hubCode, roleCodeChild, []string{}, userNames)...)
	}
	return userNames
}

func UpdateReconcileCodCollected(hubShippingOrder *model.HubShippingOrder) *common.APIResponse {

	request := &request.ReconcileCodCollectedRequest{
		ReferenceCode: hubShippingOrder.ReferenceCode,
		TrackingCode:  hubShippingOrder.TrackingCode,
		HubCode:       hubShippingOrder.HUBCode,
		UserId:        hubShippingOrder.DriverID,
	}

	response := client.Services.AccountingClient.UpdateReconcileCodCollected(request)
	if response.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Cập nhật đối soát cho tài xế thất bại.",
		}
	}
	return response
}
