package action

import (
	"fmt"
	"strings"
	"time"

	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/shipping"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
)

type ghtk struct{}

var GHTK ghtk

func (ghtk) BookShipping(input *request.BookShipping, saleOrder *model.SaleOrder, carrierModel *model.Carrier, pickupAddress *model.Address, feeCollectOn *enum.FeeCollectMethodValue) (result *model.ShippingInfo, err error) {
	if shipping.TplShippingClient.GHTKClient == nil {
		err = fmt.Errorf("Dịch vụ book GHTK chưa được khởi tạo")
		return
	}

	if len(carrierModel.Service) == 0 {
		err = fmt.Errorf("Không tìm thấy dịch vụ vận chuyển phù hợp")
	}

	requestBooking := request.BookGHTKRequest{
		Products: []*request.GHTKProduct{
			{
				Name:        "Thuốc",
				Weight:      input.Weight,
				Quantity:    1,
				ProductCode: input.SO,
			},
		},
		Order: &request.GHTKOrder{
			Id:           input.SO,
			TotalWeight:  input.Weight,
			TotalBox:     input.NbOfPackages,
			PickAddress:  pickupAddress.Address,
			PickDistrict: pickupAddress.DistrictName,
			PickName:     pickupAddress.Name,
			PickProvince: pickupAddress.ProvinceName,
			PickWard:     pickupAddress.WardName,
			PickTel:      pickupAddress.Phone,
			Hamlet:       "Khác",
			IsFreeShip:   1,
			Transport:    carrierModel.Service,
			Address:      saleOrder.CustomerInfos.Delivery.Address,
			Province:     saleOrder.CustomerInfos.Delivery.Province,
			District:     saleOrder.CustomerInfos.Delivery.District,
			Tel:          saleOrder.CustomerInfos.Delivery.Phone,
			Email:        saleOrder.CustomerInfos.Delivery.Email,
			Note:         input.DeliveryNote,
			Ward:         saleOrder.CustomerInfos.Delivery.Ward,
			Name:         saleOrder.CustomerInfos.Name,
			PickMoney:    saleOrder.CODAmount,
		},
	}

	if saleOrder.PaymentMethod != nil && *saleOrder.PaymentMethod == enum.PaymentMethod.BANK {
		requestBooking.Order.PickMoney = 0
	}

	// Nếu người nhận trả fee thì thu phí + cod người nhận
	if feeCollectOn != nil && *feeCollectOn == enum.FeeCollectMethod.RECEIVER_PAY {
		requestBooking.Order.IsFreeShip = 0
	}

	requestBooking.Order.Value = int64(saleOrder.DeliveryAmount)
	if requestBooking.Order.Value > int64(carrierModel.InsuranceValue) {
		requestBooking.Order.Value = int64(carrierModel.InsuranceValue)
	}

	// Nếu không khai giá hàng hóa thì mặc định khai giá nhỏ nhất
	if requestBooking.Order.Value == 0 {
		requestBooking.Order.Value = int64(carrierModel.InsuranceValue)
	}

	result, err = shipping.TplShippingClient.GHTKClient.CreateTrackingGHTK(requestBooking)
	if err != nil {
		if strings.Contains(err.Error(), "Vui lòng chọn cụ thể địa chỉ") {
			// Get level 4 address
			res, getAddressErr := shipping.TplShippingClient.GHTKClient.GetLevel4Address(
				saleOrder.CustomerInfos.Delivery.Province,
				saleOrder.CustomerInfos.Delivery.District,
				saleOrder.CustomerInfos.Delivery.Ward,
				saleOrder.CustomerInfos.Delivery.Address,
			)

			if getAddressErr != nil || res == "" {
				err = fmt.Errorf("Không tìm thấy địa chỉ cấp 4")
				return
			}

			requestBooking.Order.Hamlet = res

			result, err = shipping.TplShippingClient.GHTKClient.CreateTrackingGHTK(requestBooking)

			if err != nil {
				return
			}

			result.CODAmount = requestBooking.Order.PickMoney
			return
		}

		return
	}

	result.CODAmount = requestBooking.Order.PickMoney

	return
}

func (ghtk) GetShippingFee(input *request.GetShippingService) (feeAmount float64) {
	var err error
	feeAmount, err = shipping.TplShippingClient.GHTKClient.GetShippingFee(input)
	if err != nil {
		return -1
	}
	return
}

func (ghtk) BookShippingOrder(
	input *request.BookShippingOrder,
	carrierModel *model.Carrier) (
	result *model.ShippingInfo, err error) {

	if shipping.TplShippingClient.GHTKClient == nil {
		err = fmt.Errorf("Dịch vụ book GHTK chưa được khởi tạo")
		return
	}

	// IsFreeShip:
	// 1 Được miễn phí vận chuyển, người nhận chỉ trả COD
	// 0 Người nhận trả COD + Phí vận chuyển

	requestBooking := request.BookGHTKRequest{
		Products: []*request.GHTKProduct{
			{
				Name:        "Thuốc",
				Weight:      input.Weight,
				Quantity:    1,
				ProductCode: input.ReferenceCode,
			},
		},
		Order: &request.GHTKOrder{
			Id:           input.ReferenceCode,
			TotalWeight:  input.Weight,
			TotalBox:     input.NumPackage,
			PickAddress:  input.From.Address,
			PickDistrict: input.From.DistrictName,
			PickName:     input.From.Name,
			PickProvince: input.From.ProvinceName,
			PickWard:     input.From.WardName,
			PickTel:      input.From.Phone,
			Hamlet:       "Khác",
			IsFreeShip:   1, // Mặc định sẽ là free ship
			Transport:    carrierModel.Service,
			Address:      input.To.Address,
			Province:     input.To.ProvinceName,
			District:     input.To.DistrictName,
			Tel:          input.To.Phone,
			Email:        input.To.Email,
			Note:         input.DeliveryNote,
			Ward:         input.To.WardName,
			Name:         input.To.Name,
			PickMoney:    input.CODAmount,
		},
	}

	requestBooking.Order.Value = int64(input.DeliveryAmount)
	if requestBooking.Order.Value > int64(carrierModel.InsuranceValue) {
		requestBooking.Order.Value = int64(carrierModel.InsuranceValue)
	}

	if requestBooking.Order.Value == 0 {
		requestBooking.Order.Value = int64(carrierModel.InsuranceValue)
	}

	// Nếu người nhận trả fee thì thu phí + cod người nhận
	if input.FeeCollectedOn != nil &&
		*input.FeeCollectedOn == enum.FeeCollectMethod.RECEIVER_PAY {
		requestBooking.Order.IsFreeShip = 0
	}

	result, err = shipping.TplShippingClient.GHTKClient.CreateTrackingGHTK(requestBooking)
	if err != nil {
		return
	}

	result.CODAmount = requestBooking.Order.PickMoney
	return
}

const (
	YYYYMMDD = "2006-01-02"
)

func (ghtk) BookOPMOrder(
	input *request.BookShippingOrder,
	carrierModel *model.Carrier) (
	*model.ShippingInfo, error) {

	if shipping.TplShippingClient.GHTKClient == nil {
		err := fmt.Errorf("Dịch vụ book GHTK chưa được khởi tạo")
		return nil, err
	}

	shipping.TplShippingClient.GHTKClient.ChangeInformation(carrierModel)

	now := time.Now().Format(YYYYMMDD)

	requestBooking := request.BookGHTKRequest{
		Details: []*request.GHTKDetails{
			{
				Value: *input.PickMoney,
				Type:  "opm",
				Date:  now,
			},
		},
		Order: &request.GHTKOrder{
			Id:           input.ReferenceCode,
			TotalWeight:  input.Weight,
			PickAddress:  input.From.Address,
			PickDistrict: input.From.DistrictName,
			PickName:     input.From.Name,
			PickProvince: input.From.ProvinceName,
			PickWard:     input.From.WardName,
			PickTel:      input.From.Phone,
			Hamlet:       "Khác",
			IsFreeShip:   1, // Mặc định sẽ là free ship
			Transport:    carrierModel.Service,
			Address:      input.To.Address,
			Province:     input.To.ProvinceName,
			District:     input.To.DistrictName,
			Tel:          input.To.Phone,
			Email:        input.To.Email,
			Note:         input.DeliveryNote,
			Ward:         input.To.WardName,
			Name:         input.To.Name,
			PickMoney:    *input.PickMoney,
			Value:        int64(*input.PickMoney),
			OPM:          1,
		},
		Products: nil,
	}

	// Nếu người nhận trả fee thì thu phí + cod người nhận
	if input.FeeCollectedOn != nil && *input.FeeCollectedOn == enum.FeeCollectMethod.RECEIVER_PAY {
		requestBooking.Order.IsFreeShip = 0
	}

	result, err := shipping.TplShippingClient.GHTKClient.CreateTrackingGHTK(requestBooking)
	if err != nil {
		return nil, err
	}
	return result, nil
}
