package action

import (
	"errors"
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func GetListLeadTimeConfig(query *request.LeadTimeQuery, offset int64, limit int64, getTotal bool) *common.APIResponse {
	filter := bson.M{}

	if query.CarrierCode != "" {
		filter["carrier_code"] = query.CarrierCode
	}

	if len(query.CarrierCodes) > 0 {
		filter["carrier_code"] = bson.M{
			"$in": query.CarrierCodes,
		}
	}
	if len(query.FromCodes) > 0 {
		filter["from_code"] = bson.M{
			"$in": query.FromCodes,
		}
	}
	if len(query.ToCodes) > 0 {
		filter["to_code"] = bson.M{
			"$in": query.ToCodes,
		}
	}

	if query.Type != nil {
		filter["type"] = query.Type
	}

	if query.Level > 0 {
		filter["level"] = query.Level
	}

	if query.ToCode != "" {
		filter["to_code"] = query.ToCode
	}

	if query.FromCode != "" {
		filter["from_code"] = query.FromCode
	}

	if query.Active != nil {
		filter["active"] = query.Active
	}

	result := model.ConfigLeadTimeDB.Query(
		filter,
		offset,
		limit,
		nil,
	)

	if getTotal {
		count := model.ConfigLeadTimeDB.Count(filter)
		result.Total = count.Total
	}

	return result
}
func ImportLeadTimeConfig(input []model.ConfigLeadTime) *common.APIResponse {
	if len(input) <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Data config không được để trống",
			ErrorCode: "DATA_CONFIG must not empty",
		}
	}
	for _, s := range input {
		_ = CreateLeadTimeConfig(&s)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Upload config LeadTime thành công",
	}
}
func CreateLeadTimeConfig(input *model.ConfigLeadTime) *common.APIResponse {
	if input.CarrierCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "NVC không được để trống",
			ErrorCode: "CARRIER_CODE must not empty",
		}
	}

	if input.ToCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã địa chỉ đến không được để trống",
			ErrorCode: "TO_CODE must not empty",
		}
	}

	if input.FromCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã địa chỉ đi không được để trống",
			ErrorCode: "FROM_CODE must not empty",
		}
	}

	if input.Type == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Loại không được để trống",
			ErrorCode: "TYPE must not empty",
		}
	}
	if input.CommitmentTime <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Commitment Time không được để trống",
			ErrorCode: "COMMITMENT_TIME must not empty",
		}
	}
	if input.Level <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "LEVEL không được để trống",
			ErrorCode: "LEVEL must not empty",
		}
	}

	createLeadTimeConfig := model.ConfigLeadTimeDB.Upsert(bson.M{
		"carrier_code": input.CarrierCode,
		"to_code":      input.ToCode,
		"from_code":    input.FromCode,
		"type":         input.Type,
		"level":        input.Level,
	}, input)

	if createLeadTimeConfig.Status != common.APIStatus.Ok {
		return createLeadTimeConfig
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Tạo config LeadTime thành công",
	}
}

// This func is used when calculate leadtime of shipping order
func CalculateLeadTime(req *request.CalculateLeadTimeRequest) *common.APIResponse {
	type leadTimeResponse struct {
		LeadTime int64 `json:"leadTime"`
	}

	if req.Partner == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Đơn vị vận chuyển không được để trống",
		}
	}

	if req.ServiceType == nil || *req.ServiceType == "" {
		req.ServiceType = new(enum.LeadTimeConfigValue)
		*req.ServiceType = enum.LeadTimeConfigType.DELIVERING
	}

	if !utils.CheckExistInEnum(*req.ServiceType, *enum.LeadTimeConfigType) {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Loại dịch vụ không hợp lệ",
		}
	}

	if req.From == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Địa chỉ gửi không được để trống",
		}
	}

	if req.To == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Địa chỉ nhận không được để trống",
		}
	}

	checkCarrier := model.CarrierDB.QueryOne(bson.M{
		"carrier_code": req.Partner,
	})

	if checkCarrier.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã đối tác không hợp lệ",
		}
	}

	buildFromAddressError := BuildAddress(req.From)
	if buildFromAddressError != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: buildFromAddressError.Message,
		}
	}
	buildToAddressError := BuildAddress(req.To)
	if buildToAddressError != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: buildToAddressError.Message,
		}
	}

	// Find if any direct lead-time configuration from -> to
	leadTimeConfig, _ := utils.ParseLeadTimeConfig(req.Partner, &model.Address{
		WardCode:     req.From.WardCode,
		DistrictCode: req.From.DistrictCode,
		ProvinceCode: req.From.ProvinceCode,
	}, &model.Address{
		WardCode:     req.To.WardCode,
		DistrictCode: req.To.DistrictCode,
		ProvinceCode: req.To.ProvinceCode,
	}, *req.ServiceType)

	if leadTimeConfig != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Lấy thời gian giao hàng dự kiến thành công",
			Data: []leadTimeResponse{
				{LeadTime: leadTimeConfig.CommitmentTime},
			},
		}
	}

	var leadTime int64 = 0
	var defaultPickTime int64 = 3600 * 3
	var defaultDeliveryTime int64 = 3600 * 3
	var defaultTransportTime int64 = 3600 * 12

	// If not use lead-time: from -> fromHub -> toHub -> to
	fromHub := FindNearestHubOfAddress(*req.From)
	if fromHub == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Chưa có hub hỗ trợ gửi hàng trong khu vực này",
		}
	}
	toHub := FindNearestHubOfAddress(*req.To)
	if toHub == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Chưa có hub hỗ trợ nhận hàng trong khu vực này",
		}
	}

	pickupTime, _ := utils.ParseLeadTimeConfig(req.Partner, &model.Address{
		WardCode:     fromHub.Address.WardCode,
		DistrictCode: fromHub.Address.DistrictCode,
		ProvinceCode: fromHub.Address.ProvinceCode,
	}, &model.Address{
		WardCode:     req.From.WardCode,
		DistrictCode: req.From.DistrictCode,
		ProvinceCode: req.From.ProvinceCode,
	}, *req.ServiceType)

	if pickupTime != nil {
		leadTime += pickupTime.CommitmentTime
	} else {
		leadTime += defaultPickTime
	}

	deliveryTime, _ := utils.ParseLeadTimeConfig(req.Partner, &model.Address{
		WardCode:     toHub.Address.WardCode,
		DistrictCode: toHub.Address.DistrictCode,
		ProvinceCode: toHub.Address.ProvinceCode,
	}, &model.Address{
		WardCode:     req.To.WardCode,
		DistrictCode: req.To.DistrictCode,
		ProvinceCode: req.To.ProvinceCode,
	}, *req.ServiceType)

	if deliveryTime != nil {
		leadTime += deliveryTime.CommitmentTime
	} else {
		leadTime += defaultDeliveryTime
	}

	// Transport
	if fromHub.Code != toHub.Code {
		transportTime, _ := utils.ParseLeadTimeConfig(req.Partner, &model.Address{
			WardCode:     fromHub.Address.WardCode,
			DistrictCode: fromHub.Address.DistrictCode,
			ProvinceCode: fromHub.Address.ProvinceCode,
		}, &model.Address{
			WardCode:     toHub.Address.WardCode,
			DistrictCode: toHub.Address.DistrictCode,
			ProvinceCode: toHub.Address.ProvinceCode,
		}, *req.ServiceType)

		if transportTime != nil {
			leadTime += transportTime.CommitmentTime
		} else {
			leadTime += defaultTransportTime
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Lấy thời gian giao hàng dự kiến thành công",
		Data: []leadTimeResponse{
			{LeadTime: leadTime},
		},
	}
}

func CalculateHubOrderLeadTime(req *request.CalculateLeadTimeRequest) *common.APIResponse {
	if req.Partner == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Đơn vị vận chuyển không được để trống",
		}
	}

	if req.ServiceType == nil || *req.ServiceType == "" {
		req.ServiceType = new(enum.LeadTimeConfigValue)
		*req.ServiceType = enum.LeadTimeConfigType.DELIVERING
	}

	if !utils.CheckExistInEnum(*req.ServiceType, *enum.LeadTimeConfigType) {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Loại dịch vụ không hợp lệ",
		}
	}

	if req.From == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Địa chỉ gửi không được để trống",
		}
	}

	if req.To == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Địa chỉ nhận không được để trống",
		}
	}

	checkCarrier := model.CarrierDB.QueryOne(bson.M{
		"carrier_code": req.Partner,
	})

	if checkCarrier.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã đối tác không hợp lệ",
		}
	}

	// Find if any direct lead-time configuration from -> to
	leadTimeConfig, _ := utils.ParseLeadTimeConfig(req.Partner, &model.Address{
		WardCode:     req.From.WardCode,
		DistrictCode: req.From.DistrictCode,
		ProvinceCode: req.From.ProvinceCode,
	}, &model.Address{
		WardCode:     req.To.WardCode,
		DistrictCode: req.To.DistrictCode,
		ProvinceCode: req.To.ProvinceCode,
	}, *req.ServiceType)

	if leadTimeConfig != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Lấy thời gian giao hàng dự kiến thành công",
			Data: []int64{
				leadTimeConfig.CommitmentTime,
			},
		}
	}

	var fourDays int64 = 60 * 60 * 24 * 4
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Lấy thời gian giao hàng dự kiến thành công",
		Data: []int64{
			fourDays,
		},
	}
}

type LeadTimeBound struct {
	Gte                   int64
	TimeFrame             enum.TimeFrameValue
	HubToProvinceLeadTime map[string]map[string]*model.ProvinceLeadTime
}

// Index: type_1,tpl_service_id_1, delivered_time_1, _id_1
func CalculateLeadTimeJob() {
	isJobLockAvailable := utils.CheckJobLockAvailable(enum.CALCULATE_SUGGEST_LEAD_TIME_JOB, conf.Config.RepeatCalculateLeadTimeJob)
	if !isJobLockAvailable {
		return
	}
	fmt.Println("Start calculate lead time job: " + time.Now().String())
	carriersRaw := model.CarrierDB.Query(bson.M{
		"parent_code": bson.M{
			"$ne": "",
		},
	}, 0, 1000, nil)
	if carriersRaw.Status != common.APIStatus.Ok {
		return
	}

	carriers := carriersRaw.Data.([]*model.Carrier)

	for _, carrier := range carriers {
		// TODO: refactor here, store it in db
		threeMonthAgo := time.Now().AddDate(0, -3, 0)
		twoMonthAgo := time.Now().AddDate(0, -2, 0)
		oneMonthAgo := time.Now().AddDate(0, -1, 0)

		leadTimeBounds := []LeadTimeBound{
			{
				Gte:                   threeMonthAgo.Unix(),
				TimeFrame:             enum.TimeFrame.THREE_MONTH,
				HubToProvinceLeadTime: map[string]map[string]*model.ProvinceLeadTime{},
			},
			{
				Gte:                   twoMonthAgo.Unix(),
				TimeFrame:             enum.TimeFrame.TWO_MONTH,
				HubToProvinceLeadTime: map[string]map[string]*model.ProvinceLeadTime{},
			},
			{
				Gte:                   oneMonthAgo.Unix(),
				TimeFrame:             enum.TimeFrame.ONE_MONTH,
				HubToProvinceLeadTime: map[string]map[string]*model.ProvinceLeadTime{},
			},
		}

		// Find the biggest time frame
		smallestTimeFrame := leadTimeBounds[0].Gte
		for _, leadTimeBound := range leadTimeBounds {
			if leadTimeBound.Gte < smallestTimeFrame {
				smallestTimeFrame = leadTimeBound.Gte
			}
		}

		filter := bson.M{
			"type":           enum.ShippingOrderType.DELIVERY,
			"tpl_service_id": carrier.CarrierId,
			"delivered_time": bson.M{
				"$gte": smallestTimeFrame,
			},
		}

		// Loop 3 tháng gần nhất để fill vào cache
		for {
			shippingOrdersRaw := model.ShippingOrderDB.SecondaryInstance.Query(
				filter,
				0,
				200,
				&primitive.M{"_id": 1})

			if shippingOrdersRaw.Status != common.APIStatus.Ok {
				break
			}
			shippingOrders := shippingOrdersRaw.Data.([]*model.ShippingOrder)
			biggestIds := shippingOrders[len(shippingOrders)-1].ID
			filter["_id"] = bson.M{
				"$gt": biggestIds,
			}
			for _, shippingOrder := range shippingOrders {
				donePackTime := shippingOrder.BookingTime
				if shippingOrder.DonePackTime > 0 {
					donePackTime = shippingOrder.DonePackTime
				}
				if donePackTime <= 0 {
					donePackTime = shippingOrder.ID.Timestamp().Unix()
				}
				leadTime := shippingOrder.DeliveredTime - donePackTime

				if leadTime <= 0 {
					continue
				}

				if shippingOrder.CustomerProvinceCode == "" ||
					shippingOrder.CustomerDistrictCode == "" {
					continue
				}

				hubCode := GetFMHubCodeFromDeliveryShippingOrder(shippingOrder)
				if hubCode == "" {
					continue
				}

				for _, leadTimeBound := range leadTimeBounds {
					if _, ok := leadTimeBound.HubToProvinceLeadTime[hubCode]; !ok {
						leadTimeBound.HubToProvinceLeadTime[hubCode] = map[string]*model.ProvinceLeadTime{}
					}
					if leadTimeBound.Gte <= shippingOrder.DeliveredTime {
						if _, ok := leadTimeBound.
							HubToProvinceLeadTime[hubCode][shippingOrder.CustomerProvinceCode]; !ok {
							leadTimeBound.HubToProvinceLeadTime[hubCode][shippingOrder.CustomerProvinceCode] =
								&model.ProvinceLeadTime{
									ProvinceCode: shippingOrder.CustomerProvinceCode,
									ProvinceName: shippingOrder.CustomerProvinceName,
								}
						}
						SumProvinceLeadTime(
							leadTimeBound.HubToProvinceLeadTime[hubCode][shippingOrder.CustomerProvinceCode],
							shippingOrder.CustomerDistrictCode,
							shippingOrder.CustomerDistrictName,
							shippingOrder.CustomerWardCode,
							shippingOrder.CustomerWardName,
							leadTime,
						)
					}
				}
			}
		}
		// Có 3 map chứa lead time của 3 tháng gần nhất rồi thì tính trung bình
		// để có lead time theo tỉnh
		for _, leadTimeBound := range leadTimeBounds {
			for _, hub := range leadTimeBound.HubToProvinceLeadTime {
				for _, provinceLeadTime := range hub {
					AvgProvinceLeadTime(provinceLeadTime)
				}
			}
		}

		// Có lead time rồi thì insert vào db, nếu config đã tồn tại cần merge lại config lead time đã config sẵn
		for _, leadTimeBound := range leadTimeBounds {
			for hubCode, hub := range leadTimeBound.HubToProvinceLeadTime {
				for _, provinceLeadTime := range hub {
					suggestLeadTime := tempLeadTimeModelToConfigLeadTime(provinceLeadTime)
					model.ConfigLeadTimeDB.Upsert(bson.M{
						"shipping_order_type": enum.ShippingOrderType.DELIVERY,
						"time_frame":          leadTimeBound.TimeFrame,
						"from_hub":            hubCode,
						"province.code":       provinceLeadTime.ProvinceCode,
						"compute_method":      enum.ComputeLeadTimeMethod.SUGGEST,
						"carrier_code":        carrier.CarrierCode,
					}, bson.M{
						"shipping_order_type": enum.ShippingOrderType.DELIVERY,
						"time_frame":          leadTimeBound.TimeFrame,
						"from_hub":            hubCode,
						"compute_method":      enum.ComputeLeadTimeMethod.SUGGEST,
						"carrier_code":        carrier.CarrierCode,
						"carrier_name":        carrier.CarrierName,
						"province":            suggestLeadTime,
					})
				}
			}
		}
	}
	fmt.Println("End calculate lead time job: " + time.Now().String())
}

func SumProvinceLeadTime(
	provinceLeadTime *model.ProvinceLeadTime,
	districtCode string,
	districtName string,
	wardCode string,
	wardName string,
	leadTime int64) {
	if provinceLeadTime.DistrictLeadTimeConfigs == nil {
		provinceLeadTime.DistrictLeadTimeConfigs = map[string]*model.DistrictLeadTime{}
	}
	if _, ok := provinceLeadTime.DistrictLeadTimeConfigs[districtCode]; !ok {
		provinceLeadTime.DistrictLeadTimeConfigs[districtCode] = &model.DistrictLeadTime{
			DistrictCode: districtCode,
			DistrictName: districtName,
		}
	}

	// Nếu không có ward thì tính lead time theo district
	if wardCode == "" {
		provinceLeadTime.DistrictLeadTimeConfigs[districtCode].SumLeadTime += leadTime
		provinceLeadTime.DistrictLeadTimeConfigs[districtCode].TotalOrder++
		return
	}

	if provinceLeadTime.DistrictLeadTimeConfigs[districtCode].WardLeadTimeConfigs == nil {
		provinceLeadTime.DistrictLeadTimeConfigs[districtCode].WardLeadTimeConfigs = map[string]*model.WardLeadTime{}
	}

	if _, ok := provinceLeadTime.DistrictLeadTimeConfigs[districtCode].WardLeadTimeConfigs[wardCode]; !ok {
		provinceLeadTime.
			DistrictLeadTimeConfigs[districtCode].
			WardLeadTimeConfigs[wardCode] = &model.WardLeadTime{
			WardCode: wardCode,
			WardName: wardName,
		}
	}

	provinceLeadTime.
		DistrictLeadTimeConfigs[districtCode].
		WardLeadTimeConfigs[wardCode].SumLeadTime += leadTime

	provinceLeadTime.
		DistrictLeadTimeConfigs[districtCode].
		WardLeadTimeConfigs[wardCode].TotalOrder++

	return
}

func AvgProvinceLeadTime(
	provinceLeadTime *model.ProvinceLeadTime) {
	var totalProvinceOrder int64 = 0
	var totalProvinceLeadTime int64 = 0

	for _, districtLeadTime := range provinceLeadTime.DistrictLeadTimeConfigs {
		var totalDistrictOrder int64 = 0
		var totalDistrictLeadTime int64 = 0

		if len(districtLeadTime.WardLeadTimeConfigs) == 0 {
			totalProvinceOrder += districtLeadTime.TotalOrder
			totalProvinceLeadTime += districtLeadTime.SumLeadTime
			districtLeadTime.AvgLeadTime = districtLeadTime.SumLeadTime / districtLeadTime.TotalOrder
			continue
		}

		for _, wardLeadTime := range districtLeadTime.WardLeadTimeConfigs {
			totalProvinceOrder += wardLeadTime.TotalOrder
			totalProvinceLeadTime += wardLeadTime.SumLeadTime

			totalDistrictOrder += wardLeadTime.TotalOrder
			totalDistrictLeadTime += wardLeadTime.SumLeadTime

			wardLeadTime.AvgLeadTime = wardLeadTime.SumLeadTime / wardLeadTime.TotalOrder
		}

		districtLeadTime.AvgLeadTime = totalDistrictLeadTime / totalDistrictOrder
	}
	provinceLeadTime.AvgLeadTime = totalProvinceLeadTime / totalProvinceOrder
}

func GetFMHubCodeFromDeliveryShippingOrder(shippingOrder *model.ShippingOrder) string {
	if shippingOrder == nil {
		return ""
	}

	if len(shippingOrder.Scope) > 0 {
		return shippingOrder.Scope[0]
	}

	// TODO: May use regex to determine hub code

	return ""
}

func mergeSuggestLeadTime(suggestLeadTime *model.ProvinceLeadTime, existedConfigLeadTime *model.ConfigLeadTime) *model.ConfigLeadTime {
	if suggestLeadTime == nil {
		return existedConfigLeadTime
	}

	result := &model.ConfigLeadTimeProvince{
		Code:               suggestLeadTime.ProvinceCode,
		Name:               suggestLeadTime.ProvinceName,
		SuggestLeadTime:    suggestLeadTime.AvgLeadTime,
		ConfiguredLeadTime: existedConfigLeadTime.ConfiguredLeadTime,
		Districts:          []model.ConfigLeadTimeDistrict{},
	}

	if len(suggestLeadTime.DistrictLeadTimeConfigs) == 0 {
		existedConfigLeadTime.Province = result
		return existedConfigLeadTime
	}

	type SuggestLeadTimeToArea struct {
		SuggestLeadTime int64
		Code            string
		Name            string
	}

	suggestDistrictLeadTime := map[string]SuggestLeadTimeToArea{}
	suggestWardLeadTime := map[string]SuggestLeadTimeToArea{}

	for _, district := range suggestLeadTime.DistrictLeadTimeConfigs {
		if district.WardLeadTimeConfigs == nil {
			continue
		}
		for _, ward := range district.WardLeadTimeConfigs {
			suggestWardLeadTime[ward.WardCode] = SuggestLeadTimeToArea{
				SuggestLeadTime: ward.AvgLeadTime,
				Code:            ward.WardCode,
				Name:            ward.WardName,
			}
		}
		suggestDistrictLeadTime[district.DistrictCode] = SuggestLeadTimeToArea{
			SuggestLeadTime: district.AvgLeadTime,
			Code:            district.DistrictCode,
			Name:            district.DistrictName,
		}
	}

	if existedConfigLeadTime.Province == nil {

		existedConfigLeadTime.Province = result
		return existedConfigLeadTime
	}

	existedConfigLeadTime.Province = result
	return existedConfigLeadTime
}

func tempLeadTimeModelToConfigLeadTime(tempLeadTime *model.ProvinceLeadTime) *model.ConfigLeadTimeProvince {
	result := &model.ConfigLeadTimeProvince{
		Code:            tempLeadTime.ProvinceCode,
		Name:            tempLeadTime.ProvinceName,
		SuggestLeadTime: tempLeadTime.AvgLeadTime,
	}
	if len(tempLeadTime.DistrictLeadTimeConfigs) == 0 {
		return result
	}

	for _, district := range tempLeadTime.DistrictLeadTimeConfigs {
		var wardLeadTimeConfigs []model.ConfigLeadTimeWard
		for _, ward := range district.WardLeadTimeConfigs {
			wardLeadTimeConfigs = append(wardLeadTimeConfigs, model.ConfigLeadTimeWard{
				Code:               ward.WardCode,
				Name:               ward.WardName,
				SuggestLeadTime:    ward.AvgLeadTime,
				ConfiguredLeadTime: 0,
			})
		}
		result.Districts = append(result.Districts, model.ConfigLeadTimeDistrict{
			Code:               district.DistrictCode,
			Name:               district.DistrictName,
			SuggestLeadTime:    district.AvgLeadTime,
			Wards:              wardLeadTimeConfigs,
			ConfiguredLeadTime: 0,
		})
	}

	return result
}

func GetAvailableConfigLeadTimeCarriers(req request.GetAvailableLeadTimeRequest) *common.APIResponse {
	if req.FromHub == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã hub không được để trống",
		}
	}

	if req.ShippingOrderType == nil || *req.ShippingOrderType == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Loại đơn hàng không được để trống",
		}
	}

	filter := bson.M{
		"shipping_order_type": req.ShippingOrderType,
		"from_hub":            req.FromHub,
		"is_deleted": bson.M{
			"$ne": true,
		},
	}

	matchStage := bson.D{{"$match", filter}}
	groupStage := bson.D{{"$group", bson.M{
		"_id":          "$carrier_code",
		"carrier_code": bson.M{"$first": "$carrier_code"},
		"carrier_name": bson.M{"$first": "$carrier_name"},
	}}}

	type Result struct {
		CarrierCode string `json:"carrierCode" bson:"carrier_code"`
		CarrierName string `json:"carrierName" bson:"carrier_name"`
	}
	var results []Result

	result := model.ConfigLeadTimeDB.Aggregate([]bson.D{matchStage, groupStage}, &results)

	if result.Status != common.APIStatus.Ok {
		return result
	}

	if len(results) > 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Lấy danh sách đối tác thành công",
			Data:    results,
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.NotFound,
		Message: "Không tìm thấy đối tác nào",
	}
}

func GetConfigProvinceOfCarrier(req request.GetAvailableLeadTimeRequest) *common.APIResponse {
	if req.FromHub == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã hub không được để trống",
		}
	}

	if req.ShippingOrderType == nil || *req.ShippingOrderType == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Loại đơn hàng không được để trống",
		}
	}

	if req.TimeFrame == nil || *req.TimeFrame == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Thời gian không được để trống",
		}
	}

	if req.CarrierCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã tỉnh không được để trống",
		}
	}

	filter := bson.M{
		"shipping_order_type": req.ShippingOrderType,
		"from_hub":            req.FromHub,
		"carrier_code":        req.CarrierCode,
		"is_deleted": bson.M{
			"$ne": true,
		},
		"$or": []bson.M{
			{
				"compute_method": enum.ComputeLeadTimeMethod.CONFIG,
			},
			{
				"compute_method": enum.ComputeLeadTimeMethod.SUGGEST,
				"time_frame":     req.TimeFrame,
			},
		},
	}

	matchStage := bson.D{{"$match", filter}}
	groupStage := bson.D{{"$group", bson.M{
		"_id": "$province.code",
		"code": bson.M{
			"$first": "$province.code",
		},
		"name": bson.M{
			"$first": "$province.name",
		},
		"suggest_lead_time": bson.M{
			"$sum": "$province.suggest_lead_time",
		},
		"configured_lead_time": bson.M{
			"$sum": "$province.configured_lead_time",
		},
	}}}

	type Result struct {
		ProvinceCode       string `json:"code" bson:"code"`
		ProvinceName       string `json:"name" bson:"name"`
		SuggestLeadTime    int64  `json:"suggestLeadTime" bson:"suggest_lead_time"`
		ConfiguredLeadTime int64  `json:"configuredLeadTime" bson:"configured_lead_time"`
	}
	var results []Result
	result := model.ConfigLeadTimeDB.Aggregate([]bson.D{matchStage, groupStage}, &results)

	if result.Status != common.APIStatus.Ok {
		return result
	}

	if len(results) > 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Lấy danh sách đối tác thành công",
			Data:    results,
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.NotFound,
		Message: "Không tìm thấy đối tác nào",
	}
}

func GetProvinceLeadTime(req request.GetAvailableLeadTimeRequest) *common.APIResponse {
	if req.FromHub == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã hub không được để trống",
		}
	}

	if req.ShippingOrderType == nil || *req.ShippingOrderType == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Loại đơn hàng không được để trống",
		}
	}

	if req.TimeFrame == nil || *req.TimeFrame == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Thời gian không được để trống",
		}
	}

	if req.CarrierCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã tỉnh không được để trống",
		}
	}

	if req.ProvinceCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã tỉnh không được để trống",
		}
	}

	filter := bson.M{
		"shipping_order_type": req.ShippingOrderType,
		"from_hub":            req.FromHub,
		"carrier_code":        req.CarrierCode,
		"province.code":       req.ProvinceCode,
		"is_deleted": bson.M{
			"$ne": true,
		},
	}

	var configuredLeadTime *model.ConfigLeadTime
	filter["compute_method"] = enum.ComputeLeadTimeMethod.CONFIG
	configuredLeadTimeRaw := model.ConfigLeadTimeDB.QueryOne(filter)
	if configuredLeadTimeRaw.Status == common.APIStatus.Ok {
		configuredLeadTime = configuredLeadTimeRaw.Data.([]*model.ConfigLeadTime)[0]
	}
	var suggestLeadTime *model.ConfigLeadTime
	filter["compute_method"] = enum.ComputeLeadTimeMethod.SUGGEST
	filter["time_frame"] = req.TimeFrame
	suggestLeadTimeRaw := model.ConfigLeadTimeDB.QueryOne(filter)
	if suggestLeadTimeRaw.Status == common.APIStatus.Ok {
		suggestLeadTime = suggestLeadTimeRaw.Data.([]*model.ConfigLeadTime)[0]
	}

	resp, err := mergeConfigAndSuggestLeadTime(req.ProvinceCode, configuredLeadTime, suggestLeadTime)

	if err != nil || resp == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Lỗi khi merge lead time: " + err.Error(),
		}
	}

	if configuredLeadTime == nil && suggestLeadTime == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			ErrorCode: "EMPTY_LEAD_TIME",
			Data:      []model.ConfigLeadTime{*resp},
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Lấy lead time thành công",
		Data:    []model.ConfigLeadTime{*resp},
	}
}

func GetProvincesLeadTime(req request.GetAvailableLeadTimeRequest) *common.APIResponse {
	if req.FromHub == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã hub không được để trống",
		}
	}

	if req.ShippingOrderType == nil || *req.ShippingOrderType == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Loại đơn hàng không được để trống",
		}
	}

	if req.TimeFrame == nil || *req.TimeFrame == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Thời gian không được để trống",
		}
	}

	if req.CarrierCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã tỉnh không được để trống",
		}
	}

	filter := bson.M{
		"shipping_order_type": req.ShippingOrderType,
		"from_hub":            req.FromHub,
		"carrier_code":        req.CarrierCode,
		"is_deleted": bson.M{
			"$ne": true,
		},
	}
	var configuredLeadTimes []*model.ConfigLeadTime
	filter["compute_method"] = enum.ComputeLeadTimeMethod.CONFIG
	configuredLeadTimeRaw := model.ConfigLeadTimeDB.Query(filter, 0, 100, nil)
	if configuredLeadTimeRaw.Status == common.APIStatus.Ok {
		configuredLeadTimes = configuredLeadTimeRaw.Data.([]*model.ConfigLeadTime)
	}
	var suggestLeadTimes []*model.ConfigLeadTime
	filter["compute_method"] = enum.ComputeLeadTimeMethod.SUGGEST
	filter["time_frame"] = req.TimeFrame
	suggestLeadTimeRaw := model.ConfigLeadTimeDB.Query(filter, 0, 100, nil)
	if suggestLeadTimeRaw.Status == common.APIStatus.Ok {
		suggestLeadTimes = suggestLeadTimeRaw.Data.([]*model.ConfigLeadTime)
	}

	leadTimes, err := mergeManyConfigAndSuggestLeadTime(configuredLeadTimes, suggestLeadTimes)
	if err != nil || len(leadTimes) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Lỗi khi merge lead time: " + err.Error(),
		}
	}

	if configuredLeadTimes == nil && suggestLeadTimes == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			ErrorCode: "EMPTY_LEAD_TIME",
		}
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Lấy lead time thành công",
		Data:    leadTimes,
	}

}

func mergeConfigAndSuggestLeadTime(
	provinceCode string,
	configLeadTime,
	suggestLeadTime *model.ConfigLeadTime) (*model.ConfigLeadTime, error) {
	var result *model.ConfigLeadTime
	var configLeadTimeProvince int64
	var suggestLeadTimeProvince int64
	var configLeadTimeDistrict = map[string]int64{}
	var suggestLeadTimeDistrict = map[string]int64{}
	var configLeadTimeWard = map[string]int64{}
	var suggestLeadTimeWard = map[string]int64{}

	// Build lead time map
	if configLeadTime != nil {
		result = &model.ConfigLeadTime{
			ShippingOrderType: configLeadTime.ShippingOrderType,
			TimeFrame:         configLeadTime.TimeFrame,
			FromHub:           configLeadTime.FromHub,
			CarrierCode:       configLeadTime.CarrierCode,
			CarrierName:       configLeadTime.CarrierName,
			Province: &model.ConfigLeadTimeProvince{
				Code: configLeadTime.Province.Code,
				Name: configLeadTime.Province.Name,
			},
		}
		configLeadTimeProvince = configLeadTime.Province.ConfiguredLeadTime
		if len(configLeadTime.Province.Districts) > 0 {
			for _, district := range configLeadTime.Province.Districts {
				configLeadTimeDistrict[district.Code] = district.ConfiguredLeadTime
				if len(district.Wards) > 0 {
					for _, ward := range district.Wards {
						configLeadTimeWard[ward.Code] = ward.ConfiguredLeadTime
					}
				}
			}
		}
	}
	if suggestLeadTime != nil {
		if result == nil {
			result = &model.ConfigLeadTime{
				ShippingOrderType: suggestLeadTime.ShippingOrderType,
				TimeFrame:         suggestLeadTime.TimeFrame,
				FromHub:           suggestLeadTime.FromHub,
				CarrierCode:       suggestLeadTime.CarrierCode,
				CarrierName:       suggestLeadTime.CarrierName,
				Province: &model.ConfigLeadTimeProvince{
					Code: suggestLeadTime.Province.Code,
					Name: suggestLeadTime.Province.Name,
				},
			}
		}
		suggestLeadTimeProvince = suggestLeadTime.Province.SuggestLeadTime
		if len(suggestLeadTime.Province.Districts) > 0 {
			for _, district := range suggestLeadTime.Province.Districts {
				suggestLeadTimeDistrict[district.Code] = district.SuggestLeadTime
				if len(district.Wards) > 0 {
					for _, ward := range district.Wards {
						suggestLeadTimeWard[ward.Code] = ward.SuggestLeadTime
					}
				}
			}
		}
	}

	if result == nil {
		result = &model.ConfigLeadTime{
			Province: &model.ConfigLeadTimeProvince{
				Code: provinceCode,
			},
		}
	}

	err := FillProvinceLeadTimeTree(result.Province)
	if err != nil {
		return nil, err
	}
	result.Province.SuggestLeadTime = suggestLeadTimeProvince
	result.Province.ConfiguredLeadTime = configLeadTimeProvince
	for id, district := range result.Province.Districts {
		result.Province.Districts[id].SuggestLeadTime = suggestLeadTimeDistrict[district.Code]
		result.Province.Districts[id].ConfiguredLeadTime = configLeadTimeDistrict[district.Code]
		for iw, ward := range district.Wards {
			result.Province.Districts[id].Wards[iw].SuggestLeadTime = suggestLeadTimeWard[ward.Code]
			result.Province.Districts[id].Wards[iw].ConfiguredLeadTime = configLeadTimeWard[ward.Code]
		}
	}

	return result, nil
}

func FillProvinceLeadTimeTree(provinceLeadTime *model.ConfigLeadTimeProvince) error {
	// Get province's districts, wards
	districtMappingRaw := model.DistrictMappingDB.Query(bson.M{
		"province_code": provinceLeadTime.Code,
	}, 0, 1000, nil)
	if districtMappingRaw.Status != common.APIStatus.Ok {
		return errors.New("Không tìm thấy thông tin tỉnh: " + provinceLeadTime.Code)
	}
	districtMapping := districtMappingRaw.Data.([]*model.DistrictMapping)
	type wardResult struct {
		DistrictMapping *model.DistrictMapping
		WardMappings    []*model.WardMapping
		Error           error
	}
	numOfWorker := 4
	jobs := make(chan *model.DistrictMapping, len(districtMapping))
	results := make(chan wardResult, len(districtMapping))

	for i := 0; i < numOfWorker; i++ {
		go func(jbs <-chan *model.DistrictMapping, results chan<- wardResult) {
			for job := range jbs {
				wardMappingRaw := model.WardMappingDB.Query(bson.M{
					"district_code": job.Code,
				}, 0, 1000, nil)
				if wardMappingRaw.Status != common.APIStatus.Ok {
					results <- wardResult{
						DistrictMapping: job,
						WardMappings:    nil,
						Error:           errors.New("Không tìm thấy thông tin quận huyện: " + job.Code),
					}
					continue
				}
				wardMapping := wardMappingRaw.Data.([]*model.WardMapping)
				results <- wardResult{
					DistrictMapping: job,
					WardMappings:    wardMapping,
				}
			}
		}(jobs, results)
	}

	for _, district := range districtMapping {
		jobs <- district
	}
	close(jobs)

	for i := 0; i < len(districtMapping); i++ {
		result := <-results
		if len(result.WardMappings) == 0 {
			provinceLeadTime.Districts = append(provinceLeadTime.Districts, model.ConfigLeadTimeDistrict{
				Code: result.DistrictMapping.Code,
				Name: result.DistrictMapping.Name,
			})
			continue
		}
		var wardLeadTimeConfigs []model.ConfigLeadTimeWard
		for _, ward := range result.WardMappings {
			wardLeadTimeConfigs = append(wardLeadTimeConfigs, model.ConfigLeadTimeWard{
				Code: ward.Code,
				Name: ward.Name,
			})
		}
		provinceLeadTime.Districts = append(provinceLeadTime.Districts, model.ConfigLeadTimeDistrict{
			Code:  result.DistrictMapping.Code,
			Name:  result.DistrictMapping.Name,
			Wards: wardLeadTimeConfigs,
		})
	}
	close(results)

	return nil
}

func DeleteConfigLeadTime(req request.DeleteConfigLeadTimeRequest) *common.APIResponse {
	if req.FromHub == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã hub không được để trống",
		}
	}

	if req.ShippingOrderType == nil || *req.ShippingOrderType == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Loại đơn hàng không được để trống",
		}
	}

	if req.CarrierCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã tỉnh không được để trống",
		}
	}
	// Nếu không có province code nghĩa là xóa toàn bộ config của carrier
	if req.ProvinceCode == "" {
		return model.ConfigLeadTimeDB.UpdateMany(bson.M{
			"shipping_order_type": req.ShippingOrderType,
			"from_hub":            req.FromHub,
			"carrier_code":        req.CarrierCode,
			"compute_method":      enum.ComputeLeadTimeMethod.CONFIG,
			"is_deleted": bson.M{
				"$ne": true,
			},
		}, bson.M{
			"is_deleted": true,
		})
	}

	// Nếu có province code thì xóa config của province đó
	return model.ConfigLeadTimeDB.UpdateMany(bson.M{
		"shipping_order_type": req.ShippingOrderType,
		"from_hub":            req.FromHub,
		"carrier_code":        req.CarrierCode,
		"province.code":       req.ProvinceCode,
		"compute_method":      enum.ComputeLeadTimeMethod.CONFIG,
		"is_deleted": bson.M{
			"$ne": true,
		},
	}, bson.M{
		"is_deleted": true,
	})
}

func UpdateConfigLeadTime(req request.UpdateConfigLeadTimeRequest) *common.APIResponse {
	if req.FromHub == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã hub không được để trống",
		}
	}

	if req.ShippingOrderType == nil || *req.ShippingOrderType == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Loại đơn hàng không được để trống",
		}
	}

	if req.CarrierCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã tỉnh không được để trống",
		}
	}

	if len(req.Provinces) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không có tỉnh nào được cập nhật",
		}
	}

	for _, province := range req.Provinces {
		if len(province.Districts) == 0 {
			if province.ConfiguredLeadTime == 0 {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Thời gian giao hàng của tỉnh không được để trống",
				}
			}
			var configLeadTimeProvince *model.ConfigLeadTime
			var err error
			existedConfigLeadTimeRaw := model.ConfigLeadTimeDB.QueryOne(bson.M{
				"shipping_order_type": req.ShippingOrderType,
				"from_hub":            req.FromHub,
				"carrier_code":        req.CarrierCode,
				"compute_method":      enum.ComputeLeadTimeMethod.CONFIG,
				"province.code":       province.Code,
				"is_deleted": bson.M{
					"$ne": true,
				},
			})
			if existedConfigLeadTimeRaw.Status != common.APIStatus.Ok {
				configLeadTimeProvince, err = mergeConfigAndSuggestLeadTime(province.Code, nil, nil)
				if err != nil || configLeadTimeProvince == nil {
					return &common.APIResponse{
						Status:  common.APIStatus.Error,
						Message: "Lỗi khi merge lead time: " + err.Error(),
					}
				}
				configLeadTimeProvince.Province.ConfiguredLeadTime = province.ConfiguredLeadTime
				configLeadTimeProvince.Province.Name = province.Name
				createResp := model.ConfigLeadTimeDB.Create(&model.ConfigLeadTime{
					ShippingOrderType: req.ShippingOrderType,
					FromHub:           req.FromHub,
					CarrierCode:       req.CarrierCode,
					CarrierName:       req.CarrierName,
					ComputeMethod:     &enum.ComputeLeadTimeMethod.CONFIG,
					Province:          configLeadTimeProvince.Province,
					IsDeleted:         &enum.False,
				})
				if createResp.Status != common.APIStatus.Ok {
					return createResp
				}
				continue
			}

			resp := model.ConfigLeadTimeDB.UpdateOne(bson.M{
				"shipping_order_type": req.ShippingOrderType,
				"from_hub":            req.FromHub,
				"carrier_code":        req.CarrierCode,
				"compute_method":      enum.ComputeLeadTimeMethod.CONFIG,
				"province.code":       province.Code,
				"is_deleted": bson.M{
					"$ne": true,
				},
			}, bson.M{
				"province.configured_lead_time": province.ConfiguredLeadTime,
				"carrier_code":                  req.CarrierCode,
				"is_deleted":                    false,
			})

			if resp.Status != common.APIStatus.Ok {
				return resp
			}
			continue
		}

		resp := model.ConfigLeadTimeDB.Upsert(bson.M{
			"shipping_order_type": req.ShippingOrderType,
			"from_hub":            req.FromHub,
			"carrier_code":        req.CarrierCode,
			"compute_method":      enum.ComputeLeadTimeMethod.CONFIG,
			"province.code":       province.Code,
			"is_deleted": bson.M{
				"$ne": true,
			},
		}, bson.M{
			"carrier_name": req.CarrierName,
			"province":     province,
			"is_deleted":   false,
		})

		if resp.Status != common.APIStatus.Ok {
			return resp
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật config lead time thành công",
	}
}

func GetCommitmentTime(req request.GetAvailableLeadTimeRequest) (int64, error) {
	if req.FromHub == "" {
		return 0, errors.New("Mã hub không được để trống")
	}

	if req.ShippingOrderType == nil || *req.ShippingOrderType == "" {
		return 0, errors.New("Loại đơn hàng không được để trống")
	}

	if req.CarrierCode == "" {
		return 0, errors.New("Mã tỉnh không được để trống")
	}

	if req.ProvinceCode == "" {
		return 0, errors.New("Mã tỉnh không được để trống")
	}

	configLeadTimeRaw := model.ConfigLeadTimeDB.QueryOne(bson.M{
		"shipping_order_type": req.ShippingOrderType,
		"from_hub":            req.FromHub,
		"carrier_code":        req.CarrierCode,
		"province.code":       req.ProvinceCode,
		"compute_method":      enum.ComputeLeadTimeMethod.CONFIG,
		"is_deleted": bson.M{
			"$ne": true,
		},
	})
	if configLeadTimeRaw.Status != common.APIStatus.Ok {
		return 0, errors.New("Không tìm thấy config lead time")
	}

	configLeadTime := configLeadTimeRaw.Data.([]*model.ConfigLeadTime)[0]
	for _, district := range configLeadTime.Province.Districts {
		for _, ward := range district.Wards {
			if ward.Code == req.WardCode {
				if ward.ConfiguredLeadTime != 0 {
					return ward.ConfiguredLeadTime, nil
				}
			}
		}
		if district.Code == req.DistrictCode {
			if district.ConfiguredLeadTime != 0 {
				return district.ConfiguredLeadTime, nil
			}
		}
	}

	if configLeadTime.Province.ConfiguredLeadTime != 0 {
		return configLeadTime.Province.ConfiguredLeadTime, nil
	}

	return 0, errors.New("Không tìm thấy config lead time")
}

func mergeManyConfigAndSuggestLeadTime(
	configLeadTimes,
	suggestLeadTimes []*model.ConfigLeadTime) ([]*model.ConfigLeadTime, error) {
	var result []*model.ConfigLeadTime
	var configLeadTimeProvince = map[string]*model.ConfigLeadTime{}
	var configLeadTimeDistrict = map[string]map[string]*model.ConfigLeadTimeDistrict{}
	var configLeadTimeWard = map[string]map[string]map[string]*model.ConfigLeadTimeWard{}
	var suggestLeadTimeProvince = map[string]*model.ConfigLeadTime{}
	var suggestLeadTimeDistrict = map[string]map[string]*model.ConfigLeadTimeDistrict{}
	var suggestLeadTimeWard = map[string]map[string]map[string]*model.ConfigLeadTimeWard{}

	var allProvinceCodes []string
	var allDistrictCodes []string
	var allWardCodes []string
	for configLeadTimeI, configLeadTime := range configLeadTimes {
		if configLeadTime.Province == nil {
			continue
		}
		allProvinceCodes = append(allProvinceCodes, configLeadTime.Province.Code)
		configLeadTimeProvince[configLeadTime.Province.Code] = configLeadTimes[configLeadTimeI]

		for cltDistrictI, cltDistrict := range configLeadTime.Province.Districts {
			allDistrictCodes = append(allDistrictCodes, cltDistrict.Code)

			if configLeadTimeDistrict[configLeadTime.Province.Code] == nil {
				configLeadTimeDistrict[configLeadTime.Province.Code] = map[string]*model.ConfigLeadTimeDistrict{}
			}
			configLeadTimeDistrict[configLeadTime.Province.Code][cltDistrict.Code] = &configLeadTime.Province.Districts[cltDistrictI]

			for cltWardI, cltWard := range cltDistrict.Wards {
				allWardCodes = append(allWardCodes, cltWard.Code)

				if configLeadTimeWard[configLeadTime.Province.Code] == nil {
					configLeadTimeWard[configLeadTime.Province.Code] = map[string]map[string]*model.ConfigLeadTimeWard{}
				}
				if configLeadTimeWard[configLeadTime.Province.Code][cltDistrict.Code] == nil {
					configLeadTimeWard[configLeadTime.Province.Code][cltDistrict.Code] = map[string]*model.ConfigLeadTimeWard{}
				}
				configLeadTimeWard[configLeadTime.Province.Code][cltDistrict.Code][cltWard.Code] = &configLeadTime.Province.Districts[cltDistrictI].Wards[cltWardI]

			}
		}
	}
	for suggestLeadTimeI, suggestLeadTime := range suggestLeadTimes {
		if suggestLeadTime.Province == nil {
			continue
		}
		if suggestLeadTime.Province.SuggestLeadTime <= 0 {
			continue
		}
		suggestLeadTimeProvince[suggestLeadTime.Province.Code] = suggestLeadTimes[suggestLeadTimeI]
		allProvinceCodes = append(allProvinceCodes, suggestLeadTime.Province.Code)
		for sltDistrictI, sltDistrict := range suggestLeadTime.Province.Districts {
			if sltDistrict.SuggestLeadTime <= 0 {
				continue
			}
			if suggestLeadTimeDistrict[suggestLeadTime.Province.Code] == nil {
				suggestLeadTimeDistrict[suggestLeadTime.Province.Code] = map[string]*model.ConfigLeadTimeDistrict{}
			}
			allDistrictCodes = append(allDistrictCodes, sltDistrict.Code)
			suggestLeadTimeDistrict[suggestLeadTime.Province.Code][sltDistrict.Code] = &suggestLeadTime.Province.Districts[sltDistrictI]
			for sltWardI, sltWard := range sltDistrict.Wards {
				if sltWard.SuggestLeadTime <= 0 {
					continue
				}
				if suggestLeadTimeWard[suggestLeadTime.Province.Code] == nil {
					suggestLeadTimeWard[suggestLeadTime.Province.Code] = map[string]map[string]*model.ConfigLeadTimeWard{}
				}
				if suggestLeadTimeWard[suggestLeadTime.Province.Code][sltDistrict.Code] == nil {
					suggestLeadTimeWard[suggestLeadTime.Province.Code][sltDistrict.Code] = map[string]*model.ConfigLeadTimeWard{}
				}
				allWardCodes = append(allWardCodes, sltWard.Code)
				suggestLeadTimeWard[suggestLeadTime.Province.Code][sltDistrict.Code][sltWard.Code] = &sltDistrict.Wards[sltWardI]
			}
		}
	}
	allProvinceCodes = utils.RemoveDuplicateStr(allProvinceCodes)
	allDistrictCodes = utils.RemoveDuplicateStr(allDistrictCodes)
	allWardCodes = utils.RemoveDuplicateStr(allWardCodes)

	for _, provinceCode := range allProvinceCodes {
		rp := &model.ConfigLeadTime{
			Province: &model.ConfigLeadTimeProvince{
				Code: provinceCode,
			},
		}
		cltProvince, okClt := configLeadTimeProvince[provinceCode]
		sltProvince, okSlt := suggestLeadTimeProvince[provinceCode]
		if okClt {
			rp.Province.ConfiguredLeadTime = cltProvince.Province.ConfiguredLeadTime
			rp.Province.Name = cltProvince.Province.Name
		}
		if okSlt {
			rp.Province.SuggestLeadTime = sltProvince.Province.SuggestLeadTime
			rp.Province.Name = sltProvince.Province.Name
		}
		for _, districtCode := range allDistrictCodes {
			rd := &model.ConfigLeadTimeDistrict{
				Code: districtCode,
			}
			cltDistrict, okCltDistrict := configLeadTimeDistrict[provinceCode][districtCode]
			sltDistrict, okSltDistrict := suggestLeadTimeDistrict[provinceCode][districtCode]
			if okCltDistrict {
				rd.ConfiguredLeadTime = cltDistrict.ConfiguredLeadTime
				rd.Name = cltDistrict.Name
			}
			if okSltDistrict {
				rd.SuggestLeadTime = sltDistrict.SuggestLeadTime
				rd.Name = sltDistrict.Name
			}
			if !okCltDistrict && !okSltDistrict {
				continue
			}
			for _, wardCode := range allWardCodes {
				rw := &model.ConfigLeadTimeWard{
					Code: wardCode,
				}
				cltWard, okCltWard := configLeadTimeWard[provinceCode][districtCode][wardCode]
				sltWard, okSltWard := suggestLeadTimeWard[provinceCode][districtCode][wardCode]
				if okCltWard {
					rw.ConfiguredLeadTime = cltWard.ConfiguredLeadTime
					rw.Name = cltWard.Name
				}
				if okSltWard {
					rw.SuggestLeadTime = sltWard.SuggestLeadTime
					rw.Name = sltWard.Name
				}
				if !okCltWard && !okSltWard {
					continue
				}
				rd.Wards = append(rd.Wards, *rw)
			}
			rp.Province.Districts = append(rp.Province.Districts, *rd)
		}
		result = append(result, rp)
	}

	if len(result) == 0 {
		return nil, errors.New("Không tìm thấy lead time")
	}

	removeEmptyConfigResult := []*model.ConfigLeadTime{}

	for _, province := range result {
		p := &model.ConfigLeadTimeProvince{
			Code:               province.Province.Code,
			Name:               province.Province.Name,
			ConfiguredLeadTime: province.Province.ConfiguredLeadTime,
			SuggestLeadTime:    province.Province.SuggestLeadTime,
		}
		for _, district := range province.Province.Districts {
			d := &model.ConfigLeadTimeDistrict{
				Code:               district.Code,
				Name:               district.Name,
				ConfiguredLeadTime: district.ConfiguredLeadTime,
				SuggestLeadTime:    district.SuggestLeadTime,
			}
			for _, ward := range district.Wards {
				w := &model.ConfigLeadTimeWard{
					Code:               ward.Code,
					Name:               ward.Name,
					ConfiguredLeadTime: ward.ConfiguredLeadTime,
					SuggestLeadTime:    ward.SuggestLeadTime,
				}
				if ward.ConfiguredLeadTime != 0 || ward.SuggestLeadTime != 0 {
					d.Wards = append(d.Wards, *w)
				}
			}
			if district.ConfiguredLeadTime != 0 || district.SuggestLeadTime != 0 || len(d.Wards) > 0 {
				p.Districts = append(p.Districts, *d)
			}
		}
		if province.Province.ConfiguredLeadTime != 0 || province.Province.SuggestLeadTime != 0 || len(p.Districts) > 0 {
			removeEmptyConfigResult = append(removeEmptyConfigResult, &model.ConfigLeadTime{
				Province: p,
			})
		}
	}

	return removeEmptyConfigResult, nil
}
