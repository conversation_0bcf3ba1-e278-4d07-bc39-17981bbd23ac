package geocode

import (
	"encoding/json"
	"fmt"
	"hash/fnv"
	"log"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/response"
	"go.mongodb.org/mongo-driver/bson"
)

func hashInt64(s string) uint64 {
	h := fnv.New64a()
	h.Write([]byte(s))
	return h.Sum64()
}

func (cli *GeoCodeClient) GetLatLng(fullAddress string) (lat, long float64, err error) {
	// TODO: Normalize full address goes here
	params := map[string]string{
		"address": fullAddress,
		"key":     cli.apiKey,
	}
	addressId := int64(hashInt64(fullAddress))
	cacheRaw := model.LatLongCacheDB.QueryOne(bson.M{
		"_id": addressId,
	})

	if cacheRaw.Status == common.APIStatus.Ok {
		cache := cacheRaw.Data.([]*model.LatLongCache)[0]
		lat = cache.Latitude
		long = cache.Longitude
		return
	}

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGeoCoder, nil)
	if err != nil {
		log.Printf("[ERROR] Call API %s: %v\n", pathGeoCoder, err)
		return
	}

	resBody := new(response.GeoCodeResponse)
	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		log.Printf("[ERROR] Call API %s: %v\n", pathGeoCoder, err)
		return
	}

	if resBody.Status != common.APIStatus.Ok {
		err = fmt.Errorf("[ERROR] Call API %s: %v\n", pathGeoCoder, resBody.Message)
		return
	}

	lat = resBody.Results[0].Geometry.Location.Lat
	long = resBody.Results[0].Geometry.Location.Lng

	_ = model.LatLongCacheDB.Create(model.LatLongCache{
		AddressId:   addressId,
		FullAddress: fullAddress,
		Latitude:    lat,
		Longitude:   long,
	})

	return
}
