package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func CreateSnapshot(input model.Snapshot) *common.APIResponse {
	input.ID = primitive.NilObjectID
	return model.SnapshotDB.Upsert(bson.M{
		"type": input.Type,
		"key":  input.Key,
	}, input)
}
