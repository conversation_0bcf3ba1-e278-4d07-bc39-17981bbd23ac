package warehouse_core

import (
	"encoding/json"
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
)

func (cli *Client) SendDoneTransport(req *request.DoneTransportForReturn) error {
	var res *client.RestResult
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, req, pathDoneTransportWarehouse, nil)
	if err != nil {
		return err
	}

	type myResponse struct {
		Status  string `json:"status"`
		Code    string `json:"code"`
		Message string `json:"message"`
	}

	response := new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return err
	}

	if response.Status != common.APIStatus.Ok {
		return fmt.Errorf("%v", response.Message)
	}

	return nil
}

func (cli *Client) SendDoneTransportTO(req *request.DoneTransportForTO) error {
	var res *client.RestResult
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, req, pathDoneTOWarehouse, nil)
	if err != nil {
		return err
	}

	type myResponse struct {
		Status  string `json:"status"`
		Code    string `json:"code"`
		Message string `json:"message"`
	}

	response := new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return err
	}

	if response.Status != common.APIStatus.Ok {
		return fmt.Errorf("%v", response.Message)
	}

	return nil
}

func (cli *Client) SendDoneTransportRO(req *request.DoneTransportForRO) error {
	var res *client.RestResult
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, req, pathDoneReturnRO, nil)
	if err != nil {
		return err
	}

	type myResponse struct {
		Status  string `json:"status"`
		Code    string `json:"code"`
		Message string `json:"message"`
	}

	response := new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return err
	}

	if response.Status != common.APIStatus.Ok {
		return fmt.Errorf("%v", response.Message)
	}

	return nil
}

func (cli *Client) SendDoneReceive(req *request.DoneTransportForReceiveSession) error {
	var res *client.RestResult
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, req, pathDoneReceive, nil)
	if err != nil {
		return err
	}

	type myResponse struct {
		Status  string `json:"status"`
		Code    string `json:"code"`
		Message string `json:"message"`
	}

	response := new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return err
	}

	if response.Status != common.APIStatus.Ok {
		return fmt.Errorf("%v", response.Message)
	}

	return nil
}

func (cli *Client) UpdateFMCode(poCode, fmCode string) error {
	req := map[string]string{
		"poCode": poCode,
		"fmCode": fmCode,
	}

	var res *client.RestResult
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, nil, req, pathUpdateFMCode, nil)
	if err != nil {
		return err
	}

	type myResponse struct {
		Status  string `json:"status"`
		Code    string `json:"code"`
		Message string `json:"message"`
	}

	response := new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return err
	}

	if response.Status != common.APIStatus.Ok {
		return fmt.Errorf("%v", response.Message)
	}

	return nil
}
