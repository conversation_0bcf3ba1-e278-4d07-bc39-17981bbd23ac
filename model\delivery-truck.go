package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type DeliveryTruck struct {
	ID           primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	LicensePlate string             `json:"licensePlate" bson:"license_plate,omitempty"`
	IdCard       string             `json:"idCard" bson:"id_card,omitempty"`
	Phone        string             `json:"phone" bson:"phone,omitempty"`
	Email        string             `json:"email" bson:"email,omitempty"`
	OwnerName    string             `json:"ownerName" bson:"owner_name,omitempty"`
	DriverID     int                `json:"driverId,omitempty" bson:"driver_id,omitempty"`
	// tracking status
	VersionNo       string     `json:"versionNo,omitempty" bson:"version_no,omitempty"`
	CreatedBy       string     `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	CreatedTime     *time.Time `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedBy   string     `json:"lastUpdatedBy,omitempty" bson:"last_updated_by,omitempty"`
	LastUpdatedTime *time.Time `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
}

// DeliveryTruckDB is an instance db
var DeliveryTruckDB = &db.Instance{
	ColName:        "delivery_truck",
	TemplateObject: &DeliveryTruck{},
}

// InitDeliveryTruckModel is func init model deliveryTruck
func InitDeliveryTruckModel(s *mongo.Database) {
	DeliveryTruckDB.ApplyDatabase(s)

	//t := true
	//DeliveryTruckDB.CreateIndex(bson.D{
	//	{"license_plate", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//	Unique:     &t,
	//})
}
