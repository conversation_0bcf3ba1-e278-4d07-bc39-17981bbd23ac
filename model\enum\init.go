package enum

var True = true
var False = false

var Timezone = "Asia/Bangkok"

const (
	SYNC_ACCOUNT_JOB                = "SYNC_ACCOUNT_JOB"
	SYNC_LEADTIME_JOB               = "SYNC_LEADTIME_JOB"
	CRAWL_PRODUCTIVITY              = "CRAWL_PRODUCTIVITY"
	CALCULATE_SUGGEST_LEAD_TIME_JOB = "CALCULATE_SUGGEST_LEAD_TIME_JOB"
	AUTO_CANCEL_ORDER_JOB           = "AUTO_CANCEL_ORDER_JOB"
)

type AreaLevelValue string
type AreaLevelEnt struct {
	URBAN    AreaLevelValue
	SUBURBAN AreaLevelValue
	EXURBAN  AreaLevelValue
}

var AreaLevel = &AreaLevelEnt{
	"URBAN",    // Nội thành
	"SUBURBAN", // Ngoại thành
	"EXURBAN",  // Huyện xã
}

type FeeAreaValue string
type FeeAreaEnt struct {
	HUB_TO_HUB        FeeAreaValue
	WH_TO_HUB         FeeAreaValue
	WH_TO_PROVINCE    FeeAreaValue
	WH_TO_ZONE        FeeAreaValue
	HUB_TO_WH         FeeAreaValue
	PROVINCE_TO_WH    FeeAreaValue
	ZONE_TO_WH        FeeAreaValue
	INTERNAL_PROVINCE FeeAreaValue
	INTERNAL_ZONE     FeeAreaValue
	NEAR_ZONE         FeeAreaValue
	DISTANT_ZONE      FeeAreaValue
}

var FeeArea = &FeeAreaEnt{
	"HUB_TO_HUB",        // Hub - hub
	"WH_TO_HUB",         // Kho nhận hàng - hub
	"WH_TO_PROVINCE",    // Kho nhận hàng - tỉnh
	"WH_TO_ZONE",        // Kho nhận hàng - miền
	"HUB_TO_WH",         // Kho gửi hàng - hub
	"PROVINCE_TO_WH",    // Kho gửi hàng - tỉnh
	"ZONE_TO_WH",        // Kho gửi hàng - miền
	"INTERNAL_PROVINCE", // Nội tỉnh
	"INTERNAL_ZONE",     // Nội miền
	"NEAR_ZONE",         //Cận miền
	"DISTANT_ZONE",      // Liên miền
}

type GHTKStatusValue int
type GHTKStatusEnt struct {
	CANCEL                 GHTKStatusValue
	WAITING                GHTKStatusValue
	WAIT_TO_PICK           GHTKStatusValue
	STORED                 GHTKStatusValue
	DELIVERING             GHTKStatusValue
	DELIVERED              GHTKStatusValue
	RECONCILED             GHTKStatusValue
	PICK_FAIL              GHTKStatusValue
	PICK_PENDING           GHTKStatusValue
	NOT_DELIVERED          GHTKStatusValue
	DELIVERY_PENDING       GHTKStatusValue
	PICKING                GHTKStatusValue
	RETURNING              GHTKStatusValue
	RETURNED               GHTKStatusValue
	PICKED                 GHTKStatusValue
	DELIVERY_FAIL          GHTKStatusValue
	SHIPPER_DELAY_DELIVERY GHTKStatusValue
	SHIPPER_DELAY_PICK     GHTKStatusValue
	SHIPPER_SHIPPED        GHTKStatusValue
	SHIPPER_INCOMPLETED    GHTKStatusValue
}

var GHTKStatus = &GHTKStatusEnt{
	-1,  //-1	Hủy đơn hàng
	1,   //1	Chưa tiếp nhận
	2,   //2	Đã tiếp nhận
	3,   //3	Đã lấy hàng/Đã nhập kho
	4,   //4	Đã điều phối giao hàng/Đang giao hàng
	5,   //5	Đã giao hàng/Chưa đối soát
	6,   //6	Đã đối soát
	7,   //7	Không lấy được hàng
	8,   //8	Hoãn lấy hàng
	9,   //9	Không giao được hàng
	10,  //10	Delay giao hàng
	12,  //12	Đã điều phối lấy hàng/Đang lấy hàng
	20,  //20	Đang trả hàng (COD cầm hàng đi trả)
	21,  //21	Đã trả hàng (COD đã trả xong hàng)
	123, //123	Shipper báo đã lấy hàng
	49,  //49	Shipper báo không giao được hàng
	410, //410	Shipper báo delay giao hàng
	128, //128	Shipper báo delay lấy hàng
	45,  //45	Shipper báo đã giao hàng
	49,  //49	Shipper báo không giao được giao hàng
}

type NhatTinStatusValue int
type NhatTinStatusEnt struct {
	WAITING        NhatTinStatusValue
	WAIT_TO_PICK   NhatTinStatusValue
	PICKED         NhatTinStatusValue
	DELIVERING     NhatTinStatusValue
	DELIVERED      NhatTinStatusValue
	CANCEL         NhatTinStatusValue
	RETURN         NhatTinStatusValue
	RETURNING      NhatTinStatusValue
	DELIVERY_FAIL  NhatTinStatusValue
	TRANSPORTING   NhatTinStatusValue
	PICK_FAIL      NhatTinStatusValue
	PICK_ERROR     NhatTinStatusValue
	DELIVERY_ERROR NhatTinStatusValue
}

var NhatTinStatus = &NhatTinStatusEnt{
	1,  // Đơn hàng mới được tạo bên hệ thống Nhất Tín
	2,  // Đơn hàng đã được Nhất Tín xác nhận
	3,  // Nhất Tín đã chốt hàng và shipper lấy hàng
	4,  // Đang giao hàng
	5,  // Đã giao hàng
	6,  // Đơn bị hủy
	7,  // Trả hàng
	8,  // Đang Trả hàng
	9,  // Giao hàng không được
	10, // Đơn hàng được vận chuyển từ các kho của Nhất Tín
	11, // Nhất Tín gặp sự cố không lấy được hàng
	12, // Nhất tín không lấy được hàng từ kho
	13, // Gặp sự cố khi giao hàng
}

type GHNStatusValue string
type GHNStatusEnt struct {
	READY_TO_PICK            GHNStatusValue
	PICKING                  GHNStatusValue
	PICK_FAIL                GHNStatusValue
	CANCEL                   GHNStatusValue
	MONEY_COLLECT_PICKING    GHNStatusValue
	PICKED                   GHNStatusValue
	STORING                  GHNStatusValue
	TRANSPORTING             GHNStatusValue
	SORTING                  GHNStatusValue
	DELIVERING               GHNStatusValue
	MONEY_COLLECT_DELIVERING GHNStatusValue
	DELIVERED                GHNStatusValue
	DELIVERY_FAIL            GHNStatusValue
	WAITING_TO_RETURN        GHNStatusValue
	RETURN                   GHNStatusValue
	RETURN_TRANSPORTING      GHNStatusValue
	RETURN_SORTING           GHNStatusValue
	RETURNING                GHNStatusValue
	RETURN_FAIL              GHNStatusValue
	RETURNED                 GHNStatusValue
	EXCEPTION                GHNStatusValue
	DAMAGE                   GHNStatusValue
	LOST                     GHNStatusValue
}

var GHNStatus = &GHNStatusEnt{
	"ready_to_pick",            // Đơn hàng vừa được tạo
	"picking",                  // Đang lấy hàng
	"pick_fail",                // Shipper lấy hang that bai
	"cancel",                   // Hủy đơn
	"money_collect_picking",    //	Shipper đang tương tác với người bán
	"picked",                   // Shipper đã lấy hàng
	"storing",                  // Hàng được đưa tới trung tâm phấn loại của GHN
	"transporting",             //Hàng đang được luân chuyển
	"sorting",                  //Hàng đang được phân loại
	"delivering",               // đang giao
	"money_collect_delivering", // đang tương tác với người mua hàng
	"delivered",                //Hàng đã được giao xong
	"delivery_fail",            //	Không giao được hàng
	"waiting_to_return",        //Đợi trả hàng
	"return",                   // Trả hàng
	"return_transporting",      //Hàng được trả và đang được luân chuyển về kho GHN
	"return_sorting",           //Đang phân loại hàng được trả về
	"returning",                //Đang trả hàng cho người bán
	"return_fail",              //Trả hàng cho người bán không thành công
	"returned",                 // Đã trả hàng cho người bán thành công
	"exception",                // Người bán yêu cầu trả hàng hoặc người mua đã nhận hàng yêu cầu trả hàng
	"damage",                   // Hàng bị hư hỏng
	"lost",                     // Hàng bị mất
}

type GHNServiceValue int
type GHNServiceEnv struct {
	TRUCK GHNServiceValue
	AIR   GHNServiceValue
}

var GHNService = &GHNServiceEnv{
	2,
	1,
}

type VTPStatusValue int
type VTPStatusEnt struct {
	NEW_ORDER_CREATED                                   VTPStatusValue
	ORDERS_HAVE_BEEN_SENT_AT_COLLECTION_ORDER           VTPStatusValue
	ORDER_ARE_HANDED_OVER_BY_POST_OFFICE                VTPStatusValue
	RECEIVING_CUSTOMER_ORDER_VIETTEL_POST_PROCESSING    VTPStatusValue
	VTP_REQUEST_CUSTOMER_CANCEL_ORDER                   VTPStatusValue
	ORDER_ON_PROCESSING                                 VTPStatusValue
	DELIVER_TO_POST_OFFICE_VTP_PROCESSING_ORDER         VTPStatusValue
	DELIVER_TO_RECEIVER_POSTMAN                         VTPStatusValue
	POSTMAN_RECEIVED_ORDER                              VTPStatusValue
	REQUEST_PARTNER_RECUPERATE_ORDER                    VTPStatusValue
	PARTNER_REQUEST_CANCEL_ORDER                        VTPStatusValue
	RECEIVED_FROM_POSTMAN_RECEIVING_POST_OFFICE         VTPStatusValue
	CANCEL_KEY_IN_DELIVERY_NOTE                         VTPStatusValue
	CLOSE_DELIVERY_FILE                                 VTPStatusValue
	CLOSE_DELIVERY_PACK_DELIVER_FROM                    VTPStatusValue
	CLOSE_DELIVERY_FILE_MAIL_TRACK_DELIVER_FROM         VTPStatusValue
	CLOSE_DELIVERY_FILE_TRUCK_LANE_DELIVER_FROM         VTPStatusValue
	RECEIVING_INCOME_FILE_RECEIVE_AT                    VTPStatusValue
	RECEIVING_POCKET_BAG_RECEIVE_AT                     VTPStatusValue
	RECEIVING_MAIL_TRACK_RECEIVE_AT                     VTPStatusValue
	RECEIVING_TRUCK_LANE_RECEIVE_AT                     VTPStatusValue
	DELIVER_TO_DELIVERY_POSTMAN                         VTPStatusValue
	SUCCESSFUL_DELIVERING_SUCCESS                       VTPStatusValue
	DELIVERING_BACK_TO_RECEIVER_POST_OFFICE             VTPStatusValue
	CANCEL_CUSTOMER_REQUIMENT                           VTPStatusValue
	SUCCESSFUL_DELIVERING_BACK_TO_CUSTOMER              VTPStatusValue
	INVENTORIES_DELIVERING_BACK_TO_RECEIVER_POST_OFFICE VTPStatusValue
	INVENTORIES_NO_PICK_UP_CUSTOMER                     VTPStatusValue
	INVENTORIES_CUSTOMER_PICK_UP_AT_POST_OFFICE         VTPStatusValue
	DELIVERING                                          VTPStatusValue
	DELIVERING_TO_OTHER_POST_OFFICE                     VTPStatusValue
	CANCEL_DELIVERING                                   VTPStatusValue
	DELIVERING_POST_OFFICE_RETURN_ORDER_APPROVAL        VTPStatusValue
	REQUEST_DELIVER_POST_OFFICE_RESEND                  VTPStatusValue
}

var VTPStatus = &VTPStatusEnt{
	-100, // Đơn hàng mới được tạo, chưa được chấp thuận
	-109, //Đơn đặt hàng đã được gửi tại các điểm thu tiền
	-110, // Đơn hàng được chuyển qua bưu điện
	100,  // Tiếp nhận đơn hàng của khách hàng Viettel Post xử lý đơn hàng
	101,  // ViettelPost yêu cầu khách hàng hủy đơn hàng
	102,  // Đơn hàng đang xử lý
	103,  // Giao cho Bưu điện Viettel Post xử lý đơn hàng
	104,  // Giao cho người nhận bưu tá
	105,  // Người đưa thư đã nhận được đơn đặt hàng
	106,  // Yêu cầu đối tác khôi phục đơn hàng
	107,  // Yêu cầu đối tác hủy đơn đặt hàng qua API
	200,  // Nhận từ Bưu điện Nhận Bưu điện
	201,  // Hủy khóa trong phiếu giao hàng
	300,  // Đóng tệp giao hàng
	301,  // Đóng gói giao hàng Giao từ "
	302,  // Đóng theo dõi thư gửi Giao hàng từ "
	303,  // Gần làn đường dành cho xe tải Giao hàng từ "
	400,  // Nhận hồ sơ thu nhập Nhận tại "
	401,  // Nhận túi bỏ túi Nhận tại "
	402,  // Theo dõi nhận thư Nhận tại
	403,  // Nhận làn đường dành cho xe tải Nhận tại "
	500,  // Giao cho Bưu tá Giao hàng
	501,  // Thành công-Mang đến thành công
	502,  // Gửi lại cho Bưu điện Người nhận
	503,  // Hủy-Yêu cầu của khách hàng
	504,  // Thành công-Trả lại cho khách hàng
	505,  // Hàng tồn kho-Giao lại cho Bưu điện Người nhận
	506,  // Hàng tồn kho-Không nhận Khách hàng
	507,  // Hàng tồn kho-Khách hàng nhận tại Văn phòng Pos
	508,  // Giao hàng
	509,  // Chuyển đến Bưu điện khác
	510,  // Hủy giao hàng
	515,  // Chuyển phát Bưu điện phê duyệt lệnh trả lại
	550,  // Yêu cầu Chuyển phát Bưu điện gửi lại
}

type VTPUpdateTrackingStatusValue int
type VTPUpdateTrackingStatusEnt struct {
	CONFIRM_ORDER           VTPUpdateTrackingStatusValue
	CONFIRM_RETURN_SHIPPING VTPUpdateTrackingStatusValue
	DELIVERY_AGAIN          VTPUpdateTrackingStatusValue
	CANCEL_ORDER            VTPUpdateTrackingStatusValue
	GET_BACK_ORDER          VTPUpdateTrackingStatusValue
	DELETE_CANCELED_ORDER   VTPUpdateTrackingStatusValue
}

var VTPUpdateTrackingStatus = &VTPUpdateTrackingStatusEnt{
	1,
	2,
	3,
	4,
	5,
	11,
}

type AhamoveMainStatusValue string
type AhamoveMainStatusEnt struct {
	IDLE       AhamoveMainStatusValue
	ASSIGNING  AhamoveMainStatusValue
	ACCEPTED   AhamoveMainStatusValue
	IN_PROCESS AhamoveMainStatusValue
	COMPLETED  AhamoveMainStatusValue
	CANCELLED  AhamoveMainStatusValue
}

var AhamoveMainStatus = &AhamoveMainStatusEnt{
	"IDLE",       // Đơn hàng đã được xác nhận
	"ASSIGNING",  // Đang assign cho tài xế
	"ACCEPTED",   // Đã tìm được tài xế
	"IN PROCESS", // Tài xế đang tới lấy hàng
	"COMPLETED",  // Đơn hàng đã hoàn thành
	"CANCELLED",  // Hủy đơn hàng
}

type AhamoveDeliveryStatusValue string
type AhamoveDeliveryStatusEnt struct {
	COMPLETED AhamoveDeliveryStatusValue
	FAILED    AhamoveDeliveryStatusValue
}

var AhamoveDeliveryStatus = &AhamoveDeliveryStatusEnt{
	"COMPLETED", // Giao hàng thành công
	"FAILED",    // Giao hàng thất bại
}

type AhamoveSubStatusValue string
type AhamoveSubStatusEnt struct {
	BOARDED    AhamoveSubStatusValue
	COMPLETING AhamoveSubStatusValue
}

var AhamoveSubStatus = &AhamoveSubStatusEnt{
	"BOARDED",    // Shipper tới lấy hàng
	"COMPLETING", // Trả hàng
}

type SnappyStatusValue string
type SnappyStatusEnt struct {
	REQUEST_RECEIVED      SnappyStatusValue
	PROCESSING_PICKED_UP  SnappyStatusValue
	PICKED_UP_FAIL        SnappyStatusValue
	PICK_UP               SnappyStatusValue
	WAITING_ON_THE_WAY    SnappyStatusValue
	PROCESSING_ON_THE_WAY SnappyStatusValue
	ON_THE_WAY            SnappyStatusValue
	OUT_FOR_DELIVERY      SnappyStatusValue
	PART_DELIVERY         SnappyStatusValue
	DELIVERED             SnappyStatusValue
	UNDELIVERABLE         SnappyStatusValue
	WAITING_FOR_RETURN    SnappyStatusValue
	RETURNING             SnappyStatusValue
	RETURNED              SnappyStatusValue
	CANCELED              SnappyStatusValue
}

var SnappyStatus = &SnappyStatusEnt{
	"request_received",      // Đơn mới
	"processing_picked_up",  // Đang lấy hàng
	"picked_up_fail",        // Chưa lấy được hàng
	"picked_up",             // Đã lấy hàng
	"waiting_on_the_way",    // Chờ trung chuyển
	"processing_on_the_way", // Đang trung chuyển
	"on_the_way",            // Trong kho
	"out_for_delivery",      // Đang giao
	"part_delivery",         // Giao một phần
	"delivered",             // Đã giao hàng thành công
	"undeliverable",         // giao hàng không thành công
	"waiting_for_return",    // chờ trả hàng
	"returning",             // Đang trả hàng
	"returned",              // Đã trả hàng
	"canceled",              // Đơn hàng bị hủy
}

type StatusValue string
type StatusEnt struct {
	CREATE_ORDER     StatusValue
	CANCEL           StatusValue
	READY_TO_PICK    StatusValue
	PROCESSING_ORDER StatusValue
	PICKING          StatusValue
	PICKED           StatusValue
	PICK_FAIL        StatusValue
	STORING          StatusValue
	TRANSPORTING     StatusValue
	DELIVERING       StatusValue
	DELIVERED        StatusValue
	DELIVERY_FAIL    StatusValue
	COD_COLLECTED    StatusValue
	RETURN           StatusValue
	RETURNING        StatusValue
	RETURNED         StatusValue
	RETURN_FAIL      StatusValue
	DAMAGE           StatusValue
	LOST             StatusValue
	EXCEPTION        StatusValue
	CANCEL_DELIVERY  StatusValue
}

var Status = &StatusEnt{
	"CREATE_ORDER",     // Hệ thống tự tạo đơn
	"CANCEL",           // Hủy đơn
	"READY_TO_PICK",    // Chuẩn bị lấy hàng
	"PROCESSING_ORDER", // Nhà vận chuyển đang xử lí đơn hàng
	"PICKING",          // Đang lấy hàng
	"PICKED",           // Đã lấy hàng
	"PICK_FAIL",        // Lấy hàng thất bại
	"STORING",          // Đang phân loại hàng
	"TRANSPORTING",     // Đang vận chuyển hàng
	"DELIVERING",       // Đang giao
	"DELIVERED",        // Đã giao
	"DELIVERY_FAIL",    // Giao hàng không thành công
	"COD_COLLECTED",    // Đã lấy tiền thu hộ
	"RETURN",           // Trả hàng
	"RETURNING",        // Đang trả hàng
	"RETURNED",         // Đã trả hàng
	"RETURN_FAIL",      // Trả hàng thất bại
	"DAMAGE",           //     // Hàng bị hư hỏng
	"LOST",             // Hàng bị mất
	"EXCEPTION",        // Hàng bị mất hoặc hư hỏng
	"CANCEL_DELIVERY",  // Hủy giao hàng
}

type PartnerValue string
type PartnerEnt struct {
	GHN          PartnerValue
	GHTK         PartnerValue
	VIETTEL_POST PartnerValue
	NHAT_TIN     PartnerValue
	AHAMOVE      PartnerValue
	SNAPPY       PartnerValue
	HUBCANTHO    PartnerValue
	NVCT         PartnerValue
	NINJAVAN     PartnerValue
	VNPOST       PartnerValue
	BE           PartnerValue
}

var Partner = &PartnerEnt{
	"GHN",
	"GHTK",
	"VIETTEL_POST",
	"NHAT_TIN",
	"AHAMOVE",
	"SNAPPY",
	"HUBCANTHO",
	"NVCT",
	"NINJAVAN",
	"VNPOST",
	"BE",
}

type PaymentMethodValue string
type paymentMethodEnv struct {
	COD  PaymentMethodValue
	BANK PaymentMethodValue
	CASH PaymentMethodValue
}

var PaymentMethod = &paymentMethodEnv{
	COD:  "COD",
	BANK: "BANK",
	CASH: "CASH",
}

type SnappyServiceValue string
type SnappyServiceEnt struct {
	EXPRESS  SnappyServiceValue
	STANDARD SnappyServiceValue
}

var SnappyService = &SnappyServiceEnt{
	"EXPRESS",
	"STANDARD",
}

type TypeConditionValue string
type TypeConditionEnt struct {
	WEIGHT  TypeConditionValue
	PRICE   TypeConditionValue
	PACKAGE TypeConditionValue
}

var TypeCondition = &TypeConditionEnt{
	"WEIGHT",
	"PRICE",
	"PACKAGE",
}

type StreamTypeValue string
type StreamTypeEnt struct {
	INSERT  StreamTypeValue
	UPDATE  StreamTypeValue
	REPLACE StreamTypeValue
	DELETE  StreamTypeValue
}

var StreamType = &StreamTypeEnt{
	"insert",
	"update",
	"replace",
	"delete",
}

type TPLCallbackStatusValue string
type TPLCallbackStatusEnt struct {
	INIT             TPLCallbackStatusValue
	CREATE_ORDER     TPLCallbackStatusValue
	CREATE_FAIL      TPLCallbackStatusValue
	CANCEL           TPLCallbackStatusValue
	PROCESSING_ORDER TPLCallbackStatusValue
	READY_TO_PICK    TPLCallbackStatusValue
	PICKING          TPLCallbackStatusValue
	PICKED           TPLCallbackStatusValue
	PICK_FAIL        TPLCallbackStatusValue
	STORING          TPLCallbackStatusValue
	TRANSPORTING     TPLCallbackStatusValue
	DELIVERING       TPLCallbackStatusValue
	DELIVERED        TPLCallbackStatusValue
	DELIVERY_FAIL    TPLCallbackStatusValue
	COD_COLLECTED    TPLCallbackStatusValue
	RETURN           TPLCallbackStatusValue
	RETURNING        TPLCallbackStatusValue
	RETURNED         TPLCallbackStatusValue
	RETURN_FAIL      TPLCallbackStatusValue
	DAMAGE           TPLCallbackStatusValue
	LOST             TPLCallbackStatusValue
	EXCEPTION        TPLCallbackStatusValue
	CANCEL_DELIVERY  TPLCallbackStatusValue
	MERGED           TPLCallbackStatusValue
	COMPLETED        TPLCallbackStatusValue
}

var TPLCallbackStatus = &TPLCallbackStatusEnt{
	"INIT",             // Khởi tạo đơn
	"CREATE_ORDER",     // Hệ thống tự tạo đơn
	"CREATE_FAIL",      // Đơn không book nhà vận chuyển thành công
	"CANCEL",           // Hủy đơn
	"PROCESSING_ORDER", // Nhà vận chuyển đang xử lí đơn hàng
	"READY_TO_PICK",    // Chuẩn bị lấy hàng
	"PICKING",          // Đang lấy hàng
	"PICKED",           // Đã lấy hàng
	"PICK_FAIL",        // Lấy hàng thất bại
	"STORING",          // Đang phân loại hàng
	"TRANSPORTING",     // Đang vận chuyển hàng
	"DELIVERING",       // Đang giao
	"DELIVERED",        // Đã giao
	"DELIVERY_FAIL",    // Giao hàng không thành công
	"COD_COLLECTED",    // Đã lấy tiền thu hộ
	"RETURN",           // Trả hàng
	"RETURNING",        // Đang trả hàng
	"RETURNED",         // Đã trả hàng
	"RETURN_FAIL",      // Trả hàng thất bại
	"DAMAGE",           // Hàng bị hư hỏng
	"LOST",             // Hàng bị mất
	"EXCEPTION",        // Hàng bị mất hoặc hư hỏng
	"CANCEL_DELIVERY",  // Hủy giao hàng
	"MERGED",           // Đã gộp
	"COMPLETED",        // Đã đối soát
}

type HandoverStatusValue string
type HandoverStatusEnt struct {
	DRAFT         HandoverStatusValue
	TRANSPORTING  HandoverStatusValue
	WAIT_TO_CHECK HandoverStatusValue
	CHECKING      HandoverStatusValue
	COMPLETED     HandoverStatusValue
	CANCEL        HandoverStatusValue
}

var HandoverStatus = &HandoverStatusEnt{
	"DRAFT",
	"TRANSPORTING",
	"WAIT_TO_CHECK",
	"CHECKING",
	"COMPLETED",
	"CANCEL",
}

type HubOrderTypeValue string
type HubOrderTypeEnt struct {
	RETURN       HubOrderTypeValue
	PICKUP       HubOrderTypeValue
	TRANSPORTING HubOrderTypeValue
	DELIVERY     HubOrderTypeValue
	CS           HubOrderTypeValue
}

var HubOrderType = &HubOrderTypeEnt{
	"RETURN",
	"PICKUP",
	"TRANSPORTING",
	"DELIVERY",
	"CS",
}

type HandoverTypeValue string

type HandoverTypeEnt struct {
	TRANSPORTING HandoverTypeValue // internal transfer
	HANDOVERING  HandoverTypeValue // 3pl transfer
	RECEIVING    HandoverTypeValue
	WITHDRAWING  HandoverTypeValue
}

var HandoverType = &HandoverTypeEnt{
	"TRANSPORTING",
	"HANDOVERING",
	"RECEIVING",
	"WITHDRAWING",
}

type HandoverItemStatusValue string

type HandoverItemStatusEnt struct {
	CANCEL  HandoverItemStatusValue
	DONE    HandoverItemStatusValue
	PARTIAL HandoverItemStatusValue
}

var HandoverItemStatus = &HandoverItemStatusEnt{
	"CANCEL",
	"DONE",
	"PARTIAL",
}

type HandoverItemTypeValue string

type HandoverItemTypeEnt struct {
	PO   HandoverItemTypeValue
	SO   HandoverItemTypeValue
	BIN  HandoverItemTypeValue
	TO   HandoverItemTypeValue
	FMPO HandoverItemTypeValue
	RO   HandoverItemTypeValue
	EO   HandoverItemTypeValue
}

var HandoverItemType = &HandoverItemTypeEnt{
	"PO",
	"SO",
	"BIN",
	"TO",
	"FMPO",
	"RO",
	"EO",
}

type SaleOrderStatusValue string
type saleOrderStatusEnv struct {
	DRAFT            SaleOrderStatusValue
	CONFIRMED        SaleOrderStatusValue
	RESERVING        SaleOrderStatusValue
	WAIT_TO_PICK     SaleOrderStatusValue
	PICKING          SaleOrderStatusValue
	WAIT_TO_CHECK    SaleOrderStatusValue
	CHECKING         SaleOrderStatusValue
	WAIT_TO_PACK     SaleOrderStatusValue
	PACKING          SaleOrderStatusValue
	WAIT_TO_DELIVERY SaleOrderStatusValue
	DELIVERING       SaleOrderStatusValue
	DELIVERED        SaleOrderStatusValue
	COMPLETED        SaleOrderStatusValue
	RETURN           SaleOrderStatusValue
	RETURNING        SaleOrderStatusValue
	RETURNED         SaleOrderStatusValue
	DAMAGE           SaleOrderStatusValue
	LOST             SaleOrderStatusValue
	CANCEL           SaleOrderStatusValue
}

var SaleOrderStatus = &saleOrderStatusEnv{
	DRAFT:            "DRAFT",
	CONFIRMED:        "CONFIRMED",
	RESERVING:        "RESERVING",
	WAIT_TO_PICK:     "WAIT_TO_PICK",
	PICKING:          "PICKING",
	WAIT_TO_CHECK:    "WAIT_TO_CHECK",
	CHECKING:         "CHECKING",
	WAIT_TO_PACK:     "WAIT_TO_PACK",
	PACKING:          "PACKING",
	WAIT_TO_DELIVERY: "WAIT_TO_DELIVERY",
	DELIVERING:       "DELIVERING",
	DELIVERED:        "DELIVERED",
	COMPLETED:        "COMPLETED",
	RETURN:           "RETURN",
	RETURNING:        "RETURNING",
	RETURNED:         "RETURNED",
	DAMAGE:           "DAMAGE",
	LOST:             "LOST",
	CANCEL:           "CANCEL",
}

type GHNTypeOrderValue string
type GHNTypeOrderEnt struct {
	CREATE        GHNTypeOrderValue
	SWITCH_STATUS GHNTypeOrderValue
	UPDATE_WEIGHT GHNTypeOrderValue
	UPDATE_FEE    GHNTypeOrderValue
	UPDATE_COD    GHNTypeOrderValue
}

var GHNType = &GHNTypeOrderEnt{
	"create",
	"switch_status",
	"update_weight",
	"update_fee",
	"update_cod",
}

type HubShippingOrderStatusValue string
type HubShippingOrderStatusEnt struct {
	WAIT_TO_STORING   HubShippingOrderStatusValue
	READY_TO_PICK     HubShippingOrderStatusValue
	STORING           HubShippingOrderStatusValue
	WAIT_TO_DELIVERY  HubShippingOrderStatusValue
	DELIVERING        HubShippingOrderStatusValue
	PICKING           HubShippingOrderStatusValue
	DELIVERED         HubShippingOrderStatusValue
	COMPLETED         HubShippingOrderStatusValue
	DELIVERY_FAIL     HubShippingOrderStatusValue
	PICK_FAIL         HubShippingOrderStatusValue
	COD_COLLECTED     HubShippingOrderStatusValue
	RETURN            HubShippingOrderStatusValue
	RETURNING         HubShippingOrderStatusValue
	RETURNED          HubShippingOrderStatusValue
	RETURN_FAIL       HubShippingOrderStatusValue
	DAMAGE            HubShippingOrderStatusValue
	LOST              HubShippingOrderStatusValue
	EXCEPTION         HubShippingOrderStatusValue
	CANCEL_DELIVERY   HubShippingOrderStatusValue
	CANCEL            HubShippingOrderStatusValue
	MERGED            HubShippingOrderStatusValue
	DONE_TRANSPORTING HubShippingOrderStatusValue
}

var HubShippingOrderStatus = &HubShippingOrderStatusEnt{
	"WAIT_TO_STORING",  // Chờ nhận hàng
	"READY_TO_PICK",    // Chờ gán tài xế lấy hàng
	"STORING",          // Đang phân loại hàng
	"WAIT_TO_DELIVERY", // Đã gán tài xế, chờ giao hàng
	"DELIVERING",       // Đang giao
	"PICKING",          // Đang lấy hàng
	"DELIVERED",        // Đã giao
	"COMPLETED",        // Kế toán đã đối soát
	"DELIVERY_FAIL",    // Giao hàng không thành công
	"PICK_FAIL",        // Lấy hàng không thành công
	"COD_COLLECTED",    // Đã lấy tiền thu hộ
	"RETURN",           // Trả hàng
	"RETURNING",        // Đang trả hàng
	"RETURNED",         // Đã trả hàng
	"RETURN_FAIL",      // Trả hàng thất bại
	"DAMAGE",           // Hàng bị hư hỏng
	"LOST",             // Hàng bị mất
	"EXCEPTION",        // Hàng bị mất hoặc hư hỏng
	"CANCEL_DELIVERY",  // Hủy giao hàng
	"CANCEL",
	"MERGED",
	"DONE_TRANSPORTING",
}

var MappingNhatTinCodeToStatus = map[string]int{
	"AIS": 1,
	"PRI": 2,
	"LPC": 3,
	"EOD": 4,
	"FBC": 5,
	"GBV": 6,
	"MRC": 7,
	"NRT": 8,
	"QIU": 9,
	"DIT": 10,
	"OEP": 11,
	"FUD": 13,
}

type LeadTimeConfigValue string
type LeadTimeConfigTypeEnt struct {
	DELIVERING LeadTimeConfigValue
	PICKING    LeadTimeConfigValue
	RETURNING  LeadTimeConfigValue
}

var LeadTimeConfigType = &LeadTimeConfigTypeEnt{
	"DELIVERING",
	"PICKING",
	"RETURNING",
}

type ShippingOrderTypeValue string
type ShippingOrderTypeEnt struct {
	DELIVERY       ShippingOrderTypeValue
	RETURN         ShippingOrderTypeValue
	PICKUP         ShippingOrderTypeValue
	INTERNAL_TRANS ShippingOrderTypeValue
	FMPO           ShippingOrderTypeValue
	CS             ShippingOrderTypeValue
	EO             ShippingOrderTypeValue
	OPM            ShippingOrderTypeValue
	// This only support for config fee
	RO   ShippingOrderTypeValue
	RONS ShippingOrderTypeValue
	PO   ShippingOrderTypeValue
	PGH  ShippingOrderTypeValue
	// This only support for config fee
}

var ShippingOrderType = &ShippingOrderTypeEnt{
	"DELIVERY",
	"RETURN",
	"PICKUP",
	"INTERNAL_TRANS",
	"FMPO",
	"CS",
	"EO",
	"OPM",
	// This only support for config fee
	"RO",
	"RONS",
	"PO",
	"PGH",
	// This only support for config fee
}

type NotifyTypeValue string
type NotifyTypeEnv struct {
	ACCOUNTANT NotifyTypeValue
	DRIVER     NotifyTypeValue
	HUB        NotifyTypeValue
}

// ReconcileStatus mapping
var NotifyType = &NotifyTypeEnv{
	"ACCOUNTANT",
	"DRIVER",
	"HUB",
}

type ProductStatusValue string
type ProductStatusEnv struct {
	WAIT_TO_STORING   ProductStatusValue
	SCANNING          ProductStatusValue
	STORING           ProductStatusValue
	DONE_TRANSPORTING ProductStatusValue
	TRANSPORTING      ProductStatusValue
	LOST              ProductStatusValue
	REMOVED           ProductStatusValue
	DELIVERING        ProductStatusValue
	DELIVERED         ProductStatusValue
}

// ReconcileStatus mapping
var ProductStatus = &ProductStatusEnv{
	"WAIT_TO_STORING",   // chờ nhập hub
	"SCANNING",          // đang quét kiện để nhập hub
	"STORING",           // đang lưu trữ
	"DONE_TRANSPORTING", // hoàn thành tạo luân chuyển
	"TRANSPORTING",      // đang luân chuyển
	"LOST",              // mất
	"REMOVED",           // xóa khỏi đơn
	"DELIVERING",        // đang giao
	"DELIVERED",         // đã giao
}

// ReconcileStatus mapping
type ReconcileTypeRequestValue string
type ReconcileTypeRequestEnt struct {
	TPL_COMP           ReconcileTypeRequestValue
	RIDER_HUB          ReconcileTypeRequestValue
	HUB_COMP           ReconcileTypeRequestValue
	LOGISTICS_CUS_COMP ReconcileTypeRequestValue
}

// ReconcileStatus mapping
var ReconcileTypeRequest = &ReconcileTypeRequestEnt{
	"TPL_COMP",           // Kế toán và 3PL
	"RIDER_HUB",          // Tài xế và hub
	"HUB_COMP",           // Hub và công ty
	"LOGISTICS_CUS_COMP", // Khach hang va cong ty
}

type TripStatusValue string
type TripStatusValueEnt struct {
	DRAFT        TripStatusValue // Just created when assign driver or create handover trip
	TRANSPORTING TripStatusValue
	IN_PROCESS   TripStatusValue // Driver is delivering or picking
	COMPLETE     TripStatusValue
	CANCELED     TripStatusValue
}

var TripStatus = &TripStatusValueEnt{
	"DRAFT",
	"TRANSPORTING",
	"IN_PROCESS",
	"COMPLETE",
	"CANCELED",
}

type AddressStatusValue string
type AddressStatusEnt struct {
	DRAFT         AddressStatusValue
	DELIVERED     AddressStatusValue
	DELIVERY_FAIL AddressStatusValue
}

// TODO: Add picked and pick_fail if support route opt for PICKUP
var AddressStatus = &AddressStatusEnt{
	"DRAFT",
	"DELIVERED",
	"DELIVERY_FAIL",
}

type AccountStatusValue string
type AccountStatusEnt struct {
	ACTIVE   AccountStatusValue
	INACTIVE AccountStatusValue
}

// EmailStatus ...
var AccountStatus = AccountStatusEnt{
	ACTIVE:   "ACTIVE",
	INACTIVE: "INACTIVE",
}

type FeeCollectMethodValue string
type feeCollectMethodEnv struct {
	SENDER_PAY   FeeCollectMethodValue
	RECEIVER_PAY FeeCollectMethodValue
	WALLET_PAY   FeeCollectMethodValue
	DEBT         FeeCollectMethodValue
}

var FeeCollectMethod = &feeCollectMethodEnv{
	SENDER_PAY:   "SENDER_PAY",
	RECEIVER_PAY: "RECEIVER_PAY",
	WALLET_PAY:   "WALLET_PAY",
	DEBT:         "DEBT",
}

var MapWeekday = map[string]int{
	"Monday":    1,
	"Tuesday":   2,
	"Wednesday": 3,
	"Thursday":  4,
	"Friday":    5,
	"Saturday":  6,
	"Sunday":    7,
}

var WeekendDay = []string{"Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"}

type FailReasonTypeValue string
type FailReasonTypeEnt struct {
	DELIVERY_FAIL FailReasonTypeValue
	PICK_FAIL     FailReasonTypeValue
}

var FailReasonType = &FailReasonTypeEnt{
	"DELIVERY_FAIL",
	"PICK_FAIL",
}

var VietnamReasonCode = map[string]string{
	// Delivery reason
	"101": "Khách từ chối nhận hàng",
	"102": "Khách không đủ tiền nhận hàng",
	"103": "Hàng không đúng quy định",
	"104": "Người nhận hẹn lại ngày giao hàng",
	"105": "Không liên lạc được với khách",
	"107": "Không thể thực hiện đồng thời việc lấy và giao đơn hàng này",
	"106": "Lý do khác",

	// Pick reason
	"201": "Người gửi hẹn lại ngày lấy hàng",
	"202": "Khách làm mất BIN, yêu cầu được đền bù",
	"203": "Không liên lạc được với khách",
	"204": "Lý do khác",
	// Customer support
	"301": "Không liên lạc được với khách",
	"302": "Khách hàng từ chối chăm sóc",
	"303": "Khách hẹn ngày chăm sóc",
	"304": "Lý do khác",
}

type FailReasonCode string

type FailReasonStatusEnt struct {
	// Delivery
	REFUSE                     FailReasonCode
	NOT_ENOUGH_MONEY           FailReasonCode
	INCORRECT                  FailReasonCode
	ANOTHER_DAY_DELIVERY       FailReasonCode
	UNABLE_TO_CONTACT_DELIVERY FailReasonCode
	CANT_PICK_AND_DELIVERY     FailReasonCode
	ANOTHER_REASON_DELIVERY    FailReasonCode
	// Pickup
	ANOTHER_DAY_PICK         FailReasonCode
	LOST_BIN                 FailReasonCode
	UNABLE_TO_CONTACT_PICKUP FailReasonCode
	ANOTHER_REASON_PICKUP    FailReasonCode
	// Customer support
	UNABLE_TO_CONTACT_CS FailReasonCode
	REFUSE_CS            FailReasonCode
	ANOTHER_DAY_CS       FailReasonCode
	ANOTHER_REASON_CS    FailReasonCode
}

var FailReason = &FailReasonStatusEnt{
	"101",
	"102",
	"103",
	"104",
	"105",
	"107",
	"106",
	"201",
	"202",
	"203",
	"204",
	"301",
	"302",
	"303",
	"304",
}

var EnglishReasonCode = map[string]string{
	// Delivery reason
	"101": "Customer refuses to receive the goods",
	"102": "Customers dont't have enough money to receive goods",
	"103": "Item is not in accordance with regulations",
	"104": "Recipient reschedules the delivery date",
	"105": "Other reasons: ",
	"106": "Cant contact customer",
	// Pick reason
	"201": "Sender rescheduled the pick-up date",
	"202": "Customer lost BIN, claim compensation",
	"203": "Cant contact customer",
	"204": "Other reasons: ",
}

var TplCodeFailGHTK = map[string]TPLCallbackStatusValue{
	// Code các trường hợp Giao hàng thất bại
	"9":   TPLCallbackStatus.DELIVERY_FAIL,
	"10":  TPLCallbackStatus.DELIVERY_FAIL,
	"49":  TPLCallbackStatus.DELIVERY_FAIL,
	"410": TPLCallbackStatus.DELIVERY_FAIL,

	// Code các trường hợp Lấy hàng thất bại
	"7":   TPLCallbackStatus.PICK_FAIL,
	"8":   TPLCallbackStatus.PICK_FAIL,
	"127": TPLCallbackStatus.PICK_FAIL,
	"128": TPLCallbackStatus.PICK_FAIL,
}

var NeedAddReconcileOpmGHTK = map[string]TPLCallbackStatusValue{
	"5": TPLCallbackStatus.DELIVERED,
}

var TplCodeFailVTP = map[string]TPLCallbackStatusValue{
	// Code các trường hợp Giao hàng thất bại
	"505": TPLCallbackStatus.DELIVERY_FAIL,
	"506": TPLCallbackStatus.DELIVERY_FAIL,
}

var GHTKFailDetailReason = map[string]string{
	// Lý do chậm lấy hàng
	"100": "Nhà cung cấp (NCC) hẹn lấy vào ca tiếp theo",
	"101": "GHTK không liên lạc được với NCC",
	"102": "NCC chưa có hàng",
	"103": "NCC đổi địa chỉ",
	"104": "NCC hẹn ngày lấy hàng",
	"105": "GHTK quá tải, không lấy kịp",
	"106": "Do điều kiện thời tiết, khách quan",
	"107": "Lý do khác",

	// Lý do không lấy được hàng
	"110": "Địa chỉ ngoài vùng phục vụ",
	"111": "Hàng không nhận vận chuyển",
	"112": "NCC báo hủy",
	"113": "NCC hoãn/không liên lạc được 3 lần",
	"114": "Lý do khác",
	"115": "Đối tác hủy đơn qua API",

	// Lý do chậm giao hàng
	"120":  "GHTK quá tải, giao không kịp",
	"121":  "Người nhận hàng hẹn giao ca tiếp theo",
	"122":  "Không gọi được cho người nhận hàng",
	"123":  "Người nhận hàng hẹn ngày giao",
	"124":  "Người nhận hàng chuyển địa chỉ nhận mới",
	"125":  "Địa chỉ người nhận sai, cần NCC check lại",
	"126":  "Do điều kiện thời tiết, khách quan",
	"127":  "Lý do khác",
	"128":  "Đối tác hẹn thời gian giao hàng",
	"129":  "Không tìm thấy hàng",
	"1200": "SĐT người nhận sai, cần NCC check lại",

	// Lý do không giao được hàng
	"130": "Người nhận không đồng ý nhận sản phẩm",
	"131": "Không liên lạc được với KH 3 lần",
	"132": "KH hẹn giao lại quá 3 lần",
	"133": "Shop báo hủy đơn hàng",
	"134": "Lý do khác",
	"135": "Đối tác hủy đơn qua API",
}

type SupportedLangValue string
type supportedLangEnt struct {
	VN  SupportedLangValue
	ENG SupportedLangValue
}

// EmailStatus ...
var SupportedLang = supportedLangEnt{
	VN:  "VN",
	ENG: "ENG",
}

var FailReasonCodeToType = map[string]string{
	// Delivery reason
	"101": string(FailReasonType.DELIVERY_FAIL),
	"102": string(FailReasonType.DELIVERY_FAIL),
	"103": string(FailReasonType.DELIVERY_FAIL),
	"104": string(FailReasonType.DELIVERY_FAIL),
	"105": string(FailReasonType.DELIVERY_FAIL),
	"106": string(FailReasonType.DELIVERY_FAIL),
	"107": string(FailReasonType.DELIVERY_FAIL),
	// Pick reason
	"201": string(FailReasonType.PICK_FAIL),
	"202": string(FailReasonType.PICK_FAIL),
	"203": string(FailReasonType.PICK_FAIL),
	"204": string(FailReasonType.PICK_FAIL),
}

type SupportedPartnerValue string
type SupportedPartnerEnt struct {
	GHTK         SupportedPartnerValue
	VIETTEL_POST SupportedPartnerValue
}

var SupportedPartner = &SupportedPartnerEnt{
	"GHTK",
	"VIETTEL_POST",
}

type CheckInAtValue string
type CheckInAtEnt struct {
	WAREHOUSE CheckInAtValue
	HUB       CheckInAtValue
}

var CheckInAt = &CheckInAtEnt{
	"WAREHOUSE",
	"HUB",
}

type DropOffPointTypeValue string
type dropOffPointTypeValueEnv struct {
	START    DropOffPointTypeValue
	END      DropOffPointTypeValue
	FIXED    DropOffPointTypeValue
	OPTIONAL DropOffPointTypeValue
}

var DropOffPointType = &dropOffPointTypeValueEnv{
	START:    "START",
	END:      "END",
	FIXED:    "FIXED",
	OPTIONAL: "OPTIONAL",
}

type RouteStatusValue string
type RouteStatusEnv struct {
	ACTIVE  RouteStatusValue
	DELETED RouteStatusValue
}

var RouteStatus = &RouteStatusEnv{
	ACTIVE:  "ACTIVE",
	DELETED: "DELETED",
}

type TruckStatusValue string
type TruckStatusEnv struct {
	AVAILABLE TruckStatusValue
	USING     TruckStatusValue
	DELETED   TruckStatusValue
}

var TruckStatus = &TruckStatusEnv{
	AVAILABLE: "AVAILABLE",
	USING:     "USING",
	DELETED:   "DELETED",
}

type TruckBrandValue string
type TruckBrandEnv struct {
	ISUZU    TruckBrandValue
	TATA     TruckBrandValue
	SUZUKI   TruckBrandValue
	HYUNDAI  TruckBrandValue
	VEAM     TruckBrandValue
	KIA      TruckBrandValue
	HINO     TruckBrandValue
	TMT      TruckBrandValue
	JAC      TruckBrandValue
	DONGFENG TruckBrandValue
	FUSO     TruckBrandValue
	DAEWOO   TruckBrandValue
	THACO    TruckBrandValue
}

var TruckBrand = &TruckBrandEnv{
	ISUZU:    "ISUZU",
	TATA:     "TATA",
	SUZUKI:   "SUZUKI",
	HYUNDAI:  "HYUNDAI",
	VEAM:     "VEAM",
	KIA:      "KIA",
	HINO:     "HINO",
	TMT:      "TMT",
	JAC:      "JAC",
	DONGFENG: "DONGFENG",
	FUSO:     "FUSO",
	DAEWOO:   "DAEWOO",
	THACO:    "THACO",
}

type TruckTypeValue string
type TruckTypeEnv struct {
	SMALL  TruckTypeValue
	MEDIUM TruckTypeValue
	BIG    TruckTypeValue
	SUPER  TruckTypeValue
}

var TruckType = &TruckTypeEnv{
	SMALL:  "SMALL",
	MEDIUM: "MEDIUM",
	BIG:    "BIG",
	SUPER:  "SUPER",
}

type ReferenceCodeTypeValue string
type ReferenceCodeTypeEnt struct {
	TRACKING_CODE ReferenceCodeTypeValue
	BIN_CODE      ReferenceCodeTypeValue
}

var ReferenceCodeType = ReferenceCodeTypeEnt{
	TRACKING_CODE: "TRACKING_CODE",
	BIN_CODE:      "BIN_CODE",
}

type AdjustReceivePackageValue string
type AdjustReceivePackageEnt struct {
	CHECKIN_BIN   AdjustReceivePackageValue
	FIND_LOST_BIN AdjustReceivePackageValue
}

var AdjustReceivePackage = AdjustReceivePackageEnt{
	CHECKIN_BIN:   "CHECKIN_BIN",
	FIND_LOST_BIN: "FIND_LOST_BIN",
}

type CheckInActionValue string
type CheckInActionEnt struct {
	DONE       CheckInActionValue
	RESET      CheckInActionValue
	RESET_LOST CheckInActionValue
	EXTEND     CheckInActionValue
	CHECKIN    CheckInActionValue
}

var CheckInAction = CheckInActionEnt{
	DONE:       "Done",
	RESET:      "Reset",
	RESET_LOST: "Reset_Lost",
	EXTEND:     "Extend",
	CHECKIN:    "Checkin",
}

type SubTypeValue string
type SubTypeEnt struct {
	RETURN         SubTypeValue
	PICKUP         SubTypeValue
	DELIVERY       SubTypeValue
	INTERNAL_TRANS SubTypeValue
	FMPO           SubTypeValue
	CS             SubTypeValue
	EO             SubTypeValue
}

var SubType = &SubTypeEnt{
	"RETURN",
	"PICKUP",
	"DELIVERY",
	"INTERNAL_TRANS",
	"FMPO",
	"CS",
	"EO",
}

type CompanyCodeValue string
type CompanyCodeEnt struct {
	BUYMED           CompanyCodeValue
	BUYMED_LOGISTICS CompanyCodeValue
	MEDX             CompanyCodeValue
	CIRCA            CompanyCodeValue
}

var CompanyCode = &CompanyCodeEnt{
	"BUYMED",
	"BUYMED_LOGISTICS",
	"MEDX",
	"CIRCA",
}

type FeeConditionTypeValue string
type FeeConditionTypeEnt struct {
	PACKAGE            FeeConditionTypeValue
	WEIGHT             FeeConditionTypeValue
	FROM_PROVINCE_CODE FeeConditionTypeValue
	TO_PROVINCE_CODE   FeeConditionTypeValue
	ORDER_VALUE        FeeConditionTypeValue
	TOTAL_PACKAGE      FeeConditionTypeValue
	TOTAL_ORDER        FeeConditionTypeValue
	ORDER_TYPE         FeeConditionTypeValue
}

var FeeConditionType = &FeeConditionTypeEnt{
	PACKAGE:            "PACKAGE",
	WEIGHT:             "WEIGHT",
	FROM_PROVINCE_CODE: "FROM_PROVINCE_CODE",
	TO_PROVINCE_CODE:   "TO_PROVINCE_CODE",
	ORDER_VALUE:        "ORDER_VALUE",
	TOTAL_PACKAGE:      "TOTAL_PACKAGE",
	TOTAL_ORDER:        "TOTAL_ORDER",
	ORDER_TYPE:         "ORDER_TYPE",
}

type ComparableMethodValue string
type ComparableMethodEnt struct {
	EQ  ComparableMethodValue // equal
	LT  ComparableMethodValue // less than
	GT  ComparableMethodValue // greater than
	LTE ComparableMethodValue // less than or equal
	GTE ComparableMethodValue // greater than or equal
}

var ComparableMethod = &ComparableMethodEnt{
	EQ:  "==",
	LT:  "<",
	GT:  ">",
	LTE: "<=",
	GTE: ">=",
}

type FeeTypeValue string
type FeeTypeEnt struct {
	FEE_PER_PACKAGE     FeeTypeValue
	ADDITIONAL_FEE      FeeTypeValue
	INSURANCE_FEE       FeeTypeValue
	PERCENT_ORDER_VALUE FeeTypeValue
}

var FeeType = &FeeTypeEnt{
	FEE_PER_PACKAGE:     "FEE_PER_PACKAGE",
	ADDITIONAL_FEE:      "ADDITIONAL_FEE",
	INSURANCE_FEE:       "INSURANCE_FEE",
	PERCENT_ORDER_VALUE: "PERCENT_ORDER_VALUE",
}

type ReconcileStatusValue string
type ReconcileStatusEnv struct {
	Init          ReconcileStatusValue
	WaitToPayment ReconcileStatusValue
	WaitToApprove ReconcileStatusValue
	Reconciling   ReconcileStatusValue
	Done          ReconcileStatusValue
	Cancel        ReconcileStatusValue
}

// ReconcileStatus mapping
var ReconcileStatus = &ReconcileStatusEnv{
	"INIT",
	"WAIT_TO_PAYMENT",
	"WAIT_TO_APPROVE",
	"RECONCILING",
	"DONE",
	"CANCEL",
}

type ReconcileOrderStatusValue string
type ReconcileOrderStatusEnv struct {
	Init      ReconcileOrderStatusValue
	Checking  ReconcileOrderStatusValue
	Matching  ReconcileOrderStatusValue
	Conflict  ReconcileOrderStatusValue
	Completed ReconcileOrderStatusValue
	Removed   ReconcileOrderStatusValue
}

// ReconcileOrderStatus mapping
var ReconcileOrderStatus = &ReconcileOrderStatusEnv{
	"INIT",
	"CHECKING",
	"MATCHING",
	"CONFLICT",
	"COMPLETED",
	"REMOVED",
}

type ConfigFeeValue string
type ConfigFeeEnt struct {
	PICK_N_TRANSPORT     ConfigFeeValue
	TRANSPORT_N_DELIVERY ConfigFeeValue
	TRANSPORT            ConfigFeeValue
	PICK_N_DELIVERY      ConfigFeeValue
}

var ConfigFeeType = &ConfigFeeEnt{
	"PICK_N_TRANSPORT",
	"TRANSPORT_N_DELIVERY",
	"TRANSPORT",
	"PICK_N_DELIVERY",
}

type TripTypeValue string
type TripTypeEnt struct {
	LAST_MILE_TRIP  TripTypeValue
	TRANSPORT_TRIP  TripTypeValue
	FIRST_MILE_TRIP TripTypeValue
}

var TripType = &TripTypeEnt{
	"LAST_MILE_TRIP",
	"TRANSPORT_TRIP",
	"FIRST_MILE_TRIP",
}

type SignatureTypeValue string
type SignatureTypeEnt struct {
	SENDER_SIGN   SignatureTypeValue
	RECEIVER_SIGN SignatureTypeValue
}

var SignatureType = &SignatureTypeEnt{
	"SENDER_SIGN",
	"RECEIVER_SIGN",
}

type ReconcileBetweenValue string
type ReconcileBetweenEnt struct {
	TPL_COMP  ReconcileTypeRequestValue
	RIDER_HUB ReconcileTypeRequestValue
	HUB_COMP  ReconcileTypeRequestValue
	CUS_COMP  ReconcileTypeRequestValue
}

var ReconcileBetween = &ReconcileBetweenEnt{
	"TPL_COMP",  // Kế toán và 3PL
	"RIDER_HUB", // Tài xế và hub
	"HUB_COMP",  // Hub và công ty
	"CUS_COMP",  // Khách hàng và công ty
}

type ReconcileTypeValue string
type ReconcileTypeEnt struct {
	COD ReconcileTypeValue
	FEE ReconcileTypeValue
}

var ReconcileType = &ReconcileTypeEnt{
	"COD",
	"FEE",
}

type PreferPaymentMethodValue string
type PreferPaymentMethodEnt struct {
	CASH PreferPaymentMethodValue
	DEBT PreferPaymentMethodValue
}

var PreferPaymentMethod = &PreferPaymentMethodEnt{
	"CASH",
	"DEBT",
}

type CustomerTypeValue string
type CustomerTypeEnt struct {
	SELLER   CustomerTypeValue
	VENDOR   CustomerTypeValue
	INTERNAL CustomerTypeValue
	EXTERNAL CustomerTypeValue
}

var CustomerType = &CustomerTypeEnt{
	"SELLER",
	"VENDOR",
	"INTERNAL",
	"EXTERNAL",
}

type ReadPreferenceValue string
type ReadPreferenceEnt struct {
	PRIMARY   ReadPreferenceValue
	SECONDARY ReadPreferenceValue
}

var ReadPreference = &ReadPreferenceEnt{
	PRIMARY:   "PRIMARY",
	SECONDARY: "SECONDARY",
}

type MergeStatusValue string
type MergeStatusEnt struct {
	WAIT_TO_MERGE MergeStatusValue
	EXPIRED       MergeStatusValue
	MERGED        MergeStatusValue
	DEFAULT       MergeStatusValue
}

var MergeStatus = &MergeStatusEnt{
	"WAIT_TO_MERGE",
	"EXPIRED",
	"MERGED",
	"DEFAULT",
}

type CustomerOrderStatusValue string
type CustomerOrderStatusEnt struct {
	READY_TO_PICK    CustomerOrderStatusValue
	PICKED           CustomerOrderStatusValue
	WAIT_TO_DELIVERY CustomerOrderStatusValue
	DELIVERED        CustomerOrderStatusValue
}

var CustomerOrderStatus = &CustomerOrderStatusEnt{
	"READY_TO_PICK",
	"PICKED",
	"WAIT_TO_DELIVERY",
	"DELIVERED",
}

type BankCodeValue string
type BankCodeEnt struct {
	VPBANK      BankCodeValue
	TECHCOMBANK BankCodeValue
	MSB         BankCodeValue
}

var BankCode = &BankCodeEnt{
	"VPBANK",
	"TECHCOMBANK",
	"MSB",
}

var BankCodeToACQID = map[BankCodeValue]string{
	BankCode.VPBANK:      "970432",
	BankCode.TECHCOMBANK: "970407",
	BankCode.MSB:         "970426",
}

type ProductTypeValue string
type ProductTypeEnt struct {
	CUSTOMER_GOODS       ProductTypeValue
	FOOD                 ProductTypeValue
	MEDICINE             ProductTypeValue
	AGRICULTURAL_PRODUCT ProductTypeValue
	FRAGILE_PRODUCT      ProductTypeValue
	CHEMICAL             ProductTypeValue
	OTHER                ProductTypeValue
}

var ProductType = &ProductTypeEnt{
	"CUSTOMER_GOODS",
	"FOOD",
	"MEDICINE",
	"AGRICULTURAL_PRODUCT",
	"FRAGILE_PRODUCT",
	"CHEMICAL",
	"OTHER",
}

type ViewModeValue string
type ViewModeEnt struct {
	MOY ViewModeValue
	DOM ViewModeValue
}

var ViewMode = &ViewModeEnt{
	"MOY",
	"DOM",
}

type ProductivityActionValue string
type ProductivityActionEnt struct {
	ASSIGN_PICK     ProductivityActionValue
	ASSIGN_DELIVERY ProductivityActionValue
	PICKED          ProductivityActionValue
	DELIVERED       ProductivityActionValue
	PICK_FAIL       ProductivityActionValue
	DELIVERY_FAIL   ProductivityActionValue
	TRANSPORTED     ProductivityActionValue
}

var ProductivityAction = &ProductivityActionEnt{
	"ASSIGN_PICK",
	"ASSIGN_DELIVERY",
	"PICKED",
	"DELIVERED",
	"PICK_FAIL",
	"DELIVERY_FAIL",
	"TRANSPORTED",
}

type ProductivityQueryValue string
type ProductivityQueryEnt struct {
	DAILY ProductivityQueryValue
	RANGE ProductivityQueryValue
}

var ProductivityQuery = &ProductivityQueryEnt{
	"DAILY",
	"RANGE",
}

type TimeFrameValue string
type TimeFrameEnt struct {
	ONE_MONTH   TimeFrameValue
	TWO_MONTH   TimeFrameValue
	THREE_MONTH TimeFrameValue
}

var TimeFrame = &TimeFrameEnt{
	"ONE_MONTH",
	"TWO_MONTH",
	"THREE_MONTH",
}

type ComputeLeadTimeMethodValue string
type ComputeLeadTimeMethodEnt struct {
	SUGGEST ComputeLeadTimeMethodValue
	CONFIG  ComputeLeadTimeMethodValue
}

var ComputeLeadTimeMethod = &ComputeLeadTimeMethodEnt{
	"SUGGEST",
	"CONFIG",
}

type AdditionalFeeTypeValue string
type AdditionalFeeTypeEnt struct {
	FRAGILE_PRODUCT    AdditionalFeeTypeValue
	STORAGE_FEE        AdditionalFeeTypeValue
	HIGH_VALUE_PRODUCT AdditionalFeeTypeValue
	COLLECT_COD_FEE    AdditionalFeeTypeValue
	RETURN_FEE         AdditionalFeeTypeValue
	AUDIT_FEE          AdditionalFeeTypeValue
}

var AdditionalFeeType = &AdditionalFeeTypeEnt{
	FRAGILE_PRODUCT:    "FRAGILE_PRODUCT",
	STORAGE_FEE:        "STORAGE_FEE",
	HIGH_VALUE_PRODUCT: "HIGH_VALUE_PRODUCT",
	COLLECT_COD_FEE:    "COLLECT_COD_FEE",
	RETURN_FEE:         "RETURN_FEE",
	AUDIT_FEE:          "AUDIT_FEE",
}

type CarrierTypeValue string
type CarrierTypeEnt struct {
	ONEPL   CarrierTypeValue
	TWOPL   CarrierTypeValue
	THREEPL CarrierTypeValue
}

var CarrierType = &CarrierTypeEnt{
	"ONEPL",
	"TWOPL",
	"THREEPL",
}

type PrepareCheckinStatusValue string
type PrepareCheckinStatusEnt struct {
	// Invalid
	DELIVERING             PrepareCheckinStatusValue // Khi bin thuộc 1 đơn hàng đang giao và chưa có đơn lấy bin( thu hồi bin)
	MISSING_TRANSFER_TO_WH PrepareCheckinStatusValue // Chỉ xảy ra khi có duy nhất 1 đơn chứa bin chưa tạo lc
	NOT_USED               PrepareCheckinStatusValue // Chưa đc sửa dụng
	LOST                   PrepareCheckinStatusValue // Thất lạc
	// Valid
	PICK_AT_WH         PrepareCheckinStatusValue
	TRANSPORTING_TO_WH PrepareCheckinStatusValue
}

var PrepareCheckinStatus = &PrepareCheckinStatusEnt{
	DELIVERING:             "DELIVERING",
	MISSING_TRANSFER_TO_WH: "MISSING_TRANSFER_TO_WH",
	NOT_USED:               "NOT_USED",
	PICK_AT_WH:             "PICK_AT_WH",
	TRANSPORTING_TO_WH:     "TRANSPORTING_TO_WH",
	LOST:                   "LOST",
}

type SnapshotTypeValue string
type SnapshotTypeEnt struct {
	CACHE_ORDER_STATUS SnapshotTypeValue
}

var SnapshotType = &SnapshotTypeEnt{
	"CACHE_ORDER_STATUS",
}
