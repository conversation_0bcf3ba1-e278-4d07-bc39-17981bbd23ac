package customer

import (
	"log"
	"sync"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
	"go.mongodb.org/mongo-driver/mongo"
)

type ExecutorJob struct {
	Job *job.Executor
}

var (
	instance            map[string]*ExecutorJob
	onceInit            map[string]*sync.Once
	createCustomerTopic = "customer"
)

func init() {
	onceInit = map[string]*sync.Once{}
	instance = map[string]*ExecutorJob{}
	createCustomerTopic = conf.Config.Topics["customer"]
}

func InitCustomerJob(dbSession *mongo.Database, Database, Collection string) {
	instanceName := createCustomerTopic
	if onceInit[instanceName] == nil {
		onceInit[instanceName] = &sync.Once{}
	}
	onceInit[instanceName].Do(func() {
		instance[instanceName] = &ExecutorJob{
			&job.Executor{ColName: Collection},
		}

		instance[instanceName].Job.InitWithConfig(dbSession,
			Database,
			&job.ExecutorConfiguration{
				ParallelTopicProcessing: false,
				FailThreshold:           50,
				ChannelCount:            5,
				SortedItem:              false,
				LogSize:                 5,
				SelectorDelayMS:         100,
				WaitForReadyTime:        false,
				UniqueItem:              false,
				ConsumedExipredTime:     time.Duration(720) * time.Hour,
				CurVersionTimeoutS:      30,
				OldVersionTimeoutS:      30,
				MaximumWaitToRetryS:     15,
			},
		)

		log.Println("StartConsume", instanceName)
		instance[instanceName].createCustomerConsume()
		instance[instanceName].Job.StartConsume()
	})
}

func PushCreateCustomer(data interface{}, sortedKey string) (err error) {
	return instance[createCustomerTopic].Job.Push(
		data, &job.JobItemMetadata{
			Topic:     createCustomerTopic,
			SortedKey: sortedKey,
		})
}
