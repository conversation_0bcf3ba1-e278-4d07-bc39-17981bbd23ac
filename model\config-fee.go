package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type AreaIdentifier struct {
	Code string `json:"code,omitempty" bson:"code,omitempty"`
	Name string `json:"name,omitempty" bson:"name,omitempty"`
}

type FeeArea struct {
	FromAreaIdentifiers []AreaIdentifier      `json:"fromAreaIdentifiers,omitempty" bson:"from_area_identifiers,omitempty"`
	ToAreaIdentifiers   []AreaIdentifier      `json:"toAreaIdentifiers,omitempty" bson:"to_area_identifiers,omitempty"`
	AreaLevels          []enum.AreaLevelValue `json:"areaLevels,omitempty" bson:"area_levels,omitempty"`
	ArrAreaIdentifiers  [][]AreaIdentifier    `json:"arrAreaIdentifiers,omitempty" bson:"arr_area_identifiers,omitempty"`
	Type                enum.FeeAreaValue     `json:"type,omitempty" bson:"type,omitempty"`
	WarehouseCode       string                `json:"warehouseCode,omitempty" bson:"warehouse_code"`
}

type FeeCondition struct {
	ConditionType enum.FeeConditionTypeValue `json:"conditionType,omitempty" bson:"condition_type,omitempty"`
	Value         interface{}                `json:"value,omitempty" bson:"value,omitempty"`
	Method        enum.ComparableMethodValue `json:"method,omitempty" bson:"method,omitempty"`
}

type FeeStructure struct {
	FeeConditions []FeeCondition                `json:"conditions,omitempty" bson:"conditions,omitempty"`
	Fees          map[enum.FeeTypeValue]float64 `json:"fees,omitempty" bson:"fees,omitempty"`
}

type AdditionalFee struct {
	AdditionalFeeType enum.AdditionalFeeTypeValue `json:"additionalFeeType,omitempty" bson:"additional_fee_type,omitempty"`
	Fee               float64                     `json:"fee,omitempty" bson:"fee,omitempty"`
}

type ConfigFee struct {
	ID                   primitive.ObjectID             `json:"-" bson:"_id,omitempty" `
	ConfigId             int64                          `json:"configId,omitempty" bson:"config_id,omitempty"`
	Name                 string                         `json:"configName,omitempty" bson:"config_name,omitempty"`
	FeeStructures        []FeeStructure                 `json:"feeStructures,omitempty" bson:"fee_structures,omitempty"`
	ConfigFeeTypes       []enum.ConfigFeeValue          `json:"configFeeTypes,omitempty" bson:"config_fee_types,omitempty"`
	LastUpdatedBy        string                         `json:"lastUpdatedBy,omitempty" bson:"last_updated_by,omitempty"`
	LastUpdatedTime      *time.Time                     `json:"lastUpdateTime,omitempty" bson:"last_updated_time,omitempty"`
	IsDeleted            *bool                          `json:"isDeleted,omitempty" bson:"is_deleted,omitempty"`
	ShippingOrderTypes   *[]enum.ShippingOrderTypeValue `json:"shippingOrderTypes,omitempty" bson:"shipping_order_types,omitempty"`
	Code                 string                         `json:"code,omitempty" bson:"code,omitempty"`
	IsApplyToArea        *bool                          `json:"isApplyToArea,omitempty" bson:"is_apply_to_area,omitempty"`
	FeeArea              *FeeArea                       `json:"feeArea,omitempty" bson:"fee_area,omitempty"`
	FeeAreaList          []ConfigFee                    `json:"feeAreaList,omitempty" bson:"fee_area_list,omitempty"`
	Keyword              string                         `json:"keyword,omitempty" bson:"keyword,omitempty"`
	AdditionalFees       *[]AdditionalFee               `json:"additionalFees,omitempty" bson:"additional_fees,omitempty"`
	DefaultConfigFeeType enum.ConfigFeeValue            `json:"defaultConfigFeeType,omitempty" bson:"default_config_fee_type,omitempty"`
}

var ConfigFeeDB = &db.Instance{
	ColName:        "config_fee",
	TemplateObject: &ConfigFee{},
}

func InitConfigFeeModel(s *mongo.Database) {
	ConfigFeeDB.ApplyDatabase(s)
}
