package api

import (
	"encoding/json"
	"log"
	"strconv"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"go.mongodb.org/mongo-driver/bson"
)

// BookShippingService api
func BookShippingService(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input request.BookShipping
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.<PERSON>rror(),
		})
	}

	//Book by ?
	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}
	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	return resp.Respond(action.BookShippingService(&input, UserInfo.Account.AccountID))
}

// GetShippingService api
func GetShippingService(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input request.GetShippingService
	str := req.GetParam("q")
	if str == "" {
		str = "{}"
	}

	err := json.Unmarshal([]byte(str), &input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data: " + err.Error(),
			ErrorCode: "JSON_INVALID",
		})
	}
	return resp.Respond(action.GetAvailableShippingCarrier(input))
}

// GetShippingService api
func GetShippingServiceFee(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input request.GetShippingService
	str := req.GetParam("q")
	if str == "" {
		str = "{}"
	}

	err := json.Unmarshal([]byte(str), &input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data: " + err.Error(),
			ErrorCode: "JSON_INVALID",
		})
	}
	if input.ServiceType != nil &&
		*input.ServiceType == enum.ShippingOrderType.RETURN &&
		len(input.CarrierIds) == 0 {
		deliveredShippingOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
			"reference_code": input.ReferenceCode,
		})
		if deliveredShippingOrderRaw.Status != common.APIStatus.Ok {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Không tìm thấy đơn giao hàng thành công",
				ErrorCode: "NOT_FOUND_SHIPPING_ORDER",
			})
		}
		deliveredShippingOrder := deliveredShippingOrderRaw.Data.([]*model.ShippingOrder)[0]
		carrier, _, err := action.FindSuitableCarrierAndHubForReturn(&request.BookShippingOrder{
			From: &model.Address{
				Code:         strconv.FormatInt(input.CustomerID, 10),
				ProvinceName: input.FromProvince,
				ProvinceCode: input.FromProvinceCode,
				DistrictName: input.FromDistrict,
				DistrictCode: input.FromDistrictCode,
				WardName:     input.FromWard,
				WardCode:     input.FromWardCode,
			},
			To: &model.Address{
				Code:         input.WarehouseCode,
				ProvinceName: input.Province,
				ProvinceCode: input.ProvinceCode,
				DistrictName: input.District,
				DistrictCode: input.DistrictCode,
				WardName:     input.Ward,
				WardCode:     input.WardCode,
			},
			NumPackage: input.PackageCount,
			Weight:     input.Weight,
		}, deliveredShippingOrder)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   err.Error(),
				ErrorCode: "NOT_FOUND_VALID_CARRIER",
			})
		}
		if carrier == nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Chưa có nhà vận chuyển nào hộ trợ trong khu vực này",
			})
		}
		input.Carriers = append(input.Carriers, carrier)
		return resp.Respond(action.GetShippingServiceFee(&input))
	}

	if input.ServiceType != nil &&
		*input.ServiceType == enum.ShippingOrderType.EO {
		return resp.Respond(action.GetShippingServiceFeeV2(&input))
	}

	if len(input.CarrierIds) > 0 {
		carriersRaw := model.CarrierDB.Query(bson.M{
			"carrier_id": bson.M{
				"$in": input.CarrierIds,
			},
		}, 0, int64(len(input.CarrierIds)), nil)

		if carriersRaw.Status != common.APIStatus.Ok {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không tìm thấy nhà vận chuyển",
			})
		}
		input.Carriers = carriersRaw.Data.([]*model.Carrier)
	}

	return resp.Respond(action.GetShippingServiceFee(&input))
}

// CancelShippingService api
func CancelShippingService(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input request.CancelShippingService
	err := req.GetContent(&input)
	log.Println("Cancel booking GetContentText => ", req.GetContentText())
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.CancelShippingService(&input))
}

// CreateHubOrderByShippingOrder api
func CreateHubOrderByShippingOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input request.CreateHubOrderRequest
	err := req.GetContent(&input)
	log.Println("Create HubOrder=> ", req.GetContentText())
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.CreateHubOrderByShippingOrder(&input))
}

func MigrateCategory(req sdk.APIRequest, resp sdk.APIResponder) error {
	type referenceCodes struct {
		TestOrder  []string `json:"referenceCodes"`
		MassUpdate bool     `json:"massUpdate"`
	}
	var input referenceCodes
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.MigrateCategory(input.TestOrder, input.MassUpdate))
}

func MigrateSubType(req sdk.APIRequest, resp sdk.APIResponder) error {
	type referenceCodes struct {
		TestOrder  []string `json:"referenceCodes"`
		MassUpdate bool     `json:"massUpdate"`
	}
	var input referenceCodes
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.MigrateSubType(input.TestOrder, input.MassUpdate))
}
