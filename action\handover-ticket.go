package action

import (
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"

	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/response"

	shippingOrderQueue "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action/consumer/shipping-order"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/utils"

	"github.com/google/uuid"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action/consumer/backup"
	handOverTransfer "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action/consumer/hand-over-transfer"
	hub_shipping_order "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action/consumer/hub-shipping-order"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// GetHandoverTicketByID get handover ticket by id
func GetHandoverTicketByID(ticketID int) *common.APIResponse {
	if ticketID <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã phiếu bàn giao không được để trống.",
			ErrorCode: "TICKET_ID_REQUIRED",
		}
	}
	return model.HandoverTicketDB.QueryOne(bson.M{
		"ticket_id": ticketID,
	})
}

// GetHandoverTicketList get handover ticket list
func GetHandoverTicketList(query *request.GetHandoverTicketRequest, offset int64, limit int64, getTotal bool) *common.APIResponse {
	filter := bson.M{}
	if query.TicketID > 0 {
		filter["ticket_id"] = query.TicketID
	}

	if query.TripId != 0 {
		filter["trip_id"] = query.TripId
	}

	if query.TripCode != "" {
		filter["trip_code"] = query.TripCode
	}

	if query.TruckID != 0 {
		filter["truck_id"] = query.TruckID
	}

	if query.RouteCode != "" {
		filter["route_code"] = query.RouteCode
	}

	if query.DriverID != 0 {
		filter["driver_id"] = query.DriverID
	}

	if query.Code != "" {
		filter["code"] = query.Code
	}

	if query.IsAssignToDriver != nil {
		if !*query.IsAssignToDriver {
			filter["$or"] = bson.A{
				bson.M{
					"driver_id": "",
				},
				bson.M{
					"driver_id": bson.M{
						"$exists": query.IsAssignToDriver,
					},
				},
			}
		}

		if *query.IsAssignToDriver {
			filter["driver_id"] = bson.M{
				"$exists": query.IsAssignToDriver,
			}
		}
	}

	if query.TPLCode != "" {
		filter["tpl_code"] = query.TPLCode
	}

	if query.FromDepartmentCode != "" {
		filter["from_department_code"] = query.FromDepartmentCode
	}

	if query.ToDepartmentCode != "" {
		filter["to_department_code"] = query.ToDepartmentCode
	}

	if query.WarehouseCode != "" {
		filter["warehouse_code"] = query.WarehouseCode
	}

	if query.Status != nil && *query.Status != "" {
		filter["status"] = *query.Status
	}

	if query.CreatedBy > 0 {
		filter["created_by"] = query.CreatedBy
	}

	if len(query.ListTicketId) > 0 {
		if len(query.ListTicketId) == 1 {
			filter["ticket_id"] = query.ListTicketId[0]
		} else {
			filter["ticket_id"] = bson.M{
				"$in": query.ListTicketId,
			}
		}
	}

	if query.SO != "" {
		filter["so_list.so"] = query.SO
	}

	if len(query.ListSo) > 0 {
		if len(query.ListSo) == 1 {
			filter["so_list.so"] = query.ListSo[0]
		} else {
			filter["so_list.so"] = bson.M{
				"$in": query.ListSo,
			}
		}
	}

	if len(query.ListReferenceCode) > 0 {
		if len(query.ListReferenceCode) == 1 {
			filter["so_list.reference_code"] = query.ListReferenceCode[0]
		} else {
			filter["so_list.reference_code"] = bson.M{
				"$in": query.ListReferenceCode,
			}
		}
	}

	if query.ItemType != nil {
		filter["item_type"] = query.ItemType
	}

	if query.DepartmentCode != "" {
		filter["$or"] = []bson.M{
			{
				"from_department_code": query.DepartmentCode,
			},
			{
				"to_department_code": query.DepartmentCode,
			},
		}
	}

	if len(query.DepartmentCodes) > 0 {
		filter["$or"] = []bson.M{
			{
				"from_department_code": bson.M{
					"$in": query.DepartmentCodes,
				},
			},
			{
				"to_department_code": bson.M{
					"$in": query.DepartmentCodes,
				},
			},
		}
	}

	// Query by Completed time
	if query.FromTime > 0 && query.ToTime > 0 {
		fromTime := time.Unix(query.FromTime, 0)
		toTime := time.Unix(query.ToTime, 0)

		filter["completed_time"] = bson.M{
			"$gte": fromTime,
			"$lte": toTime,
		}
	} else if query.FromTime > 0 && query.ToTime == 0 {
		fromTime := time.Unix(query.FromTime, 0)
		filter["completed_time"] = bson.M{
			"$gte": fromTime,
		}
	} else if query.FromTime == 0 && query.ToTime > 0 {
		toTime := time.Unix(query.ToTime, 0)
		filter["completed_time"] = bson.M{
			"$lte": toTime,
		}
	}

	// Query by Created time
	if query.FromCreatedTime > 0 && query.ToCreatedTime > 0 {
		fromCreatedTime := time.Unix(query.FromCreatedTime, 0)
		toCreatedTime := time.Unix(query.ToCreatedTime, 0)

		filter["created_time"] = bson.M{
			"$gte": fromCreatedTime,
			"$lte": toCreatedTime,
		}
	} else if query.FromCreatedTime > 0 && query.ToCreatedTime == 0 {
		fromCreatedTime := time.Unix(query.FromCreatedTime, 0)
		filter["created_time"] = bson.M{
			"$gte": fromCreatedTime,
		}
	} else if query.FromCreatedTime == 0 && query.ToCreatedTime > 0 {
		toCreatedTime := time.Unix(query.ToCreatedTime, 0)
		filter["created_time"] = bson.M{
			"$lte": toCreatedTime,
		}
	}

	// Query by Handover time
	if query.FromDoneHandoverTime > 0 && query.ToDoneHandoverTime > 0 {
		fromHandoverTime := time.Unix(query.FromDoneHandoverTime, 0)
		toHandoverTime := time.Unix(query.ToDoneHandoverTime, 0)

		filter["done_handover_time"] = bson.M{
			"$gte": fromHandoverTime,
			"$lte": toHandoverTime,
		}
	} else if query.FromDoneHandoverTime > 0 && query.ToDoneHandoverTime == 0 {
		fromHandoverTime := time.Unix(query.FromDoneHandoverTime, 0)
		filter["done_handover_time"] = bson.M{
			"$gte": fromHandoverTime,
		}
	} else if query.FromDoneHandoverTime == 0 && query.ToDoneHandoverTime > 0 {
		toHandoverTime := time.Unix(query.ToDoneHandoverTime, 0)
		filter["done_handover_time"] = bson.M{
			"$lte": toHandoverTime,
		}
	}

	// Check xem có phiếu luân chuyển để trả hàng nào đang về kho hay không
	// Ex:
	// Input: ToDepartmentCode: "HUBVSIPII" ParentReferenceCode: "SOBD123"
	// Output: TRANSPORT-123 { so: "SOBD123R1" } || Not found
	if query.ParentReferenceCode != "" {
		// Tại một thời điểm chỉ có 1 đơn trả hàng chưa hoàn thành hoặc chưa hủy
		// Có thể mã đơn SO123 (luồng cũ) tạo luân chuyển nhưng cũng có thể SO123R1 (luồng mới) tạo luân chuyển về kho
		hubOrdersRaw := model.HUBShippingOrderDB.QueryOne(bson.M{
			"$or": []interface{}{
				bson.M{"parent_reference_code": query.ParentReferenceCode},
				bson.M{"reference_code": query.ParentReferenceCode},
			},
			"hub_code": query.ToDepartmentCode,
			"status":   enum.HubShippingOrderStatus.WAIT_TO_STORING,
		})

		if hubOrdersRaw.Status == common.APIStatus.NotFound {
			return &common.APIResponse{
				Status:  common.APIStatus.NotFound,
				Message: "Not found any match ticket",
			}
		}

		hubOrder := hubOrdersRaw.Data.([]*model.HubShippingOrder)[0]
		if _, ok := hubOrder.ExtraInfo["handoverCode"]; ok {
			filter["code"] = hubOrder.ExtraInfo["handoverCode"].(string)
		} else {
			// Nếu chưa luân chuyển thì trả not found
			return &common.APIResponse{
				Status:  common.APIStatus.NotFound,
				Message: "Not found any match ticket",
			}
		}
	}

	if query.ReadPreference != nil &&
		*query.ReadPreference == enum.ReadPreference.SECONDARY {
		result := model.HandoverTicketDB.SecondaryInstance.Query(
			filter,
			offset,
			limit,
			&primitive.M{"_id": -1})

		if getTotal {
			countResult := model.HandoverTicketDB.SecondaryInstance.Count(filter)
			result.Total = countResult.Total
		}

		return result
	}

	result := model.HandoverTicketDB.Query(
		filter,
		offset,
		limit,
		&primitive.M{"_id": -1})

	if getTotal {
		countResult := model.HandoverTicketDB.Count(filter)
		result.Total = countResult.Total
	}

	return result
}

// CountHandoverTicketGroupByStatus count ticket group by status
func CountHandoverTicketGroupByStatus(query *request.GetHandoverTicketRequest) *common.APIResponse {
	type CountTicketByStatusModel struct {
		Status   string `json:"status,omitempty" bson:"_id,omitempty"`
		Quantity int64  `json:"quantity,omitempty" bson:"total,omitempty"`
	}

	filter := bson.D{}

	if query.Code != "" {
		filter = append(filter, bson.E{
			Key:   "code",
			Value: query.Code,
		})
	}

	if query.SO != "" {
		filter = append(filter, bson.E{
			Key:   "so_list.so",
			Value: query.SO,
		})
	}

	if len(query.ListSo) > 0 {
		filter = append(filter, bson.E{
			Key: "so_list.so",
			Value: bson.M{
				"$in": query.ListSo,
			},
		})
	}

	if len(query.ListReferenceCode) > 0 {
		filter = append(filter, bson.E{
			Key: "so_list.reference_code",
			Value: bson.M{
				"$in": query.ListReferenceCode,
			},
		})
	}

	if query.TPLCode != "" {
		filter = append(filter, bson.E{
			Key:   "tpl_code",
			Value: query.TPLCode,
		})
	}

	if query.WarehouseCode != "" {
		filter = append(filter, bson.E{
			Key:   "warehouse_code",
			Value: query.WarehouseCode,
		})
	}

	if len(query.ListTicketId) > 0 {
		filter = append(filter, bson.E{
			Key: "ticket_id",
			Value: bson.M{
				"$in": query.ListTicketId,
			},
		})
	}

	if query.FromDepartmentCode != "" {
		filter = append(filter, bson.E{
			Key:   "from_department_code",
			Value: query.FromDepartmentCode,
		})
	}

	if query.ToDepartmentCode != "" {
		filter = append(filter, bson.E{
			Key:   "to_department_code",
			Value: query.ToDepartmentCode,
		})
	}

	if query.CreatedBy > 0 {
		filter = append(filter, bson.E{
			Key:   "created_by",
			Value: query.CreatedBy,
		})
	}

	if query.ItemType != nil {
		filter = append(filter, bson.E{
			Key:   "item_type",
			Value: query.ItemType,
		})
	}

	if query.TripId != 0 {
		filter = append(filter, bson.E{
			Key:   "trip_id",
			Value: query.TripId,
		})
	}

	if query.Code != "" {
		filter = append(filter, bson.E{
			Key:   "trip_code",
			Value: query.TripCode,
		})
	}

	if query.DriverID != 0 {
		filter = append(filter, bson.E{
			Key:   "driver_id",
			Value: query.DriverID,
		})
	}

	if query.RouteCode != "" {
		filter = append(filter, bson.E{
			Key:   "route_code",
			Value: query.RouteCode,
		})
	}

	if query.TruckID != 0 {
		filter = append(filter, bson.E{
			Key:   "truck_id",
			Value: query.TruckID,
		})
	}

	// Query by Completed time
	if query.FromTime > 0 && query.ToTime > 0 {
		fromTime := time.Unix(query.FromTime, 0)
		toTime := time.Unix(query.ToTime, 0)

		filter = append(filter, bson.E{
			Key: "completed_time",
			Value: bson.M{
				"$gte": fromTime,
				"$lte": toTime,
			},
		})
	} else if query.FromTime > 0 && query.ToTime == 0 {
		fromTime := time.Unix(query.FromTime, 0)
		filter = append(filter, bson.E{
			Key: "completed_time",
			Value: bson.M{
				"$gte": fromTime,
			},
		})
	} else if query.FromTime == 0 && query.ToTime > 0 {
		toTime := time.Unix(query.ToTime, 0)
		filter = append(filter, bson.E{
			Key: "completed_time",
			Value: bson.M{
				"$lte": toTime,
			},
		})
	}

	// Query to Created time
	if query.FromCreatedTime > 0 && query.ToCreatedTime > 0 {
		fromCreatedTime := time.Unix(query.FromCreatedTime, 0)
		toCreatedTime := time.Unix(query.ToCreatedTime, 0)

		filter = append(filter, bson.E{
			Key: "created_time",
			Value: bson.M{
				"$gte": fromCreatedTime,
				"$lte": toCreatedTime,
			},
		})
	} else if query.FromCreatedTime > 0 && query.ToCreatedTime == 0 {
		fromCreatedTime := time.Unix(query.FromCreatedTime, 0)
		filter = append(filter, bson.E{
			Key: "created_time",
			Value: bson.M{
				"$gte": fromCreatedTime,
			},
		})
	} else if query.FromCreatedTime == 0 && query.ToCreatedTime > 0 {
		toCreatedTime := time.Unix(query.ToCreatedTime, 0)
		filter = append(filter, bson.E{
			Key: "created_time",
			Value: bson.M{
				"$lte": toCreatedTime,
			},
		})
	}

	// Query by DoneHandover time
	if query.FromDoneHandoverTime > 0 && query.ToDoneHandoverTime > 0 {
		fromDoneHandoverTime := time.Unix(query.FromDoneHandoverTime, 0)
		toDoneHandoverTime := time.Unix(query.ToDoneHandoverTime, 0)
		filter = append(filter, bson.E{
			Key: "done_handover_time",
			Value: bson.M{
				"$gte": fromDoneHandoverTime,
				"$lte": toDoneHandoverTime,
			},
		})
	} else if query.FromDoneHandoverTime > 0 && query.ToDoneHandoverTime == 0 {
		fromDoneHandoverTime := time.Unix(query.FromDoneHandoverTime, 0)
		filter = append(filter, bson.E{
			Key: "done_handover_time",
			Value: bson.M{
				"$gte": fromDoneHandoverTime,
			},
		})
	} else if query.FromDoneHandoverTime == 0 && query.ToDoneHandoverTime > 0 {
		toDoneHandoverTime := time.Unix(query.ToDoneHandoverTime, 0)
		filter = append(filter, bson.E{
			Key: "done_handover_time",
			Value: bson.M{
				"$lte": toDoneHandoverTime,
			},
		})
	}

	if query.DepartmentCode != "" {
		filter = append(filter, bson.E{
			Key: "$or",
			Value: []bson.M{
				{
					"from_department_code": query.DepartmentCode,
				},
				{
					"to_department_code": query.DepartmentCode,
				},
			},
		})
	}

	if len(query.DepartmentCodes) > 0 {
		filter = append(filter, bson.E{
			Key: "$or",
			Value: []bson.M{
				{
					"from_department_code": bson.M{
						"$in": query.DepartmentCodes,
					},
				},
				{
					"to_department_code": bson.M{
						"$in": query.DepartmentCodes,
					},
				},
			},
		})
	}

	statuses := utils.EnumToStringSlice(*enum.HandoverStatus)
	if query.Status != nil && *query.Status != "" {
		statuses = []string{string(*query.Status)}
	}

	if len(query.Statuses) > 0 {
		statuses = []string{}
		for _, status := range query.Statuses {
			statuses = append(statuses, string(*status))
		}
	}

	result := make([]*CountTicketByStatusModel, len(statuses))
	waitGroup := new(sync.WaitGroup)
	waitGroup.Add(len(statuses))

	for index, status := range statuses {
		go func(i int, s string, f bson.D, r []*CountTicketByStatusModel, wg *sync.WaitGroup) {
			defer wg.Done()
			// Don't need to deep clone filter because pass variable to function by value means it is already clone the filter
			copyFilter := append(f, bson.E{
				Key:   "status",
				Value: s,
			})

			if query.ReadPreference != nil &&
				*query.ReadPreference == enum.ReadPreference.SECONDARY {
				countResult := model.HandoverTicketDB.SecondaryInstance.Count(copyFilter)
				r[i] = &CountTicketByStatusModel{
					Status:   s,
					Quantity: countResult.Total,
				}
			} else {
				countResult := model.HandoverTicketDB.Count(copyFilter)
				r[i] = &CountTicketByStatusModel{
					Status:   s,
					Quantity: countResult.Total,
				}
			}

		}(index, status, filter, result, waitGroup)
	}

	waitGroup.Wait()

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Thông tin số lượng phiếu theo trạng thái.",
		Data:    result,
	}
}

// CreateHandoverTicket create a handover ticket
func CreateHandoverTicket(ticket *request.CreateHandoverRequest, accountID int64) *common.APIResponse {
	if ticket.HandoverTicket.HandoverType == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Loại bàn giao không được để trống",
			ErrorCode: "HANDOVER_TYPE_REQUIRED",
		}
	}

	if *ticket.HandoverTicket.HandoverType == enum.HandoverType.WITHDRAWING {
		if ticket.ReceiverId <= 0 {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Số CMND/CCCD không được để trống",
				ErrorCode: "RECEIVER_ID_IS_REQUIRED",
			}
		}

		if ticket.ReceiverName == "" {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Tên người nhận không được để trống",
				ErrorCode: "RECEIVER_NAME_IS_REQUIRED",
			}
		}
	}

	if *ticket.HandoverTicket.HandoverType == enum.HandoverType.HANDOVERING && ticket.HandoverTicket.TPLCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã nhà vận chuyển không được bỏ trống.",
			ErrorCode: "TPL_CODE_REQUIRED",
		}
	}

	if *ticket.HandoverTicket.HandoverType == enum.HandoverType.TRANSPORTING {
		if ticket.HandoverTicket.ToDepartmentCode == "" {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Mã đơn vị luân chuyển không được bỏ trống.",
				ErrorCode: "TO_DEPARTMENT_CODE_REQUIRED",
			}
		}

		if ticket.DeliveryTruck == nil {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Thông tin xe luân chuyển không hợp lệ.",
				ErrorCode: "DELIVERY_TRUCK_REQUIRED",
			}
		}
	}

	ticket.HandoverTicket.TicketID = int(model.GenId("HANDOVER_TICKET"))
	ticket.HandoverTicket.Code = "HANDOVER-" + strconv.Itoa(ticket.HandoverTicket.TicketID)
	if *ticket.HandoverTicket.HandoverType == enum.HandoverType.TRANSPORTING {
		ticket.HandoverTicket.Code = "TRANSPORT-" + strconv.Itoa(ticket.HandoverTicket.TicketID)
	}

	if *ticket.HandoverTicket.HandoverType == enum.HandoverType.WITHDRAWING {
		ticket.HandoverTicket.Code = "WITHDRAW-" + strconv.Itoa(ticket.HandoverTicket.TicketID)
	}

	if *ticket.HandoverTicket.HandoverType == enum.HandoverType.RECEIVING {
		ticket.HandoverTicket.Code = "RECEIVE-" + strconv.Itoa(ticket.HandoverTicket.TicketID)
	}

	now := time.Now()

	if ticket.DeliveryTruck != nil {
		if ticket.DeliveryTruck.OwnerName == "" {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Tên chủ xe không được để trống",
				ErrorCode: "OWNER_NAME_REQUIRED",
			}
		}

		if ticket.DeliveryTruck.Phone == "" {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Số điện thoại không được để trống",
				ErrorCode: "PHONE_REQUIRED",
			}
		}

		if ticket.DeliveryTruck.IdCard == "" {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Chứng minh thư không được để trống",
				ErrorCode: "ID_CARD_REQUIRED",
			}
		}

		if ticket.DeliveryTruck.LicensePlate == "" {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Biển số xe không được để trống",
				ErrorCode: "LICENSE_PLATE_REQUIRED",
			}
		}

		hubsRaw := model.HubDB.Query(bson.M{
			"code": bson.M{
				"$in": []string{ticket.FromDepartmentCode, ticket.ToDepartmentCode},
			},
		}, 0, 2, nil)

		if hubsRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Điểm giao hoặc nhận hàng không hợp lệ",
			}
		}

		hubs := hubsRaw.Data.([]*model.Hub)
		if len(hubs) != 2 {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Điểm giao hoặc nhận hàng không hợp lệ",
			}
		}

		for _, hub := range hubs {
			if hub.Code == ticket.FromDepartmentCode {
				ticket.FromAddress = hub.Address
			}
			if hub.Code == ticket.ToDepartmentCode {
				ticket.ToAddress = hub.Address
			}
		}

		logs := model.HandoverLogs{
			ActionTime: &now,
			Status:     ticket.HandoverTicket.Status,
			ExtraData: map[string]interface{}{
				"licensePlate": ticket.DeliveryTruck.LicensePlate,
				"idCard":       ticket.DeliveryTruck.IdCard,
				"ownerName":    ticket.DeliveryTruck.OwnerName,
				"phone":        ticket.DeliveryTruck.Phone,
			},
		}

		ticket.HandoverTicket.Logs = append(ticket.HandoverTicket.Logs, &logs)
	}

	ticket.HandoverTicket.Status = &enum.HandoverStatus.DRAFT
	ticket.HandoverTicket.VersionNo = uuid.New().String()
	ticket.HandoverTicket.CreatedBy = accountID
	ticket.HandoverTicket.CreatedTime = &now
	ticket.HandoverTicket.ActionTime = &now
	ticket.HandoverTicket.LastUpdatedTime = &now

	ticket.HandoverTicket.CarrierId = ticket.CarrierId
	ticket.HandoverTicket.CarrierName = ticket.CarrierName

	createResult := model.HandoverTicketDB.Create(ticket.HandoverTicket)

	if createResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Tạo phiếu bàn giao lỗi!",
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Tạo phiếu bàn giao thành công.",
		Data:    createResult.Data.([]*model.HandoverTicket),
	}
}
func DoneWithDrawHandoverTicket(ticket *model.HandoverTicket, accountID int64) *common.APIResponse {
	if ticket.TicketID <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã nhập hàng không được để trống.",
			ErrorCode: "TICKET_ID_REQUIRED",
		}
	}

	getTicketResult := model.HandoverTicketDB.QueryOne(bson.M{
		"ticket_id": ticket.TicketID,
	})

	if getTicketResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy phiếu bàn giao.",
			ErrorCode: "TICKET_NOT_FOUND",
		}
	}

	existedTicket := getTicketResult.Data.([]*model.HandoverTicket)[0]

	if *existedTicket.Status == enum.HandoverStatus.COMPLETED || *existedTicket.Status == enum.HandoverStatus.CANCEL || *existedTicket.Status == enum.HandoverStatus.TRANSPORTING {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không thể cập nhật phiếu đã hoàn thành hoặc hủy.",
			ErrorCode: "TICKET_NOT_UPDATE",
		}
	}

	now := time.Now()
	versionNo := existedTicket.VersionNo
	existedTicket.LastUpdatedTime = &now
	existedTicket.UpdatedBy = accountID
	existedTicket.VersionNo = uuid.New().String()
	existedTicket.Status = ticket.Status

	existedTicket.CompletedTime = &now
	existedTicket.ActionTime = &now

	afterOption := options.After
	updateResult := model.HandoverTicketDB.UpdateOne(bson.M{
		"_id":        existedTicket.ID,
		"version_no": versionNo,
	},
		existedTicket,
		&options.FindOneAndUpdateOptions{
			Upsert:         &enum.False,
			ReturnDocument: &afterOption,
		})

	if updateResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Cập nhật phiếu bàn giao lỗi!",
		}
	}
	if *ticket.Status != enum.HandoverStatus.CANCEL {
		go func(handoverTicket *model.HandoverTicket) {
			updateHandoverAndDoneTransfer(handoverTicket)
		}(existedTicket)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật phiếu bàn giao thành công.",
		Data:    updateResult.Data.([]*model.HandoverTicket),
	}
}
func updateHandoverAndDoneTransfer(ticket *model.HandoverTicket) {
	for _, v := range ticket.SOList {
		if *v.Status == enum.HandoverItemStatus.CANCEL {
			continue
		}

		_ = handOverTransfer.Push(request.CompleteHandOverRequest{
			SO: v.SO,
		}, v.SO)

		resp := client.Services.WarehouseCoreClient.UpdateSaleOrder(model.SaleOrder{
			SaleOrderCode: v.SO,
			Status:        &enum.SaleOrderStatus.COMPLETED,
		})

		if resp.Status != common.APIStatus.Ok {
			_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
				CmdType: "ERROR_FAIL_TO_UPDATE_SALE_ORDER " + resp.ErrorCode,
				Title:   "Update sale order COMPLETED FAIL",
				Message: resp.Message,
			})
		}
	}
}

// DoneHandoverTicket update a handover ticket
func DoneHandoverTicket(ticket *model.HandoverTicket, accountID int64) *common.APIResponse {
	if ticket.TicketID <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã nhập hàng không được để trống.",
			ErrorCode: "TICKET_ID_REQUIRED",
		}
	}

	getTicketResult := model.HandoverTicketDB.QueryOne(bson.M{
		"ticket_id": ticket.TicketID,
	})
	if getTicketResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy phiếu bàn giao.",
			ErrorCode: "TICKET_NOT_FOUND",
		}
	}

	existedTicket := getTicketResult.Data.([]*model.HandoverTicket)[0]
	if ticket.CreatedFrom == "HANDOVER_TICKET" && existedTicket.TripId != 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Phiếu bàn giao này đã được gán vào tuyến " + strconv.FormatInt(existedTicket.TripId, 10),
		}
	}

	if *existedTicket.Status == enum.HandoverStatus.COMPLETED ||
		*existedTicket.Status == enum.HandoverStatus.CANCEL ||
		*existedTicket.Status == enum.HandoverStatus.TRANSPORTING {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không thể cập nhật phiếu đã hoàn thành hoặc hủy.",
			ErrorCode: "TICKET_NOT_UPDATE",
		}
	}

	mappingReferenceCodeShippingOrder := make(map[string]*model.ShippingOrder)
	mappingReferenceCodeItem := make(map[string]model.HandoverSOItem)
	totalBaskets := 0
	if len(existedTicket.SOList) > 0 {
		var referenceCodes []string
		for _, item := range existedTicket.SOList {
			referenceCodes = append(referenceCodes, item.SO)
			mappingReferenceCodeItem[item.SO] = item
			if item.Status != nil && *item.Status != enum.HandoverItemStatus.CANCEL {
				totalBaskets += len(item.Baskets)
			}
			if len(item.Products) > 0 {
				for _, product := range item.Products {
					// Only transport already scan product, if product is lost skip it
					if product.Status != nil && *product.Status == enum.ProductStatus.SCANNING {
						product.Status = &enum.ProductStatus.TRANSPORTING
					}
				}
			}
		}
		resultQueryShippingOrder := model.ShippingOrderDB.Query(bson.M{
			"reference_code": bson.M{
				"$in": referenceCodes,
			},
		}, 0, int64(len(referenceCodes)), nil)

		if resultQueryShippingOrder.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   resultQueryShippingOrder.Message,
				ErrorCode: "TICKET_NOT_UPDATE",
			}
		}

		shippingOrders := resultQueryShippingOrder.Data.([]*model.ShippingOrder)

		for _, shippingOrder := range shippingOrders {
			if !CheckItemInArray(shippingOrder.ReferenceCode, referenceCodes) {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   shippingOrder.ReferenceCode + " Không có thông tin phiếu giao hàng",
					ErrorCode: "TICKET_NOT_UPDATE",
				}
			}
			if _, ok := mappingReferenceCodeItem[shippingOrder.ReferenceCode]; ok && mappingReferenceCodeItem[shippingOrder.ReferenceCode].Status != nil && *mappingReferenceCodeItem[shippingOrder.ReferenceCode].Status != enum.HandoverItemStatus.CANCEL {
				if shippingOrder.Status != nil {
					if *shippingOrder.Status == enum.TPLCallbackStatus.CANCEL {
						return &common.APIResponse{
							Status:    common.APIStatus.Invalid,
							Message:   shippingOrder.ReferenceCode + " Đã hủy nên không thể hoàn thành phiên này.",
							ErrorCode: "TICKET_NOT_UPDATE",
						}
					} else if *shippingOrder.Status == enum.TPLCallbackStatus.INIT {
						return &common.APIResponse{
							Status:    common.APIStatus.Invalid,
							Message:   shippingOrder.ReferenceCode + " Chưa được book nhà vận chuyển nên không thể hoàn thành phiên này.",
							ErrorCode: "TICKET_NOT_UPDATE",
						}

					} else if *shippingOrder.Status == enum.TPLCallbackStatus.CREATE_FAIL {
						return &common.APIResponse{
							Status:    common.APIStatus.Invalid,
							Message:   shippingOrder.ReferenceCode + " Book nhà vận chuyển thất bại nên không thể hoàn thành phiên này.",
							ErrorCode: "TICKET_NOT_UPDATE",
						}
					}
				}
			}

			mappingReferenceCodeShippingOrder[shippingOrder.ReferenceCode] = shippingOrder
		}
	}

	if existedTicket.Status == nil || existedTicket.HandoverType == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Thông tin phiếu bàn giao lỗi",
			ErrorCode: "HANDOVER_STATUS_ERROR",
		}
	}

	now := time.Now()
	versionNo := existedTicket.VersionNo
	existedTicket.LastUpdatedTime = &now
	existedTicket.UpdatedBy = accountID
	existedTicket.VersionNo = uuid.New().String()
	existedTicket.Status = ticket.Status
	existedTicket.NumOfBaskets = int64(totalBaskets)
	byPass := false

	if ticket.Status != nil && *ticket.Status != enum.HandoverStatus.CANCEL {
		resultQueryHub := model.HubDB.QueryOne(bson.M{
			"code": existedTicket.ToDepartmentCode,
		})

		if resultQueryHub.Status == common.APIStatus.Ok {
			hub := resultQueryHub.Data.([]*model.Hub)[0]
			if existedTicket.ToDepartmentCode == existedTicket.FromDepartmentCode {
				if hub.Code == existedTicket.ToDepartmentCode && hub.WarehouseReferenceCode != "" {
					existedTicket.Code = "TRANSPORT-" + strconv.Itoa(ticket.TicketID)
					existedTicket.Status = &enum.HandoverStatus.COMPLETED
					existedTicket.HandoverType = &enum.HandoverType.TRANSPORTING
					for i, handoverItem := range existedTicket.SOList {
						if handoverItem.Status != nil && *handoverItem.Status == enum.HandoverItemStatus.CANCEL {
							continue
						}

						handoverItem.ReceivedQuantity = handoverItem.ScannedQuantity

						existedTicket.SOList[i] = handoverItem
					}
					byPass = true
				}
			}
		}
	}

	logs := model.HandoverLogs{
		ActionTime:         &now,
		FromDepartmentCode: existedTicket.FromDepartmentCode,
		FromDepartmentName: existedTicket.FromDepartmentName,
		ToDepartmentCode:   existedTicket.ToDepartmentCode,
		ToDepartmentName:   existedTicket.ToDepartmentName,
		HandoverNote:       existedTicket.HandoverNote,
		Status:             ticket.Status,
		ExtraData: map[string]interface{}{
			"actionByAccountId": accountID,
		},
	}
	existedTicket.Logs = append(existedTicket.Logs, &logs)
	if ticket.Status != nil {
		if *ticket.Status == enum.HandoverStatus.CANCEL || *ticket.Status == enum.HandoverStatus.COMPLETED || byPass {
			existedTicket.CompletedTime = &now
			if existedTicket.HandoverType != nil && *existedTicket.HandoverType == enum.HandoverType.HANDOVERING {
				existedTicket.DoneHandoverTime = &now
			}
		} else if *ticket.Status == enum.HandoverStatus.TRANSPORTING {
			existedTicket.DoneHandoverTime = &now
		} else {
			existedTicket.ActionTime = &now
		}
	}

	afterOption := options.After
	updateResult := model.HandoverTicketDB.UpdateOne(bson.M{
		"_id":        existedTicket.ID,
		"version_no": versionNo,
	},
		existedTicket,
		&options.FindOneAndUpdateOptions{
			Upsert:         &enum.False,
			ReturnDocument: &afterOption,
		})

	if updateResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Cập nhật phiếu bàn giao lỗi!",
		}
	}

	if *existedTicket.Status != enum.HandoverStatus.CANCEL {
		// TODO: This function need refactor
		go func(*model.HandoverTicket, map[string]*model.ShippingOrder, bool) {
			transferAndInitData(existedTicket, mappingReferenceCodeShippingOrder, byPass)
		}(existedTicket, mappingReferenceCodeShippingOrder, byPass)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật phiếu bàn giao thành công.",
		Data:    updateResult.Data.([]*model.HandoverTicket),
	}
}

func transferAndInitData(ticket *model.HandoverTicket, mappingReferenceCodeShippingOrder map[string]*model.ShippingOrder, byPass bool) {
	outWarehouse := false

	queryHubResult := model.HubDB.QueryOne(bson.M{
		"code": ticket.FromDepartmentCode,
	})

	if queryHubResult.Status == common.APIStatus.Ok {
		if queryHubResult.Data.([]*model.Hub)[0].WarehouseReferenceCode != "" {
			outWarehouse = true
		}
	}

	for _, item := range ticket.SOList {
		if item.Status != nil && *item.Status == enum.HandoverItemStatus.CANCEL {
			continue
		}
		current := time.Now()

		// không cập nhật hub order ở hub nguồn thành đã luân chuyển nếu bàn giao hub nguồn - hub nguồn
		if !byPass {
			updateRequest := &request.UpdateHubOrder{
				HubCode:       ticket.FromDepartmentCode,
				ReferenceCode: item.SO,
				Action:        "OUT",
				ActionName:    "Đã bàn giao",
				Status:        &enum.HubShippingOrderStatus.DONE_TRANSPORTING,
			}

			if len(item.Products) > 0 {
				for _, product := range item.Products {
					// Only done transfer TRANSPORTING product
					if product.Status != nil && *product.Status == enum.ProductStatus.TRANSPORTING {
						product.Status = &enum.ProductStatus.DONE_TRANSPORTING
					}
				}
				updateRequest.Products = item.Products
			}

			hub_shipping_order.PushUpdateHubShippingOrderQueue(updateRequest, item.SO)
		}

		createHubShippingOrderRequest := request.HandoverTicketCreateHubOrder{
			UpdatedBy:          ticket.UpdatedBy,
			FromDepartmentCode: ticket.FromDepartmentCode,
			FromDepartmentName: ticket.FromDepartmentName,
			ToDepartmentCode:   ticket.ToDepartmentCode,
			ToDepartmentName:   ticket.ToDepartmentName,
			TicketID:           ticket.TicketID,
			TPLCode:            ticket.TPLCode,
			Action:             "TRANSPORTING",
			ActionName:         "Đang Luân chuyển hàng",
			TPLName:            ticket.TPLName,
			HandoverType:       ticket.HandoverType,
			HandoverNote:       ticket.HandoverNote,
			Code:               ticket.Code,
			SO:                 item.SO,
			ReceivePackage:     item.ScannedQuantity,
			HubStatus:          &enum.HubShippingOrderStatus.WAIT_TO_STORING,
		}

		if len(item.Products) > 0 {
			for _, product := range item.Products {
				// Only create new product in hub order if done create handover ticket product
				if product.Status != nil && *product.Status == enum.ProductStatus.DONE_TRANSPORTING {
					product.Status = &enum.ProductStatus.TRANSPORTING
				}
			}
			createHubShippingOrderRequest.Products = item.Products
		}

		if byPass {
			createHubShippingOrderRequest.HubStatus = &enum.HubShippingOrderStatus.STORING
			createHubShippingOrderRequest.ActionName = "Đã nhập kho " + ticket.ToDepartmentName
			createHubShippingOrderRequest.Action = "IN"
		}

		var leadTime int64

		shippingOrder := mappingReferenceCodeShippingOrder[item.SO]
		if shippingOrder.Status != nil && (*shippingOrder.Status == enum.TPLCallbackStatus.RETURN || *shippingOrder.Status == enum.TPLCallbackStatus.RETURNING) {
			// tạo callback return
			CreateCallbackData(item.SO, ticket.FromDepartmentName, ticket.ToDepartmentName, ticket.FromDepartmentCode, &enum.TPLCallbackStatus.RETURNING, &enum.ShippingOrderType.RETURN, "")

			// Tạo hub Order cho hub đến với trạng thái return
			createHubShippingOrderRequest.HubOrderType = &enum.HubOrderType.RETURN
			_ = hub_shipping_order.PushCreateHubShippingOrderQueue(createHubShippingOrderRequest, item.SO)

			configLeadTime, _ := utils.ParseLeadTimeConfig(item.TplCode, &model.Address{
				Name:         shippingOrder.CustomerName,
				Address:      shippingOrder.CustomerShippingAddress,
				Phone:        shippingOrder.CustomerPhone,
				Email:        shippingOrder.CustomerEmail,
				WardName:     shippingOrder.CustomerWardName,
				DistrictName: shippingOrder.CustomerDistrictName,
				ProvinceName: shippingOrder.CustomerProvinceName,
				WardCode:     shippingOrder.CustomerWardCode,
				DistrictCode: shippingOrder.CustomerDistrictCode,
				ProvinceCode: shippingOrder.CustomerProvinceCode,
			}, &model.Address{
				Name:         shippingOrder.FromCustomerName,
				Address:      shippingOrder.FromCustomerAddress,
				Phone:        shippingOrder.FromCustomerPhone,
				Email:        shippingOrder.FromCustomerEmail,
				WardName:     shippingOrder.FromWardName,
				DistrictName: shippingOrder.FromDistrictName,
				ProvinceName: shippingOrder.FromProvinceName,
				WardCode:     shippingOrder.FromWardCode,
				DistrictCode: shippingOrder.FromDistrictCode,
				ProvinceCode: shippingOrder.FromProvinceCode,
			}, enum.LeadTimeConfigType.RETURNING)

			if configLeadTime != nil {
				leadTimePicking := current.Add(time.Second * time.Duration(configLeadTime.CommitmentTime))
				leadTime = leadTimePicking.Unix()
			}
			// lấy leadtime return
			_ = hub_shipping_order.PushUpdateShippingOrder(request.UpdateShippingOrderRequest{
				ReferenceCode:     item.SO,
				LeadTimeReturning: leadTime,
			}, item.SO)
		} else {
			// tạo callback picked cho nhà vận chuyển lấy hàng
			CreateCallbackData(item.SO, ticket.FromDepartmentName, ticket.ToDepartmentName, ticket.FromDepartmentCode, &enum.TPLCallbackStatus.PICKED, shippingOrder.ShippingType, "")
			if *ticket.HandoverType == enum.HandoverType.TRANSPORTING {
				// bắn callback transporting
				CreateCallbackData(item.SO, ticket.FromDepartmentName, ticket.ToDepartmentName, ticket.FromDepartmentCode, &enum.TPLCallbackStatus.TRANSPORTING, shippingOrder.ShippingType, "")
				createHubShippingOrderRequest.HubOrderType = &enum.HubOrderType.TRANSPORTING
				// Check last mile hub
				if shippingOrder.LastMileHubCode == ticket.ToDepartmentCode {
					createHubShippingOrderRequest.HubOrderType = &enum.HubOrderType.DELIVERY
				}
				// tạo hub order cho hub đến
				_ = hub_shipping_order.PushCreateHubShippingOrderQueue(createHubShippingOrderRequest, item.SO)
			} else if !byPass {
				// cập nhật current hub cho shipping order với type bàn giao nhà vận chuyển

				err := UpdateShippingOrderCurrentHub(item, item.TplCode, false)
				if err != nil {
					continue
				}
			}
			// lấy leadtime delivery
			configLeadTime, _ := utils.ParseLeadTimeConfig(item.TplCode, &model.Address{
				Name:         shippingOrder.FromCustomerName,
				Address:      shippingOrder.FromCustomerAddress,
				Phone:        shippingOrder.FromCustomerPhone,
				Email:        shippingOrder.FromCustomerEmail,
				WardName:     shippingOrder.FromWardName,
				DistrictName: shippingOrder.FromDistrictName,
				ProvinceName: shippingOrder.FromProvinceName,
				WardCode:     shippingOrder.FromWardCode,
				DistrictCode: shippingOrder.FromDistrictCode,
				ProvinceCode: shippingOrder.FromProvinceCode,
			}, &model.Address{
				Name:         shippingOrder.CustomerName,
				Address:      shippingOrder.CustomerShippingAddress,
				Phone:        shippingOrder.CustomerPhone,
				Email:        shippingOrder.CustomerEmail,
				WardName:     shippingOrder.CustomerWardName,
				DistrictName: shippingOrder.CustomerDistrictName,
				ProvinceName: shippingOrder.CustomerProvinceName,
				WardCode:     shippingOrder.CustomerWardCode,
				DistrictCode: shippingOrder.CustomerDistrictCode,
				ProvinceCode: shippingOrder.CustomerProvinceCode,
			}, enum.LeadTimeConfigType.DELIVERING)

			if configLeadTime != nil {
				current := time.Now()
				leadTimePicking := current.Add(time.Second * time.Duration(configLeadTime.CommitmentTime))
				leadTime = leadTimePicking.Unix()
			}
			hub_shipping_order.PushUpdateShippingOrder(request.UpdateShippingOrderRequest{
				HandoverTime:       current.Unix(),
				ReferenceCode:      item.SO,
				LeadTimeDelivering: leadTime,
			}, item.SO)
		}

		needUpdateShippingOrder := false
		var updater = bson.M{}
		if outWarehouse {
			// bắn số xuất kho
			handOverTransfer.Push(request.CompleteHandOverRequest{
				SO: item.SO,
			}, item.SO)

			if shippingOrder.PickedTime == 0 {
				needUpdateShippingOrder = true
				// Cập nhật thời gian xuất kho cho đơn
				updater["picked_time"] = current.Unix()
			}
		}
		// Đơn luân chuyển giao fromHub == toHub
		if byPass {
			needUpdateShippingOrder = true
			// Cập nhật lưu kho kho cho đơn
			updater["stored_at_lm_hub_time"] = current.Unix()
		}

		if shippingOrder.ShippingType != nil &&
			(*shippingOrder.ShippingType == enum.ShippingOrderType.PICKUP ||
				*shippingOrder.ShippingType == enum.ShippingOrderType.RETURN ||
				*shippingOrder.ShippingType == enum.ShippingOrderType.INTERNAL_TRANS ||
				*shippingOrder.ShippingType == enum.ShippingOrderType.FMPO ||
				*shippingOrder.ShippingType == enum.ShippingOrderType.EO) {
			needUpdateShippingOrder = true
			updater["status"] = enum.TPLCallbackStatus.TRANSPORTING
			updater["scope"] = append(shippingOrder.Scope, ticket.ToDepartmentCode)
			updater["action_time"] = current.Unix()
		}
		if shippingOrder.LeavedFirstHubTime == 0 {
			updater["leaved_first_hub_time"] = current.Unix()
		}
		if needUpdateShippingOrder {
			_ = model.ShippingOrderDB.UpdateOne(&model.ShippingOrder{ReferenceCode: shippingOrder.ReferenceCode}, &updater)
		}
	}
}

// UpdateHandoverTicket func
func UpdateHandoverTicket(ticket *request.UpdateHandoverRequest, accountId int64) *common.APIResponse {
	if ticket.HandoverTicket.TicketID <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã nhập hàng không được để trống.",
			ErrorCode: "TICKET_ID_REQUIRED",
		}
	}

	resultQueryHandover := model.HandoverTicketDB.QueryOne(bson.M{
		"ticket_id": ticket.TicketID,
	})

	if resultQueryHandover.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   "Không tìm thấy thông tin phiếu bàn giao",
			ErrorCode: "HANDOVER_TICKET_NOTFOUND",
		}
	}

	handoverTicket := resultQueryHandover.Data.([]*model.HandoverTicket)[0]
	if handoverTicket.Status != nil &&
		*handoverTicket.Status == enum.HandoverStatus.COMPLETED &&
		ticket.Status != nil &&
		*ticket.Status != enum.HandoverStatus.COMPLETED {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   "Không thể cập nhật trạng thái cho đơn đã hoàn thành",
			ErrorCode: "INVALID_HANDOVER_TICKET_STATUS",
		}
	}

	if ticket.SealCode != "" {
		current := time.Now()
		logs := model.HandoverLogs{
			ActionTime: &current,
			ExtraData: map[string]interface{}{
				"sealCode":          ticket.SealCode,
				"actionByAccountId": accountId,
			},
		}

		handoverTicket.Logs = append(handoverTicket.Logs, &logs)
	}

	now := time.Now()
	var waitToCheckTickets []int
	if ticket.Status != nil && *ticket.Status == enum.HandoverStatus.WAIT_TO_CHECK {
		handoverTicket.DeliveredTime = &now
		if handoverTicket.ItemType != nil &&
			*handoverTicket.ItemType != enum.HandoverItemType.PO &&
			*handoverTicket.ItemType != enum.HandoverItemType.FMPO {
			waitToCheckTickets = append(waitToCheckTickets, handoverTicket.TicketID)
		}
	}

	afterOption := options.After
	handoverTicket.UpdatedBy = accountId
	handoverTicket.Status = ticket.Status
	handoverTicket.ExtraData = utils.MergeMaps(handoverTicket.ExtraData, ticket.ExtraData)
	updateHandoverTicket := model.HandoverTicketDB.UpdateOne(
		bson.M{
			"ticket_id": ticket.TicketID,
		},
		handoverTicket,
		&options.FindOneAndUpdateOptions{
			Upsert:         &enum.False,
			ReturnDocument: &afterOption,
		})

	if updateHandoverTicket.Status != common.APIStatus.Ok {
		return updateHandoverTicket
	}

	if handoverTicket.TripId != 0 &&
		ticket.Status != nil &&
		*ticket.Status == enum.HandoverStatus.WAIT_TO_CHECK {
		tripResp := model.TripDB.QueryOne(bson.M{
			"trip_id": handoverTicket.TripId,
		})
		if tripResp.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:    common.APIStatus.NotFound,
				Message:   "Không tìm thấy thông tin chuyến đi, nên không thể tự hoàn thành những phiếu luân chuyển còn lại",
				ErrorCode: "TRIP_NOT_FOUND",
			}
		}
		trip := tripResp.Data.([]*model.Trip)[0]
		var checkAutoDeliveredTickets []int
		for _, t := range trip.HandoverTickets {
			if t.TicketID != handoverTicket.TicketID &&
				t.ToDepartmentCode == handoverTicket.ToDepartmentCode {
				// Ignore delivered FMPO and PO
				if t.ItemType != nil &&
					*t.ItemType != enum.HandoverItemType.PO &&
					*t.ItemType != enum.HandoverItemType.FMPO {
					waitToCheckTickets = append(waitToCheckTickets, t.TicketID)
				}
				checkAutoDeliveredTickets = append(checkAutoDeliveredTickets, t.TicketID)
			}
		}
		model.HandoverTicketDB.UpdateMany(bson.M{
			"ticket_id": bson.M{
				"$in": checkAutoDeliveredTickets,
			},
			"status": enum.HandoverStatus.TRANSPORTING,
		}, bson.M{
			"status":         enum.HandoverStatus.WAIT_TO_CHECK,
			"delivered_time": &now,
			"extra_data":     handoverTicket.ExtraData,
		})
	}

	if len(waitToCheckTickets) > 0 {
		go sdk.Execute(func() {
			needCallbackTicketsResp := model.HandoverTicketDB.Query(bson.M{
				"ticket_id": bson.M{
					"$in": waitToCheckTickets,
				},
			}, 0, int64(len(waitToCheckTickets)), nil)
			if needCallbackTicketsResp.Status != common.APIStatus.Ok {
				return
			}
			needCallbackTickets := needCallbackTicketsResp.Data.([]*model.HandoverTicket)
			for _, t := range needCallbackTickets {
				for _, so := range t.SOList {
					if so.Status != nil && *so.Status == enum.HandoverItemStatus.CANCEL {
						continue
					}
					CreateCallbackData(
						so.SO,
						t.FromDepartmentName,
						t.ToDepartmentName,
						t.FromDepartmentCode,
						&enum.TPLCallbackStatus.TRANSPORTING,
						&enum.ShippingOrderType.DELIVERY,
						"Đã bàn giao cho hub:  "+t.ToDepartmentName,
					)
				}
			}
		})
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật thành công",
	}
}

// AddSO add so to handover ticket
func AddSO(input *request.HandoverItemRequest, accountID int64) *common.APIResponse {
	if input.TicketID <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã bàn giao không được để trống.",
			ErrorCode: "TICKET_ID_REQUIRED",
		}
	}

	if input.ScannedQuantity <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Số lượng quét package phải lớn hơn 0",
			ErrorCode: "SCANNED_QUANTITY_INVALID",
		}
	}

	getTicketResult := model.HandoverTicketDB.QueryOne(bson.M{
		"ticket_id": input.TicketID,
	})

	if getTicketResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy phiếu bàn giao.",
			ErrorCode: "TICKET_NOT_FOUND",
		}
	}

	existedTicket := getTicketResult.Data.([]*model.HandoverTicket)[0]

	if *existedTicket.Status == enum.HandoverStatus.COMPLETED || *existedTicket.Status == enum.HandoverStatus.CANCEL || *existedTicket.Status == enum.HandoverStatus.TRANSPORTING {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không thể cập nhật phiếu đã hoàn thành/đang luân chuyển hoặc hủy.",
			ErrorCode: "TICKET_NOT_UPDATE",
		}
	}

	if existedTicket.ItemType != nil && *existedTicket.ItemType == enum.HandoverItemType.PO {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Loại phiếu luân chuyển không hợp lệ",
			ErrorCode: "INVALID_ITEM_TYPE",
		}
	}

	shippingOrderQuery := model.ShippingOrderDB.QueryOne(bson.M{
		"reference_code": input.SO,
	})

	if shippingOrderQuery.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy thông tin của đơn hàng: " + input.SO,
			ErrorCode: "TICKET_NOT_UPDATE",
		}
	}

	shippingOrder := shippingOrderQuery.Data.([]*model.ShippingOrder)[0]

	// check shipping order handover for 3pl
	if existedTicket.HandoverType != nil && *existedTicket.HandoverType == enum.HandoverType.RECEIVING {
		if shippingOrder.Status != nil && (*shippingOrder.Status == enum.TPLCallbackStatus.RETURNED || *shippingOrder.Status == enum.TPLCallbackStatus.CANCEL) {
			message := "Trạng thái của phiếu giao hàng không hợp lệ"
			if *shippingOrder.Status == enum.TPLCallbackStatus.RETURNED {
				message = "Đơn hàng đã được trả hàng về kho."
			} else {
				message = "Phiếu giao hàng này đã bị hủy"
			}
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   message,
				ErrorCode: "TICKET_NOT_UPDATE",
			}
		}
		hubQuery := model.HubDB.QueryOne(bson.M{
			"code": shippingOrder.CurrentHub,
		})

		if hubQuery.Status == common.APIStatus.Ok {
			hub := hubQuery.Data.([]*model.Hub)[0]

			if hub.Code != existedTicket.ToDepartmentCode {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   "Đơn hàng hiện tại đang nằm ở HUB khác. Bạn không được quyền thao tác",
					ErrorCode: "TICKET_NOT_UPDATE",
				}
			}

			hubOrderQuery := model.HUBShippingOrderDB.QueryOne(&model.HubShippingOrder{
				ReferenceCode: input.SO,
				HUBCode:       hub.Code,
			})

			if hubOrderQuery.Status == common.APIStatus.Ok {
				hubOrder := hubOrderQuery.Data.([]*model.HubShippingOrder)[0]
				if hubOrder.Status != nil && *hubOrder.Status != enum.HubShippingOrderStatus.DELIVERED && *hubOrder.Status != enum.HubShippingOrderStatus.COMPLETED {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   "Trạng thái của phiếu giao hàng không hợp lệ.",
						ErrorCode: "TICKET_NOT_UPDATE",
					}
				}
			} else {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   "Không được phép với các đơn hàng đã thuộc hub khác.",
					ErrorCode: "TICKET_NOT_UPDATE",
				}
			}
		}
	}

	handoverTicketQuery := model.HandoverTicketDB.Query(bson.M{
		"so_list": bson.M{
			"$elemMatch": bson.M{
				"so": input.SO,
				"status": bson.M{
					"$ne": "CANCEL",
				},
			},
		},
	}, 0, 100, nil)

	if handoverTicketQuery.Status == common.APIStatus.Ok {
		existedHandover := false
		handoverTickets := handoverTicketQuery.Data.([]*model.HandoverTicket)
		dupTicketId := 0
		dupTripId := 0
		for _, ticket := range handoverTickets {
			if ticket.Status != nil &&
				*ticket.Status != enum.HandoverStatus.CANCEL &&
				*ticket.Status != enum.HandoverStatus.COMPLETED &&
				ticket.TicketID != existedTicket.TicketID {
				existedHandover = true
				dupTicketId = ticket.TicketID
				dupTripId = int(ticket.TripId)
				break
			}
		}

		if existedHandover {
			message := "Đơn hàng đã nằm trong phiếu bàn giao khác: " + strconv.Itoa(dupTicketId)
			if dupTripId > 0 {
				message += " - Trip: " + strconv.Itoa(dupTripId)
			}
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   message,
				ErrorCode: "EXISTED_HANDOVER_TICKET",
			}
		}
	}

	var statusCheck enum.HandoverItemStatusValue
	if input.ScannedQuantity == input.NumPackage {
		statusCheck = enum.HandoverItemStatus.DONE
	} else {
		statusCheck = enum.HandoverItemStatus.PARTIAL
	}
	soItem := model.HandoverSOItem{
		SO:              input.SO,
		Weight:          float64(input.Weight),
		NumPackage:      input.NumPackage,
		ScannedQuantity: input.ScannedQuantity,
		TrackingCode:    input.TrackingCode,
		Status:          &statusCheck,
		TplCode:         input.TplCode,
		Baskets:         shippingOrder.Baskets,
		Tags:            shippingOrder.Tags,
	}

	isSOExisted := false
	isOverPackage := false
	isFronzen := false

	if utils.StringSliceContain(soItem.Tags, "FROZEN") {
		isFronzen = true
	}

	// If exist, increase numPackage
	for i, existedSO := range existedTicket.SOList {
		if existedSO.SO == soItem.SO {
			isSOExisted = true
			existedSO.ScannedQuantity = soItem.ScannedQuantity + existedSO.ScannedQuantity
			existedSO.Status = soItem.Status
			if existedSO.ScannedQuantity == existedSO.NumPackage {
				existedSO.Status = &enum.HandoverItemStatus.DONE
			}

			if existedSO.ScannedQuantity > existedSO.NumPackage {
				isOverPackage = true
				break
			}

			existedTicket.SOList[i] = existedSO
		}
	}

	if isOverPackage {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Quá số kiện",
			ErrorCode: "OVER_PACKAGE",
		}
	}
	if !isSOExisted {
		existedTicket.SOList = append(existedTicket.SOList, soItem)
	}

	now := time.Now()
	versionNo := existedTicket.VersionNo
	existedTicket.LastUpdatedTime = &now
	existedTicket.UpdatedBy = accountID
	existedTicket.VersionNo = uuid.New().String()
	if isFronzen {
		existedTicket.IsContainsFrozenItem = &enum.True
	}

	afterOption := options.After
	updateResult := model.HandoverTicketDB.UpdateOne(bson.M{
		"ticket_id":  existedTicket.TicketID,
		"version_no": versionNo,
	},
		existedTicket,
		&options.FindOneAndUpdateOptions{
			Upsert:         &enum.False,
			ReturnDocument: &afterOption,
		})

	if updateResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Cập nhật phiếu bàn giao lỗi!",
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật phiếu bàn giao thành công.",
		Data:    updateResult.Data.([]*model.HandoverTicket),
	}
}

// DoneListHandoverTicket func
func DoneListHandoverTicket(input request.DoneTransportRequest, accountID int64, employeeName string) *common.APIResponse {
	if len(input.ListTicketId) <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã bàn giao không được để trống.",
			ErrorCode: "TICKET_ID_REQUIRED",
		}
	}

	getTicketResult := model.HandoverTicketDB.Query(bson.M{
		"to_department_code": input.HubCode,
		"ticket_id": bson.M{
			"$in": input.ListTicketId,
		},
	},
		0, int64(len(input.ListTicketId)), nil)

	if getTicketResult.Status != common.APIStatus.Ok || getTicketResult.Data == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy phiếu bàn giao.",
			ErrorCode: "TICKET_NOT_FOUND",
		}
	}

	existedTicket := getTicketResult.Data.([]*model.HandoverTicket)

	var listTicketExisted []int64
	for _, ticket := range existedTicket {
		listTicketExisted = append(listTicketExisted, int64(ticket.TicketID))
		if ticket.HandoverType != nil && *ticket.HandoverType != enum.HandoverType.TRANSPORTING {
			return &common.APIResponse{
				Status:    common.APIStatus.Error,
				Message:   "Loại của phiếu bàn giao: " + strconv.Itoa(ticket.TicketID) + " ,không hợp lệ",
				ErrorCode: "TYPE_TICKET_ERROR",
			}
		}

		if ticket.Status != nil && (*ticket.Status == enum.HandoverStatus.COMPLETED || *ticket.Status == enum.HandoverStatus.CANCEL) {
			message := "đã hủy"
			if *ticket.Status == enum.HandoverStatus.COMPLETED {
				message = "đã hoàn thành"
			}
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Không thể cập nhật phiếu: " + strconv.Itoa(ticket.TicketID) + " vì phiếu này " + message,
				ErrorCode: "TICKET_NOT_UPDATE",
			}
		}

		for _, handoverItem := range ticket.SOList {
			if handoverItem.Status != nil && *handoverItem.Status == enum.HandoverItemStatus.CANCEL {
				continue
			}
			if handoverItem.ReceivedQuantity < handoverItem.ScannedQuantity && !input.DoneAll {
				// OPS-4044: ignore case bin a,b,c
				if len(handoverItem.Products) == 1 {
					bin := handoverItem.Products[0].SKU
					if input.IgnoreBins != nil && input.IgnoreBins[bin] {
						continue
					}
				}
				if input.Note[strconv.Itoa(ticket.TicketID)][handoverItem.SO] == "" {
					return &common.APIResponse{
						Status:  common.APIStatus.Error,
						Message: "Vui lòng điền ghi chú cho " + handoverItem.SO,
					}
				}
			}
		}
	}

	if len(input.ListTicketId) != len(existedTicket) {
		var listTicketError []int64
		for _, ticket := range input.ListTicketId {
			if !CheckItemInArray(ticket, listTicketExisted) {
				listTicketError = append(listTicketError, ticket)
			}
		}

		message := strings.Trim(strings.Join(strings.Fields(fmt.Sprint(listTicketError)), ","), "[]")

		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy phiếu bàn giao: " + message,
			ErrorCode: "TICKET_NOT_FOUND",
		}
	}

	getHubResult := model.HubDB.QueryOne(bson.M{
		"code": input.HubCode,
	})

	if getHubResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy thông tin hub: " + input.HubCode,
			ErrorCode: "HUB_NOT_FOUND",
		}
	}

	hub := getHubResult.Data.([]*model.Hub)[0]
	var needCheckInReturnItems []*request.TransportItem
	var needCheckInReturnRo []*request.ROItem
	var needCheckInTOs []*request.DoneTransportForTO
	var needCheckInReceive []*request.DoneTransportForReceiveSession
	now := time.Now()
	for _, ticket := range existedTicket {
		current := time.Now()
		var listSoItem []string
		mapHubShippingOrder := make(map[string]*model.HubShippingOrder)
		if len(ticket.SOList) <= 0 {
			continue
		}
		for _, item := range ticket.SOList {
			listSoItem = append(listSoItem, item.SO)
		}

		resultQueryHubOrder := model.HUBShippingOrderDB.Query(bson.M{
			"reference_code": bson.M{
				"$in": listSoItem,
			},
			"hub_code": ticket.ToDepartmentCode,
		}, 0, int64(len(listSoItem)), nil)

		if resultQueryHubOrder.Status != common.APIStatus.Ok || resultQueryHubOrder.Data == nil {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   resultQueryHubOrder.Message,
				ErrorCode: resultQueryHubOrder.ErrorCode,
			}
		}

		hubOrders := resultQueryHubOrder.Data.([]*model.HubShippingOrder)
		for _, hubOrder := range hubOrders {
			mapHubShippingOrder[hubOrder.ReferenceCode] = hubOrder
		}
		doneToTicket := &request.DoneTransportForTO{
			TransportCode:   ticket.Code,
			EmployeeId:      accountID,
			Employee:        employeeName,
			ReceivedTime:    &now,
			ToWarehouseCode: hub.WarehouseReferenceCode,
		}
		for i, handoverItem := range ticket.SOList {
			if handoverItem.Status != nil && *handoverItem.Status == enum.HandoverItemStatus.CANCEL {
				continue
			}

			if mapHubShippingOrder[handoverItem.SO] == nil {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   "Không tìm thấy thông tin phiếu giao " + handoverItem.SO,
					ErrorCode: "SO-ITEM_NOT_FOUND",
				}
			}

			if input.DoneAll {
				handoverItem.ReceivedQuantity = handoverItem.ScannedQuantity
			}

			handoverItem.Note = input.Note[strconv.Itoa(ticket.TicketID)][handoverItem.SO]
			ticket.SOList[i] = handoverItem

			if mapHubShippingOrder[handoverItem.SO].Type == nil {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   "Trạng thái của phiếu giao không hợp lẹ: " + handoverItem.SO,
					ErrorCode: "STATUS_SO-ITEM_ERROR",
				}
			}

			var storingProducts []*model.Product
			updateHubOrderRequest := &request.UpdateHubOrder{
				HubCode:         ticket.ToDepartmentCode,
				ReferenceCode:   handoverItem.SO,
				ReceiveQuantity: handoverItem.ReceivedQuantity,
				Action:          "IN",
				ActionName:      "Đã nhập kho " + ticket.ToDepartmentName,
				Status:          &enum.HubShippingOrderStatus.STORING,
				CheckInAt:       input.CheckInAt,
			}

			if handoverItem.ReceivedQuantity < handoverItem.ScannedQuantity {
				updateHubOrderRequest.Status = &enum.HubShippingOrderStatus.LOST
			}

			var removedBin []*model.Product
			if len(handoverItem.Products) > 0 {
				// if done all is true, all product except LOST will change to storing
				if input.DoneAll {
					for _, product := range handoverItem.Products {
						if product.Status != nil && *product.Status != enum.ProductStatus.LOST && *product.Status != enum.ProductStatus.REMOVED {
							product.Status = &enum.ProductStatus.STORING
						}
					}
				} else {
					// if not done all, only store SCANNING item to hub
					for _, product := range handoverItem.Products {
						if input.IgnoreBins != nil && input.IgnoreBins[product.SKU] {
							continue
						}
						if product.Status != nil && *product.Status == enum.ProductStatus.SCANNING {
							product.Status = &enum.ProductStatus.STORING
							storingProducts = append(storingProducts, product)
							continue
						}
						if product.Status != nil &&
							*product.Status != enum.ProductStatus.LOST &&
							*product.Status != enum.ProductStatus.STORING &&
							*product.Status != enum.ProductStatus.REMOVED {
							if *product.Status == enum.ProductStatus.TRANSPORTING {
								product.Status = &enum.ProductStatus.LOST
							} else {
								product.Status = &enum.ProductStatus.REMOVED
							}
							// append list order can't pickup
							removedBin = append(removedBin, product)
						}
					}
				}
				updateHubOrderRequest.Products = handoverItem.Products
			}

			if *mapHubShippingOrder[handoverItem.SO].Type == enum.HubOrderType.RETURN {
				updateHubOrderRequest.Status = &enum.HubShippingOrderStatus.RETURN
				if hub.WarehouseReferenceCode != "" {
					updateHubOrderRequest.Status = &enum.HubShippingOrderStatus.RETURNING
				}
			}

			// Nếu có product được nhập kho => đơn hub có trạng thái STORING
			if len(storingProducts) > 0 {
				updateHubOrderRequest.Status = &enum.HubShippingOrderStatus.STORING
			}

			if len(removedBin) > 0 {
				_ = shippingOrderQueue.PushCreateHubShippingOrder(shippingOrderQueue.RequestBodyHandover{
					HubOrder:       *mapHubShippingOrder[handoverItem.SO],
					RemovedBinList: removedBin,
				}, handoverItem.SO)
			}

			err := UpdateShippingOrderCurrentHub(handoverItem, ticket.ToDepartmentCode, true)
			if err != nil {
				continue
			}

			_ = hub_shipping_order.PushUpdateHubShippingOrderQueue(updateHubOrderRequest, handoverItem.SO)
			//	Mặc định quét transport vào inbound wms sẽ ngầm hiểu là trả hàng
			if input.CheckInAt == enum.CheckInAt.WAREHOUSE &&
				ticket.ItemType != nil &&
				*ticket.ItemType != enum.HandoverItemType.TO {
				if handoverItem.ReceivedQuantity == 0 {
					continue
				}
				item := &request.TransportItem{
					PackageNum: handoverItem.ReceivedQuantity,
				}
				if mapHubShippingOrder[handoverItem.SO].ParentReferenceCode != "" {
					item.SO = mapHubShippingOrder[handoverItem.SO].ParentReferenceCode
				} else {
					item.SO = mapHubShippingOrder[handoverItem.SO].ReferenceCode
				}

				if (strings.HasPrefix(mapHubShippingOrder[handoverItem.SO].ReferenceCode, "SO") ||
					strings.HasPrefix(mapHubShippingOrder[handoverItem.SO].ReferenceCode, "LO") ||
					strings.HasPrefix(mapHubShippingOrder[handoverItem.SO].ReferenceCode, "CO") ||
					strings.HasPrefix(mapHubShippingOrder[handoverItem.SO].ReferenceCode, "RO")) &&
					(strings.Contains(mapHubShippingOrder[handoverItem.SO].ReferenceCode, "-P") ||
						strings.Contains(mapHubShippingOrder[handoverItem.SO].ReferenceCode, "-F")) {
					item.DeliveryOrderCode = mapHubShippingOrder[handoverItem.SO].ReferenceCode
				}

				if strings.HasPrefix(mapHubShippingOrder[handoverItem.SO].ReferenceCode, "SO") ||
					strings.HasPrefix(mapHubShippingOrder[handoverItem.SO].ReferenceCode, "LO") ||
					strings.HasPrefix(mapHubShippingOrder[handoverItem.SO].ReferenceCode, "CO") {
					needCheckInReturnItems = append(needCheckInReturnItems, item)
				}

				if strings.HasPrefix(mapHubShippingOrder[handoverItem.SO].ReferenceCode, "RO") {
					orderCode := mapHubShippingOrder[handoverItem.SO].ReferenceCode
					if strings.Contains(orderCode, "-") {
						orderCode = strings.Split(orderCode, "-")[0]
					}
					needCheckInReturnRo = append(needCheckInReturnRo, &request.ROItem{
						OrderCode:     orderCode,
						WarehouseCode: hub.WarehouseReferenceCode,
					})
				}
				if strings.HasPrefix(mapHubShippingOrder[handoverItem.SO].ReferenceCode, "BMX") &&
					mapHubShippingOrder[handoverItem.SO].ParentReceiveSessionCode != "" {
					needCheckInReceive = append(needCheckInReceive, &request.DoneTransportForReceiveSession{
						PoCode:                  mapHubShippingOrder[handoverItem.SO].ParentReceiveSessionCode,
						WarehouseCode:           hub.WarehouseReferenceCode,
						ReceivedPackageQuantity: handoverItem.ReceivedQuantity,
					})
				}
			}

			// Nếu loại hàng là TO
			if input.CheckInAt == enum.CheckInAt.WAREHOUSE &&
				ticket.ItemType != nil &&
				*ticket.ItemType == enum.HandoverItemType.TO {
				if handoverItem.ReceivedQuantity == 0 {
					continue
				}

				item := &request.TransportItem{
					PackageQuantity: handoverItem.ReceivedQuantity,
					POCOde:          handoverItem.SO,
				}

				doneToTicket.TransportItems = append(doneToTicket.TransportItems, item)
			}

		}

		ticket.Status = &enum.HandoverStatus.COMPLETED
		afterOption := options.After
		ticket.CompletedTime = &current
		ticket.UpdatedBy = accountID
		updateResult := model.HandoverTicketDB.UpdateOne(bson.M{
			"ticket_id": ticket.TicketID,
		},
			ticket,
			&options.FindOneAndUpdateOptions{
				Upsert:         &enum.False,
				ReturnDocument: &afterOption,
			})

		if updateResult.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Cập nhật phiếu bàn giao lỗi!",
			}
		}

		if len(doneToTicket.TransportItems) > 0 {
			needCheckInTOs = append(needCheckInTOs, doneToTicket)
		}
	}

	if len(needCheckInReturnItems) > 0 {
		err := client.Services.WarehouseCoreClient.SendDoneTransport(&request.DoneTransportForReturn{
			TransportItems: needCheckInReturnItems,
			CheckInById:    accountID,
			WarehouseCode:  hub.WarehouseReferenceCode,
		})

		if err != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: err.Error(),
			}
		}
	}

	if len(needCheckInReturnRo) > 0 {
		err := client.Services.WarehouseCoreClient.SendDoneTransportRO(&request.DoneTransportForRO{
			Items: needCheckInReturnRo,
			Employee: &model.Account{
				AccountID: accountID,
				Fullname:  employeeName,
			},
		})

		if err != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: err.Error(),
			}
		}
	}

	if len(needCheckInTOs) > 0 {
		var errs []error
		for _, to := range needCheckInTOs {
			err := client.Services.WarehouseCoreClient.SendDoneTransportTO(to)
			if err != nil {
				errs = append(errs, err)
			}
		}

		if len(errs) > 0 {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Đã có lỗi xảy ra khi cập nhật phiên luân chuyển nội bộ",
				Data:    errs,
			}
		}
	}

	if len(needCheckInReceive) > 0 {
		for _, receive := range needCheckInReceive {
			err := client.Services.WarehouseCoreClient.SendDoneReceive(receive)
			if err != nil {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: err.Error(),
				}
			}
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật phiếu bàn giao thành công.",
	}
}

func UpdateShippingOrderCurrentHub(item model.HandoverSOItem, currentHub string, isStored bool) *common.Error {
	resultQueryDataShippingOrder := model.ShippingOrderDB.QueryOne(bson.M{
		"reference_code": item.SO,
	})
	if resultQueryDataShippingOrder.Status != common.APIStatus.Ok {
		_ = backup.Push(item, "query_shipping_order_fail")
		return &common.Error{
			Message: "Cant query shipping order of: " + item.SO,
		}
	}
	shippingOrder := resultQueryDataShippingOrder.Data.([]*model.ShippingOrder)[0]

	if shippingOrder.Status != nil && (*shippingOrder.Status != enum.TPLCallbackStatus.DELIVERED ||
		*shippingOrder.Status != enum.TPLCallbackStatus.COMPLETED) && currentHub != "" {
		now := time.Now().Unix()
		updater := &model.ShippingOrder{
			CurrentHub: currentHub,
			ActionTime: now,
		}

		if isStored {
			updater.HandoverTime = now
			updater.StoredAtLMHubTime = now
			if shippingOrder.PickedTime == 0 {
				updater.PickedTime = now
			}

			if shippingOrder.ShippingType != nil &&
				*shippingOrder.ShippingType == enum.ShippingOrderType.PICKUP ||
				*shippingOrder.ShippingType == enum.ShippingOrderType.RETURN ||
				*shippingOrder.ShippingType == enum.ShippingOrderType.INTERNAL_TRANS ||
				*shippingOrder.ShippingType == enum.ShippingOrderType.FMPO ||
				*shippingOrder.ShippingType == enum.ShippingOrderType.EO {
				updater.Status = &enum.TPLCallbackStatus.STORING
			}
		}

		updateResult := model.ShippingOrderDB.UpdateOne(
			&model.ShippingOrder{
				ReferenceCode: item.SO,
			},
			updater, nil)

		if updateResult.Status != common.APIStatus.Ok {
			itemByte, _ := json.Marshal(item)
			_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
				CmdType: "UPDATE_HUB_SHIPPING_ORDER_FAILED",
				Title:   "UPDATE_HUB_SHIPPING_ORDER_FAILED",
				Message: string(itemByte) + "\n" + updateResult.Message,
			})

			return &common.Error{
				Message: string(itemByte) + "\n" + updateResult.Message,
			}
		}
	}
	return nil
}

// ResetHandoverTicket func
func ResetHandoverTicket(input request.DoneTransportRequest, accountId int64) *common.APIResponse {
	if len(input.ListTicketId) <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã bàn giao không được để trống.",
			ErrorCode: "TICKET_ID_REQUIRED",
		}
	}

	getTicketResult := model.HandoverTicketDB.Query(bson.M{
		"to_department_code": input.HubCode,
		"ticket_id": bson.M{
			"$in": input.ListTicketId,
		},
	},
		0, int64(len(input.ListTicketId)), nil)

	if getTicketResult.Status != common.APIStatus.Ok || getTicketResult.Data == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy phiếu bàn giao.",
			ErrorCode: "TICKET_NOT_FOUND",
		}
	}

	existedTicket := getTicketResult.Data.([]*model.HandoverTicket)

	var listTicketExisted []int64
	for _, ticket := range existedTicket {
		listTicketExisted = append(listTicketExisted, int64(ticket.TicketID))
		if ticket.HandoverType != nil && *ticket.HandoverType != enum.HandoverType.TRANSPORTING {
			return &common.APIResponse{
				Status:    common.APIStatus.Error,
				Message:   "Loại của phiếu bàn giao: " + strconv.Itoa(ticket.TicketID) + " ,không hợp lệ",
				ErrorCode: "TYPE_TICKET_ERROR",
			}
		}

		if ticket.Status != nil && (*ticket.Status == enum.HandoverStatus.COMPLETED || *ticket.Status == enum.HandoverStatus.CANCEL) {
			message := "đã hủy"
			if *ticket.Status == enum.HandoverStatus.COMPLETED {
				message = "đã hoàn thành"
			}
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Không thể cập nhật phiếu: " + strconv.Itoa(ticket.TicketID) + " vì phiếu này " + message,
				ErrorCode: "TICKET_NOT_UPDATE",
			}
		}
	}

	if len(input.ListTicketId) != len(existedTicket) {
		var listTicketError []int64
		for _, ticket := range input.ListTicketId {
			if !CheckItemInArray(ticket, listTicketExisted) {
				listTicketError = append(listTicketError, ticket)
			}
		}

		message := strings.Trim(strings.Join(strings.Fields(fmt.Sprint(listTicketError)), ","), "[]")

		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy phiếu bàn giao: " + message,
			ErrorCode: "TICKET_NOT_FOUND",
		}
	}

	for _, ticket := range existedTicket {
		for i, handoverItem := range ticket.SOList {
			if handoverItem.Status != nil && *handoverItem.Status == enum.HandoverItemStatus.CANCEL {
				continue
			}

			for _, product := range handoverItem.Products {
				// Only reset scanning product, if it was lost, skip it
				if product.Status != nil && *product.Status == enum.ProductStatus.SCANNING {
					product.Status = &enum.ProductStatus.TRANSPORTING
				}
			}

			handoverItem.ReceivedQuantity = 0
			handoverItem.Note = ""
			ticket.SOList[i] = handoverItem
		}

		ticket.Status = &enum.HandoverStatus.WAIT_TO_CHECK

		afterOption := options.After
		ticket.UpdatedBy = accountId
		updateResult := model.HandoverTicketDB.UpdateOne(bson.M{
			"ticket_id": ticket.TicketID,
		},
			ticket,
			&options.FindOneAndUpdateOptions{
				Upsert:         &enum.False,
				ReturnDocument: &afterOption,
			})

		if updateResult.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Cập nhật phiếu bàn giao lỗi!",
			}
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật phiếu bàn giao thành công.",
	}
}

func AssignDriverToTicket(input *model.HandoverTicket, accountID int64, accountName string) *common.APIResponse {
	if input.TicketID <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã bàn giao không được để trống.",
			ErrorCode: "TICKET_ID_REQUIRED",
		}
	}

	getTicketResult := model.HandoverTicketDB.QueryOne(bson.M{
		"ticket_id": input.TicketID,
		"$or": bson.A{
			bson.M{
				"driver_id": "",
			},
			bson.M{
				"driver_id": bson.M{
					"$exists": false,
				},
			},
		},
	})

	if getTicketResult.Status != common.APIStatus.Ok || getTicketResult.Data == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   "Không tìm thấy phiếu bàn giao hợp lệ.",
			ErrorCode: "TICKET_NOT_FOUND",
		}
	}

	existedTicket := getTicketResult.Data.([]*model.HandoverTicket)[0]

	if existedTicket.Status != nil &&
		*existedTicket.Status != enum.HandoverStatus.TRANSPORTING {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Trạng thái phiếu không hợp lệ.",
			ErrorCode: "TICKET_NOT_UPDATE",
		}
	}

	if input.ExtraData == nil || input.ExtraData["pop"] == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "MISSING_POP",
			Message:   "Thiếu thông tin hình ảnh xác nhận tài xế đã lấy hàng.",
		}
	}

	popByte, _ := json.Marshal(input.ExtraData["pop"])
	var pop []string
	err := json.Unmarshal(popByte, &pop)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "INVALID_POP",
			Message:   "Định dạng pop không hợp lệ",
		}
	}
	for _, ele := range pop {
		if isOk, _ := regexp.Match("^(?:http(s)?:\\/\\/)?[\\w.-]+(?:\\.[\\w\\.-]+)+[\\w\\-\\._~:/?#[\\]@!\\$&'\\(\\)\\*\\+,;=.]+$", []byte(ele)); !isOk {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				ErrorCode: "INVALID_POP",
				Message:   "Link không đúng định dạng.",
			}
		}
	}

	if existedTicket.ExtraData == nil {
		existedTicket.ExtraData = map[string]interface{}{}
	}

	existedTicket.ExtraData["pop"] = input.ExtraData["pop"]

	tripRaw := model.TripDB.QueryOne(bson.M{
		"trip_id": input.TripId,
	})

	if tripRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Không tìm thấy chuyến đi.",
		}
	}

	trip := tripRaw.Data.([]*model.Trip)[0]
	if trip.Status != nil &&
		(*trip.Status == enum.TripStatus.COMPLETE ||
			*trip.Status == enum.TripStatus.DRAFT ||
			*trip.Status == enum.TripStatus.CANCELED) {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Trạng thái trip không hợp lệ.",
		}
	}

	flag := 0
	for _, dropOffPoint := range trip.Route.DropOffPoints {
		if dropOffPoint.Code == existedTicket.FromDepartmentCode || dropOffPoint.Code == existedTicket.ToDepartmentCode {
			flag++
		}
	}
	if flag != 2 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Hub bàn giao hoặc Hub nhận không thuộc lộ trình chuyến đi",
		}
	}
	trip.HandoverTickets = append(trip.HandoverTickets, existedTicket)

	updateTripResult := model.TripDB.UpdateOne(bson.M{
		"trip_id": trip.TripId,
	}, trip)

	if updateTripResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Cập nhật chuyến đi lỗi!",
		}
	}
	existedTicket.DriverID = int(trip.Driver.AccountID)
	existedTicket.DriverName = trip.Driver.Fullname
	existedTicket.TripId = trip.TripId
	existedTicket.TripCode = trip.TripCode
	existedTicket.TruckID = trip.Truck.TruckId
	existedTicket.LicensePlate = trip.Truck.LicensePlate
	existedTicket.RouteName = trip.Route.RouteName
	existedTicket.RouteCode = trip.Route.RouteCode
	existedTicket.UpdatedBy = accountID
	afterOption := options.After

	updateResult := model.HandoverTicketDB.UpdateOne(bson.M{
		"ticket_id": existedTicket.TicketID,
	},
		existedTicket,
		&options.FindOneAndUpdateOptions{
			Upsert:         &enum.False,
			ReturnDocument: &afterOption,
		})

	if updateResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Cập nhật phiếu bàn giao lỗi!",
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật phiếu bàn giao thành công.",
		Data:    updateResult.Data.([]*model.HandoverTicket),
	}
}

// CheckItemHandover func
func CheckItemHandover(input *request.ScanHandoverItemRequest, accountID int64) *common.APIResponse {
	if input.TicketID <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã bàn giao không được để trống.",
			ErrorCode: "TICKET_ID_REQUIRED",
		}
	}

	if input.ScannedQuantity <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Số lượng quét package phải lớn hơn 0",
			ErrorCode: "SCANNED_QUANTITY_INVALID",
		}
	}

	getTicketResult := model.HandoverTicketDB.QueryOne(bson.M{
		"ticket_id": input.TicketID,
	})

	if getTicketResult.Status != common.APIStatus.Ok || getTicketResult.Data == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy phiếu bàn giao.",
			ErrorCode: "TICKET_NOT_FOUND",
		}
	}

	existedTicket := getTicketResult.Data.([]*model.HandoverTicket)[0]
	if existedTicket.HandoverType != nil && *existedTicket.HandoverType != enum.HandoverType.TRANSPORTING {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   "Loại phiếu bàn giao không hợp lệ.",
			ErrorCode: "TYPE_TICKET_ERROR",
		}
	}

	if *existedTicket.Status == enum.HandoverStatus.COMPLETED || *existedTicket.Status == enum.HandoverStatus.CANCEL {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không thể cập nhật phiếu đã hoàn thành hoặc hủy.",
			ErrorCode: "TICKET_NOT_UPDATE",
		}
	}

	if existedTicket.Status != nil &&
		*existedTicket.Status == enum.HandoverStatus.DRAFT &&
		existedTicket.ItemType != nil &&
		*existedTicket.ItemType == enum.HandoverItemType.PO {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không thể quét phiên kiểm khi chưa hoàn thành tạo luân chuyển",
			ErrorCode: "INVALID_TICKET_STATUS",
		}
	}

	isProductExisted := false
	if input.Sku != "" {
		for _, existedSO := range existedTicket.SOList {
			for _, existedProduct := range existedSO.Products {
				if existedProduct.SKU == input.Sku && *existedProduct.Status == enum.ProductStatus.TRANSPORTING {
					input.SO = existedSO.SO
					isProductExisted = true
					break
				}
			}
			if isProductExisted {
				break
			}
		}

		if !isProductExisted {
			return &common.APIResponse{
				Message: "Trạng thái của sản phẩm không hợp lệ",
				Status:  common.APIStatus.Invalid,
			}
		}
	}

	if input.ReceiveSessionCode != "" {
		hubOrderRaw := model.HUBShippingOrderDB.QueryOne(bson.M{
			"hub_code":             existedTicket.ToDepartmentCode,
			"status":               &enum.HubShippingOrderStatus.WAIT_TO_STORING,
			"receive_session_code": input.ReceiveSessionCode,
		})

		if hubOrderRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Message: "Không tìm thấy phiên nhận: " + input.ReceiveSessionCode + " tại hub " + existedTicket.ToDepartmentCode,
				Status:  common.APIStatus.Invalid,
			}
		}
		hubOrder := hubOrderRaw.Data.([]*model.HubShippingOrder)[0]
		input.SO = hubOrder.ReferenceCode
	}

	var statusCheck enum.HandoverItemStatusValue
	if int64(input.ScannedQuantity) == int64(input.NumPackage) {
		statusCheck = enum.HandoverItemStatus.DONE
	} else {
		statusCheck = enum.HandoverItemStatus.PARTIAL
	}

	isSOExisted := false
	isOverPackage := false
	// If exist, increase numPackage
	for i, existedSO := range existedTicket.SOList {
		if existedSO.SO == input.SO {
			if existedSO.Status != nil && *existedSO.Status == enum.HandoverItemStatus.CANCEL {
				return &common.APIResponse{
					Status:  common.APIStatus.Error,
					Message: "Đơn hàng không thuộc phiếu giao hàng này.",
				}
			}
			isSOExisted = true
			existedSO.ReceivedQuantity = input.ScannedQuantity + existedSO.ReceivedQuantity
			existedSO.Status = &statusCheck
			if existedSO.ScannedQuantity == existedSO.ReceivedQuantity {
				existedSO.Status = &enum.HandoverItemStatus.DONE
			}

			if existedSO.ReceivedQuantity > existedSO.NumPackage {
				isOverPackage = true
				break
			}

			if isProductExisted {
				for _, existedProduct := range existedSO.Products {
					if existedProduct.SKU == input.Sku && *existedProduct.Status != enum.ProductStatus.TRANSPORTING {
						return &common.APIResponse{
							Message: "Trạng thái của sản phẩm không hợp lệ",
							Status:  common.APIStatus.Invalid,
						}
					}
					if existedProduct.SKU == input.Sku && *existedProduct.Status == enum.ProductStatus.TRANSPORTING {
						existedProduct.Status = &enum.ProductStatus.SCANNING
					}
				}
			}

			existedTicket.SOList[i] = existedSO
		}
	}

	if isOverPackage {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Quá số kiện",
			ErrorCode: "OVER_PACKAGE",
		}
	}
	if !isSOExisted {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Đơn hàng không thuộc phiếu giao hàng này.",
		}
	}

	versionNo := existedTicket.VersionNo
	existedTicket.UpdatedBy = accountID
	existedTicket.VersionNo = uuid.New().String()
	existedTicket.Status = &enum.HandoverStatus.CHECKING

	afterOption := options.After
	updateResult := model.HandoverTicketDB.UpdateOne(bson.M{
		"ticket_id":  existedTicket.TicketID,
		"version_no": versionNo,
	},
		existedTicket,
		&options.FindOneAndUpdateOptions{
			Upsert:         &enum.False,
			ReturnDocument: &afterOption,
		})

	if updateResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Cập nhật phiếu bàn giao lỗi!",
		}
	}

	//if existedTicket.TripId != 0 &&
	//	oldTicketStatus != enum.HandoverStatus.CHECKING {
	//	trip.PushAutoCompleteTrip(&trip.CompleteHandoverTripRequest{
	//		TripId: existedTicket.TripId,
	//	},
	//		strconv.FormatInt(existedTicket.TripId, 10))
	//}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật phiếu bàn giao thành công.",
		Data:    updateResult.Data.([]*model.HandoverTicket),
	}
}

// RemovedSO func
func RemovedSO(input *request.HandoverItemRequest, accountID int64) *common.APIResponse {
	if input.TicketID <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã bàn giao không được để trống.",
			ErrorCode: "TICKET_ID_REQUIRED",
		}
	}
	if input.SO == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã đơn hàng không được để trống.",
			ErrorCode: "SO_REQUIRED",
		}
	}
	if input.Status != nil && *input.Status == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Trạng thái không được để trống.",
			ErrorCode: "STATUS_REQUIRED",
		}
	}
	if *input.Status != enum.HandoverItemStatus.CANCEL {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Trạng thái không đúng.",
			ErrorCode: "STATUS_INVALID",
		}
	}

	getTicketResult := model.HandoverTicketDB.QueryOne(bson.M{
		"ticket_id": input.TicketID,
	})

	if getTicketResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy phiếu bàn giao.",
			ErrorCode: "TICKET_NOT_FOUND",
		}
	}

	existedTicket := getTicketResult.Data.([]*model.HandoverTicket)[0]

	if *existedTicket.Status == enum.HandoverStatus.COMPLETED || *existedTicket.Status == enum.HandoverStatus.CANCEL || *existedTicket.Status == enum.HandoverStatus.TRANSPORTING {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không thể cập nhật phiếu đã hoàn thành/đang luân chuyển hoặc hủy.",
			ErrorCode: "TICKET_NOT_UPDATE",
		}
	}
	soItemInit := model.HandoverSOItem{
		SO:     input.SO,
		Status: input.Status,
	}

	isFoundSO := false
	isContainsFrozen := false
	for i, existedSO := range existedTicket.SOList {
		if utils.StringSliceContain(existedSO.Tags, "FROZEN") {
			isContainsFrozen = true
		}
		if existedSO.SO == soItemInit.SO {
			isFoundSO = true
			existedSO.Status = soItemInit.Status
			existedSO.ScannedQuantity = 0
			if len(existedSO.Products) > 0 {
				existedSO.Products = nil
			}

			existedTicket.SOList[i] = existedSO
			break
		}
	}

	if !isFoundSO {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy mã đơn hàng trong phiếu bàn giao",
			ErrorCode: "NOT_FOUND",
		}
	}
	now := time.Now()
	versionNo := existedTicket.VersionNo
	existedTicket.LastUpdatedTime = &now
	existedTicket.UpdatedBy = accountID
	existedTicket.VersionNo = uuid.New().String()
	existedTicket.IsContainsFrozenItem = &isContainsFrozen

	afterOption := options.After
	updateResult := model.HandoverTicketDB.UpdateOne(bson.M{
		"ticket_id":  existedTicket.TicketID,
		"version_no": versionNo,
	},
		existedTicket,
		&options.FindOneAndUpdateOptions{
			Upsert:         &enum.False,
			ReturnDocument: &afterOption,
		})

	if updateResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Không thể xóa đơn hàng ra khỏi phiếu bàn giao!",
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Xóa đơn hàng ra khỏi phiếu bàn giao thành công!",
		Data:    updateResult.Data.([]*model.HandoverTicket),
	}
}

// DoneReceiveTicket func
func DoneReceiveTicket(input *request.DoneReceiveTicketRequest, accountId int64) *common.APIResponse {
	if input.TicketId <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã phiếu nhận hàng không được để trống.",
			ErrorCode: "TICKET_ID_REQUIRED",
		}
	}

	if input.Status == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Trạng thái của phiếu nhận hàng không được để trống.",
			ErrorCode: "STATUS_REQUIRED",
		}
	}

	resultQueryTicket := model.HandoverTicketDB.QueryOne(&model.HandoverTicket{
		TicketID: int(input.TicketId),
	})

	if resultQueryTicket.Status != common.APIStatus.Ok {
		return resultQueryTicket
	}

	receiveTicket := resultQueryTicket.Data.([]*model.HandoverTicket)[0]

	var referenceCodes []string
	if *input.Status != enum.HandoverStatus.CANCEL {
		resultQueryHub := model.HubDB.QueryOne(bson.M{
			"code": input.HubCode,
		})

		if resultQueryHub.Status != common.APIStatus.Ok {
			return resultQueryHub
		}

		hub := resultQueryHub.Data.([]*model.Hub)[0]

		var itemCodes, listItemCodeEmpty []string

		for _, item := range receiveTicket.SOList {
			if item.Status == nil || *item.Status == enum.HandoverItemStatus.CANCEL {
				continue
			}
			itemCodes = append(itemCodes, item.SO)
		}

		resultQueryHubOrder := model.HUBShippingOrderDB.Query(bson.M{
			"reference_code": bson.M{
				"$in": itemCodes,
			},
			"hub_code": input.HubCode,
		}, 0, int64(len(itemCodes)), nil)

		if resultQueryHubOrder.Status != common.APIStatus.Ok {
			if resultQueryHubOrder.Status != common.APIStatus.NotFound {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   resultQueryHubOrder.Message,
					ErrorCode: resultQueryHubOrder.ErrorCode,
				}
			}
			listItemCodeEmpty = itemCodes
		} else {
			if len(itemCodes) != len(resultQueryHubOrder.Data.([]*model.HubShippingOrder)) {
				var hubOrderExisted []string
				for _, hubOrder := range resultQueryHubOrder.Data.([]*model.HubShippingOrder) {
					hubOrderExisted = append(hubOrderExisted, hubOrder.ReferenceCode)
				}

				for _, item := range itemCodes {
					if !CheckItemInArray(item, hubOrderExisted) {
						listItemCodeEmpty = append(listItemCodeEmpty, item)
					}
				}
			}
		}

		for i, item := range receiveTicket.SOList {
			if item.Status == nil || *item.Status == enum.HandoverItemStatus.CANCEL {
				continue
			}

			if item.NumPackage <= 0 || item.ScannedQuantity <= 0 {
				if input.Note[item.SO] == "" {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   "Số kiện hàng không hợp lệ",
						ErrorCode: "NUM_PACKAGE_INVALID",
					}
				}
			}

			if item.ScannedQuantity < item.NumPackage {
				if input.Note[item.SO] == "" {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   "Vui lòng điền ghi chú cho " + item.SO,
						ErrorCode: "NOTE_REQUIRED",
					}
				}
			}

			item.Note = input.Note[item.SO]
			receiveTicket.SOList[i] = item

			if CheckItemInArray(item.SO, listItemCodeEmpty) {
				// đơn chưa luân chuyển qua hub thì tạo hub order mới
				hub_shipping_order.PushCreateHubShippingOrderQueue(&request.HandoverTicketCreateHubOrder{
					UpdatedBy:          accountId,
					WarehouseCode:      receiveTicket.WarehouseCode,
					FromDepartmentCode: receiveTicket.FromDepartmentCode,
					FromDepartmentName: receiveTicket.FromDepartmentName,
					ToDepartmentCode:   receiveTicket.ToDepartmentCode,
					ToDepartmentName:   receiveTicket.ToDepartmentName,
					TicketID:           receiveTicket.TicketID,
					ReceivePackage:     item.ScannedQuantity,
					TPLCode:            receiveTicket.TPLCode,
					TPLName:            receiveTicket.TPLName,
					HubOrderType:       nil,
					Action:             "IN",
					ActionName:         "Đã nhập kho " + hub.Name,
					SO:                 item.SO,
					HubStatus:          &enum.HubShippingOrderStatus.STORING,
					HandoverType:       receiveTicket.HandoverType,
					HandoverNote:       receiveTicket.HandoverNote,
					Code:               receiveTicket.Code,
				}, item.SO)
			} else {
				// đơn đã luân chuyển qua hub thì cập nhật trạng thái lại và đẩy vào log
				hub_shipping_order.PushUpdateHubShippingOrderQueue(&request.UpdateHubOrder{
					Action:             "IN",
					ActionName:         "Đã nhập kho " + hub.Name,
					ReferenceCode:      item.SO,
					HubCode:            hub.Code,
					Status:             &enum.HubShippingOrderStatus.STORING,
					HandoverTicketId:   receiveTicket.TicketID,
					HandoverTicketCode: receiveTicket.Code,
					HandoverTicketNote: receiveTicket.HandoverNote,
					ReceiveQuantity:    item.ScannedQuantity,
				}, item.SO)
			}

			// Ghi  nhận list đơn nhập kho
			referenceCodes = append(referenceCodes, item.SO)
		}
	}

	now := time.Now()
	receiveTicket.LastUpdatedTime = &now
	receiveTicket.UpdatedBy = accountId
	receiveTicket.Status = input.Status
	receiveTicket.CompletedTime = &now

	// Update Shipping order stored time
	if len(referenceCodes) > 0 {
		defer func(refCodes []string) {
			_ = model.ShippingOrderDB.UpdateMany(&bson.M{
				"reference_code": bson.M{
					"$in": refCodes,
				},
			}, &bson.M{
				"stored_at_lm_hub_time": time.Now().Unix(),
			})
		}(referenceCodes)
	}

	afterOption := options.After
	updateResult := model.HandoverTicketDB.UpdateOne(bson.M{
		"_id": receiveTicket.ID,
	},
		receiveTicket,
		&options.FindOneAndUpdateOptions{
			Upsert:         &enum.False,
			ReturnDocument: &afterOption,
		})

	if updateResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   updateResult.Message,
			ErrorCode: updateResult.ErrorCode,
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Hoàn thành phiếu nhận hàng thành công",
	}

}

func AddPo(input *request.HandoverItemRequest, accountID int64) *common.APIResponse {
	if input.TicketID <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã bàn giao không được để trống.",
			ErrorCode: "TICKET_ID_REQUIRED",
		}
	}

	if input.ScannedQuantity <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Số lượng quét package phải lớn hơn 0",
			ErrorCode: "SCANNED_QUANTITY_INVALID",
		}
	}

	getTicketResult := model.HandoverTicketDB.QueryOne(bson.M{
		"ticket_id": input.TicketID,
	})

	if getTicketResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy phiếu bàn giao.",
			ErrorCode: "TICKET_NOT_FOUND",
		}
	}

	existedTicket := getTicketResult.Data.([]*model.HandoverTicket)[0]
	// Check concurrent add item at hub
	checkHandoverTicketRaw := model.HandoverTicketDB.QueryOne(bson.M{
		"from_department_code": existedTicket.FromDepartmentCode,
		"to_department_code":   existedTicket.ToDepartmentCode,
		"status":               &enum.HandoverStatus.DRAFT,
		"so_list.so":           input.SO,
	})

	if checkHandoverTicketRaw.Status == common.APIStatus.Ok {
		checkHandoverTicket := checkHandoverTicketRaw.Data.([]*model.HandoverTicket)[0]
		if checkHandoverTicket.TicketID != existedTicket.TicketID {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Phiếu đang được quét trong một phiếu luân chuyển khác, hoàn thành hoặc hủy phiếu luân chuyển để tiếp tục",
			}
		}
	}

	var statusCheck enum.HandoverItemStatusValue
	if input.ScannedQuantity == input.NumPackage {
		statusCheck = enum.HandoverItemStatus.DONE
	} else {
		statusCheck = enum.HandoverItemStatus.PARTIAL
	}

	item := model.HandoverSOItem{
		SO:              input.SO,
		Weight:          float64(input.Weight),
		NumPackage:      input.NumPackage,
		ScannedQuantity: input.ScannedQuantity,
		TrackingCode:    input.TrackingCode,
		TplCode:         input.TplCode,
		Status:          &statusCheck,
		CheckInCode:     input.CheckInCode,
	}

	// Check xem luồng mới hay luồng cũ, nếu luồng cũ trong item của phiếu luân chuyển sẽ không có reference_code
	// Nếu có đơn có mã checkin đang lưu kho tại hub nghĩa là theo luồng mới
	hubOrderRaw := model.HUBShippingOrderDB.QueryOne(bson.M{
		"receive_session_code": item.CheckInCode,
		"status":               enum.HubShippingOrderStatus.STORING,
		"hub_code":             existedTicket.FromDepartmentCode,
	})

	if hubOrderRaw.Status == common.APIStatus.Ok {
		hubOrder := hubOrderRaw.Data.([]*model.HubShippingOrder)[0]
		shippingOrderResp := model.ShippingOrderDB.QueryOne(bson.M{
			"reference_code": hubOrder.ReferenceCode,
		})
		if shippingOrderResp.Status == common.APIStatus.Ok && existedTicket.CarrierId == 0 {
			shippingOrder := shippingOrderResp.Data.([]*model.ShippingOrder)[0]
			existedTicket.CarrierId = shippingOrder.TplServiceId
			existedTicket.CarrierName = shippingOrder.TplName
		}
		item.ReferenceCode = hubOrder.ReferenceCode
	}

	isSOExisted := false
	isOverPackage := false

	for i, existedSO := range existedTicket.SOList {
		if existedSO.SO == item.SO {
			isSOExisted = true
			existedSO.ScannedQuantity = item.ScannedQuantity + existedSO.ScannedQuantity
			existedSO.Status = item.Status
			if existedSO.ScannedQuantity == existedSO.NumPackage {
				existedSO.Status = &enum.HandoverItemStatus.DONE
			}

			if existedSO.ScannedQuantity > existedSO.NumPackage {
				isOverPackage = true
				break
			}
			existedTicket.SOList[i] = existedSO
		}
	}

	if isOverPackage {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Quá số kiện",
			ErrorCode: "OVER_PACKAGE",
		}
	}

	if !isSOExisted {
		existedTicket.SOList = append(existedTicket.SOList, item)
	}

	now := time.Now()
	versionNo := existedTicket.VersionNo
	existedTicket.LastUpdatedTime = &now
	existedTicket.UpdatedBy = accountID
	existedTicket.VersionNo = uuid.New().String()

	afterOption := options.After
	updateResult := model.HandoverTicketDB.UpdateOne(bson.M{
		"ticket_id":  existedTicket.TicketID,
		"version_no": versionNo,
	},
		existedTicket,
		&options.FindOneAndUpdateOptions{
			Upsert:         &enum.False,
			ReturnDocument: &afterOption,
		})

	if updateResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Cập nhật phiếu bàn giao lỗi!",
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật phiếu bàn giao thành công.",
		Data:    updateResult.Data.([]*model.HandoverTicket),
	}
}

func RemoveItem(input *request.HandoverItemRequest, accountID int64) *common.APIResponse {
	if input.TicketID <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã bàn giao không được để trống.",
			ErrorCode: "TICKET_ID_REQUIRED",
		}
	}

	if input.SO == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã đơn hàng không được để trống.",
			ErrorCode: "SO_REQUIRED",
		}
	}

	getTicketResult := model.HandoverTicketDB.QueryOne(bson.M{
		"ticket_id": input.TicketID,
	})

	if getTicketResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy phiếu bàn giao.",
			ErrorCode: "TICKET_NOT_FOUND",
		}
	}

	existedTicket := getTicketResult.Data.([]*model.HandoverTicket)[0]
	if *existedTicket.Status == enum.HandoverStatus.COMPLETED || *existedTicket.Status == enum.HandoverStatus.CANCEL || *existedTicket.Status == enum.HandoverStatus.TRANSPORTING {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không thể cập nhật phiếu đã hoàn thành/đang luân chuyển hoặc hủy.",
			ErrorCode: "TICKET_NOT_UPDATE",
		}
	}

	soItemInit := model.HandoverSOItem{
		SO:     input.SO,
		Status: &enum.HandoverItemStatus.CANCEL,
	}

	isFoundSO := false

	for i, existedSO := range existedTicket.SOList {
		if existedSO.SO == soItemInit.SO {
			isFoundSO = true
			existedSO.Status = soItemInit.Status
			existedSO.ScannedQuantity = 0
			existedTicket.SOList[i] = existedSO
			break
		}
	}

	if !isFoundSO {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy mã đơn hàng trong phiếu bàn giao",
			ErrorCode: "NOT_FOUND",
		}
	}

	now := time.Now()
	versionNo := existedTicket.VersionNo
	existedTicket.LastUpdatedTime = &now
	existedTicket.UpdatedBy = accountID
	existedTicket.VersionNo = uuid.New().String()

	afterOption := options.After
	updateResult := model.HandoverTicketDB.UpdateOne(bson.M{
		"ticket_id":  existedTicket.TicketID,
		"version_no": versionNo,
	},
		existedTicket,
		&options.FindOneAndUpdateOptions{
			Upsert:         &enum.False,
			ReturnDocument: &afterOption,
		})

	if updateResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Không thể xóa đơn hàng ra khỏi phiếu bàn giao!",
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Xóa đơn hàng ra khỏi phiếu bàn giao thành công!",
		Data:    updateResult.Data.([]*model.HandoverTicket),
	}
}

func AddBin(input *request.HandoverItemRequest, accountID int64) *common.APIResponse {
	if input.TicketID <= 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã bàn giao không được để trống",
		}
	}

	if len(input.Skus) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Danh sách bin không được để trống",
		}
	}

	ticketRaw := model.HandoverTicketDB.QueryOne(bson.M{
		"ticket_id": input.TicketID,
	})

	if ticketRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không tìm thấy phiếu bàn giao",
		}
	}

	ticket := ticketRaw.Data.([]*model.HandoverTicket)[0]
	if *ticket.Status == enum.HandoverStatus.COMPLETED ||
		*ticket.Status == enum.HandoverStatus.CANCEL ||
		*ticket.Status == enum.HandoverStatus.TRANSPORTING {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không thể cập nhật phiếu đã hoàn thành/đang luân chuyển hoặc hủy.",
			ErrorCode: "TICKET_NOT_UPDATE",
		}
	}

	// Remove duplicate bin code
	input.Skus = utils.RemoveDuplicateStr(input.Skus)
	skusLength := len(input.Skus)

	type result struct {
		BinCode       string                  `json:"binCode"`
		HubOrder      *model.HubShippingOrder `json:"hubOrder"`
		ShippingOrder *model.ShippingOrder    `json:"shippingOrder"`
		Product       *model.Product          `json:"product"`
		Err           *common.Error           `json:"err"`
	}

	jobs := make(chan string, skusLength)
	results := make(chan result, skusLength)

	// Handle bin code verification in parallel, change this num if exec time is slow
	numberOfWorker := 4
	for i := 0; i < numberOfWorker; i++ {
		go func(jobs <-chan string, results chan<- result, ticket *model.HandoverTicket) {
			for job := range jobs {
				hubOrder, shippingOrder, product, err := ValidateBinCode(job, ticket)
				results <- result{BinCode: job, HubOrder: hubOrder, Product: product, Err: err, ShippingOrder: shippingOrder}
			}
		}(jobs, results, ticket)
	}

	for _, sku := range input.Skus {
		jobs <- sku
	}
	close(jobs)

	type addBinResponse struct {
		Ticket   *model.HandoverTicket `json:"ticket,omitempty"`
		Err      []*common.Error       `json:"err,omitempty"`
		ValidBin []*result             `json:"validBin,omitempty"`
	}

	var response = &addBinResponse{
		Err:      []*common.Error{},
		ValidBin: []*result{},
		Ticket:   nil,
	}

	// Validate bin code
	for i := 0; i < skusLength; i++ {
		validateResult := <-results
		if validateResult.Err != nil {
			response.Err = append(response.Err, validateResult.Err)
			continue
		}
		response.ValidBin = append(response.ValidBin, &validateResult)
	}
	close(results)

	if len(response.ValidBin) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Danh sách mã bin không hợp lệ",
			Data:    []*addBinResponse{response},
		}
	}

	var soListIndex = map[string]int{}
	for i := range ticket.SOList {
		soListIndex[ticket.SOList[i].SO] = i
	}

	var carrierId int64 = 0
	var carrierName = ""
	for _, validBin := range response.ValidBin {
		// if so already exists, change product status to scanning
		if index, ok := soListIndex[validBin.HubOrder.ReferenceCode]; ok {
			ticket.SOList[index].ScannedQuantity++
			if ticket.SOList[index].ScannedQuantity == validBin.HubOrder.NumPackage {
				ticket.SOList[index].Status = &enum.HandoverItemStatus.DONE
			} else {
				ticket.SOList[index].Status = &enum.HandoverItemStatus.PARTIAL
			}

			// in case of cancel and scan again, assign hub order product to handover item
			if len(ticket.SOList[index].Products) == 0 {
				ticket.SOList[index].Products = validBin.HubOrder.Products
			}

			for _, product := range ticket.SOList[index].Products {
				if product.SKU == validBin.BinCode {
					product.Status = &enum.ProductStatus.SCANNING
				}
			}
			continue
		}

		// if so not exist yet, create new item
		item := model.HandoverSOItem{
			SO:              validBin.HubOrder.ReferenceCode,
			Weight:          float64(validBin.HubOrder.Weight),
			NumPackage:      validBin.HubOrder.NumPackage,
			ScannedQuantity: 1,
			TrackingCode:    validBin.HubOrder.TrackingCode,
			TplCode:         input.TplCode,
			Products:        validBin.HubOrder.Products,
			Baskets:         validBin.HubOrder.Baskets,
		}
		// Update scanned product status to scanning
		for _, product := range item.Products {
			if product.SKU == validBin.BinCode {
				product.Status = &enum.ProductStatus.SCANNING
			}
		}

		if validBin.HubOrder.NumPackage == item.ScannedQuantity {
			item.Status = &enum.HandoverItemStatus.DONE
		} else {
			item.Status = &enum.HandoverItemStatus.PARTIAL
		}

		ticket.SOList = append(ticket.SOList, item)
		soListIndex[item.SO] = len(ticket.SOList) - 1
		carrierId = validBin.ShippingOrder.TplServiceId
		carrierName = validBin.ShippingOrder.TplName
	}

	now := time.Now()
	versionNo := ticket.VersionNo
	ticket.LastUpdatedTime = &now
	ticket.UpdatedBy = accountID
	ticket.VersionNo = uuid.New().String()
	if ticket.CarrierId == 0 {
		ticket.CarrierId = carrierId
		ticket.CarrierName = carrierName
	}
	afterOption := options.After
	updateResult := model.HandoverTicketDB.UpdateOne(bson.M{
		"ticket_id":  ticket.TicketID,
		"version_no": versionNo,
	},
		ticket,
		&options.FindOneAndUpdateOptions{
			Upsert:         &enum.False,
			ReturnDocument: &afterOption,
		})

	if updateResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Cập nhật phiếu bàn giao lỗi!",
		}
	}

	response.Ticket = updateResult.Data.([]*model.HandoverTicket)[0]
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật phiếu bàn giao thành công.",
		Data:    []*addBinResponse{response},
	}

}

func DoneCreateHanoverTicket(ticket *model.HandoverTicket, accountID int64) *common.APIResponse {
	if ticket.TicketID <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã nhập hàng không được để trống.",
			ErrorCode: "TICKET_ID_REQUIRED",
		}
	}
	getTicketResult := model.HandoverTicketDB.QueryOne(bson.M{
		"ticket_id": ticket.TicketID,
	})
	if getTicketResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy phiếu bàn giao.",
			ErrorCode: "TICKET_NOT_FOUND",
		}
	}

	existedTicket := getTicketResult.Data.([]*model.HandoverTicket)[0]
	if *existedTicket.Status == enum.HandoverStatus.COMPLETED ||
		*existedTicket.Status == enum.HandoverStatus.CANCEL ||
		*existedTicket.Status == enum.HandoverStatus.TRANSPORTING {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không thể cập nhật phiếu đã hoàn thành hoặc hủy.",
			ErrorCode: "TICKET_NOT_UPDATE",
		}
	}

	now := time.Now()
	versionNo := existedTicket.VersionNo
	existedTicket.LastUpdatedTime = &now
	existedTicket.UpdatedBy = accountID
	existedTicket.VersionNo = uuid.New().String()
	existedTicket.Status = ticket.Status
	logs := model.HandoverLogs{
		ActionTime:         &now,
		FromDepartmentCode: existedTicket.FromDepartmentCode,
		FromDepartmentName: existedTicket.FromDepartmentName,
		ToDepartmentCode:   existedTicket.ToDepartmentCode,
		ToDepartmentName:   existedTicket.ToDepartmentName,
		HandoverNote:       existedTicket.HandoverNote,
		Status:             ticket.Status,
		ExtraData: map[string]interface{}{
			"actionByAccountId": accountID,
		},
	}
	existedTicket.Logs = append(existedTicket.Logs, &logs)
	if ticket.Status != nil {
		if *ticket.Status == enum.HandoverStatus.CANCEL || *ticket.Status == enum.HandoverStatus.COMPLETED {
			existedTicket.CompletedTime = &now
		} else if *ticket.Status == enum.HandoverStatus.TRANSPORTING {
			existedTicket.DoneHandoverTime = &now
		} else {
			existedTicket.ActionTime = &now
		}
	}

	fromHubRaw := model.HubDB.QueryOne(bson.M{
		"code": existedTicket.FromDepartmentCode,
	})

	if fromHubRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Message: "Mã hub " + existedTicket.FromDepartmentCode + " không hợp lệ",
			Status:  common.APIStatus.Invalid,
		}
	}

	toHubRaw := model.HubDB.QueryOne(bson.M{
		"code": existedTicket.ToDepartmentCode,
	})

	if toHubRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Message: "Mã hub " + existedTicket.ToDepartmentCode + " không hợp lệ",
			Status:  common.APIStatus.Invalid,
		}
	}

	fromHub := fromHubRaw.Data.([]*model.Hub)[0]
	toHub := toHubRaw.Data.([]*model.Hub)[0]

	req := &request.DoneTransportForPO{
		TransportCode:  existedTicket.Code,
		FromHubCode:    fromHub.Code,
		FromWHCode:     fromHub.WarehouseReferenceCode,
		ToHubCode:      toHub.Code,
		ToWHCode:       toHub.WarehouseReferenceCode,
		DoneType:       "CREATED",
		CreatedBy:      accountID,
		TransportItems: []*request.TransportItem{},
	}

	doneCreateHandover := handOverTransfer.DoneHandoverTicketRequest{
		TicketId: ticket.TicketID,
	}

	for _, item := range existedTicket.SOList {
		if item.ScannedQuantity == 0 {
			continue
		}

		req.TransportItems = append(req.TransportItems, &request.TransportItem{
			PackageQuantity: item.ScannedQuantity,
			SessionCode:     item.SO,
			CheckInCode:     item.CheckInCode,
		})

		// Luồng mới
		if item.ReferenceCode != "" {
			doneCreateHandover.Items = append(doneCreateHandover.Items, item)
		}
	}

	if len(req.TransportItems) == 0 {
		return &common.APIResponse{
			Message: "Phải có ít nhất một kiện để hoàn thành luân chuyển",
			Status:  common.APIStatus.Invalid,
		}
	}

	err := client.Services.WarehouseCoreClient.SendDoneReceipt(req)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Error(),
		}
	}

	if len(doneCreateHandover.Items) > 0 {
		handOverTransfer.PushDoneCreateHandover(doneCreateHandover, ticket.Code)
	}

	afterOption := options.After
	updateResult := model.HandoverTicketDB.UpdateOne(bson.M{
		"_id":        existedTicket.ID,
		"version_no": versionNo,
	},
		existedTicket,
		&options.FindOneAndUpdateOptions{
			Upsert:         &enum.False,
			ReturnDocument: &afterOption,
		})

	if updateResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Cập nhật phiếu bàn giao lỗi!",
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật phiếu bàn giao thành công.",
		Data:    updateResult.Data.([]*model.HandoverTicket),
	}
}

func DoneReceiveHandoverTicket(input *request.DoneTransportRequest, account *model.ActionSource) *common.APIResponse {
	if len(input.ListTicketId) <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã bàn giao không được để trống.",
			ErrorCode: "TICKET_ID_REQUIRED",
		}
	}

	getTicketResult := model.HandoverTicketDB.Query(bson.M{
		"to_department_code": input.HubCode,
		"ticket_id": bson.M{
			"$in": input.ListTicketId,
		},
	},
		0, int64(len(input.ListTicketId)), nil)

	if getTicketResult.Status != common.APIStatus.Ok || getTicketResult.Data == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy phiếu bàn giao.",
			ErrorCode: "TICKET_NOT_FOUND",
		}
	}

	existedTicket := getTicketResult.Data.([]*model.HandoverTicket)
	var listTicketExisted []int64
	for i, ticket := range existedTicket {
		listTicketExisted = append(listTicketExisted, int64(ticket.TicketID))
		if ticket.HandoverType != nil && *ticket.HandoverType != enum.HandoverType.TRANSPORTING {
			return &common.APIResponse{
				Status:    common.APIStatus.Error,
				Message:   "Loại của phiếu bàn giao: " + strconv.Itoa(ticket.TicketID) + " ,không hợp lệ",
				ErrorCode: "TYPE_TICKET_ERROR",
			}
		}

		if ticket.Status != nil && (*ticket.Status == enum.HandoverStatus.COMPLETED || *ticket.Status == enum.HandoverStatus.CANCEL) {
			message := "đã hủy"
			if *ticket.Status == enum.HandoverStatus.COMPLETED {
				message = "đã hoàn thành"
			}
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Không thể cập nhật phiếu: " + strconv.Itoa(ticket.TicketID) + " vì phiếu này " + message,
				ErrorCode: "TICKET_NOT_UPDATE",
			}
		}

		for j, handoverItem := range ticket.SOList {
			if handoverItem.Status != nil && *handoverItem.Status == enum.HandoverItemStatus.CANCEL {
				continue
			}
			if handoverItem.ReceivedQuantity < handoverItem.ScannedQuantity && !input.DoneAll {
				existedTicket[i].SOList[j].Status = &enum.HandoverItemStatus.PARTIAL
				if input.Note[strconv.Itoa(ticket.TicketID)][handoverItem.SO] == "" {
					return &common.APIResponse{
						Status:  common.APIStatus.Error,
						Message: "Vui lòng điền ghi chú cho " + handoverItem.SO,
					}
				}
			}
		}
	}

	if len(input.ListTicketId) != len(existedTicket) {
		var listTicketError []int64
		for _, ticket := range input.ListTicketId {
			if !CheckItemInArray(ticket, listTicketExisted) {
				listTicketError = append(listTicketError, ticket)
			}
		}

		message := strings.Trim(strings.Join(strings.Fields(fmt.Sprint(listTicketError)), ","), "[]")

		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy phiếu bàn giao: " + message,
			ErrorCode: "TICKET_NOT_FOUND",
		}
	}

	for _, ticket := range existedTicket {
		current := time.Now()
		for i, handoverItem := range ticket.SOList {
			if handoverItem.Status != nil && *handoverItem.Status == enum.HandoverItemStatus.CANCEL {
				continue
			}

			if input.DoneAll {
				handoverItem.ReceivedQuantity = handoverItem.ScannedQuantity
			}

			handoverItem.Note = input.Note[strconv.Itoa(ticket.TicketID)][handoverItem.SO]
			ticket.SOList[i] = handoverItem
		}

		fromHubRaw := model.HubDB.QueryOne(bson.M{
			"code": ticket.FromDepartmentCode,
		})

		if fromHubRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Message: "Mã hub " + ticket.FromDepartmentCode + " không hợp lệ",
				Status:  common.APIStatus.Invalid,
			}
		}

		toHubRaw := model.HubDB.QueryOne(bson.M{
			"code": ticket.ToDepartmentCode,
		})

		if toHubRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Message: "Mã hub " + ticket.ToDepartmentCode + " không hợp lệ",
				Status:  common.APIStatus.Invalid,
			}
		}

		fromHub := fromHubRaw.Data.([]*model.Hub)[0]
		toHub := toHubRaw.Data.([]*model.Hub)[0]

		now := time.Now()
		req := &request.DoneTransportForPO{
			TransportCode:  ticket.Code,
			FromHubCode:    fromHub.Code,
			FromWHCode:     fromHub.WarehouseReferenceCode,
			ToHubCode:      toHub.Code,
			ToWHCode:       toHub.WarehouseReferenceCode,
			DoneType:       "RECEIVED",
			Employee:       account.Account.Fullname,
			EmployeeId:     account.Account.AccountID,
			CreatedBy:      ticket.CreatedBy,
			TransportItems: []*request.TransportItem{},
			ReceivedTime:   &now,
		}

		doneReceiveHandover := handOverTransfer.DoneHandoverTicketRequest{
			TicketId: ticket.TicketID,
		}

		for _, item := range ticket.SOList {
			if item.Status != nil && *item.Status == enum.HandoverItemStatus.CANCEL {
				continue
			}

			req.TransportItems = append(req.TransportItems, &request.TransportItem{
				PackageQuantity: item.ReceivedQuantity,
				SessionCode:     item.SO,
				CheckInCode:     item.CheckInCode,
			})

			if item.ReferenceCode != "" {
				doneReceiveHandover.Items = append(doneReceiveHandover.Items, item)
			}
		}

		err := client.Services.WarehouseCoreClient.SendDoneReceipt(req)
		if err != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: err.Error(),
			}
		}

		if len(doneReceiveHandover.Items) > 0 {
			_ = handOverTransfer.PushDoneReceiveHandover(doneReceiveHandover, ticket.Code)
		}

		ticket.Status = &enum.HandoverStatus.COMPLETED
		afterOption := options.After
		ticket.CompletedTime = &current
		ticket.UpdatedBy = account.Account.AccountID
		updateResult := model.HandoverTicketDB.UpdateOne(bson.M{
			"ticket_id": ticket.TicketID,
		},
			ticket,
			&options.FindOneAndUpdateOptions{
				Upsert:         &enum.False,
				ReturnDocument: &afterOption,
			})

		if updateResult.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Cập nhật phiếu bàn giao lỗi!",
			}
		}
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật phiếu bàn giao thành công.",
	}

}

func ValidateBinCode(binCode string, ticket *model.HandoverTicket) (
	*model.HubShippingOrder, *model.ShippingOrder, *model.Product, *common.Error) {
	// Verify Hub
	toHubRaw := model.HubDB.QueryOne(bson.M{
		"code": ticket.ToDepartmentCode,
	})

	if toHubRaw.Status != common.APIStatus.Ok {
		return nil, nil, nil, &common.Error{
			Data: map[string]string{
				binCode: "Hub nhận luân chuyển không tồn tại",
			},
		}
	}

	toHub := toHubRaw.Data.([]*model.Hub)[0]

	// Verify Hub order
	hubOrderRaw := model.HUBShippingOrderDB.QueryOne(bson.M{
		"hub_code": ticket.FromDepartmentCode,
		"status":   &enum.HubShippingOrderStatus.STORING,
		"products": bson.M{
			"$elemMatch": bson.M{
				"sku":    binCode,
				"status": &enum.ProductStatus.STORING,
			},
		},
	})

	if hubOrderRaw.Status != common.APIStatus.Ok {
		return nil, nil, nil, &common.Error{
			Data: map[string]string{
				binCode: "Không tìm thấy đơn lấy bin đã lưu kho tại hub " + ticket.FromDepartmentCode,
			},
		}
	}

	hubOrder := hubOrderRaw.Data.([]*model.HubShippingOrder)[0]
	var product *model.Product
	// Verify product status
	for _, p := range hubOrder.Products {
		// Only scan product with status storing
		if binCode == p.SKU &&
			p.Status != nil &&
			*p.Status == enum.ProductStatus.STORING {

			product = p
			product.Status = &enum.ProductStatus.SCANNING
		}
	}

	if product == nil {
		return nil, nil, nil, &common.Error{
			Data: map[string]string{
				binCode: "Chỉ có thể tạo luân chuyển cho bin đã lưu kho",
			},
		}
	}

	// Verify shipping order, also get tpl service id to check carrier id
	// Query by ref code is much faster than query by skus
	shippingOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
		"reference_code": hubOrder.ReferenceCode,
	})

	if shippingOrderRaw.Status != common.APIStatus.Ok {
		return nil, nil, nil, &common.Error{
			Data: map[string]string{
				binCode: "Không tìm thấy đơn lấy bin" + ticket.FromDepartmentCode,
			},
		}
	}

	shippingOrder := shippingOrderRaw.Data.([]*model.ShippingOrder)[0]
	// Verify Carrier
	if !CheckItemInArray(shippingOrder.TplServiceId, toHub.ListCarrierRefId) {
		return nil, nil, nil, &common.Error{
			Data: map[string]string{
				binCode: "Nhà vận chuyển không hợp lệ",
			},
		}
	}

	if shippingOrder.CustomerCode != "" &&
		toHub.WarehouseReferenceCode != "" &&
		shippingOrder.CustomerCode != toHub.WarehouseReferenceCode {
		return nil, nil, nil, &common.Error{
			Data: map[string]string{
				binCode: "Luân chuyển sai kho",
			},
		}
	}

	// Check scan bin with status storing
	for _, so := range ticket.SOList {
		for _, p := range so.Products {
			if p.SKU == binCode &&
				(p.Status == nil || (*p.Status != enum.ProductStatus.STORING && *p.Status != enum.ProductStatus.REMOVED)) {
				return nil, nil, nil, &common.Error{
					Data: map[string]string{
						binCode: "Bin phải ở trạng thái lưu kho để có thể bàn giao",
					},
				}
			}
		}
	}

	// Check if item is in other handover ticket
	handoverTicketQuery := model.HandoverTicketDB.Query(bson.M{
		"so_list": bson.M{
			"$elemMatch": bson.M{
				"so": hubOrder.ReferenceCode,
				"status": bson.M{
					"$ne": "CANCEL",
				},
			},
		},
	}, 0, 100, nil)

	if handoverTicketQuery.Status == common.APIStatus.Ok {
		existedHandover := false
		handoverTickets := handoverTicketQuery.Data.([]*model.HandoverTicket)
		dupTicketId := 0
		dupTripId := 0
		for _, hot := range handoverTickets {
			if hot.Status != nil &&
				*hot.Status != enum.HandoverStatus.CANCEL &&
				*hot.Status != enum.HandoverStatus.COMPLETED &&
				hot.TicketID != ticket.TicketID {
				existedHandover = true
				dupTicketId = hot.TicketID
				dupTripId = int(hot.TripId)
				break
			}
		}

		if existedHandover {
			message := "Đơn bin đang nằm trong phiếu bàn giao khác: " + strconv.Itoa(dupTicketId)
			if dupTripId > 0 {
				message += " - Trip: " + strconv.Itoa(dupTripId)
			}
			return nil, nil, nil, &common.Error{
				Data: map[string]string{
					binCode: message,
				},
			}
		}
	}

	return hubOrder, shippingOrder, product, nil

}

func CancelReceiveItem(po string, ticketId int64, accountID int64) *common.APIResponse {
	if ticketId <= 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã bàn giao không được để trống.",
		}
	}

	if po == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã phiên nhận không được để trống.",
		}
	}

	getTicketResult := model.HandoverTicketDB.QueryOne(bson.M{
		"ticket_id": ticketId,
	})

	if getTicketResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy phiếu bàn giao.",
			ErrorCode: "TICKET_NOT_FOUND",
		}
	}
	existedTicket := getTicketResult.Data.([]*model.HandoverTicket)[0]
	if existedTicket.Status == nil ||
		*existedTicket.Status == enum.HandoverStatus.COMPLETED ||
		*existedTicket.Status == enum.HandoverStatus.CANCEL {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Trạng thái phiếu không hợp lệ",
		}
	}

	isFoundPO := false
	for i, existePO := range existedTicket.SOList {
		if existePO.SO == po {
			isFoundPO = true
			existePO.ReceivedQuantity = 0
			existedTicket.SOList[i] = existePO
			break
		}
	}
	if !isFoundPO {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không tìm thấy phiên nhận trong phiếu bàn giao",
		}
	}

	now := time.Now()
	versionNo := existedTicket.VersionNo
	existedTicket.LastUpdatedTime = &now
	existedTicket.UpdatedBy = accountID
	existedTicket.VersionNo = uuid.New().String()

	afterOption := options.After
	updateResult := model.HandoverTicketDB.UpdateOne(bson.M{
		"ticket_id":  existedTicket.TicketID,
		"version_no": versionNo,
	},
		existedTicket,
		&options.FindOneAndUpdateOptions{
			Upsert:         &enum.False,
			ReturnDocument: &afterOption,
		})

	if updateResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Không thể cập nhật đơn nhận trong phiếu bàn giao",
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật số của đơn nhận thành công",
		Data:    updateResult.Data.([]*model.HandoverTicket),
	}
}

func CancelHandoverTicket(ticketId int64, accountID int64) *common.APIResponse {
	if ticketId <= 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã bàn giao không được để trống.",
		}
	}

	getTicketResult := model.HandoverTicketDB.QueryOne(bson.M{
		"ticket_id": ticketId,
	})

	if getTicketResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy phiếu bàn giao.",
			ErrorCode: "TICKET_NOT_FOUND",
		}
	}

	existedTicket := getTicketResult.Data.([]*model.HandoverTicket)[0]
	if *existedTicket.Status == enum.HandoverStatus.COMPLETED ||
		*existedTicket.Status == enum.HandoverStatus.CANCEL ||
		*existedTicket.Status == enum.HandoverStatus.TRANSPORTING {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không thể cập nhật phiếu đã hoàn thành hoặc hủy.",
			ErrorCode: "TICKET_NOT_UPDATE",
		}
	}

	now := time.Now()
	existedTicket.LastUpdatedTime = &now
	versionNo := existedTicket.VersionNo
	existedTicket.UpdatedBy = accountID
	existedTicket.VersionNo = uuid.New().String()
	existedTicket.Status = &enum.HandoverStatus.CANCEL
	existedTicket.CompletedTime = &now

	logs := model.HandoverLogs{
		ActionTime:         &now,
		FromDepartmentCode: existedTicket.FromDepartmentCode,
		FromDepartmentName: existedTicket.FromDepartmentName,
		ToDepartmentCode:   existedTicket.ToDepartmentCode,
		ToDepartmentName:   existedTicket.ToDepartmentName,
		HandoverNote:       existedTicket.HandoverNote,
		Status:             existedTicket.Status,
		ExtraData: map[string]interface{}{
			"actionByAccountId": accountID,
		},
	}
	existedTicket.Logs = append(existedTicket.Logs, &logs)
	afterOption := options.After
	updateResult := model.HandoverTicketDB.UpdateOne(bson.M{
		"_id":        existedTicket.ID,
		"version_no": versionNo,
	},
		existedTicket,
		&options.FindOneAndUpdateOptions{
			Upsert:         &enum.False,
			ReturnDocument: &afterOption,
		})

	if updateResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Cập nhật phiếu bàn giao lỗi!",
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Hủy phiếu bàn giao thành công.",
		Data:    updateResult.Data.([]*model.HandoverTicket),
	}
}

func MigrateCheckIn(input model.HandoverTicket) *common.APIResponse {
	if len(input.SOList) == 0 {
		return &common.APIResponse{
			Message: "Danh sách phiên kiểm trống",
			Status:  common.APIStatus.Invalid,
		}
	}

	if input.Code == "" {
		return &common.APIResponse{
			Message: "Mã phiếu luân chuyển trống",
			Status:  common.APIStatus.Invalid,
		}
	}

	handoverTicketRaw := model.HandoverTicketDB.QueryOne(bson.M{
		"code": input.Code,
	})

	if handoverTicketRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Message: "Mã phiếu luân chuyển không hợp lệ",
			Status:  common.APIStatus.Invalid,
		}
	}

	handoverTicket := handoverTicketRaw.Data.([]*model.HandoverTicket)[0]
	checkinMap := map[string]string{}
	for _, item := range input.SOList {
		checkinMap[item.SO] = item.CheckInCode
	}

	for i, item := range handoverTicket.SOList {
		if checkinCode, ok := checkinMap[item.SO]; ok {
			handoverTicket.SOList[i].CheckInCode = checkinCode
		}
	}

	updateResult := model.HandoverTicketDB.UpdateOne(bson.M{
		"code": handoverTicket.Code,
	}, bson.M{
		"so_list": handoverTicket.SOList,
	})

	if updateResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Message: updateResult.Message,
			Status:  common.APIStatus.Error,
		}
	}

	return &common.APIResponse{
		Message: "Ok",
		Status:  common.APIStatus.Ok,
	}
}

func MigrateDoneTime() *common.APIResponse {
	var limit int64 = 200
	filter := bson.M{}

	for {
		handoverRaw := model.HandoverTicketDB.Query(filter, 0, limit, &bson.M{"_id": -1})
		if handoverRaw.Status != common.APIStatus.Ok {
			break
		}
		handoverTickets := handoverRaw.Data.([]*model.HandoverTicket)
		smallestId := handoverTickets[len(handoverTickets)-1].ID
		filter["_id"] = bson.M{
			"$lt": smallestId,
		}

		for _, handoverTicket := range handoverTickets {
			if handoverTicket.HandoverType != nil && *handoverTicket.HandoverType == enum.HandoverType.HANDOVERING {
				_ = model.HandoverTicketDB.UpdateOne(bson.M{
					"ticket_id": handoverTicket.TicketID,
				}, bson.M{
					"done_handover_time": handoverTicket.CompletedTime,
				})
				continue
			}

			if len(handoverTicket.Logs) == 0 {
				continue
			}

			for _, log := range handoverTicket.Logs {
				if log.Status != nil && *log.Status == enum.HandoverStatus.TRANSPORTING {
					_ = model.HandoverTicketDB.UpdateOne(bson.M{
						"ticket_id": handoverTicket.TicketID,
					}, bson.M{
						"done_handover_time": log.ActionTime,
					})
				}
			}
		}
	}

	return &common.APIResponse{
		Message: "Ok",
		Status:  common.APIStatus.Ok,
	}
}

func MigrateItemType(ids []int) *common.APIResponse {
	// Test some ids before running a full table scan
	if len(ids) > 0 {
		handoverTicketsRaw := model.HandoverTicketDB.Query(bson.M{
			"ticket_id": bson.M{
				"$in": ids,
			},
		}, 0, int64(len(ids)), nil)

		handoverTickets := handoverTicketsRaw.Data.([]*model.HandoverTicket)
		for _, handoverTicket := range handoverTickets {
			if handoverTicket.ItemType != nil && *handoverTicket.ItemType == enum.HandoverItemType.PO {
				continue
			}

			if len(handoverTicket.SOList) == 0 {
				_ = model.HandoverTicketDB.UpdateOne(bson.M{
					"ticket_id": handoverTicket.TicketID,
				}, bson.M{
					"item_type": &enum.HandoverItemType.SO,
				})

				continue
			}

			shippingOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
				"reference_code": handoverTicket.SOList[0].SO,
			})

			if shippingOrderRaw.Status != common.APIStatus.Ok {
				_ = model.HandoverTicketDB.UpdateOne(bson.M{
					"ticket_id": handoverTicket.TicketID,
				}, bson.M{
					"item_type": &enum.HandoverItemType.SO,
				})

				continue
			}

			shippingOrder := shippingOrderRaw.Data.([]*model.ShippingOrder)[0]
			if shippingOrder.ShippingType != nil && *shippingOrder.ShippingType == enum.ShippingOrderType.PICKUP {
				_ = model.HandoverTicketDB.UpdateOne(bson.M{
					"ticket_id": handoverTicket.TicketID,
				}, bson.M{
					"item_type": &enum.HandoverItemType.BIN,
				})
			} else {
				_ = model.HandoverTicketDB.UpdateOne(bson.M{
					"ticket_id": handoverTicket.TicketID,
				}, bson.M{
					"item_type": &enum.HandoverItemType.SO,
				})
			}

		}

		return &common.APIResponse{
			Message: "Ok",
			Status:  common.APIStatus.Ok,
		}
	}

	var limit int64 = 1000
	filter := bson.M{}
	for {
		handoverTicketsRaw := model.HandoverTicketDB.Query(filter, 0, limit, &bson.M{"_id": -1})
		if handoverTicketsRaw.Status != common.APIStatus.Ok {
			break
		}
		handoverTickets := handoverTicketsRaw.Data.([]*model.HandoverTicket)
		smallestId := handoverTickets[len(handoverTickets)-1].ID
		filter["_id"] = bson.M{
			"$lt": smallestId,
		}

		for _, handoverTicket := range handoverTickets {
			if handoverTicket.ItemType != nil && *handoverTicket.ItemType == enum.HandoverItemType.PO {
				continue
			}

			if len(handoverTicket.SOList) == 0 {
				_ = model.HandoverTicketDB.UpdateOne(bson.M{
					"ticket_id": handoverTicket.TicketID,
				}, bson.M{
					"item_type": &enum.HandoverItemType.SO,
				})

				continue
			}

			shippingOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
				"reference_code": handoverTicket.SOList[0].SO,
			})

			if shippingOrderRaw.Status != common.APIStatus.Ok {
				_ = model.HandoverTicketDB.UpdateOne(bson.M{
					"ticket_id": handoverTicket.TicketID,
				}, bson.M{
					"item_type": &enum.HandoverItemType.SO,
				})

				continue
			}

			shippingOrder := shippingOrderRaw.Data.([]*model.ShippingOrder)[0]
			if shippingOrder.ShippingType != nil && *shippingOrder.ShippingType == enum.ShippingOrderType.PICKUP {
				_ = model.HandoverTicketDB.UpdateOne(bson.M{
					"ticket_id": handoverTicket.TicketID,
				}, bson.M{
					"item_type": &enum.HandoverItemType.BIN,
				})
			} else {
				_ = model.HandoverTicketDB.UpdateOne(bson.M{
					"ticket_id": handoverTicket.TicketID,
				}, bson.M{
					"item_type": &enum.HandoverItemType.SO,
				})
			}
		}
	}

	return &common.APIResponse{
		Message: "Ok",
		Status:  common.APIStatus.Ok,
	}
}

func AddHandoverItem(input *request.HandoverItemRequest, accountID int64) *common.APIResponse {
	if input.TicketID <= 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã bàn giao không được để trống",
		}
	}

	if input.ItemType == nil ||
		!utils.CheckExistInEnum(*input.ItemType, *enum.HandoverItemType) {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Loại sản phẩm không hợp lệ",
		}
	}

	for _, trackingCode := range input.TrackingCodes {

		if isValid, _ := regexp.Match("^([^50IOZ\\W]+)$", []byte(trackingCode)); !isValid {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Mã Tracking Code không hợp lệ",
			}
		}

		shippingOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
			"tracking_code": trackingCode,
		})

		if shippingOrderRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Shipping order không tồn tại",
			}
		}

		shippingOrder := shippingOrderRaw.Data.([]*model.ShippingOrder)[0]
		input.ReferenceCodes = append(input.ReferenceCodes, shippingOrder.ReferenceCode)
	}

	for _, refCode := range input.ReferenceCodes {
		if input.ItemType != nil &&
			*input.ItemType == enum.HandoverItemType.RO {
			continue
		}

		var itemType *enum.HandoverItemTypeValue
		if strings.HasPrefix(refCode, "SO") ||
			strings.HasPrefix(refCode, "LO") ||
			strings.HasPrefix(refCode, "CO") ||
			strings.HasPrefix(refCode, "RO") {
			itemType = &enum.HandoverItemType.SO
		}

		if strings.HasPrefix(refCode, "BIN") {
			itemType = &enum.HandoverItemType.BIN
		}

		if strings.HasPrefix(refCode, "PO") || strings.HasPrefix(refCode, "PGH") {
			itemType = &enum.HandoverItemType.PO
		}

		if strings.HasPrefix(refCode, "TO") {
			itemType = &enum.HandoverItemType.TO
		}

		if strings.HasPrefix(refCode, "BMX") || strings.HasPrefix(refCode, "DH") {
			itemType = &enum.HandoverItemType.EO
		}

		if itemType == nil || *itemType != *input.ItemType {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Loại đơn không hợp lệ",
			}
		}
	}

	if *input.ItemType == enum.HandoverItemType.BIN {
		return AddBin(input, accountID)
	}

	if *input.ItemType == enum.HandoverItemType.SO ||
		*input.ItemType == enum.HandoverItemType.RO {
		return AddSo(input, accountID)
	}

	if *input.ItemType == enum.HandoverItemType.PO ||
		*input.ItemType == enum.HandoverItemType.FMPO {
		return AddPo(input, accountID)
	}

	if *input.ItemType == enum.HandoverItemType.TO {
		return AddTo(input, accountID)
	}

	if *input.ItemType == enum.HandoverItemType.EO {
		return AddEo(input, accountID)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Invalid,
		Message: "Loại sản phẩm không hợp lệ",
	}
}

func ValidateSoCode(referenceCode string, ticket *model.HandoverTicket) (
	*model.HubShippingOrder, *model.ShippingOrder, *common.Error) {
	shippingOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
		"reference_code": referenceCode,
	})

	if shippingOrderRaw.Status != common.APIStatus.Ok {
		return nil, nil, &common.Error{
			Data: map[string]string{
				referenceCode: "Không tìm thấy đơn giao hàng " + referenceCode,
			},
		}
	}

	hubOrderRaw := model.HUBShippingOrderDB.QueryOne(bson.M{
		"hub_code":       ticket.FromDepartmentCode,
		"reference_code": referenceCode,
	})

	if hubOrderRaw.Status != common.APIStatus.Ok {
		return nil, nil, &common.Error{
			Data: map[string]string{
				referenceCode: "Không tìm thấy đơn giao hàng đã lưu kho tại hub " + ticket.FromDepartmentCode,
			},
		}
	}

	hubOrder := hubOrderRaw.Data.([]*model.HubShippingOrder)[0]
	shippingOrder := shippingOrderRaw.Data.([]*model.ShippingOrder)[0]

	isReturn := IsReturnOrder(*shippingOrder)
	if isReturn &&
		ticket.ItemType != nil &&
		*ticket.ItemType == enum.HandoverItemType.SO {
		return nil, nil, &common.Error{
			Data: map[string]string{
				referenceCode: "Không thể thêm đơn trả hàng trong phiếu giao hàng",
			},
		}
	}

	if !isReturn &&
		ticket.ItemType != nil &&
		*ticket.ItemType == enum.HandoverItemType.RO {
		return nil, nil, &common.Error{
			Data: map[string]string{
				referenceCode: "Không thể thêm đơn giao hàng trong phiếu trả hàng",
			},
		}
	}

	if shippingOrder.MergeStatus != nil &&
		*shippingOrder.MergeStatus == enum.MergeStatus.WAIT_TO_MERGE &&
		!isReturn {
		whs, err := client.Services.WarehouseCoreClient.GetAllWarehouse()
		if err != nil {
			return nil, nil, &common.Error{
				Data: map[string]string{
					referenceCode: "Không thể lấy thông tin kho",
				},
			}
		}
		for _, wh := range whs {
			if wh.MainHubCode == ticket.FromDepartmentCode && wh.IsMergeDO {
				return nil, nil, &common.Error{
					Data: map[string]string{
						referenceCode: "Đơn chờ gộp không được xuất kho",
					},
				}
			}
		}
	}

	// TODO: Block return here
	if shippingOrder.Status != nil &&
		(*shippingOrder.Status == enum.TPLCallbackStatus.RETURN ||
			*shippingOrder.Status == enum.TPLCallbackStatus.RETURNING ||
			*shippingOrder.Status == enum.TPLCallbackStatus.RETURNED) &&
		shippingOrder.MergeStatus == nil {
		var deliveryOrder *response.DeliveryOrder
		var err error
		if strings.HasPrefix(referenceCode, "RONS") {
			saleOrder, getSaleOrderErr := client.Services.WarehouseCoreClient.GetSaleOrder(shippingOrder.ReferenceCode, "")
			if getSaleOrderErr != nil || saleOrder == nil {
				return nil, nil, &common.Error{
					Data: map[string]string{
						referenceCode: "Không tìm thấy đơn giao hàng ở kho",
					},
				}
			}
			deliveryOrder = &response.DeliveryOrder{
				WarehouseCode: saleOrder.WarehouseCode,
			}
		} else {
			// TODO: Use FromCustomerCode here can reduce network call
			deliveryOrder, err = client.Services.WarehouseCoreClient.GetDeliveryOrder(shippingOrder.ReferenceCode)
			if err != nil || deliveryOrder == nil {
				return nil, nil, &common.Error{
					Data: map[string]string{
						referenceCode: "Không tìm thấy đơn giao hàng ở kho",
					},
				}
			}
		}

		hubRaw := model.HubDB.QueryOne(bson.M{
			"warehouse_reference_code": deliveryOrder.WarehouseCode,
		})

		if hubRaw.Status == common.APIStatus.Ok {
			hub := hubRaw.Data.([]*model.Hub)[0]
			if hub.Code != ticket.ToDepartmentCode {
				return nil, nil, &common.Error{
					Data: map[string]string{
						referenceCode: "Luân chuyển sai kho nhận",
					},
				}
			}
		}
	}

	invalidShippingOrderStatus := []string{
		"DELIVERING",
		"CANCEL",
		"DELIVERED",
		"RETURNED",
		"COMPLETED",
		"COD_COLLECTED",
		"CREATE_FAIL",
		"INIT",
	}

	// Check shipping order status
	if utils.StringSliceContain(invalidShippingOrderStatus, string(*shippingOrder.Status)) {
		return nil, nil, &common.Error{
			Data: map[string]string{
				referenceCode: "Trạng thái đơn hàng không hợp lệ",
			},
		}
	}

	if ticket.HandoverType != nil && *ticket.HandoverType == enum.HandoverType.TRANSPORTING {
		// Verify Hub
		toHubRaw := model.HubDB.QueryOne(bson.M{
			"code": ticket.ToDepartmentCode,
		})

		if toHubRaw.Status != common.APIStatus.Ok {
			return nil, nil, &common.Error{
				Data: map[string]string{
					referenceCode: "Hub nhận luân chuyển không tồn tại",
				},
			}
		}

		toHub := toHubRaw.Data.([]*model.Hub)[0]

		// Verify Carrier
		if !CheckItemInArray(shippingOrder.TplServiceId, toHub.ListCarrierRefId) {
			return nil, nil, &common.Error{
				Data: map[string]string{
					referenceCode: "Nhà vận chuyển không hợp lệ",
				},
			}
		}

		validHubOrderStatus := []string{
			"STORING",
			"RETURN",
			"RETURNING",
		}

		if !utils.StringSliceContain(validHubOrderStatus, string(*hubOrder.Status)) {
			return nil, nil, &common.Error{
				Data: map[string]string{
					referenceCode: "Trạng thái đơn hàng tại hub không hợp lệ",
				},
			}
		}

	}

	if ticket.HandoverType != nil && *ticket.HandoverType == enum.HandoverType.HANDOVERING {
		if shippingOrder.TplCode == nil {
			return nil, nil, &common.Error{
				Data: map[string]string{
					referenceCode: "Nhà vận chuyển không hợp lệ, liên hệ admin để được hỗ trợ",
				},
			}
		}

		carrierRaw := model.CarrierDB.QueryOne(bson.M{
			"carrier_id": shippingOrder.TplServiceId,
		})

		if carrierRaw.Status != common.APIStatus.Ok {
			return nil, nil, &common.Error{
				Data: map[string]string{
					referenceCode: "Nhà vận chuyển không hợp lệ, liên hệ admin để được hỗ trợ",
				},
			}
		}

		carrier := carrierRaw.Data.([]*model.Carrier)[0]

		if string(*shippingOrder.TplCode) != ticket.TPLCode &&
			string(*carrier.CarrierCode) != ticket.TPLCode {
			return nil, nil, &common.Error{
				Data: map[string]string{
					referenceCode: "Nhà vận chuyển của đơn không trùng với nhà vận chuyển của phiểu luân chuyển",
				},
			}
		}
	}

	// Check if item is in other handover ticket
	handoverTicketQuery := model.HandoverTicketDB.Query(bson.M{
		"so_list": bson.M{
			"$elemMatch": bson.M{
				"so": referenceCode,
				"status": bson.M{
					"$ne": "CANCEL",
				},
			},
		},
	}, 0, 100, nil)

	if handoverTicketQuery.Status == common.APIStatus.Ok {
		existedHandover := false
		dupTicketId := 0
		dupTripId := 0
		handoverTickets := handoverTicketQuery.Data.([]*model.HandoverTicket)
		for _, hot := range handoverTickets {
			if hot.Status != nil &&
				*hot.Status != enum.HandoverStatus.CANCEL &&
				*hot.Status != enum.HandoverStatus.COMPLETED &&
				hot.TicketID != ticket.TicketID {
				existedHandover = true
				dupTicketId = hot.TicketID
				dupTripId = int(hot.TripId)
				break
			}
		}

		if existedHandover {
			message := "Đơn giao hàng đang nằm trong phiếu bàn giao khác: " + strconv.Itoa(dupTicketId)
			if dupTripId > 0 {
				message += " - Trip: " + strconv.Itoa(dupTripId)
			}

			return nil, nil, &common.Error{
				Data: map[string]string{
					referenceCode: message,
				},
			}
		}
	}

	// check over package
	for _, item := range ticket.SOList {
		if item.SO == referenceCode && item.ScannedQuantity+1 > item.NumPackage {
			return nil, nil, &common.Error{
				Data: map[string]string{
					referenceCode: "Quá số kiện",
				},
			}
		}
	}

	return hubOrder, shippingOrder, nil
}

func AddSo(input *request.HandoverItemRequest, accountID int64) *common.APIResponse {
	if len(input.ReferenceCodes) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Danh sách mã đơn không được để trống",
		}
	}

	ticketRaw := model.HandoverTicketDB.QueryOne(bson.M{
		"ticket_id": input.TicketID,
	})

	if ticketRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không tìm thấy phiếu bàn giao",
		}
	}

	ticket := ticketRaw.Data.([]*model.HandoverTicket)[0]
	if *ticket.Status == enum.HandoverStatus.COMPLETED ||
		*ticket.Status == enum.HandoverStatus.CANCEL ||
		*ticket.Status == enum.HandoverStatus.TRANSPORTING {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không thể cập nhật phiếu đã hoàn thành/đang luân chuyển hoặc hủy.",
			ErrorCode: "TICKET_NOT_UPDATE",
		}
	}

	// Remove duplicate so code
	input.ReferenceCodes = utils.RemoveDuplicateStr(input.ReferenceCodes)
	codeLength := len(input.ReferenceCodes)

	type result struct {
		ReferenceCode string                  `json:"referenceCode"`
		HubOrder      *model.HubShippingOrder `json:"hubOrder"`
		ShippingOrder *model.ShippingOrder    `json:"shippingOrder"`
		Err           *common.Error           `json:"err"`
	}

	type addSoResponse struct {
		Ticket  *model.HandoverTicket `json:"ticket,omitempty"`
		Err     []*common.Error       `json:"err,omitempty"`
		ValidSo []*result             `json:"validSo,omitempty"`
	}

	response := &addSoResponse{
		Err:     []*common.Error{},
		ValidSo: []*result{},
		Ticket:  nil,
	}

	if codeLength == 1 {
		hubOrder, shippingOrder, err := ValidateSoCode(input.ReferenceCodes[0], ticket)
		results := result{
			ReferenceCode: input.ReferenceCodes[0],
			HubOrder:      hubOrder,
			ShippingOrder: shippingOrder,
			Err:           err,
		}
		if err != nil {
			response.Err = append(response.Err, err)
		} else {
			response.ValidSo = append(response.ValidSo, &results)
		}

	} else {
		jobs := make(chan string, codeLength)
		results := make(chan result, codeLength)
		numberOfWorker := 2
		for i := 0; i < numberOfWorker; i++ {
			go func(jobs <-chan string, results chan<- result, ticket *model.HandoverTicket) {
				for job := range jobs {
					hubOrder, shippingOrder, err := ValidateSoCode(job, ticket)
					results <- result{ReferenceCode: job, HubOrder: hubOrder, Err: err, ShippingOrder: shippingOrder}
				}
			}(jobs, results, ticket)
		}

		for _, code := range input.ReferenceCodes {
			jobs <- code
		}
		close(jobs)

		// Gather errors
		for i := 0; i < codeLength; i++ {
			validateResult := <-results
			if validateResult.Err != nil {
				response.Err = append(response.Err, validateResult.Err)
				continue
			}
			response.ValidSo = append(response.ValidSo, &validateResult)
		}
		close(results)
	}

	if len(response.ValidSo) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Danh sách mã đơn hàng không hợp lệ",
			Data:    []*addSoResponse{response},
		}
	}

	var soListIndex = map[string]int{}
	for i := range ticket.SOList {
		soListIndex[ticket.SOList[i].SO] = i
	}

	var carrierId int64 = 0
	var carrierName string
	for _, validSo := range response.ValidSo {
		if utils.StringSliceContain(validSo.HubOrder.Tags, "FROZEN") {
			ticket.IsContainsFrozenItem = &enum.True
		}

		if index, ok := soListIndex[validSo.HubOrder.ReferenceCode]; ok {
			ticket.SOList[index].ScannedQuantity++
			if ticket.SOList[index].ScannedQuantity == validSo.HubOrder.NumPackage {
				ticket.SOList[index].Status = &enum.HandoverItemStatus.DONE
			} else {
				ticket.SOList[index].Status = &enum.HandoverItemStatus.PARTIAL
			}

			continue
		}

		// if so not exist yet, create new item
		item := model.HandoverSOItem{
			SO:              validSo.HubOrder.ReferenceCode,
			Weight:          float64(validSo.HubOrder.Weight),
			NumPackage:      validSo.HubOrder.NumPackage,
			ScannedQuantity: 1,
			TrackingCode:    validSo.HubOrder.TrackingCode,
			TplCode:         input.TplCode,
			MergeStatus:     validSo.HubOrder.MergeStatus,
			ExpiredAt:       validSo.HubOrder.ExpiredAt,
			Baskets:         validSo.HubOrder.Baskets,
			Tags:            validSo.HubOrder.Tags,
		}

		if validSo.HubOrder.NumPackage == item.ScannedQuantity {
			item.Status = &enum.HandoverItemStatus.DONE
		} else {
			item.Status = &enum.HandoverItemStatus.PARTIAL
		}

		ticket.SOList = append(ticket.SOList, item)
		carrierId = validSo.ShippingOrder.TplServiceId
		carrierName = validSo.ShippingOrder.TplName
	}

	now := time.Now()
	versionNo := ticket.VersionNo
	ticket.LastUpdatedTime = &now
	ticket.UpdatedBy = accountID
	ticket.VersionNo = uuid.New().String()
	if ticket.CarrierId == 0 {
		ticket.CarrierName = carrierName
		ticket.CarrierId = carrierId
	}

	afterOption := options.After
	updateResult := model.HandoverTicketDB.UpdateOne(bson.M{
		"ticket_id":  ticket.TicketID,
		"version_no": versionNo,
	},
		ticket,
		&options.FindOneAndUpdateOptions{
			Upsert:         &enum.False,
			ReturnDocument: &afterOption,
		})

	if updateResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Cập nhật phiếu bàn giao lỗi!",
		}
	}

	response.Ticket = updateResult.Data.([]*model.HandoverTicket)[0]
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật phiếu bàn giao thành công.",
		Data:    []*addSoResponse{response},
	}
}

func AddTo(input *request.HandoverItemRequest, accountID int64) *common.APIResponse {
	if len(input.ReferenceCodes) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Danh sách mã đơn không được để trống",
		}
	}

	ticketRaw := model.HandoverTicketDB.QueryOne(bson.M{
		"ticket_id": input.TicketID,
	})

	if ticketRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không tìm thấy phiếu bàn giao",
		}
	}

	ticket := ticketRaw.Data.([]*model.HandoverTicket)[0]
	if *ticket.Status == enum.HandoverStatus.COMPLETED ||
		*ticket.Status == enum.HandoverStatus.CANCEL ||
		*ticket.Status == enum.HandoverStatus.TRANSPORTING {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không thể cập nhật phiếu đã hoàn thành/đang luân chuyển hoặc hủy.",
			ErrorCode: "TICKET_NOT_UPDATE",
		}
	}

	// Remove duplicate to code
	input.ReferenceCodes = utils.RemoveDuplicateStr(input.ReferenceCodes)
	codeLength := len(input.ReferenceCodes)

	type result struct {
		ReferenceCode string                  `json:"referenceCode"`
		HubOrder      *model.HubShippingOrder `json:"hubOrder"`
		ShippingOrder *model.ShippingOrder    `json:"shippingOrder"`
		Err           *common.Error           `json:"err"`
	}

	type addToResponse struct {
		Ticket  *model.HandoverTicket `json:"ticket,omitempty"`
		Err     []*common.Error       `json:"err,omitempty"`
		ValidTo []*result             `json:"validTo,omitempty"`
	}

	response := &addToResponse{
		Err:     []*common.Error{},
		ValidTo: []*result{},
		Ticket:  nil,
	}

	if codeLength == 1 {
		hubOrder, shippingOrder, err := ValidateToCode(input.ReferenceCodes[0], ticket)
		results := result{
			ReferenceCode: input.ReferenceCodes[0],
			HubOrder:      hubOrder,
			ShippingOrder: shippingOrder,
			Err:           err,
		}
		if err != nil {
			response.Err = append(response.Err, err)
		} else {
			response.ValidTo = append(response.ValidTo, &results)
		}

	} else {
		jobs := make(chan string, codeLength)
		results := make(chan result, codeLength)
		numberOfWorker := 2
		for i := 0; i < numberOfWorker; i++ {
			go func(jobs <-chan string, results chan<- result, ticket *model.HandoverTicket) {
				for job := range jobs {
					hubOrder, shippingOrder, err := ValidateToCode(job, ticket)
					results <- result{
						ReferenceCode: job, HubOrder: hubOrder, Err: err, ShippingOrder: shippingOrder}
				}
			}(jobs, results, ticket)
		}

		for _, code := range input.ReferenceCodes {
			jobs <- code
		}
		close(jobs)

		// Gather errors
		for i := 0; i < codeLength; i++ {
			validateResult := <-results
			if validateResult.Err != nil {
				response.Err = append(response.Err, validateResult.Err)
				continue
			}
			response.ValidTo = append(response.ValidTo, &validateResult)
		}
		close(results)
	}

	if len(response.ValidTo) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Danh sách mã đơn hàng không hợp lệ",
			Data:    []*addToResponse{response},
		}
	}

	var toListIndex = map[string]int{}
	for i := range ticket.SOList {
		toListIndex[ticket.SOList[i].SO] = i
	}
	var carrierId int64 = 0
	var carrierName string
	for _, validTo := range response.ValidTo {
		if index, ok := toListIndex[validTo.HubOrder.ReferenceCode]; ok {
			ticket.SOList[index].ScannedQuantity++
			if ticket.SOList[index].ScannedQuantity == validTo.HubOrder.NumPackage {
				ticket.SOList[index].Status = &enum.HandoverItemStatus.DONE
			} else {
				ticket.SOList[index].Status = &enum.HandoverItemStatus.PARTIAL
			}
			continue
		}

		// if so not exist yet, create new item
		item := model.HandoverSOItem{
			SO:              validTo.HubOrder.ReferenceCode,
			Weight:          float64(validTo.HubOrder.Weight),
			NumPackage:      validTo.HubOrder.NumPackage,
			ScannedQuantity: 1,
			TrackingCode:    validTo.HubOrder.TrackingCode,
			TplCode:         input.TplCode,
		}

		if validTo.HubOrder.NumPackage == item.ScannedQuantity {
			item.Status = &enum.HandoverItemStatus.DONE
		} else {
			item.Status = &enum.HandoverItemStatus.PARTIAL
		}

		ticket.SOList = append(ticket.SOList, item)
		carrierId = validTo.ShippingOrder.TplServiceId
		carrierName = validTo.ShippingOrder.TplName
	}

	now := time.Now()
	versionNo := ticket.VersionNo
	ticket.LastUpdatedTime = &now
	ticket.UpdatedBy = accountID
	ticket.VersionNo = uuid.New().String()
	if ticket.CarrierId == 0 {
		ticket.CarrierName = carrierName
		ticket.CarrierId = carrierId
	}

	afterOption := options.After
	updateResult := model.HandoverTicketDB.UpdateOne(bson.M{
		"ticket_id":  ticket.TicketID,
		"version_no": versionNo,
	},
		ticket,
		&options.FindOneAndUpdateOptions{
			Upsert:         &enum.False,
			ReturnDocument: &afterOption,
		})

	if updateResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Cập nhật phiếu bàn giao lỗi!",
		}
	}

	response.Ticket = updateResult.Data.([]*model.HandoverTicket)[0]
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật phiếu bàn giao thành công.",
		Data:    []*addToResponse{response},
	}
}

func ValidateToCode(referenceCode string, ticket *model.HandoverTicket) (
	*model.HubShippingOrder, *model.ShippingOrder, *common.Error) {

	if ticket.HandoverType != nil &&
		*ticket.HandoverType == enum.HandoverType.HANDOVERING &&
		ticket.TPLCode == "" {
		return nil, nil, &common.Error{
			Data: map[string]string{
				referenceCode: "Mã nhà vận chuyển không được bỏ trống.",
			},
		}
	}

	shippingOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
		"reference_code": referenceCode,
	})

	if shippingOrderRaw.Status != common.APIStatus.Ok {
		return nil, nil, &common.Error{
			Data: map[string]string{
				referenceCode: "Không tìm thấy đơn giao hàng " + referenceCode,
			},
		}
	}

	shippingOrder := shippingOrderRaw.Data.([]*model.ShippingOrder)[0]
	if shippingOrder.ShippingType == nil ||
		*shippingOrder.ShippingType != enum.ShippingOrderType.INTERNAL_TRANS {
		return nil, nil, &common.Error{
			Data: map[string]string{
				referenceCode: "Loại đơn không hợp lệ",
			},
		}
	}

	if ticket.HandoverType != nil &&
		*ticket.HandoverType == enum.HandoverType.TRANSPORTING {
		// Verify Hub
		toHubRaw := model.HubDB.QueryOne(bson.M{
			"code": ticket.ToDepartmentCode,
		})

		if toHubRaw.Status != common.APIStatus.Ok {
			return nil, nil, &common.Error{
				Data: map[string]string{
					referenceCode: "Hub nhận luân chuyển không tồn tại",
				},
			}
		}

		toHub := toHubRaw.Data.([]*model.Hub)[0]
		// Verify Carrier
		if !CheckItemInArray(shippingOrder.TplServiceId, toHub.ListCarrierRefId) {
			return nil, nil, &common.Error{
				Data: map[string]string{
					referenceCode: "Nhà vận chuyển không hợp lệ",
				},
			}
		}
	}

	hubOrderRaw := model.HUBShippingOrderDB.QueryOne(bson.M{
		"hub_code":       ticket.FromDepartmentCode,
		"reference_code": referenceCode,
	})

	if hubOrderRaw.Status != common.APIStatus.Ok {
		return nil, nil, &common.Error{
			Data: map[string]string{
				referenceCode: "Không tìm thấy đơn giao hàng đã lưu kho tại hub " + ticket.FromDepartmentCode,
			},
		}
	}

	hubOrder := hubOrderRaw.Data.([]*model.HubShippingOrder)[0]
	invalidShippingOrderStatus := []string{
		"DELIVERING",
		"CANCEL",
		"DELIVERED",
		"RETURNED",
		"COMPLETED",
		"COD_COLLECTED",
		"CREATE_FAIL",
		"INIT",
	}

	// Check shipping order status
	if utils.StringSliceContain(invalidShippingOrderStatus, string(*shippingOrder.Status)) {
		return nil, nil, &common.Error{
			Data: map[string]string{
				referenceCode: "Trạng thái đơn hàng không hợp lệ",
			},
		}
	}

	if ticket.HandoverType != nil && *ticket.HandoverType == enum.HandoverType.TRANSPORTING {
		if hubOrder.Status != nil && *hubOrder.Status != enum.HubShippingOrderStatus.STORING {
			return nil, nil, &common.Error{
				Data: map[string]string{
					referenceCode: "Đơn tại hub phải ở trạng thái Lưu Kho để có thể luân chuyển",
				},
			}
		}
	}

	// Check if item is in other handover ticket
	handoverTicketQuery := model.HandoverTicketDB.Query(bson.M{
		"so_list": bson.M{
			"$elemMatch": bson.M{
				"so": referenceCode,
				"status": bson.M{
					"$ne": "CANCEL",
				},
			},
		},
	}, 0, 100, nil)

	if handoverTicketQuery.Status == common.APIStatus.Ok {
		existedHandover := false
		handoverTickets := handoverTicketQuery.Data.([]*model.HandoverTicket)
		dupTicketId := 0
		dupTripId := 0
		for _, hot := range handoverTickets {
			if hot.Status != nil &&
				*hot.Status != enum.HandoverStatus.CANCEL &&
				*hot.Status != enum.HandoverStatus.COMPLETED &&
				hot.TicketID != ticket.TicketID {
				existedHandover = true
				dupTicketId = hot.TicketID
				dupTripId = int(hot.TripId)
				break
			}
		}

		if existedHandover {
			message := "Đơn giao hàng đang nằm trong phiếu bàn giao khác: " + strconv.Itoa(dupTicketId)
			if dupTripId > 0 {
				message += " - Trip: " + strconv.Itoa(dupTripId)
			}
			return nil, nil, &common.Error{
				Data: map[string]string{
					referenceCode: message,
				},
			}
		}
	}

	// check over package
	for _, item := range ticket.SOList {
		if item.SO == referenceCode && item.ScannedQuantity+1 > item.NumPackage {
			return nil, nil, &common.Error{
				Data: map[string]string{
					referenceCode: "Quá số kiện",
				},
			}
		}
	}

	return hubOrder, shippingOrder, nil
}

func AddEo(input *request.HandoverItemRequest, accountID int64) *common.APIResponse {
	if len(input.ReferenceCodes) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Danh sách mã đơn không được để trống",
		}
	}

	ticketRaw := model.HandoverTicketDB.QueryOne(bson.M{
		"ticket_id": input.TicketID,
	})

	if ticketRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không tìm thấy phiếu bàn giao",
		}
	}

	ticket := ticketRaw.Data.([]*model.HandoverTicket)[0]
	if *ticket.Status == enum.HandoverStatus.COMPLETED ||
		*ticket.Status == enum.HandoverStatus.CANCEL ||
		*ticket.Status == enum.HandoverStatus.TRANSPORTING {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không thể cập nhật phiếu đã hoàn thành/đang luân chuyển hoặc hủy.",
			ErrorCode: "TICKET_NOT_UPDATE",
		}
	}

	// Remove duplicate so code
	input.ReferenceCodes = utils.RemoveDuplicateStr(input.ReferenceCodes)
	codeLength := len(input.ReferenceCodes)
	type result struct {
		ReferenceCode string                  `json:"referenceCode"`
		HubOrder      *model.HubShippingOrder `json:"hubOrder"`
		ShippingOrder *model.ShippingOrder    `json:"shippingOrder"`
		Err           *common.Error           `json:"err"`
	}

	type addEoResponse struct {
		Ticket  *model.HandoverTicket `json:"ticket,omitempty"`
		Err     []*common.Error       `json:"err,omitempty"`
		ValidEo []*result             `json:"validSo,omitempty"`
	}

	response := &addEoResponse{
		Err:     []*common.Error{},
		ValidEo: []*result{},
		Ticket:  nil,
	}

	if codeLength == 1 {
		hubOrder, shippingOrder, err := ValidateEoCode(input.ReferenceCodes[0], ticket)
		results := result{
			ReferenceCode: input.ReferenceCodes[0],
			HubOrder:      hubOrder,
			ShippingOrder: shippingOrder,
			Err:           err,
		}
		if err != nil {
			response.Err = append(response.Err, err)
		} else {
			response.ValidEo = append(response.ValidEo, &results)
		}
	} else {
		jobs := make(chan string, codeLength)
		results := make(chan result, codeLength)
		numberOfWorker := 2
		for i := 0; i < numberOfWorker; i++ {
			go func(jobs <-chan string, results chan<- result, ticket *model.HandoverTicket) {
				for job := range jobs {
					hubOrder, shippingOrder, err := ValidateEoCode(job, ticket)
					results <- result{ReferenceCode: job, HubOrder: hubOrder, Err: err, ShippingOrder: shippingOrder}
				}
			}(jobs, results, ticket)
		}

		for _, code := range input.ReferenceCodes {
			jobs <- code
		}
		close(jobs)

		// Gather errors
		for i := 0; i < codeLength; i++ {
			validateResult := <-results
			if validateResult.Err != nil {
				response.Err = append(response.Err, validateResult.Err)
				continue
			}
			response.ValidEo = append(response.ValidEo, &validateResult)
		}
		close(results)
	}

	if len(response.ValidEo) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Danh sách mã đơn hàng không hợp lệ",
			Data:    []*addEoResponse{response},
		}
	}
	var soListIndex = map[string]int{}
	for i := range ticket.SOList {
		soListIndex[ticket.SOList[i].SO] = i
	}

	var carrierId int64 = 0
	var carrierName string
	for _, validEo := range response.ValidEo {
		if index, ok := soListIndex[validEo.HubOrder.ReferenceCode]; ok {
			ticket.SOList[index].ScannedQuantity++
			if ticket.SOList[index].ScannedQuantity == validEo.HubOrder.NumPackage {
				ticket.SOList[index].Status = &enum.HandoverItemStatus.DONE
			} else {
				ticket.SOList[index].Status = &enum.HandoverItemStatus.PARTIAL
			}

			continue
		}

		// if so not exist yet, create new item
		item := model.HandoverSOItem{
			SO:              validEo.HubOrder.ReferenceCode,
			Weight:          float64(validEo.HubOrder.Weight),
			NumPackage:      validEo.HubOrder.NumPackage,
			ScannedQuantity: 1,
			TrackingCode:    validEo.HubOrder.TrackingCode,
			TplCode:         input.TplCode,
			MergeStatus:     validEo.HubOrder.MergeStatus,
			ExpiredAt:       validEo.HubOrder.ExpiredAt,
		}

		if validEo.HubOrder.NumPackage == item.ScannedQuantity {
			item.Status = &enum.HandoverItemStatus.DONE
		} else {
			item.Status = &enum.HandoverItemStatus.PARTIAL
		}

		ticket.SOList = append(ticket.SOList, item)
		carrierId = validEo.ShippingOrder.TplServiceId
		carrierName = validEo.ShippingOrder.TplName
	}

	now := time.Now()
	versionNo := ticket.VersionNo
	ticket.LastUpdatedTime = &now
	ticket.UpdatedBy = accountID
	ticket.VersionNo = uuid.New().String()
	if ticket.CarrierId == 0 {
		ticket.CarrierName = carrierName
		ticket.CarrierId = carrierId
	}
	afterOption := options.After
	updateResult := model.HandoverTicketDB.UpdateOne(bson.M{
		"ticket_id":  ticket.TicketID,
		"version_no": versionNo,
	},
		ticket,
		&options.FindOneAndUpdateOptions{
			Upsert:         &enum.False,
			ReturnDocument: &afterOption,
		})

	if updateResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Cập nhật phiếu bàn giao lỗi!",
		}
	}

	response.Ticket = updateResult.Data.([]*model.HandoverTicket)[0]
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật phiếu bàn giao thành công.",
		Data:    []*addEoResponse{response},
	}
}

func ValidateEoCode(referenceCode string, ticket *model.HandoverTicket) (
	*model.HubShippingOrder, *model.ShippingOrder, *common.Error) {
	shippingOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
		"reference_code": referenceCode,
	})

	if shippingOrderRaw.Status != common.APIStatus.Ok {
		return nil, nil, &common.Error{
			Data: map[string]string{
				referenceCode: "Không tìm thấy đơn giao hàng " + referenceCode,
			},
		}
	}

	hubOrderRaw := model.HUBShippingOrderDB.QueryOne(bson.M{
		"hub_code":       ticket.FromDepartmentCode,
		"reference_code": referenceCode,
	})

	if hubOrderRaw.Status != common.APIStatus.Ok {
		return nil, nil, &common.Error{
			Data: map[string]string{
				referenceCode: "Không tìm thấy đơn giao hàng đã lưu kho tại hub " + ticket.FromDepartmentCode,
			},
		}
	}

	hubOrder := hubOrderRaw.Data.([]*model.HubShippingOrder)[0]
	shippingOrder := shippingOrderRaw.Data.([]*model.ShippingOrder)[0]
	invalidShippingOrderStatus := []string{
		"DELIVERING",
		"CANCEL",
		"DELIVERED",
		"RETURNED",
		"COMPLETED",
		"COD_COLLECTED",
		"CREATE_FAIL",
		"INIT",
	}
	if utils.StringSliceContain(invalidShippingOrderStatus, string(*shippingOrder.Status)) {
		return nil, nil, &common.Error{
			Data: map[string]string{
				referenceCode: "Trạng thái đơn hàng không hợp lệ",
			},
		}
	}
	if ticket.HandoverType == nil || *ticket.HandoverType != enum.HandoverType.TRANSPORTING {
		return nil, nil, &common.Error{
			Data: map[string]string{
				referenceCode: "Loại phiếu không hợp lệ",
			},
		}
	}

	// Verify Hub
	toHubRaw := model.HubDB.QueryOne(bson.M{
		"code": ticket.ToDepartmentCode,
	})

	if toHubRaw.Status != common.APIStatus.Ok {
		return nil, nil, &common.Error{
			Data: map[string]string{
				referenceCode: "Hub nhận luân chuyển không tồn tại",
			},
		}
	}

	toHub := toHubRaw.Data.([]*model.Hub)[0]
	if !CheckItemInArray(shippingOrder.TplServiceId, toHub.ListCarrierRefId) {
		return nil, nil, &common.Error{
			Data: map[string]string{
				referenceCode: "Nhà vận chuyển không hợp lệ",
			},
		}
	}
	validHubOrderStatus := []string{
		"STORING",
		"RETURN",
		"RETURNING",
	}

	if !utils.StringSliceContain(validHubOrderStatus, string(*hubOrder.Status)) {
		return nil, nil, &common.Error{
			Data: map[string]string{
				referenceCode: "Trạng thái đơn hàng tại hub không hợp lệ",
			},
		}
	}

	handoverTicketQuery := model.HandoverTicketDB.Query(bson.M{
		"so_list": bson.M{
			"$elemMatch": bson.M{
				"so": referenceCode,
				"status": bson.M{
					"$ne": "CANCEL",
				},
			},
		},
	}, 0, 100, nil)

	if handoverTicketQuery.Status == common.APIStatus.Ok {
		existedHandover := false
		handoverTickets := handoverTicketQuery.Data.([]*model.HandoverTicket)
		dupTicketId := 0
		dupTripId := 0
		for _, hot := range handoverTickets {
			if hot.Status != nil &&
				*hot.Status != enum.HandoverStatus.CANCEL &&
				*hot.Status != enum.HandoverStatus.COMPLETED &&
				hot.TicketID != ticket.TicketID {
				existedHandover = true
				dupTicketId = hot.TicketID
				dupTripId = int(hot.TripId)
				break
			}
		}

		if existedHandover {
			message := "Đơn giao hàng đang nằm trong phiếu bàn giao khác: " + strconv.Itoa(dupTicketId)
			if dupTripId > 0 {
				message += " - Trip: " + strconv.Itoa(dupTripId)
			}
			return nil, nil, &common.Error{
				Data: map[string]string{
					referenceCode: message,
				},
			}
		}
	}

	for _, item := range ticket.SOList {
		if item.SO == referenceCode && item.ScannedQuantity+1 > item.NumPackage {
			return nil, nil, &common.Error{
				Data: map[string]string{
					referenceCode: "Quá số kiện",
				},
			}
		}
	}

	return hubOrder, shippingOrder, nil
}
