package model

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type District struct {
	Dictionary       []string `json:"dictionary"`
	Code             string   `json:"code"`
	Name             string   `json:"name"`
	Level            string   `json:"level"`
	IsSupport        bool     `json:"isSupport"`
	ProvinceCode     string   `json:"provinceCode"`
	ProvinceName     string   `json:"provinceName"`
	FeeValue         *float64 `json:"feeValue,omitempty"`
	FeeProvinceValue *float64 `json:"feeProvinceValue,omitempty"`
	FeeRegionValue   *float64 `json:"feeRegionValue,omitempty"`
	RegionCode       *string  `json:"regionCode,omitempty"`
	RegionName       *string  `json:"regionName,omitempty"`
}

type DistrictMapping struct {
	Code         string   `json:"code" bson:"code,omitempty"`
	Name         string   `json:"name" bson:"name,omitempty"`
	ProvinceCode string   `json:"provinceCode" bson:"province_code,omitempty"`
	VTPId        int64    `json:"vtpId" bson:"vtp_id,omitempty"`
	NhatTinId    int64    `json:"nhattinId" bson:"nhattin_id,omitempty"`
	VNPId        string   `json:"vnpId" bson:"vnp_id,omitempty"`
	GHNId        int64    `json:"ghnId" bson:"ghn_id,omitempty"`
	Dictionary   []string `json:"dictionary" bson:"dictionary,omitempty"`
}

type DistrictGHN struct {
	ProvinceId    int64    `json:"ProvinceId"`
	DistrictId    int64    `json:"DistrictId"`
	DistrictName  string   `json:"DistrictName"`
	NameExtension []string `json:"NameExtension"`
}

type DistrictNhatTin struct {
	Id           int64  `json:"id"`
	ProvinceName string `json:"province_name"`
	DistrictName string `json:"district_name"`
}

type DistrictVTP struct {
	DistrictId   int64  `json:"DISTRICT_ID"`
	DistrictName string `json:"DISTRICT_NAME"`
	ProvinceId   int64  `json:"PROVINCE_ID"`
}

type DistrictVNP struct {
	Name         string `json:"TenQuanHuyen"`
	Code         string `json:"MaQuanHuyen"`
	ProvinceCode string `json:"MaTinhThanh"`
	ProvinceName string `json:"TenTinhThanh"`
}

// DistrictMappingDB is an instance db
var DistrictMappingDB = &db.Instance{
	ColName:        "district_mapping",
	TemplateObject: &DistrictMapping{},
}

// InitDistrictMappingModel is func init model district
func InitDistrictMappingModel(s *mongo.Database) {
	DistrictMappingDB.ApplyDatabase(s)

	t := true
	_ = DistrictMappingDB.CreateIndex(bson.D{
		primitive.E{Key: "name", Value: 1},
		primitive.E{Key: "code", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
		Unique:     &t,
	})

	DistrictMappingDB.CreateIndex(bson.D{
		{"code", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	DistrictMappingDB.CreateIndex(bson.D{
		{"dictionary", 1},
		{"province_code", 1},
	}, &options.IndexOptions{
		Background: &t,
	})
}
