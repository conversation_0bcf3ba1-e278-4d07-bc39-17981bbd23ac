package action

import (
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action/consumer/customer"
	hub_shipping_order "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action/consumer/hub-shipping-order"
	shippingOrderQueue "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action/consumer/shipping-order"
	shipping_order "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action/consumer/shipping-order"
	sync_data "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action/consumer/tpl-callback"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/shipping"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/response"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// BookShippingService func
func BookShippingService(input *request.BookShipping, createdBy int64) *common.APIResponse {
	current := time.Now()

	var err error
	if validate(input.WarehouseCode) != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Mã kho không hợp lệ",
		}
	}

	if validate(input.SO) != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Mã SO không hợp lệ",
		}
	}

	isAvailable := utils.CheckApiLockAvailable(input.SO, "BookShippingService")
	if !isAvailable {
		return &common.APIResponse{
			Status:  common.APIStatus.Existed,
			Message: "Thử lại sau vài giây",
		}
	}

	defer model.CacheDB.Delete(bson.M{
		"key":  input.SO,
		"type": "BookShippingService",
	})

	if validate(input.Weight) != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Khối lượng không hợp lệ",
		}
	}

	if validate(input.NbOfPackages) != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Số kiện hàng không hợp lệ",
		}
	}

	if validate(input.CarrierId) != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Mã nhà vận chuyển không hợp lệ",
		}
	}

	shippingOrderDB := model.ShippingOrderDB.QueryOne(&model.ShippingOrder{
		ReferenceCode: input.SO,
	})

	if shippingOrderDB.Status == common.APIStatus.Ok {
		shippingOrder := shippingOrderDB.Data.([]*model.ShippingOrder)[0]

		if *shippingOrder.Status != enum.TPLCallbackStatus.CANCEL &&
			*shippingOrder.Status != enum.TPLCallbackStatus.CANCEL_DELIVERY &&
			*shippingOrder.Status != enum.TPLCallbackStatus.DELIVERY_FAIL &&
			*shippingOrder.Status != enum.TPLCallbackStatus.RETURNED &&
			*shippingOrder.Status != enum.TPLCallbackStatus.INIT &&
			*shippingOrder.Status != enum.TPLCallbackStatus.CREATE_FAIL {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Đơn hàng đã được đặt cho nhà vận chuyển: " + shippingOrder.TplName,
			}
		}

		if strings.HasPrefix(shippingOrder.ReferenceCode, "RONS") &&
			*shippingOrder.Status == enum.TPLCallbackStatus.RETURNING {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không thể đặt vận chuyển cho đơn RONS khi đang trả hàng, checkin vào kho trước rồi thử lại",
			}
		}
	}

	var saleOrder *model.SaleOrder
	saleOrder, err = client.Services.WarehouseCoreClient.GetSaleOrder(input.SO, input.WarehouseCode)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: err.Error(),
		}
	}

	if saleOrder.Status != nil &&
		*saleOrder.Status == enum.SaleOrderStatus.CANCEL {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Đơn hàng đã bị hủy",
		}
	}

	if saleOrder.CustomerInfos == nil || saleOrder.CustomerInfos.Delivery == nil {
		err = fmt.Errorf("Không tìm thấy thông tin khách hàng của đơn hàng " + input.SO)
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: err.Error(),
		}
	}

	if saleOrder.CODAmount < 0 && *saleOrder.PaymentMethod == enum.PaymentMethod.COD {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Phí thu hộ không hợp lệ " + input.SO,
			ErrorCode: "COD_INVALID",
		}
	}

	if shipping.TplShippingClient == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Dịch vụ book vận chuyển chưa được tạo",
		}
	}

	carriers := model.CarrierDB.QueryOne(&model.Carrier{CarrierId: input.CarrierId})
	if carriers.Status != common.APIStatus.Ok || carriers.Data == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy thông tin dịch vụ vận chuyển",
			ErrorCode: "CARRIER_INFO_ERROR",
		}
	}

	carrierInfo := carriers.Data.([]*model.Carrier)[0]
	if carrierInfo.Active != nil && !*carrierInfo.Active {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   carrierInfo.CarrierName + " Chưa sẵn sàng để đặt",
			ErrorCode: "CARRIER_DOES_NOT_ACTIVE",
		}
	}

	if carrierInfo.ExtraData == nil && !*carrierInfo.IsInternal {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy thông tin dịch vụ vận chuyển",
			ErrorCode: "CARRIER_INFO_ERROR",
		}
	}

	if carrierInfo.OverCOD <= saleOrder.CODAmount && *saleOrder.PaymentMethod == enum.PaymentMethod.COD {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "COD vượt quá hạn mức: " + strconv.Itoa(int(carrierInfo.OverCOD)),
			ErrorCode: "OVER_COD",
		}
	}

	warehouse, err := client.Services.WarehouseCoreClient.GetWarehouse(input.WarehouseCode)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Không tìm thấy thông tin kho " + input.WarehouseCode,
		}
	}

	// TODO: Only book delivery from warehouse, improve this to book delivery from hub
	resultQueryHub := model.HubDB.QueryOne(bson.M{
		"warehouse_reference_code": input.WarehouseCode,
	})

	if resultQueryHub.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Không tìm thấy thông tin hub nguồn",
		}
	}
	hub := resultQueryHub.Data.([]*model.Hub)[0]
	// First mile hub code
	input.FromHubCode = hub.Code

	pickupAddress := carrierInfo.PickupAddress
	if pickupAddress == nil {
		pickupAddress = &model.Address{}
		pickupAddress.ProvinceName = warehouse.ProvinceName
		pickupAddress.DistrictName = warehouse.DistrictName
		pickupAddress.WardName = warehouse.WardName
		pickupAddress.ProvinceCode = warehouse.ProvinceCode
		pickupAddress.DistrictCode = warehouse.DistrictCode
		pickupAddress.WardCode = warehouse.WardCode
		pickupAddress.Name = warehouse.Name
		pickupAddress.Address = warehouse.Address
		pickupAddress.Phone = conf.Config.Supporter["delivery"].Phone
	}

	bookingAddress := &model.Address{
		Code:         warehouse.Code,
		Name:         warehouse.Name,
		Address:      warehouse.Address,
		Phone:        conf.Config.Supporter["delivery"].Phone,
		WardName:     warehouse.WardName,
		DistrictName: warehouse.DistrictName,
		ProvinceName: warehouse.ProvinceName,
		WardCode:     warehouse.WardCode,
		DistrictCode: warehouse.DistrictCode,
		ProvinceCode: warehouse.ProvinceCode,
	}

	if input.DeliveryNote == "" {
		input.DeliveryNote = "Giao hàng từ thứ 2 - thứ 7 giờ hành chính. Cho khách kiểm hàng. Liên hệ shop khi không giao được hàng, không tự ý hủy đơn. "
	}

	// Init book shipping
	shippingOrder, err := createShippingOrder(input, carrierInfo, saleOrder, int(createdBy), bookingAddress)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Tạo thông tin book vận chuyển không thành công : " + err.Error(),
		}
	}

	createShippingOrder := model.ShippingOrderDB.Upsert(&model.ShippingOrder{
		ReferenceCode: input.SO,
	}, shippingOrder)

	if createShippingOrder.Status != common.APIStatus.Ok {
		return createShippingOrder
	}

	err = shipping.ChangeInfoCarrier(carrierInfo)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: err.Error(),
		}
	}

	var trackingInfos []*model.ShippingInfo
	var trackingInfo *model.ShippingInfo
	needCalculateShippingFee := saleOrder.Type == "RETURN" && !saleOrder.CustomerInfos.Delivery.IsDropOffAtWarehouse
	if *carrierInfo.IsInternal {
		trackingInfo, err = Internal.BookShipping(&shippingOrder, carrierInfo, saleOrder.WarehouseCode, needCalculateShippingFee)
	} else {
		if needCalculateShippingFee && shippingOrder.CustomerInfo == nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không tìm thấy thông tin khách hàng",
			}
		}
		customerRaw := model.CustomerDB.QueryOne(bson.M{
			"code": shippingOrder.CustomerInfo.Code,
		})
		if customerRaw.Status == common.APIStatus.Ok {
			customer := customerRaw.Data.([]*model.Customer)[0]
			if customer.PreferPaymentMethod != nil &&
				*customer.PreferPaymentMethod == enum.PreferPaymentMethod.DEBT {
				shippingOrder.FeeCollectMethod = &enum.FeeCollectMethod.DEBT
			}
		}
		// book shipping service
		switch *carrierInfo.ParentCode {
		case enum.Partner.SNAPPY:
			trackingInfo, err = Snappy.BookShipping(input, saleOrder, carrierInfo)
			break
		case enum.Partner.AHAMOVE:
			trackingInfo, err = Ahamove.BookShipping(input, saleOrder, carrierInfo, pickupAddress)
			break
		case enum.Partner.GHTK:
			trackingInfo, err = GHTK.BookShipping(input, saleOrder, carrierInfo, pickupAddress, shippingOrder.FeeCollectMethod)
			break
		case enum.Partner.NHAT_TIN:
			trackingInfo, err = NT.BookShipping(input, saleOrder, carrierInfo, pickupAddress)
			break
		case enum.Partner.GHN:
			trackingInfo, err = GHN.BookShipping(input, saleOrder, carrierInfo)
			break
		case enum.Partner.VIETTEL_POST:
			trackingInfo, err = VTP.BookShipping(input, saleOrder, carrierInfo, pickupAddress, shippingOrder.FeeCollectMethod)
			break
		case enum.Partner.VNPOST:
			trackingInfos, err = VNP.BookShipping(input, saleOrder, carrierInfo, pickupAddress)
			break
		default:
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Không tìm thấy thông tin nhà vận chuyển hợp lệ",
			}
		}
	}

	if err != nil {
		if strings.Contains(err.Error(), "timeout") {
			checkUpdate := model.ShippingOrderDB.QueryOne(&model.ShippingOrder{ReferenceCode: shippingOrder.ReferenceCode})
			if checkUpdate.Status == common.APIStatus.Ok {
				currentShippingOrder := checkUpdate.Data.([]*model.ShippingOrder)[0]

				resultQueryHub := model.HubDB.QueryOne(bson.M{
					"warehouse_reference_code": input.WarehouseCode,
				})

				if resultQueryHub.Status == common.APIStatus.Ok {
					hub := resultQueryHub.Data.([]*model.Hub)[0]
					_ = createHubShippingOrder(&shippingOrder, hub, input.WarehouseCode)
					currentShippingOrder.CurrentHub = hub.Code
				}

				configLeadTime, _ := utils.ParseLeadTimeConfig(string(*carrierInfo.ParentCode), bookingAddress, &model.Address{
					Name:         currentShippingOrder.CustomerName,
					Address:      currentShippingOrder.CustomerShippingAddress,
					Phone:        currentShippingOrder.CustomerPhone,
					Email:        currentShippingOrder.CustomerEmail,
					WardName:     currentShippingOrder.CustomerWardName,
					DistrictName: currentShippingOrder.CustomerDistrictName,
					ProvinceName: currentShippingOrder.CustomerProvinceName,
					WardCode:     currentShippingOrder.CustomerWardCode,
					DistrictCode: currentShippingOrder.CustomerDistrictCode,
					ProvinceCode: currentShippingOrder.CustomerProvinceCode,
				}, enum.LeadTimeConfigType.PICKING)

				if configLeadTime != nil {
					leadTimePicking := current.Add(time.Second * time.Duration(configLeadTime.CommitmentTime))
					currentShippingOrder.EstimatePickingTime = leadTimePicking.Unix()
				}

				_ = model.ShippingOrderDB.UpdateOne(&model.ShippingOrder{ReferenceCode: currentShippingOrder.ReferenceCode}, currentShippingOrder)

				if *currentShippingOrder.Status != enum.TPLCallbackStatus.INIT {
					return &common.APIResponse{
						Status:  common.APIStatus.Existed,
						Message: "Vui lòng tải lại trang",
					}
				}
			}
		}

		shippingOrder.Status = &enum.TPLCallbackStatus.CREATE_FAIL
		updateShippingOrder := model.ShippingOrderDB.UpdateOne(&model.ShippingOrder{ReferenceCode: shippingOrder.ReferenceCode}, shippingOrder)
		if updateShippingOrder.Status != common.APIStatus.Ok {
			_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
				CmdType: "ERROR_UPDATE_SHIPPING_ORDER",
				Title:   "Update shipping order when booking shipping " + shippingOrder.ReferenceCode,
				Message: updateShippingOrder.Message,
			})
		}

		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: err.Error(),
		}
	}

	if len(trackingInfos) == 0 {
		for i := 0; i < int(input.NbOfPackages); i++ {
			trackingInfos = append(trackingInfos, trackingInfo)
		}
	}

	if len(trackingInfos) == 0 {
		shippingOrder.Status = &enum.TPLCallbackStatus.CREATE_FAIL
		updateShippingOrder := model.ShippingOrderDB.UpdateOne(&model.ShippingOrder{ReferenceCode: shippingOrder.ReferenceCode}, shippingOrder)
		if updateShippingOrder.Status != common.APIStatus.Ok {
			_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
				CmdType: "ERROR_UPDATE_SHIPPING_ORDER",
				Title:   "Update shipping order when booking shipping " + shippingOrder.ReferenceCode,
				Message: updateShippingOrder.Message,
			})
		}

		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Thông tin của vận đơn mới không hợp lệ",
			ErrorCode: "TRACKING_INFO_EMPTY",
		}
	}

	trackingInfo = trackingInfos[len(trackingInfos)-1]
	shippingOrder.TrackingCode = trackingInfo.TrackingNumber
	shippingOrder.Status = &enum.TPLCallbackStatus.READY_TO_PICK
	shippingOrder.ExtraInfo = trackingInfo.ExtraInfo
	shippingOrder.Distance = trackingInfo.Distance
	shippingOrder.FeeAmount = trackingInfo.FeeAmount
	shippingOrder.VatAmount = trackingInfo.VatAmount

	// Sender chỉ trả phí nếu có
	if shippingOrder.FeeCollectMethod != nil && *shippingOrder.FeeCollectMethod == enum.FeeCollectMethod.SENDER_PAY {
		shippingOrder.FeeSenderAmount = &shippingOrder.FeeAmount
		shippingOrder.TotalCollectSenderAmount = &shippingOrder.FeeAmount
	}

	// Receiver LUÔN LUÔN phải trả COD đơn hàng
	shippingOrder.TotalCollectReceiverAmount = &shippingOrder.CODAmount
	// Nếu xác định được Receiver trả phí vận chuyển thì công thêm phí vận chuyển vào tổng thu người nhận
	if shippingOrder.FeeCollectMethod != nil && *shippingOrder.FeeCollectMethod == enum.FeeCollectMethod.RECEIVER_PAY {
		shippingOrder.FeeReceiverAmount = &shippingOrder.FeeAmount
		totalReceiverAmount := shippingOrder.CODAmount + shippingOrder.FeeAmount

		if saleOrder.Type != "" && saleOrder.Type == "RETURN" {
			totalReceiverAmount += shippingOrder.VatAmount
			shippingOrder.FeeAmount = totalReceiverAmount
		}
		shippingOrder.TotalCollectReceiverAmount = &totalReceiverAmount
	}

	if shippingOrder.FeeCollectMethod != nil && *shippingOrder.FeeCollectMethod == enum.FeeCollectMethod.DEBT {
		shippingOrder.FeeDebtAmount = &shippingOrder.FeeAmount
		shippingOrder.TotalDebtAmount = &shippingOrder.FeeAmount
	}

	if input.NbOfPackages > 1 && *carrierInfo.ParentCode == enum.Partner.VNPOST {
		var extraInfos []string
		for _, v := range trackingInfos {
			extraInfos = append(extraInfos, v.ExtraInfo["order_id"].(string))
		}
		shippingOrder.ExtraInfo["order_id"] = strings.Join(extraInfos, ",")
	}

	if shippingOrder.CustomerProvinceName != "" {
		provinces, err := client.Services.WarehouseCoreClient.GetProvince(shippingOrder.CustomerProvinceName)
		if err != nil {
			log.Println("Get province err ", shippingOrder.CustomerProvinceName, err)
		}

		if len(provinces) > 0 {
			shippingOrder.CustomerProvinceCode = provinces[0].Code
		}
	}

	if shippingOrder.CustomerDistrictName != "" {
		var districts []*model.District
		districts, err = client.Services.WarehouseCoreClient.GetDistrictByProvinceCode(shippingOrder.CustomerProvinceCode, shippingOrder.CustomerDistrictName)
		if err != nil {
			log.Println("Get district err ", shippingOrder.CustomerDistrictName, err)
		}

		if len(districts) > 0 {
			shippingOrder.CustomerDistrictCode = districts[0].Code
		}
	}

	createHubOrderErr := createHubShippingOrder(&shippingOrder, hub, input.WarehouseCode)
	if createHubOrderErr != nil {
		shippingOrderJson, _ := json.Marshal(shippingOrder)
		_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
			CmdType: "FAIL_TO_CREATE_HUB_ORDER",
			Title:   "Shipping order: " + string(shippingOrderJson) + "||" + "Error: " + createHubOrderErr.Error(),
			Message: "Không thể tạo hub order khi book shipping",
		})
	}

	// New lead time config
	commitmentTime, getConfigLeadTimeErr := GetCommitmentTime(request.GetAvailableLeadTimeRequest{
		ShippingOrderType: &enum.ShippingOrderType.DELIVERY,
		FromHub:           hub.Code,
		CarrierCode:       string(*carrierInfo.CarrierCode),
		ProvinceCode:      shippingOrder.CustomerProvinceCode,
		DistrictCode:      shippingOrder.CustomerDistrictCode,
		WardCode:          shippingOrder.CustomerWardCode,
	})
	if getConfigLeadTimeErr == nil && commitmentTime > 0 {
		trackingTime := current
		if input.DonePackTime > 0 {
			trackingTime = time.Unix(input.DonePackTime, 0)
		}

		deliveryLeadTime := trackingTime.Add(time.Second * time.Duration(commitmentTime))
		shippingOrder.DeliveryLeadTime = &deliveryLeadTime
	}

	shippingOrder.CurrentHub = hub.Code
	shippingOrder.Scope = []string{hub.Code}

	// Cập nhật lại fee amount nếu đổi nhà vận chuyển và fee của lần book trước khác fee của lần book sau
	if shippingOrderDB.Status == common.APIStatus.Ok {
		existShippingOrder := shippingOrderDB.Data.([]*model.ShippingOrder)[0]
		if existShippingOrder != nil && existShippingOrder.FeeAmount != shippingOrder.FeeAmount {
			updateShippingOrder := model.ShippingOrderDB.UpdateOne(
				bson.M{"reference_code": shippingOrder.ReferenceCode},
				bson.M{"fee_amount": shippingOrder.FeeAmount},
			)

			updateHubOrder := model.HUBShippingOrderDB.UpdateOne(
				bson.M{
					"reference_code": shippingOrder.ReferenceCode,
					"hub_code":       shippingOrder.CurrentHub,
				},
				bson.M{"fee_amount": shippingOrder.FeeAmount},
			)

			if updateShippingOrder.Status != common.APIStatus.Ok || updateHubOrder.Status != common.APIStatus.Ok {
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "ERROR_UPDATE_FEE",
					Title:   "There was an error when update fee " + shippingOrder.ReferenceCode,
					Message: "Update shipping order message: " + updateShippingOrder.Message + ", update hub order message: " + updateHubOrder.Message,
				})
			}
		}
	}

	updateShippingOrder := model.ShippingOrderDB.UpdateOne(&model.ShippingOrder{ReferenceCode: shippingOrder.ReferenceCode}, shippingOrder)
	if updateShippingOrder.Status != common.APIStatus.Ok {
		_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
			CmdType: "ERROR_UPDATE_SHIPPING_ORDER",
			Title:   "Update shipping order when booking shipping " + shippingOrder.ReferenceCode,
			Message: updateShippingOrder.Message,
		})
	}

	// init callback ready_to_pick
	if !*carrierInfo.IsInternal {
		callbackModel := request.Callback{
			SO:            input.SO,
			CreatedSource: carrierInfo.ParentCode,
			WarehouseCode: input.WarehouseCode,
			Status:        &enum.TPLCallbackStatus.READY_TO_PICK,
			Weight:        input.Weight,
			TPLCode:       trackingInfo.TrackingNumber,
			TotalFee:      trackingInfo.FeeAmount,
			COD:           trackingInfo.CODAmount,
			ActionTime:    &current,
			StatusName:    "Tạo mới vận đơn",
			ExtraCallback: map[string]interface{}{
				"shippingInfo": trackingInfos,
			},
			ExternalTPLName: carrierInfo.CarrierName,
			TPLStatus:       string(enum.TPLCallbackStatus.READY_TO_PICK),
			TPLStatusName:   "Tạo mới vận đơn",
			NumPackage:      input.NbOfPackages,
		}

		// Set nhầm thì gán với nvc cha luôn
		if callbackModel.CreatedSource == nil || *callbackModel.CreatedSource == "" {
			callbackModel.CreatedSource = carrierInfo.CarrierCode
		}

		err = client.Services.TplCallbackClient.CreateCallback(callbackModel)
		if err != nil {
			log.Println("Can not update callback ", input.SO)
		}
	}

	if shippingOrder.CustomerInfo != nil && shippingOrder.CustomerInfo.Code != "" {
		createCustomerErr := customer.PushCreateCustomer(model.Customer{
			Code:         shippingOrder.CustomerInfo.Code,
			Name:         shippingOrder.CustomerInfo.Name,
			Phone:        shippingOrder.CustomerInfo.Phone,
			CustomerType: shippingOrder.CustomerInfo.CustomerType,
		}, shippingOrder.CustomerInfo.Code)
		if createCustomerErr != nil {
			log.Println("Can not create customer: ", shippingOrder.CustomerCode)
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Book vận chuyển thành công cho đơn hàng : " + input.SO,
		Data:    trackingInfos,
	}
}

// BookOnWheel func
func BookOnWheel(input *request.BookShipping, createdBy int64) *common.APIResponse {
	var err error
	if len(input.ListSo) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Mã SO không hợp lệ",
		}
	}

	// Check carrier
	carriers := model.CarrierDB.QueryOne(&model.Carrier{
		CarrierId: input.CarrierId,
	})

	if carriers.Status != common.APIStatus.Ok || carriers.Data == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Không tìm thấy thông tin dịch vụ vận chuyển",
		}
	}

	carrierInfo := carriers.Data.([]*model.Carrier)[0]
	if carrierInfo.Active != nil && !*carrierInfo.Active {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: carrierInfo.CarrierName + " Chưa sẵn sàng để đặt",
		}
	}

	if carrierInfo.IsInternal == nil || (carrierInfo.ExtraData == nil && !*carrierInfo.IsInternal) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy thông tin dịch vụ vận chuyển",
			ErrorCode: "CARRIER_INFO_ERROR",
		}
	}

	// Verify HUB...
	resultQueryHub := model.HubDB.QueryOne(bson.M{
		"code": input.HubCode,
	})

	if resultQueryHub.Status != common.APIStatus.Ok || resultQueryHub.Data == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không tìm thấy thông tin hub: " + input.HubCode,
		}
	}

	hubInfo := resultQueryHub.Data.([]*model.Hub)[0]

	var pickupAddress model.Address
	pickupAddress.ProvinceName = hubInfo.Address.ProvinceName
	pickupAddress.DistrictName = hubInfo.Address.DistrictName
	pickupAddress.WardName = hubInfo.Address.WardName
	pickupAddress.Name = hubInfo.Address.Name
	pickupAddress.Address = hubInfo.Address.Address
	pickupAddress.Phone = hubInfo.Address.Phone

	resultQueryHubOrder := model.HUBShippingOrderDB.Query(bson.M{
		"reference_code": bson.M{
			"$in": input.ListSo,
		},
		"hub_code": input.HubCode,
	}, 0, int64(len(input.ListSo)), nil)

	if resultQueryHubOrder.Status != common.APIStatus.Ok || resultQueryHubOrder.Data == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Không tìm thấy thông tin phiếu giao hàng của hub",
		}
	}

	hubOrders := resultQueryHubOrder.Data.([]*model.HubShippingOrder)
	var listSaleOrderError []string
	for _, hubOrder := range hubOrders {
		if !CheckItemInArray(hubOrder.ReferenceCode, input.ListSo) {
			listSaleOrderError = append(listSaleOrderError, hubOrder.ReferenceCode)
		}
	}

	if len(listSaleOrderError) > 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Không tìm thấy thông tin của phiếu giao hàng ở hub: " + strings.Join(listSaleOrderError, ","),
		}
	}

	resultQueryShippingOrder := model.ShippingOrderDB.Query(bson.M{
		"reference_code": bson.M{
			"$in": input.ListSo,
		},
	}, 0, int64(len(input.ListSo)), nil)

	if resultQueryShippingOrder.Status != common.APIStatus.Ok || resultQueryShippingOrder.Data == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Không tìm thấy thông tin phiếu giao hàng",
		}
	}

	shippingOrders := resultQueryShippingOrder.Data.([]*model.ShippingOrder)

	for _, shippingOrder := range shippingOrders {
		if !CheckItemInArray(shippingOrder.ReferenceCode, input.ListSo) {
			listSaleOrderError = append(listSaleOrderError, shippingOrder.ReferenceCode)
		}
	}

	if len(listSaleOrderError) > 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Không tìm thấy thông tin phiếu giao của đơn hàng: " + strings.Join(listSaleOrderError, ","),
		}
	}

	if input.DeliveryNote == "" {
		input.DeliveryNote = "Giao hàng từ thứ 2 - thứ 7 giờ hành chính. Cho khách kiểm hàng. Liên hệ shop khi không giao được hàng, không tự ý hủy đơn. "
	}

	err = shipping.ChangeInfoCarrier(carrierInfo)

	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: err.Error(),
		}
	}

	// Booking hub shipping order...
	var trackingInfo *model.ShippingInfo
	trackingInfo, err = Ahamove.BookOnWheel(input, hubOrders, carrierInfo, &pickupAddress)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: err.Error(),
		}
	}

	for _, shippingOrder := range shippingOrders {
		current := time.Now()
		callbackModel := request.Callback{
			SO:          shippingOrder.ReferenceCode,
			TPLCode:     trackingInfo.TrackingNumber,
			TotalFee:    trackingInfo.FeeAmount,
			DeliverName: input.SupplierName,
			DeliverID:   int64(input.SupplierId),
			Status:      &enum.TPLCallbackStatus.STORING,
			COD:         trackingInfo.CODAmount,
			ActionTime:  &current,
			ExtraCallback: map[string]interface{}{
				"shippingInfo": []*model.ShippingInfo{trackingInfo},
			},
			ExternalTPLName: carrierInfo.CarrierName,
			HubCode:         input.HubCode,
		}

		_ = sync_data.PushCreateTPLCallbackQueue(callbackModel, callbackModel.SO)
		_ = hub_shipping_order.PushUpdateHubShippingOrderQueue(&request.UpdateHubOrder{
			ReferenceCode:  shippingOrder.ReferenceCode,
			DriverID:       int64(input.SupplierId),
			DriverName:     input.SupplierName,
			Status:         &enum.HubShippingOrderStatus.WAIT_TO_DELIVERY,
			TrackingNumber: trackingInfo.TrackingNumber,
			HubCode:        input.HubCode,
		}, shippingOrder.ReferenceCode)

		shippingOrder.TplServiceId = carrierInfo.CarrierId
		shippingOrder.TplName = carrierInfo.CarrierName
		shippingOrder.TplCode = carrierInfo.ParentCode
		shippingOrder.ExtraInfo = trackingInfo.ExtraInfo
		shippingOrder.CurrentHub = input.HubCode

		updateShippingOrder := model.ShippingOrderDB.UpdateOne(&model.ShippingOrder{ReferenceCode: shippingOrder.ReferenceCode}, shippingOrder)
		if updateShippingOrder.Status != common.APIStatus.Ok {
			_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
				CmdType: "ERROR_UPDATE_SHIPPING_ORDER",
				Title:   "Update shipping order when booking onWheel " + shippingOrder.ReferenceCode,
				Message: updateShippingOrder.Message,
			})
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Book vận chuyển thành công cho đơn hàng",
	}
}

// CancelShippingService func
func CancelShippingService(input *request.CancelShippingService) *common.APIResponse {
	shippingOrderQuery := model.ShippingOrderDB.QueryOne(&model.ShippingOrder{
		ReferenceCode: input.SO,
	})

	if shippingOrderQuery.Status != common.APIStatus.Ok || shippingOrderQuery.Data == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy thông tin shipping_order",
			ErrorCode: "SHIPPING_ORDER_NOT_FOUND",
		}
	}

	shippingOrder := shippingOrderQuery.Data.([]*model.ShippingOrder)[0]

	if shippingOrder.Status != nil && *shippingOrder.Status == enum.TPLCallbackStatus.CANCEL {
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Hủy vận chuyển thành công.",
		}
	}

	if shippingOrder.Status != nil {
		var err error
		switch *shippingOrder.Status {
		case enum.TPLCallbackStatus.PICKED:
			err = fmt.Errorf("Không thể hủy đơn. Nhà vận chuyển đã lấy hàng.")
			break
		case enum.TPLCallbackStatus.TRANSPORTING:
			err = fmt.Errorf("Không thể hủy đơn. Nhà vận chuyển đã lấy hàng")
			break
		case enum.TPLCallbackStatus.STORING:
			err = fmt.Errorf("Không thể hủy đơn. Nhà vận chuyển đã lấy hàng")
			break
		case enum.TPLCallbackStatus.DELIVERING:
			err = fmt.Errorf("Không thể hủy đơn đang giao hàng.")
			break
		case enum.TPLCallbackStatus.DELIVERED:
			err = fmt.Errorf("Không thể hủy đơn đã giao hàng.")
			break
		case enum.TPLCallbackStatus.COMPLETED:
			err = fmt.Errorf("Không thể hủy đơn đã đối soát.")
			break
		case enum.TPLCallbackStatus.RETURNED:
			err = fmt.Errorf("Không thể hủy đơn đã trả hàng.")
			break
		case enum.TPLCallbackStatus.CANCEL_DELIVERY:
			err = fmt.Errorf("Không thể hủy đơn đã hủy giao hàng.")
			break
		}

		if err != nil {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   err.Error(),
				ErrorCode: "CANCEL_SHIPPING_ORDER_NOT_ALLOW",
			}
		}
	}

	handoverTicketQuery := model.HandoverTicketDB.Query(bson.M{
		"so_list.so": input.SO,
		"status": bson.M{
			"$nin": []string{"CANCEL", "COMPLETED"},
		},
	}, 0, 100, nil)

	if handoverTicketQuery.Status == common.APIStatus.Ok {
		existedHandover := false
		handoverTickets := handoverTicketQuery.Data.([]*model.HandoverTicket)
		for _, ticket := range handoverTickets {
			for _, item := range ticket.SOList {
				if item.SO == input.SO &&
					item.Status != nil &&
					*item.Status != enum.HandoverItemStatus.CANCEL {
					existedHandover = true
					break
				}
			}
		}

		if existedHandover {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Đơn hàng đang được bàn giao, không thể hủy giao hàng.",
				ErrorCode: "EXISTED_HANDOVER_TICKET",
			}
		}
	}

	carriers := model.CarrierDB.Query(&model.Carrier{
		CarrierId: shippingOrder.TplServiceId,
	}, 0, 100, nil)

	if carriers.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy thông tin nhà vận chuyển",
			ErrorCode: "GET_CARRIER_ERROR",
		}
	}

	carrierModel := carriers.Data.([]*model.Carrier)[0]
	var err error

	if carrierModel.IsInternal == nil || (carrierModel.ExtraData == nil && !*carrierModel.IsInternal) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy thông tin dịch vụ vận chuyển",
			ErrorCode: "CARRIER_INFO_ERROR",
		}
	}

	err = shipping.ChangeInfoCarrier(carrierModel)

	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Error(),
		}
	}

	if carrierModel.IsInternal != nil && !*carrierModel.IsInternal {
		switch *carrierModel.ParentCode {
		case enum.Partner.SNAPPY:
			// call snappy cancel
			cancelSnappyRequest := request.CancelSnappyRequest{
				TrackingId: shippingOrder.TrackingCode,
				Note:       "cancel",
			}
			err = shipping.TplShippingClient.SnappyClient.CancelSnappy(cancelSnappyRequest, carrierModel)
			break
		case enum.Partner.GHTK:
			// call giao hàng tiết kiệm cancel
			err = shipping.TplShippingClient.GHTKClient.CancelGHTK(input.SO)
			break
		case enum.Partner.AHAMOVE:
			// call ahamove cancel
			cancelAhamoveRequest := &request.CancelAhamoveRequest{
				Token:   carrierModel.ExtraData.AccessToken,
				OrderId: shippingOrder.TrackingCode,
				Comment: "cancel",
			}
			err = shipping.TplShippingClient.AhamoveClient.CancelAhamove(cancelAhamoveRequest)
			break
		case enum.Partner.NHAT_TIN:
			// call Nhất Tín cancel
			cancelNhatTinRequest := request.CancelNTRequest{
				ListDoCode: []string{shippingOrder.TrackingCode},
			}
			err = shipping.TplShippingClient.NhatTinClient.CancelTrackingNhatTin(cancelNhatTinRequest)
			break
		case enum.Partner.GHN:
			// call Giao Hàng Nhanh cancel
			var orderCodes []string
			orderCodes = append(orderCodes, shippingOrder.TrackingCode)
			err = shipping.TplShippingClient.GHNClient.CancelGHN(orderCodes)
			break
		case enum.Partner.VIETTEL_POST:
			// call Viettel Post cancel
			trackingCode, _ := strconv.Atoi(shippingOrder.TrackingCode)
			cancelVTPRequest := request.UpdateTrackingVTP{
				Type:        &enum.VTPUpdateTrackingStatus.CANCEL_ORDER,
				OrderNumber: int64(trackingCode),
				Note:        "cancel",
			}
			err = shipping.TplShippingClient.VTPClient.CancelVTP(cancelVTPRequest)
			break
		case enum.Partner.VNPOST:
			if shippingOrder.ExtraInfo == nil {
				err = fmt.Errorf("Không tìm thấy thông tin đơn hàng của: ", carrierModel.ParentCode)
				break
			}
			if shippingOrder.NumPackage < 2 {
				cancelVNPRequest := request.CancelVNPost{
					OrderId: shippingOrder.ExtraInfo["order_id"].(string),
				}
				err = shipping.TplShippingClient.VNPClient.CancelTracking(cancelVNPRequest)
			} else {
				extraInfos := shippingOrder.ExtraInfo["order_id"].(string)
				vnpOrderIds := strings.Split(extraInfos, ",")
				if len(vnpOrderIds) == 0 {
					err = fmt.Errorf("Không tìm thấy thông tin đơn hàng của: ", carrierModel.ParentCode)
					break
				}

				for _, orderId := range vnpOrderIds {
					cancelVNPRequest := request.CancelVNPost{
						OrderId: orderId,
					}
					err = shipping.TplShippingClient.VNPClient.CancelTracking(cancelVNPRequest)
				}
			}
			break
		default:
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Không tìm thấy thông tin nhà vận chuyển",
				ErrorCode: "CANCEL_SHIPPING_ORDER_NOT_ALLOW",
			}
		}
	}

	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "CANCEL_SHIPPING_ORDER_NOT_ALLOW",
		}
	}

	shippingOrder.Status = &enum.TPLCallbackStatus.CANCEL
	updateShippingOrder := model.ShippingOrderDB.UpdateOne(&model.ShippingOrder{ReferenceCode: shippingOrder.ReferenceCode, TrackingCode: shippingOrder.TrackingCode}, shippingOrder)
	if updateShippingOrder.Status != common.APIStatus.Ok {
		_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
			CmdType: "ERROR_UPDATE_SHIPPING_ORDER",
			Title:   "Update shipping order when cancel booking " + shippingOrder.ReferenceCode,
			Message: updateShippingOrder.Message,
		})
	}

	actionTime := time.Now()
	createCallbackRequest := request.Callback{
		SO:              input.SO,
		StatusName:      "Hủy vận chuyển",
		TPLStatusName:   "Hủy vận chuyển",
		ExternalTPLName: carrierModel.CarrierName,
		CreatedSource:   carrierModel.ParentCode,
		ActionTime:      &actionTime,
		Status:          &enum.TPLCallbackStatus.CANCEL,
		TPLStatus:       string(enum.TPLCallbackStatus.CANCEL),
	}
	err = client.Services.TplCallbackClient.CreateCallback(createCallbackRequest)
	if err != nil {
		log.Println("Can not create callback cancel ", input.SO)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Hủy nhà vận chuyển thành công",
	}
}

// GetAvailableShippingCarrier func
func GetAvailableShippingCarrier(input request.GetShippingService) (response *common.APIResponse) {
	var conditionCustomers = getCustomerCarrier(input.CustomerID, input.PackageCount)
	carrierIds := CheckCondition(conditionCustomers, input.PackageCount, input.Weight)
	if len(carrierIds) == 0 {
		if input.ProvinceCode == "" {
			if input.Province == "" {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   "Không được để trống tên Tỉnh",
					ErrorCode: "PROVINCE_REQUIRED",
				}
			}

			provinces, err := client.Services.WarehouseCoreClient.GetProvince(input.Province)
			if err != nil || len(provinces) == 0 {
				return &common.APIResponse{
					Status:  common.APIStatus.Error,
					Message: "Không tìm thấy thông tin " + input.Province,
				}
			}
			input.ProvinceCode = provinces[0].Code
		}

		configs := model.ConfigDB.QueryOne(bson.M{
			"province_code":  input.ProvinceCode,
			"warehouse_code": input.WarehouseCode,
		})

		if configs.Status != common.APIStatus.Ok || configs.Data == nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Không tìm thấy thông tin thiết lập cho " + input.Province,
			}
		}

		var routing []*model.Available
		configInfo := configs.Data.([]*model.Config)[0]
		routing = configInfo.Carriers
		if input.DistrictCode == "" {
			districts, err := client.Services.WarehouseCoreClient.GetDistrictByProvinceCode(input.ProvinceCode, input.District)
			if err != nil {
				return &common.APIResponse{
					Status:  common.APIStatus.Error,
					Message: "Không tìm thấy thông tin " + input.District,
				}
			}
			input.DistrictCode = districts[0].Code
		}

		var wardsRouting []*model.WardRoute
		if len(configInfo.DistrictRoute) > 0 {
			for _, configDistrict := range configInfo.DistrictRoute {
				if configDistrict.DistrictCode == input.DistrictCode {
					routing = configDistrict.Available
					wardsRouting = configDistrict.WardRoute
				}
			}
		}

		if input.WardCode == "" {
			wards, _ := client.Services.WarehouseCoreClient.GetWard(input.Ward, input.WardCode, input.DistrictCode, "")
			if len(wards) > 0 {
				input.WardCode = wards[0].Code
			}
		}

		if wardsRouting != nil && len(wardsRouting) > 0 {
			for _, w := range wardsRouting {
				if w.WardCode == input.WardCode {
					routing = w.Available
				}
			}
		}

		// lấy danh sách nhà vận chuyển thoải điểu kiện weight,package
		carrierIds = CheckCondition(routing, input.PackageCount, input.Weight)
	}

	if len(carrierIds) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Không tìm thấy thông tin nhà vận chuyển phù hợp.",
		}
	}

	if strings.HasPrefix(input.SaleOrderCode, "RO") {
		// Get Sale order check type
		saleOrder, err := client.Services.WarehouseCoreClient.GetSaleOrder(input.SaleOrderCode, input.WarehouseCode)
		if err != nil || saleOrder == nil {
			return &common.APIResponse{
				Status:  common.APIStatus.NotFound,
				Message: "Không tìm thấy thông tin nhà vận chuyển phù hợp.",
			}
		}

		if saleOrder.CustomerInfos.Delivery.IsDropOffAtWarehouse {
			config := model.ConfigLogisticDB.QueryOne(bson.M{"key": "LOGISTIC"})
			if config.Status != common.APIStatus.Ok {
				return &common.APIResponse{
					Status:  common.APIStatus.NotFound,
					Message: "Không tìm thấy thông tin nhà vận chuyển phù hợp.",
				}
			}
			prefixConfigRaw, ok := config.Data.([]*model.ConfigLogistic)[0].Configs["FORCE_PREFIX_ORDER_TO_CARRIER"]
			if ok {
				prefixConfigArr, parseArrOk := prefixConfigRaw.(bson.A)
				if parseArrOk {
					prefixConfig := []model.PrefixConfig{}
					var builder strings.Builder
					for i, v := range prefixConfigArr {
						if str, ok := v.(string); ok {
							builder.WriteString(str)
							if i == len(prefixConfigArr)-1 {
								continue
							}
							builder.WriteString(",")
						}
					}
					err := json.Unmarshal([]byte(builder.String()), &prefixConfig)
					if err == nil {
						for _, prefix := range prefixConfig {
							if strings.HasPrefix(input.SaleOrderCode, prefix.Prefix) {
								carrierIds = prefix.CarrierIds
								break
							}
						}
					}
				}
			}
		} else {
			if saleOrder.HubCodeReceive != "" {
				hubRaw := model.HubDB.QueryOne(bson.M{"code": saleOrder.HubCodeReceive})
				if hubRaw.Status == common.APIStatus.Ok {
					hub := hubRaw.Data.([]*model.Hub)[0]
					carrierIds = []int64{hub.DefaultCarrierId}
				}
			}
		}

	}

	carriers := model.CarrierDB.Query(bson.M{
		"carrier_id": bson.M{
			"$in": carrierIds,
		},
	}, 0, 1000, nil)

	return carriers
}

func GetShippingServiceFee(input *request.GetShippingService) (response *common.APIResponse) {
	carriers := input.Carriers
	if len(input.Carriers) == 0 {
		carrierResult := GetAvailableShippingCarrier(*input)
		if carrierResult.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:    common.APIStatus.NotFound,
				ErrorCode: "NOT_FOUND_CARRIER",
				Message:   "Không tìm thấy nhà vận chuyển phù hợp",
			}
		}
		carriers = carrierResult.Data.([]*model.Carrier)
	}

	type result struct {
		CarrierID   int64              `json:"carrierCode"`
		CarrierName string             `json:"carrierName"`
		ParentCode  *enum.PartnerValue `json:"parentCode"`
		ShippingFee float64            `json:"shippingFee"`
		IsInternal  *bool              `json:"isInternal"`
	}

	var resultData []result
	wg := new(sync.WaitGroup)
	for _, carrier := range carriers {
		wg.Add(1)
		go func(c *model.Carrier, i *request.GetShippingService, group *sync.WaitGroup) {
			defer group.Done()
			res := result{
				CarrierID:   c.CarrierId,
				CarrierName: c.CarrierName,
				ParentCode:  c.ParentCode,
				IsInternal:  c.IsInternal,
			}
			if c.IsInternal != nil && *c.IsInternal {
				res.ShippingFee = calculateShippingFee(i.PackageCount, i.Weight, i.FromProvinceCode, i.ProvinceCode, i.IsReceiveAtLMHub)
			} else {
				_ = shipping.ChangeInfoCarrier(c)
				switch *c.ParentCode {
				case enum.Partner.GHTK:
					res.ShippingFee = GHTK.GetShippingFee(i)
					break
				case enum.Partner.VIETTEL_POST:
					res.ShippingFee = VTP.GetShippingFee(i, c)
					break
				}
			}
			resultData = append(resultData, res)
		}(carrier, input, wg)
	}
	wg.Wait()
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Get Shipping Service Fee Successfully",
		Data:    resultData,
	}
}

func GetShippingServiceFeeV2(input *request.GetShippingService) (response *common.APIResponse) {
	bookReq := &request.BookShippingOrder{
		From: &model.Address{
			ProvinceCode: input.FromProvinceCode,
			DistrictCode: input.FromDistrictCode,
			WardCode:     input.FromWardCode,
			ProvinceName: input.FromProvinceName,
			DistrictName: input.FromDistrictName,
			WardName:     input.FromWardName,
			Name:         input.FromName,
			Phone:        input.FromPhone,
			Address:      input.FromAddress,
		},
		To: &model.Address{
			ProvinceCode: input.ToProvinceCode,
			DistrictCode: input.ToDistrictCode,
			WardCode:     input.ToWardCode,
			ProvinceName: input.ToProvinceName,
			DistrictName: input.ToDistrictName,
			WardName:     input.ToWardName,
			Name:         input.ToName,
			Phone:        input.ToPhone,
			Address:      input.ToAddress,
		},
		CustomerInfo: &model.Customer{
			Code: input.CustomerCode,
		},
		NumPackage: input.PackageCount,
		Weight:     input.Weight,
		OPM:        input.OPM,
		Medship:    input.Medship,
	}
	carriers := FindSuitableCarrierForEO(bookReq)
	if len(carriers) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Chưa có nhà vận chuyển nào hộ trợ trong khu vực này",
		}
	}
	for _, carrier := range carriers {
		input.Carriers = append(input.Carriers, carrier)
	}

	type result struct {
		CarrierCode string  `json:"carrierCode"`
		CarrierName string  `json:"carrierName"`
		CarrierId   int64   `json:"carrierId"`
		ShippingFee float64 `json:"shippingFee"`
		IsInternal  *bool   `json:"isInternal"`
	}
	var resultData []result

	for _, carrier := range carriers {
		if carrier.IsInternal != nil && *carrier.IsInternal {
			var fmHub *model.Hub
			var lmHub *model.Hub
			var configFeeCode = conf.Config.DefaultEOPickNDeliCode
			fmHub = FindNearestHubOfAddressV2(*bookReq.From)
			if fmHub == nil {
				continue
			}
			lmHub = FindNearestHubOfAddressV2(*bookReq.To)
			if lmHub == nil {
				continue
			}
			if input.CustomerCode != "" && input.CustomerType != nil {
				var checkConfigId int64 = 0
				customerRaw := model.CustomerDB.QueryOne(bson.M{
					"code":          input.CustomerCode,
					"customer_type": input.CustomerType,
				})
				if customerRaw.Status != common.APIStatus.Ok {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   "Không tìm thấy thông tin khách hàng",
						ErrorCode: "NOT_FOUND_CUSTOMER",
					}
				}
				existedCus := customerRaw.Data.([]*model.Customer)[0]
				if existedCus.AppliedFees != nil {
					for _, fee := range *existedCus.AppliedFees {
						if fee.ConfigFeeType == enum.ConfigFeeType.PICK_N_DELIVERY {
							checkConfigId = fee.ConfigFeeId
						}
					}
				}
				if checkConfigId != 0 {
					configRaw := model.ConfigFeeDB.QueryOne(bson.M{
						"config_id": checkConfigId,
					})
					if configRaw.Status == common.APIStatus.Ok {
						configFeeCode = configRaw.Data.([]*model.ConfigFee)[0].Code
					}
				}
			}
			configFeeRaw := model.ConfigFeeDB.QueryOne(bson.M{
				"code": configFeeCode,
			})
			if configFeeRaw.Status != common.APIStatus.Ok {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   "Không tìm thấy cấu hình phí vận chuyển",
					ErrorCode: "NOT_FOUND_CONFIG_FEE",
				}
			}
			configFee := configFeeRaw.Data.([]*model.ConfigFee)[0]
			bookReq.FromHubCode = fmHub.Code
			bookReq.ToHubCode = lmHub.Code

			bookReq.FromAddress = bookReq.From
			bookReq.ToAddress = bookReq.To
			feeAmount, err := CalculateOrderFee(*bookReq, configFee)
			if err != nil {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   "Không tìm thấy cấu hình phí vận chuyển hợp lệ cho đơn",
					ErrorCode: "NOT_FOUND_VALID_CONFIG_FEE",
				}
			}

			resultData = append(resultData, result{
				CarrierCode: string(*carrier.CarrierCode),
				CarrierName: carrier.CarrierName,
				CarrierId:   fmHub.DefaultCarrierId,
				IsInternal:  carrier.IsInternal,
				ShippingFee: feeAmount,
			})
		}

		if carrier.IsInternal != nil && !*carrier.IsInternal {
			res := result{
				CarrierId:   carrier.CarrierId,
				CarrierName: carrier.CarrierName,
				CarrierCode: string(*carrier.CarrierCode),
				IsInternal:  carrier.IsInternal,
			}
			_ = shipping.ChangeInfoCarrier(carrier)
			switch *carrier.ParentCode {
			case enum.Partner.GHTK:
				if input.FromProvinceName == "" ||
					input.FromDistrictName == "" ||
					input.ToProvinceName == "" ||
					input.ToDistrictName == "" {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   "Không tìm thấy thông tin địa chỉ",
						ErrorCode: "NOT_FOUND_ADDRESS",
					}
				}
				input.Province = input.ToProvinceName
				input.District = input.ToDistrictName
				input.Ward = input.ToWardName

				input.FromProvince = input.FromProvinceName
				input.FromDistrict = input.FromDistrictName
				input.FromWard = input.FromWardName

				res.ShippingFee = GHTK.GetShippingFee(input)
				if res.ShippingFee <= 0 {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   "Không tìm thấy thông tin phí vận chuyển",
						ErrorCode: "NOT_FOUND_SHIPPING_FEE",
					}
				}
				resultData = append(resultData, res)
			case enum.Partner.AHAMOVE:
				bookAhamoveReq := request.BookAhamoveRequest{
					Token:         carrier.ExtraData.AccessToken,
					ServiceId:     carrier.Service,
					PaymentMethod: carrier.PaymentMethod,
					Path: []*request.AhamovePath{
						{
							Address: bookReq.From.Address + ", " +
								bookReq.From.WardName + ", " +
								bookReq.From.DistrictName + ", " +
								bookReq.From.ProvinceName + ", Vietnam",
							Name:   bookReq.From.Name,
							Mobile: bookReq.From.Phone,
						},
						{
							Address: bookReq.To.Address + ", " + bookReq.To.WardName + "," + bookReq.To.DistrictName + ", " + bookReq.To.ProvinceName + ", Vietnam",
							Name:    bookReq.To.Name,
							Mobile:  bookReq.To.Phone,
							// TODO: Change here to support multiple method
							COD:        int64(bookReq.CODAmount),
							RequirePod: true,
							PodType:    "photo",
						},
					},
				}
				fee, err := shipping.TplShippingClient.AhamoveClient.EstimateOrderFee(
					bookAhamoveReq,
					carrier,
				)
				if err == nil {
					res.ShippingFee = fee.FeeAmount
				}
				resultData = append(resultData, res)
			default:
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Không tìm thấy thông tin nhà vận chuyển",
				}
			}
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Get Shipping Service Fee Successfully",
		Data:    resultData,
	}

}

// CheckCondition func
func CheckCondition(carriers []*model.Available, numPackage int64, weight float64) (carrierIds []int64) {
	for _, carrier := range carriers {
		isAccept := false
		for _, condition := range carrier.Conditions {
			isAccept = false
			switch *condition.TypeCondition {
			case enum.TypeCondition.PACKAGE:
				switch condition.Method {
				case ">":
					if numPackage > condition.NumPackage {
						isAccept = true
					}
					break
				case ">=":
					if numPackage >= condition.NumPackage {
						isAccept = true
					}
					break
				case "<":
					if numPackage < condition.NumPackage {
						isAccept = true
					}
					break
				case "<=":
					if numPackage <= condition.NumPackage {
						isAccept = true
					}
					break
				case "=":
					if condition.NumPackage == numPackage {
						isAccept = true
					}
					break
				}
				break
			case enum.TypeCondition.WEIGHT:
				switch condition.Method {
				case ">":
					if weight > condition.Weight {
						isAccept = true
					}
					break
				case ">=":
					if weight >= condition.Weight {
						isAccept = true
					}
					break
				case "<":
					if weight < condition.Weight {
						isAccept = true
					}
					break
				case "<=":
					if weight <= condition.Weight {
						isAccept = true
					}
					break
				case "=":
					if condition.Weight == weight {
						isAccept = true
					}
					break
				}
			}
			if !isAccept {
				break
			}
		}
		if isAccept {
			carrierIds = append(carrierIds, carrier.CarrierId)
		}
	}
	return
}

func getCustomerCarrier(customerId, numPackage int64) (customerConfig []*model.Available) {
	if customerId == 0 {
		return
	}

	results := model.CustomerCarriersDB.QueryOne(bson.M{
		"customer_id": customerId,
		"is_active":   true,
	})

	if results.Status != common.APIStatus.Ok {
		return
	}

	if results.Data == nil {
		return
	}

	return results.Data.([]*model.CustomerCarrier)[0].Carriers
}

func createShippingOrder(input *request.BookShipping, carrierInfo *model.Carrier, saleOrder *model.SaleOrder, createdBy int, pickupAddress *model.Address) (shippingOrder model.ShippingOrder, err error) {
	current := time.Now()
	if input.ShippingType == nil || *input.ShippingType == "" {
		input.ShippingType = &enum.ShippingOrderType.DELIVERY
	}
	shippingOrder = model.ShippingOrder{
		ReferenceCode:           input.SO,
		TplCode:                 carrierInfo.ParentCode,
		TplName:                 carrierInfo.CarrierName,
		TplServiceId:            carrierInfo.CarrierId,
		Status:                  &enum.TPLCallbackStatus.INIT,
		ShippingType:            input.ShippingType,
		CustomerName:            saleOrder.CustomerInfos.Delivery.Name,
		CustomerCode:            strconv.Itoa(saleOrder.CustomerInfos.Delivery.Code),
		CustomerShippingAddress: saleOrder.CustomerInfos.Delivery.Address,
		CustomerPhone:           saleOrder.CustomerInfos.Delivery.Phone,
		CustomerEmail:           saleOrder.CustomerInfos.Delivery.Email,
		CustomerWardName:        saleOrder.CustomerInfos.Delivery.Ward,
		CustomerWardCode:        saleOrder.CustomerInfos.Delivery.WardCode,
		CustomerDistrictName:    saleOrder.CustomerInfos.Delivery.District,
		CustomerDistrictCode:    saleOrder.CustomerInfos.Delivery.DistrictCode,
		CustomerProvinceName:    saleOrder.CustomerInfos.Delivery.Province,
		CustomerProvinceCode:    saleOrder.CustomerInfos.Delivery.ProvinceCode,

		FromCustomerPhone:   pickupAddress.Phone,
		FromCustomerEmail:   pickupAddress.Email,
		FromCustomerAddress: pickupAddress.Address,
		FromCustomerName:    pickupAddress.Name,
		FromProvinceCode:    pickupAddress.ProvinceCode,
		FromProvinceName:    pickupAddress.ProvinceName,
		FromDistrictCode:    pickupAddress.DistrictCode,
		FromDistrictName:    pickupAddress.DistrictName,
		FromWardCode:        pickupAddress.WardCode,
		FromWardName:        pickupAddress.WardName,
		FromLatitude:        pickupAddress.Latitude,
		FromLongitude:       pickupAddress.Longitude,
		FromCustomerCode:    pickupAddress.Code,
		BookingTime:         current.Unix(),
		DonePackTime:        input.DonePackTime,
		PrivateNote:         input.PickupNote,
		Note:                input.DeliveryNote,

		Height:     input.Height,
		Width:      input.Width,
		Length:     input.Length,
		Weight:     input.Weight,
		NumPackage: input.NbOfPackages,

		TotalAmount:      saleOrder.TotalAmount,
		CODAmount:        saleOrder.CODAmount,
		DeliveryAmount:   saleOrder.DeliveryAmount,
		PaymentMethod:    saleOrder.PaymentMethod,
		FeeCollectMethod: &enum.FeeCollectMethod.SENDER_PAY,

		VersionNo:   uuid.New().String(),
		ActionTime:  current.Unix(),
		CreatedBy:   strconv.Itoa(createdBy),
		CreatedTime: &current,

		// Received at LM
		IsReceiveAtLMHub:    saleOrder.CustomerInfos.Delivery.IsReceiveAtLMHub,
		IsDropOffAtFMHub:    saleOrder.CustomerInfos.Delivery.IsDropOffAtWarehouse,
		Tags:                saleOrder.Tags,
		ParentReferenceCode: input.ParentReferenceCode,
		MergeStatus:         input.MergeStatus,

		CustomerInfo: &model.Customer{
			Code:         strconv.Itoa(saleOrder.CustomerInfos.Delivery.Code),
			Name:         saleOrder.CustomerInfos.Delivery.Name,
			Phone:        saleOrder.CustomerInfos.Delivery.Phone,
			CustomerType: &enum.CustomerType.INTERNAL,
		},

		Baskets: input.Baskets,
	}

	if saleOrder.CustomerInfos.Delivery.VerificationCode != "" {
		shippingOrder.VerificationCode = saleOrder.CustomerInfos.Delivery.VerificationCode
		shippingOrder.NeedVerifyReceiver = true
	}

	// Nếu đơn hàng return thì fee giao hàng sẽ thu của người nhận
	if saleOrder.Type == "RETURN" && !shippingOrder.IsDropOffAtFMHub {
		shippingOrder.FeeCollectMethod = &enum.FeeCollectMethod.RECEIVER_PAY
		seller, err := client.Services.SellerClient.GetSellerById(int64(saleOrder.CustomerInfos.Delivery.Code))
		if err != nil || seller == nil {
			return shippingOrder, fmt.Errorf("Không tìm thấy thông tin seller/ vendor")
		}
		shippingOrder.CustomerInfo.Code = seller.Code
		if seller.SellerClass == "VENDOR" {
			shippingOrder.CustomerInfo.CustomerType = &enum.CustomerType.VENDOR
			shippingOrder.Tags = append(shippingOrder.Tags, "VENDOR")
		}

		if seller.SellerClass == "EXTERNAL" {
			shippingOrder.CustomerInfo.CustomerType = &enum.CustomerType.SELLER
			shippingOrder.Tags = append(shippingOrder.Tags, "SELLER")
		}
	}

	if shippingOrder.Height == 0 {
		shippingOrder.Height = 30
	}

	if shippingOrder.Width == 0 {
		shippingOrder.Width = 40
	}

	if shippingOrder.Length == 0 {
		shippingOrder.Length = 50
	}

	if shippingOrder.CustomerProvinceCode == "" {
		provinces, err := client.Services.WarehouseCoreClient.GetProvince(shippingOrder.CustomerProvinceName)
		if err != nil {
			log.Println("Get province err ", shippingOrder.CustomerProvinceName, err)
		}

		if len(provinces) > 0 {
			shippingOrder.CustomerProvinceCode = provinces[0].Code
		}
	}

	if shippingOrder.CustomerDistrictCode == "" {
		var districts []*model.District
		districts, err = client.Services.WarehouseCoreClient.GetDistrictByProvinceCode(shippingOrder.CustomerProvinceCode, shippingOrder.CustomerDistrictName)
		if err != nil {
			log.Println("Get district err ", shippingOrder.CustomerDistrictName, err)
		}

		if len(districts) > 0 {
			shippingOrder.CustomerDistrictCode = districts[0].Code
		}
	}

	if shippingOrder.CustomerWardCode == "" {
		wards, err := client.Services.WarehouseCoreClient.GetWard(shippingOrder.CustomerWardName, "", shippingOrder.CustomerDistrictCode, "")
		if err != nil {
			log.Println("Get ward err ", shippingOrder.CustomerWardName, err)
		}

		if len(wards) > 0 {
			shippingOrder.CustomerWardCode = wards[0].Code
		}
	}

	// Find last mile hub, we only need to find last mile hub  if carrier is internal
	if carrierInfo.IsInternal != nil &&
		*carrierInfo.IsInternal &&
		shippingOrder.CustomerWardCode != "" &&
		saleOrder.HubCodeReceive == "" {
		hub := FindNearestHubOfAddress(model.Address{
			WardCode: shippingOrder.CustomerWardCode,
		})

		// If found any hub, assign last mile hub to shipping order
		if hub != nil {
			shippingOrder.LastMileHubCode = hub.Code
		}
	}

	if saleOrder.HubCodeReceive != "" {
		shippingOrder.LastMileHubCode = saleOrder.HubCodeReceive
	}

	if input.FromHubCode != "" {
		shippingOrder.FirstMileHubCode = input.FromHubCode
	}

	if len(saleOrder.OrderLines) > 0 {
		for _, product := range saleOrder.OrderLines {
			if product.ScannedQuantity <= 0 {
				continue
			}

			shippingOrder.Products = append(shippingOrder.Products, &model.Product{
				Name:     product.ProductName,
				SKU:      product.SKU,
				Quantity: int64(product.ScannedQuantity),
				Price:    product.UnitPrice,
				Weight:   product.Weight,
				Category: "PRODUCT",
			})
		}
	}
	return
}

func createHubShippingOrder(shippingOrder *model.ShippingOrder, hub *model.Hub, warehouseCode string) error {
	current := time.Now()

	hubShipping := model.HubShippingOrder{
		VersionNo:           uuid.New().String(),
		HUBCode:             hub.Code,
		ReferenceCode:       shippingOrder.ReferenceCode,
		TrackingCode:        shippingOrder.TrackingCode,
		TplCode:             shippingOrder.TplCode,
		TplName:             shippingOrder.TplName,
		Status:              &enum.HubShippingOrderStatus.STORING,
		ActionTime:          shippingOrder.ActionTime,
		FromCustomerName:    hub.Name,
		FromCustomerCode:    warehouseCode,
		FromCustomerPhone:   shippingOrder.FromCustomerPhone,
		FromCustomerEmail:   shippingOrder.FromCustomerEmail,
		FromProvinceCode:    shippingOrder.FromProvinceCode,
		FromProvinceName:    shippingOrder.FromProvinceName,
		FromDistrictCode:    shippingOrder.FromDistrictCode,
		FromDistrictName:    shippingOrder.FromDistrictName,
		FromWardCode:        shippingOrder.FromWardCode,
		FromWardName:        shippingOrder.FromWardName,
		FromCustomerAddress: shippingOrder.FromCustomerAddress,
		ToCustomerName:      shippingOrder.CustomerName,
		ToCustomerCode:      shippingOrder.CustomerCode,
		ToCustomerAddress:   shippingOrder.CustomerShippingAddress,
		ToCustomerPhone:     shippingOrder.CustomerPhone,
		ToCustomerEmail:     shippingOrder.CustomerEmail,
		ToWardCode:          shippingOrder.CustomerWardCode,
		ToWardName:          shippingOrder.CustomerWardName,
		ToDistrictCode:      shippingOrder.CustomerDistrictCode,
		ToDistrictName:      shippingOrder.CustomerDistrictName,
		ToProvinceCode:      shippingOrder.CustomerProvinceCode,
		ActionName:          "Đã nhập kho " + hub.Name,
		Type:                &enum.HubOrderType.TRANSPORTING,
		Action:              "IN",
		ToProvinceName:      shippingOrder.CustomerProvinceName,
		Height:              shippingOrder.Height,
		Width:               shippingOrder.Width,
		Length:              shippingOrder.Length,
		Weight:              shippingOrder.Weight,
		NumPackage:          shippingOrder.NumPackage,
		PrivateNote:         shippingOrder.PrivateNote,
		Note:                shippingOrder.Note,
		TotalAmount:         shippingOrder.TotalAmount,
		DeliveryAmount:      shippingOrder.DeliveryAmount,
		CODAmount:           shippingOrder.CODAmount,
		FeeAmount:           shippingOrder.FeeAmount,
		FeeCollectMethod:    shippingOrder.FeeCollectMethod,
		PaymentMethod:       shippingOrder.PaymentMethod,
		CollectOnDelivery:   shippingOrder.CollectOnDelivery,
		CreatedTime:         &current,
		ExtraInfo:           map[string]interface{}{},
		Products:            shippingOrder.Products,
		// Received at LM
		IsReceiveAtLMHub:   shippingOrder.IsReceiveAtLMHub,
		IsDropOffAtFMHub:   shippingOrder.IsDropOffAtFMHub,
		VerificationCode:   shippingOrder.VerificationCode,
		NeedVerifyReceiver: shippingOrder.NeedVerifyReceiver,
		Tags:               shippingOrder.Tags,

		TotalCollectSenderAmount:   shippingOrder.TotalCollectSenderAmount,
		TotalCollectReceiverAmount: shippingOrder.TotalCollectReceiverAmount,
		FeeSenderAmount:            shippingOrder.FeeSenderAmount,
		FeeReceiverAmount:          shippingOrder.FeeReceiverAmount,
		TotalDebtAmount:            shippingOrder.TotalDebtAmount,
		FeeDebtAmount:              shippingOrder.FeeDebtAmount,
		ParentReferenceCode:        shippingOrder.ParentReferenceCode,
		MergeStatus:                shippingOrder.MergeStatus,

		DeliveryLeadTime: shippingOrder.DeliveryLeadTime,
		Baskets:          shippingOrder.Baskets,
		References:       shippingOrder.References,
	}

	// If last mile hub, change type to delivery
	if hub.Code == shippingOrder.LastMileHubCode {
		hubShipping.Type = &enum.HubOrderType.DELIVERY
	}

	if shippingOrder.ShippingType != nil &&
		*shippingOrder.ShippingType == enum.ShippingOrderType.INTERNAL_TRANS {
		hubShipping.SubType = &enum.SubType.INTERNAL_TRANS
	}

	// Empty data...
	if shippingOrder.CustomerLatitude == 0 {
		// Get old shipping order
		result := model.ShippingAddressDB.QueryOne(&bson.M{
			"customer_code":    shippingOrder.CustomerCode,
			"ward_code":        hubShipping.ToWardCode,
			"customer_address": hubShipping.ToCustomerAddress,
		})

		if result.Status == common.APIStatus.Ok && result.Data != nil {
			address := result.Data.([]*model.ShippingAddress)[0]
			shippingOrder.FromLatitude = address.Latitude
			shippingOrder.FromLongitude = address.Longitude
		}
	}

	createResult := model.HUBShippingOrderDB.Upsert(bson.M{"reference_code": shippingOrder.ReferenceCode, "hub_code": hub.Code}, hubShipping)
	if createResult.Status != common.APIStatus.Ok {
		return fmt.Errorf(createResult.Message)
	}

	return nil
}

func MigrateProduct() *common.APIResponse {
	var limit int64 = 1000
	filter := bson.M{}
	for {
		shippingOrderRaw := model.ShippingOrderDB.Query(filter, 0, limit, &bson.M{"_id": -1})
		if shippingOrderRaw.Status != common.APIStatus.Ok {
			break
		}
		shippingOrders := shippingOrderRaw.Data.([]*model.ShippingOrder)
		smallestId := shippingOrders[len(shippingOrders)-1].ID
		filter["_id"] = bson.M{
			"$lt": smallestId,
		}
		for _, shippingOrder := range shippingOrders {
			if shippingOrder.ShippingType != nil && *shippingOrder.ShippingType == enum.ShippingOrderType.PICKUP {
				continue
			}

			saleOrder, err := client.Services.WarehouseCoreClient.GetSaleOrder(shippingOrder.ReferenceCode, "")
			if err != nil || saleOrder == nil {
				continue
			}

			if len(saleOrder.OrderLines) == 0 {
				continue
			}

			var products []*model.Product
			updater := bson.M{}
			for _, product := range saleOrder.OrderLines {
				if product.ScannedQuantity <= 0 {
					continue
				}

				products = append(products, &model.Product{
					Name:     product.Name,
					SKU:      product.SKU,
					Quantity: int64(product.ScannedQuantity),
					Price:    product.UnitPrice,
					Weight:   product.Weight,
					Category: "PRODUCT",
				})

				if len(products) > 0 {
					updater["products"] = products
				}
			}

			if len(saleOrder.Tags) > 0 {
				updater["tags"] = saleOrder.Tags
			}

			if len(updater) > 0 {
				_ = model.ShippingOrderDB.UpdateOne(bson.M{
					"reference_code": shippingOrder.ReferenceCode,
				}, updater)

				_ = model.HUBShippingOrderDB.UpdateMany(bson.M{
					"reference_code": shippingOrder.ReferenceCode,
				}, updater)
			}
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}
}

func MigrateTotalAmount(testOrder []string, massUpdate bool) *common.APIResponse {
	if len(testOrder) > 0 {
		shippingOrdersRaw := model.ShippingOrderDB.Query(bson.M{
			"reference_code": bson.M{
				"$in": testOrder,
			},
		}, 0, 1000, nil)

		if shippingOrdersRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.NotFound,
				Message: "Not found test order",
			}
		}

		shippingOrders := shippingOrdersRaw.Data.([]*model.ShippingOrder)
		for _, shippingOrder := range shippingOrders {
			updater := bson.M{}
			// Check xem ai trả phí, nếu không xác định được thì skip
			if shippingOrder.FeeCollectMethod == nil {
				continue
			}

			if *shippingOrder.FeeCollectMethod == enum.FeeCollectMethod.SENDER_PAY {
				// Nếu fee = 0 thì skip
				if shippingOrder.FeeAmount == 0 {
					continue
				}
				// Nếu sender trả phí thì cộng tổng thu người gửi fee
				updater["total_collect_sender_amount"] = shippingOrder.FeeAmount
				updater["fee_sender_amount"] = shippingOrder.FeeAmount
			}

			// Chỉ cần bắt case RO cho method là RECEIVER_PAY, chỉ có RO mới có method RECEIVER_PAY
			if *shippingOrder.FeeCollectMethod == enum.FeeCollectMethod.RECEIVER_PAY {
				// Nếu fee = 0 thì skip
				if shippingOrder.FeeAmount == 0 {
					continue
				}

				updater["total_collect_receiver_amount"] = shippingOrder.FeeAmount
				updater["fee_receiver_amount"] = shippingOrder.FeeAmount
			}

			if len(updater) > 0 {
				_ = model.ShippingOrderDB.UpdateOne(bson.M{
					"reference_code": shippingOrder.ReferenceCode,
				}, updater)

				_ = model.HUBShippingOrderDB.UpdateMany(bson.M{
					"reference_code": shippingOrder.ReferenceCode,
				}, updater)
			}
		}
	}

	if massUpdate {
		var limit int64 = 1000
		filter := bson.M{}
		for {
			shippingOrderRaw := model.ShippingOrderDB.Query(filter, 0, limit, &bson.M{"_id": -1})
			if shippingOrderRaw.Status != common.APIStatus.Ok {
				break
			}
			shippingOrders := shippingOrderRaw.Data.([]*model.ShippingOrder)
			smallestId := shippingOrders[len(shippingOrders)-1].ID
			filter["_id"] = bson.M{
				"$lt": smallestId,
			}

			for _, shippingOrder := range shippingOrders {
				updater := bson.M{}
				// Check xem ai trả phí, nếu không xác định được thì skip
				if shippingOrder.FeeCollectMethod == nil {
					continue
				}

				if *shippingOrder.FeeCollectMethod == enum.FeeCollectMethod.SENDER_PAY {
					// Nếu fee = 0 thì skip
					if shippingOrder.FeeAmount == 0 {
						continue
					}
					// Nếu sender trả phí thì cộng tổng thu người gửi fee
					updater["total_collect_sender_amount"] = shippingOrder.FeeAmount
					updater["fee_sender_amount"] = shippingOrder.FeeAmount
				}

				// Chỉ cần bắt case RO cho method là RECEIVER_PAY, chỉ có RO mới có method RECEIVER_PAY
				if *shippingOrder.FeeCollectMethod == enum.FeeCollectMethod.RECEIVER_PAY {
					// Nếu fee = 0 thì skip
					if shippingOrder.FeeAmount == 0 {
						continue
					}

					updater["total_collect_receiver_amount"] = shippingOrder.FeeAmount
					updater["fee_receiver_amount"] = shippingOrder.FeeAmount
				}

				if len(updater) > 0 {
					_ = model.ShippingOrderDB.UpdateOne(bson.M{
						"reference_code": shippingOrder.ReferenceCode,
					}, updater)

					_ = model.HUBShippingOrderDB.UpdateMany(bson.M{
						"reference_code": shippingOrder.ReferenceCode,
					}, updater)
				}
			}

		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Ok",
	}
}

func CheckinInbound(referenceCode string, orderType enum.ShippingOrderTypeValue, checkInBy string) *common.APIResponse {
	if referenceCode == "" || orderType == "" || checkInBy == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã đơn, loại đơn, người checkin không được để trống",
			ErrorCode: "MISSING_PARAMS",
		}
	}

	shippingOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
		"reference_code": referenceCode,
	})

	if shippingOrderRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy đơn hàng",
			ErrorCode: "NOT_FOUND_SHIPPING_ORDER",
		}
	}

	shippingOrder := shippingOrderRaw.Data.([]*model.ShippingOrder)[0]
	if orderType == enum.ShippingOrderType.INTERNAL_TRANS {
		if shippingOrder.ShippingType == nil || *shippingOrder.ShippingType != enum.ShippingOrderType.INTERNAL_TRANS {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Loại đơn không hợp lệ",
				ErrorCode: "INVALID_SHIPPING_TYPE",
			}
		}
		// Chỉ cần xử lí case carrier là nội bộ do nvc bên ngoài sử dụng luồng callback
		if shippingOrder.TplServiceId == 0 {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Nhà vận chuyển không hợp lệ",
				ErrorCode: "INVALID_CARRIER",
			}
		}

		carrierRaw := model.CarrierDB.QueryOne(bson.M{
			"carrier_id": shippingOrder.TplServiceId,
		})

		if carrierRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Nhà vận chuyển không hợp lệ",
				ErrorCode: "INVALID_CARRIER",
			}
		}

		carrier := carrierRaw.Data.([]*model.Carrier)[0]
		if carrier.IsInternal != nil && !*carrier.IsInternal {
			return &common.APIResponse{
				Status:  common.APIStatus.Ok,
				Message: "OK",
			}
		}

		err := shipping_order.PushCheckinTo(shippingOrder, shippingOrder.ReferenceCode)
		if err != nil {
			return &common.APIResponse{
				Status:    common.APIStatus.Error,
				Message:   "Thử lại sau vài giây",
				ErrorCode: "INTERNAL_SERVER_ERROR",
			}
		}
	}
	// TODO: If there is more type can checkin at inbound add an if statement here

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}

}

func MigrateDropOffFM() *common.APIResponse {
	shippingOrdersRaw := model.ShippingOrderDB.Query(bson.M{
		"type": enum.ShippingOrderType.FMPO,
	}, 0, 1000, nil)

	shippingOrders := shippingOrdersRaw.Data.([]*model.ShippingOrder)
	for _, shippingOrder := range shippingOrders {
		if strings.HasPrefix(shippingOrder.ReferenceCode, "PO") {
			model.ShippingOrderDB.UpdateOne(bson.M{
				"reference_code": shippingOrder.ReferenceCode,
			}, bson.M{
				"is_book_drop_off": false,
			})
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}

}

func UpdateOrderValue(parentReferenceCode string, orderValue float64) *common.APIResponse {
	if parentReferenceCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã đơn không được để trống",
			ErrorCode: "MISSING_REFERENCE_CODE",
		}
	}

	shippingOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
		"parent_reference_code": parentReferenceCode,
	})

	if shippingOrderRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   "Không tìm thấy đơn hàng",
			ErrorCode: "NOT_FOUND_SHIPPING_ORDER",
		}
	}

	shippingOrder := shippingOrderRaw.Data.([]*model.ShippingOrder)[0]
	// Nếu đơn drop off chỉ cho update khi status của shipping order là PICKED
	if shippingOrder.IsBookDropOff != nil &&
		*shippingOrder.IsBookDropOff &&
		*shippingOrder.Status != enum.TPLCallbackStatus.PICKED {
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Chỉ được cập nhật khi đơn đã lấy hàng với loại đơn dropoff",
		}
	}

	// Nếu đơn pickup thì chỉ cho update khi trạng thái là chờ lấy hàng
	if shippingOrder.IsBookDropOff != nil &&
		!*shippingOrder.IsBookDropOff &&
		*shippingOrder.Status != enum.TPLCallbackStatus.READY_TO_PICK {
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Chỉ được cập nhật khi đơn chờ lấy hàng với loại đơn pickup",
		}
	}

	return model.ShippingOrderDB.UpdateOne(bson.M{
		"reference_code": shippingOrder.ReferenceCode,
	}, bson.M{
		"order_value": orderValue,
	})
}

func EstimateShippingOrderFee(bookReq request.BookShippingOrder) *common.APIResponse {
	if bookReq.NumPackage <= 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Số kiện không được để trống",
		}
	}

	query := bson.M{
		"code": bookReq.From.Code,
	}

	if bookReq.CustomerInfo != nil && bookReq.CustomerInfo.Code != "" {
		query["code"] = bookReq.CustomerInfo.Code
	}

	if bookReq.IsBookDropOff {
		query["applied_fees.config_fee_type"] = enum.ConfigFeeType.TRANSPORT
	} else {
		query["applied_fees.config_fee_type"] = enum.ConfigFeeType.PICK_N_TRANSPORT
	}

	if bookReq.ShippingType != nil &&
		*bookReq.ShippingType == enum.ShippingOrderType.EO {
		if bookReq.ReferenceCode != "" {
			shippingOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
				"reference_code": bookReq.ReferenceCode,
			})
			if shippingOrderRaw.Status == common.APIStatus.Ok {
				shippingOrder := shippingOrderRaw.Data.([]*model.ShippingOrder)[0]
				if (shippingOrder.IsBookDropOff != nil &&
					*shippingOrder.IsBookDropOff) ||
					shippingOrder.IsDropOffAtFMHub {
					bookReq.DropOffAtHubCode = shippingOrder.FirstMileHubCode
				}
				if shippingOrder.IsReceiveAtLMHub {
					bookReq.ReceiveAtHubCode = shippingOrder.LastMileHubCode
				}

				if isContainsProductType(shippingOrder.ProductTypes, enum.ProductType.FRAGILE_PRODUCT) {
					bookReq.AdditionalFees = append(bookReq.AdditionalFees, model.AdditionalFee{
						AdditionalFeeType: enum.AdditionalFeeType.FRAGILE_PRODUCT,
					})
				}
				bookReq.CODAmount = shippingOrder.CODAmount
				if shippingOrder.CustomerInfo != nil {
					bookReq.CustomerInfo = shippingOrder.CustomerInfo
					query["code"] = shippingOrder.CustomerInfo.Code
				}
			}
		}

		if bookReq.DropOffAtHubCode == "" &&
			bookReq.ReceiveAtHubCode == "" {
			query["applied_fees.config_fee_type"] = enum.ConfigFeeType.PICK_N_DELIVERY
		}

		if bookReq.DropOffAtHubCode != "" &&
			bookReq.ReceiveAtHubCode != "" {
			query["applied_fees.config_fee_type"] = enum.ConfigFeeType.TRANSPORT
		}

		if bookReq.DropOffAtHubCode != "" &&
			bookReq.ReceiveAtHubCode == "" {
			query["applied_fees.config_fee_type"] = enum.ConfigFeeType.TRANSPORT_N_DELIVERY
		}

		if bookReq.DropOffAtHubCode == "" &&
			bookReq.ReceiveAtHubCode != "" {
			query["applied_fees.config_fee_type"] = enum.ConfigFeeType.PICK_N_TRANSPORT
		}
	}

	customerRaw := model.CustomerDB.QueryOne(query)

	if bookReq.ShippingType != nil &&
		(*bookReq.ShippingType == enum.ShippingOrderType.FMPO ||
			*bookReq.ShippingType == enum.ShippingOrderType.PGH ||
			*bookReq.ShippingType == enum.ShippingOrderType.PO) {
		filter := bson.M{
			"warehouse_reference_code": bookReq.To.Code,
		}
		if bookReq.ToHubCode != "" {
			filter = bson.M{
				"code": bookReq.ToHubCode,
			}
		}
		hubRaw := model.HubDB.QueryOne(filter)
		if hubRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không tìm thấy mã hub: " + bookReq.To.Code,
			}
		}
		hub := hubRaw.Data.([]*model.Hub)[0]
		bookReq.To.Code = hub.Code
		bookReq.To.WardCode = hub.Address.WardCode
		bookReq.To.DistrictCode = hub.Address.DistrictCode
		bookReq.To.ProvinceCode = hub.Address.ProvinceCode

		if bookReq.ReferenceCode != "" {
			if strings.HasPrefix(bookReq.ReferenceCode, "PO") {
				bookReq.ShippingType = &enum.ShippingOrderType.PO
			}
			if strings.HasPrefix(bookReq.ReferenceCode, "PGH") {
				bookReq.ShippingType = &enum.ShippingOrderType.PGH
			}
		}
	}

	useDefaultFee := true
	if customerRaw.Status == common.APIStatus.Ok {
		customer := customerRaw.Data.([]*model.Customer)[0]
		var configFeeId int64
		for _, fee := range *customer.AppliedFees {
			if bookReq.IsBookDropOff {
				if fee.ConfigFeeType == enum.ConfigFeeType.TRANSPORT &&
					bookReq.ShippingType != nil &&
					*bookReq.ShippingType != enum.ShippingOrderType.EO {
					configFeeId = fee.ConfigFeeId
				}
			} else {
				if bookReq.ShippingType != nil &&
					*bookReq.ShippingType != enum.ShippingOrderType.EO &&
					fee.ConfigFeeType == enum.ConfigFeeType.PICK_N_TRANSPORT {
					configFeeId = fee.ConfigFeeId
				}
			}
			if bookReq.ShippingType != nil &&
				*bookReq.ShippingType == enum.ShippingOrderType.EO {
				if bookReq.DropOffAtHubCode == "" &&
					bookReq.ReceiveAtHubCode == "" &&
					fee.ConfigFeeType == enum.ConfigFeeType.PICK_N_DELIVERY {
					configFeeId = fee.ConfigFeeId
				}
				if bookReq.DropOffAtHubCode != "" &&
					bookReq.ReceiveAtHubCode != "" &&
					fee.ConfigFeeType == enum.ConfigFeeType.TRANSPORT {
					configFeeId = fee.ConfigFeeId
				}
				if bookReq.DropOffAtHubCode != "" &&
					bookReq.ReceiveAtHubCode == "" &&
					fee.ConfigFeeType == enum.ConfigFeeType.TRANSPORT_N_DELIVERY {
					configFeeId = fee.ConfigFeeId
				}
				if bookReq.DropOffAtHubCode == "" &&
					bookReq.ReceiveAtHubCode != "" &&
					fee.ConfigFeeType == enum.ConfigFeeType.PICK_N_TRANSPORT {
					configFeeId = fee.ConfigFeeId
				}
			}
		}

		configFeeRaw := model.ConfigFeeDB.QueryOne(bson.M{
			"config_id": configFeeId,
		})
		if configFeeRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không tìm thấy cấu hình phí vận chuyển",
			}
		}

		// TODO: Require check hub code here after deploy
		if bookReq.IsBookDropOff {
			hubRaw := model.HubDB.QueryOne(bson.M{
				"code": bookReq.HubCode,
			})
			if hubRaw.Status == common.APIStatus.Ok {
				hub := hubRaw.Data.([]*model.Hub)[0]
				bookReq.From.Code = hub.Code
				bookReq.From.WardCode = hub.Address.WardCode
				bookReq.From.DistrictCode = hub.Address.DistrictCode
				bookReq.From.ProvinceCode = hub.Address.ProvinceCode
			}
		}

		configFee := configFeeRaw.Data.([]*model.ConfigFee)[0]
		feeAmount, err := CalculateOrderFee(bookReq, configFee)
		if err != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Biểu phí sai, liên hệ admin để được để được hỗ trợ",
			}
		}
		bookReq.CODAmount = feeAmount
		useDefaultFee = false
	}

	if useDefaultFee {
		configCode := ""
		if bookReq.IsBookDropOff {
			configCode = conf.Config.DefaultFMDropOffFeeCode
		} else {
			configCode = conf.Config.DefaultFMPickUpFeeCode
		}

		if bookReq.ShippingType != nil &&
			*bookReq.ShippingType == enum.ShippingOrderType.EO {
			if bookReq.DropOffAtHubCode != "" &&
				bookReq.ReceiveAtHubCode != "" {
				configCode = conf.Config.DefaultEOTransportFeeCode
			}
			if bookReq.DropOffAtHubCode == "" &&
				bookReq.ReceiveAtHubCode == "" {
				configCode = conf.Config.DefaultEOPickNDeliCode
			}
			if bookReq.DropOffAtHubCode != "" &&
				bookReq.ReceiveAtHubCode == "" {
				configCode = conf.Config.DefaultEOTransNDeliCode
			}
			if bookReq.DropOffAtHubCode == "" &&
				bookReq.ReceiveAtHubCode != "" {
				configCode = conf.Config.DefaultEOPickNTransFeeCode
			}
		}

		// TODO: Require check hub code here after deploy
		if bookReq.IsBookDropOff &&
			bookReq.ShippingType != nil &&
			(*bookReq.ShippingType == enum.ShippingOrderType.FMPO ||
				*bookReq.ShippingType == enum.ShippingOrderType.PGH ||
				*bookReq.ShippingType == enum.ShippingOrderType.PO) {
			hubRaw := model.HubDB.QueryOne(bson.M{
				"code": bookReq.HubCode,
			})
			if hubRaw.Status == common.APIStatus.Ok {
				hub := hubRaw.Data.([]*model.Hub)[0]
				bookReq.From.Code = hub.Code
				bookReq.From.WardCode = hub.Address.WardCode
				bookReq.From.DistrictCode = hub.Address.DistrictCode
				bookReq.From.ProvinceCode = hub.Address.ProvinceCode
			}
		}

		configFeeRaw := model.ConfigFeeDB.QueryOne(bson.M{
			"code": configCode,
		})

		if configFeeRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không tìm thấy cấu hình phí vận chuyển",
			}
		}
		configFee := configFeeRaw.Data.([]*model.ConfigFee)[0]
		feeAmount, err := CalculateOrderFee(bookReq, configFee)
		if err != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không tìm thấy cấu hình phí vận chuyển hợp lệ cho đơn",
			}
		}
		bookReq.CODAmount = feeAmount
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Lấy phí vận chuyển thành công",
		Data: []response.Fee{
			{FeeAmount: bookReq.CODAmount},
		},
	}

}

func MergeShippingOrder(shippingOrder model.ShippingOrder) *common.APIResponse {
	returnAfter := options.After
	shippingOrderResp := model.ShippingOrderDB.UpdateOne(bson.M{
		"reference_code": shippingOrder.ReferenceCode,
	}, bson.M{
		"status": enum.TPLCallbackStatus.MERGED,
	}, &options.FindOneAndUpdateOptions{
		Upsert:         &enum.True,
		ReturnDocument: &returnAfter,
	})

	if shippingOrderResp.Status != common.APIStatus.Ok {
		return shippingOrderResp
	}
	shippingOrderAfter := shippingOrderResp.Data.([]*model.ShippingOrder)[0]

	hubOrderResp := model.HUBShippingOrderDB.UpdateOne(bson.M{
		"reference_code": shippingOrder.ReferenceCode,
		"hub_code":       shippingOrderAfter.CurrentHub,
	}, bson.M{
		"status": enum.HubShippingOrderStatus.MERGED,
	})

	if hubOrderResp.Status != common.APIStatus.Ok {
		return hubOrderResp
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Gộp đơn thành công",
	}
}

func ReleaseNeedMergeShippingOrder(shippingOrder model.ShippingOrder) *common.APIResponse {
	shippingOrderResp := model.ShippingOrderDB.UpdateOne(bson.M{
		"reference_code": shippingOrder.ReferenceCode,
	}, bson.M{
		"merge_status": enum.MergeStatus.EXPIRED,
		"expired_at":   time.Now(),
	})

	if shippingOrderResp.Status != common.APIStatus.Ok {
		return shippingOrderResp
	}

	hubOrderResp := model.HUBShippingOrderDB.UpdateMany(bson.M{
		"reference_code": shippingOrder.ReferenceCode,
	}, bson.M{
		"merge_status": enum.MergeStatus.EXPIRED,
		"expired_at":   time.Now(),
	})

	if hubOrderResp.Status != common.APIStatus.Ok {
		return hubOrderResp
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Gỡ khóa gộp đơn thành công",
	}
}

func ImportOrders(req request.ImportOrdersRequest, createdBy int64) *common.APIResponse {
	if req.ShippingOrderType == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Loại đơn không được để trống",
		}
	}

	if *req.ShippingOrderType == enum.ShippingOrderType.CS {
		return ImportCsOrder(req, createdBy)
	}

	if *req.ShippingOrderType == enum.ShippingOrderType.EO {
		return ImportEOOrders(req, createdBy)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Invalid,
		Message: "Loại đơn không hợp lệ",
	}

}

func ImportCsOrderForHubs(req request.ImportOrdersRequest, createdBy int64) *common.APIResponse {
	existedHub := make(map[string]bool)
	hubOrders := make(map[string][]int)
	existedCarrier := make(map[int64]*model.Carrier)
	// Province code -> District code -> Ward code
	path := map[string]map[string]map[string]bool{}
	type ValidatePath struct {
		ProvinceName string
		DistrictName string
		WardName     string
		ProvinceCode string
		DistrictCode string
		WardCode     string
		OrderIndex   int
	}
	needVerifyDistrict := []ValidatePath{}
	needVerifyWard := []ValidatePath{}
	type NotFoundHub struct {
		LineId  int    `json:"lineId"`
		Address string `json:"address"`
		Message string `json:"message"`
	}

	notFoundHubs := []NotFoundHub{}
	for index, order := range req.Orders {
		if order.CustomerInfo == nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Thông tin khách hàng không được để trống",
			}
		}

		if order.HubCode == "" {
			err := BuildAddress(order.To)
			if err != nil {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Địa chỉ không hợp lệ",
				}
			}
			hub := FindNearestHubOfAddress(model.Address{
				ProvinceCode: order.To.ProvinceCode,
				DistrictCode: order.To.DistrictCode,
				WardCode:     order.To.WardCode,
				ProvinceName: order.To.ProvinceName,
				DistrictName: order.To.DistrictName,
				WardName:     order.To.WardName,
			})
			if hub == nil {
				message := order.To.ProvinceName +
					" " + order.To.DistrictName + " " + order.To.WardName

				notFoundHubs = append(notFoundHubs, NotFoundHub{
					LineId:  index + 1,
					Address: message,
					Message: "Không tìm thấy hub xử lý đơn cskh",
				})
				continue
			}
			order.HubCode = hub.Code
		}

		if !utils.CheckPhoneFormat(order.CustomerInfo.Phone) {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Số điện thoại không hợp lệ",
			}
		}

		if _, existed := existedHub[order.HubCode]; !existed {
			existedHub[order.HubCode] = true
		}
		hubOrders[order.HubCode] = append(hubOrders[order.HubCode], index)
	}

	if len(notFoundHubs) > 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy hub xử lý đơn cskh",
			ErrorCode: "NOT_FOUND_PROCESSING_HUB",
			Data:      notFoundHubs,
		}
	}

	for hubCode := range existedHub {
		hubRaw := model.HubDB.QueryOne(bson.M{
			"code": hubCode,
		})

		if hubRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Mã hub không hợp lệ",
			}
		}
		hub := hubRaw.Data.([]*model.Hub)[0]
		if hub.DefaultCarrierId == 0 {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Hub chưa có đơn vị vận chuyển mặc định",
			}
		}

		if _, existed := existedCarrier[hub.DefaultCarrierId]; !existed {

			carrierRaw := model.CarrierDB.QueryOne(bson.M{
				"carrier_id": hub.DefaultCarrierId,
			})

			if carrierRaw.Status != common.APIStatus.Ok {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Đơn vị vận chuyển mặc định của hub không hợp lệ",
				}
			}
			existedCarrier[hub.DefaultCarrierId] = carrierRaw.Data.([]*model.Carrier)[0]
		}

		carrier := existedCarrier[hub.DefaultCarrierId]
		// Validate orders
		for _, index := range hubOrders[hubCode] {
			order := req.Orders[index]
			order.ShippingType = req.ShippingOrderType
			order.CarrierId = carrier.CarrierId
			order.CarrierName = carrier.CarrierName
			order.CarrierCode = carrier.CarrierCode

			if order.To == nil ||
				order.To.ProvinceName == "" ||
				order.To.DistrictName == "" {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Địa chỉ giao hàng không được để trống",
				}
			}

			order.From = &model.Address{
				ProvinceName: hub.Address.ProvinceName,
				DistrictName: hub.Address.DistrictName,
				WardName:     hub.Address.WardName,
				Address:      hub.Address.Address,
				ProvinceCode: hub.Address.ProvinceCode,
				DistrictCode: hub.Address.DistrictCode,
				WardCode:     hub.Address.WardCode,
				Code:         hub.Code,
				Name:         hub.Name,
				Phone:        hub.Address.Phone,
			}

			if order.CustomerInfo == nil ||
				order.CustomerInfo.Name == "" ||
				order.CustomerInfo.Phone == "" ||
				order.CustomerInfo.Code == "" {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Thông tin khách hàng không được để trống",
				}
			}

			order.To.Name = order.CustomerInfo.Name
			order.To.Phone = order.CustomerInfo.Phone
			order.To.Code = order.CustomerInfo.Code

			order.HubCode = hub.Code

			_, provinceOk := path[order.To.ProvinceName]
			if !provinceOk {
				path[order.To.ProvinceName] = map[string]map[string]bool{}
			}
			_, districtOk := path[order.To.ProvinceName][order.To.DistrictName]
			if !districtOk {
				path[order.To.ProvinceName][order.To.DistrictName] = map[string]bool{}
			}
			if order.To.WardName != "" {
				_, wardOk := path[order.To.ProvinceName][order.To.DistrictName][order.To.WardName]
				if !wardOk {
					path[order.To.ProvinceName][order.To.DistrictName][order.To.WardName] = true
				}
			}

			if _, ok := path[order.To.ProvinceName][order.To.DistrictName][order.To.WardName]; ok &&
				order.To.WardName != "" {
				needVerifyWard = append(needVerifyWard, ValidatePath{
					ProvinceName: order.To.ProvinceName,
					DistrictName: order.To.DistrictName,
					WardName:     order.To.WardName,
					OrderIndex:   index,
				})
				continue
			}

			if _, ok := path[order.To.ProvinceName][order.To.DistrictName]; ok {
				needVerifyDistrict = append(needVerifyDistrict, ValidatePath{
					ProvinceName: order.To.ProvinceName,
					DistrictName: order.To.DistrictName,
					OrderIndex:   index,
				})
				continue
			}
		}
	}

	for _, validatePath := range needVerifyDistrict {
		provinceRaw := model.ProvinceMappingDB.QueryOne(bson.M{
			"dictionary": validatePath.ProvinceName,
		})
		if provinceRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Tỉnh/Thành phố không hợp lệ: " + validatePath.ProvinceName,
			}
		}
		province := provinceRaw.Data.([]*model.ProvinceMapping)[0]
		districtRaw := model.DistrictMappingDB.QueryOne(bson.M{
			"dictionary":    validatePath.DistrictName,
			"province_code": province.Code,
		})
		if districtRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Huyện không hợp lệ : " + validatePath.DistrictName,
			}
		}
		validatePath.ProvinceCode = province.Code
		validatePath.DistrictCode = districtRaw.Data.([]*model.DistrictMapping)[0].Code
		req.Orders[validatePath.OrderIndex].To.ProvinceCode = province.Code
		req.Orders[validatePath.OrderIndex].To.DistrictCode =
			districtRaw.Data.([]*model.DistrictMapping)[0].Code
	}

	for _, validatePath := range needVerifyWard {
		provinceRaw := model.ProvinceMappingDB.QueryOne(bson.M{
			"dictionary": validatePath.ProvinceName,
		})
		if provinceRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Tỉnh/Thành phố không hợp lệ: " + validatePath.ProvinceName,
			}
		}
		province := provinceRaw.Data.([]*model.ProvinceMapping)[0]
		districtRaw := model.DistrictMappingDB.QueryOne(bson.M{
			"dictionary":    validatePath.DistrictName,
			"province_code": province.Code,
		})
		if districtRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Huyện không hợp lệ : " + validatePath.DistrictName,
			}
		}
		district := districtRaw.Data.([]*model.DistrictMapping)[0]
		wardRaw := model.WardMappingDB.QueryOne(bson.M{
			"dictionary":    validatePath.WardName,
			"district_code": district.Code,
		})
		if wardRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Xã/Phường không hợp lệ : " + validatePath.WardName,
			}
		}
		validatePath.ProvinceCode = province.Code
		validatePath.DistrictCode = district.Code
		validatePath.WardCode = wardRaw.Data.([]*model.WardMapping)[0].Code

		req.Orders[validatePath.OrderIndex].To.ProvinceCode = province.Code
		req.Orders[validatePath.OrderIndex].To.DistrictCode = district.Code
		req.Orders[validatePath.OrderIndex].To.WardCode = wardRaw.Data.([]*model.WardMapping)[0].Code
	}

	// Book shipping order
	for _, order := range req.Orders {
		go BookShippingOrder(order, createdBy)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Import đơn hàng thành công",
	}
}

func ImportCsOrder(req request.ImportOrdersRequest, createdBy int64) *common.APIResponse {
	if len(req.Orders) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Danh sách đơn hàng không được để trống",
		}
	}

	if req.HubCode == "" {
		return ImportCsOrderForHubs(req, createdBy)
	}

	hubRaw := model.HubDB.QueryOne(bson.M{
		"code": req.HubCode,
	})

	if hubRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã hub không hợp lệ",
		}
	}

	hub := hubRaw.Data.([]*model.Hub)[0]
	if hub.DefaultCarrierId == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Hub chưa có đơn vị vận chuyển mặc định",
		}
	}

	carrierRaw := model.CarrierDB.QueryOne(bson.M{
		"carrier_id": hub.DefaultCarrierId,
	})

	if carrierRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Đơn vị vận chuyển mặc định của hub không hợp lệ",
		}
	}

	carrier := carrierRaw.Data.([]*model.Carrier)[0]

	// Province code -> District code -> Ward code
	path := map[string]map[string]map[string]bool{}
	type ValidatePath struct {
		ProvinceName string
		DistrictName string
		WardName     string
		ProvinceCode string
		DistrictCode string
		WardCode     string
		OrderIndex   int
	}

	needVerifyDistrict := []ValidatePath{}
	needVerifyWard := []ValidatePath{}
	// Validate orders
	for index, order := range req.Orders {
		order.ShippingType = req.ShippingOrderType
		order.CarrierId = carrier.CarrierId
		order.CarrierName = carrier.CarrierName
		order.CarrierCode = carrier.CarrierCode

		if order.To == nil ||
			order.To.ProvinceName == "" ||
			order.To.DistrictName == "" {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Địa chỉ giao hàng không được để trống",
			}
		}

		order.From = &model.Address{
			ProvinceName: hub.Address.ProvinceName,
			DistrictName: hub.Address.DistrictName,
			WardName:     hub.Address.WardName,
			Address:      hub.Address.Address,
			ProvinceCode: hub.Address.ProvinceCode,
			DistrictCode: hub.Address.DistrictCode,
			WardCode:     hub.Address.WardCode,
			Code:         hub.Code,
			Name:         hub.Name,
			Phone:        hub.Address.Phone,
		}

		if order.CustomerInfo == nil ||
			order.CustomerInfo.Name == "" ||
			order.CustomerInfo.Phone == "" ||
			order.CustomerInfo.Code == "" {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Thông tin khách hàng không được để trống",
			}
		}

		if !utils.CheckPhoneFormat(order.CustomerInfo.Phone) {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Số điện thoại không hợp lệ",
			}
		}

		order.To.Name = order.CustomerInfo.Name
		order.To.Phone = order.CustomerInfo.Phone
		order.To.Code = order.CustomerInfo.Code

		order.HubCode = hub.Code

		_, provinceOk := path[order.To.ProvinceName]
		if !provinceOk {
			path[order.To.ProvinceName] = map[string]map[string]bool{}
		}
		_, districtOk := path[order.To.ProvinceName][order.To.DistrictName]
		if !districtOk {
			path[order.To.ProvinceName][order.To.DistrictName] = map[string]bool{}
		}
		if order.To.WardName != "" {
			_, wardOk := path[order.To.ProvinceName][order.To.DistrictName][order.To.WardName]
			if !wardOk {
				path[order.To.ProvinceName][order.To.DistrictName][order.To.WardName] = true
			}
		}

		if _, ok := path[order.To.ProvinceName][order.To.DistrictName][order.To.WardName]; ok &&
			order.To.WardName != "" {
			needVerifyWard = append(needVerifyWard, ValidatePath{
				ProvinceName: order.To.ProvinceName,
				DistrictName: order.To.DistrictName,
				WardName:     order.To.WardName,
				OrderIndex:   index,
			})
			continue
		}

		if _, ok := path[order.To.ProvinceName][order.To.DistrictName]; ok {
			needVerifyDistrict = append(needVerifyDistrict, ValidatePath{
				ProvinceName: order.To.ProvinceName,
				DistrictName: order.To.DistrictName,
				OrderIndex:   index,
			})
			continue
		}
	}

	for _, validatePath := range needVerifyDistrict {
		provinceRaw := model.ProvinceMappingDB.QueryOne(bson.M{
			"dictionary": validatePath.ProvinceName,
		})
		if provinceRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Tỉnh/Thành phố không hợp lệ: " + validatePath.ProvinceName,
			}
		}
		province := provinceRaw.Data.([]*model.ProvinceMapping)[0]
		districtRaw := model.DistrictMappingDB.QueryOne(bson.M{
			"dictionary":    validatePath.DistrictName,
			"province_code": province.Code,
		})
		if districtRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Huyện không hợp lệ : " + validatePath.DistrictName,
			}
		}
		validatePath.ProvinceCode = province.Code
		validatePath.DistrictCode = districtRaw.Data.([]*model.DistrictMapping)[0].Code
		req.Orders[validatePath.OrderIndex].To.ProvinceCode = province.Code
		req.Orders[validatePath.OrderIndex].To.DistrictCode =
			districtRaw.Data.([]*model.DistrictMapping)[0].Code
	}

	for _, validatePath := range needVerifyWard {
		provinceRaw := model.ProvinceMappingDB.QueryOne(bson.M{
			"dictionary": validatePath.ProvinceName,
		})
		if provinceRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Tỉnh/Thành phố không hợp lệ: " + validatePath.ProvinceName,
			}
		}
		province := provinceRaw.Data.([]*model.ProvinceMapping)[0]
		districtRaw := model.DistrictMappingDB.QueryOne(bson.M{
			"dictionary":    validatePath.DistrictName,
			"province_code": province.Code,
		})
		if districtRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Huyện không hợp lệ : " + validatePath.DistrictName,
			}
		}
		district := districtRaw.Data.([]*model.DistrictMapping)[0]
		wardRaw := model.WardMappingDB.QueryOne(bson.M{
			"dictionary":    validatePath.WardName,
			"district_code": district.Code,
		})
		if wardRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Xã/Phường không hợp lệ : " + validatePath.WardName,
			}
		}
		validatePath.ProvinceCode = province.Code
		validatePath.DistrictCode = district.Code
		validatePath.WardCode = wardRaw.Data.([]*model.WardMapping)[0].Code

		req.Orders[validatePath.OrderIndex].To.ProvinceCode = province.Code
		req.Orders[validatePath.OrderIndex].To.DistrictCode = district.Code
		req.Orders[validatePath.OrderIndex].To.WardCode = wardRaw.Data.([]*model.WardMapping)[0].Code
	}

	// Book shipping order
	for _, order := range req.Orders {
		BookShippingOrder(order, createdBy)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Import đơn hàng thành công",
	}
}

func GetCustomerOrders(customerRequest request.ShippingOrderQuery, sort map[string]interface{}, offset, limit int64, getTotal bool) *common.APIResponse {
	if customerRequest.CustomerCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã khách hàng không được để trống",
		}
	}

	filter := bson.M{
		"customer_info.code": customerRequest.CustomerCode,
	}

	if len(customerRequest.ListReferenceCode) > 0 {
		filter["reference_code"] = bson.M{
			"$in": customerRequest.ListReferenceCode,
		}
	}

	if len(customerRequest.ListTrackingCode) > 0 {
		filter["tracking_code"] = bson.M{
			"$in": customerRequest.ListTrackingCode,
		}
	}

	if len(customerRequest.ListTplServiceId) > 0 {
		filter["tpl_service_id"] = bson.M{
			"$in": customerRequest.ListTplServiceId,
		}
	}

	if len(customerRequest.ProvinceCodes) > 0 {
		filter["customer_province_code"] = bson.M{"$in": customerRequest.ProvinceCodes}
	}

	if len(customerRequest.DistrictCodes) > 0 {
		filter["customer_district_code"] = bson.M{"$in": customerRequest.DistrictCodes}
	}

	if len(customerRequest.WardCodes) > 0 {
		filter["customer_ward_code"] = bson.M{"$in": customerRequest.WardCodes}
	}

	if len(customerRequest.FromProvinceCodes) > 0 {
		filter["from_province_code"] = bson.M{"$in": customerRequest.FromProvinceCodes}
	}

	if len(customerRequest.FromDistrictCodes) > 0 {
		filter["from_district_code"] = bson.M{"$in": customerRequest.FromDistrictCodes}
	}

	if len(customerRequest.FromWardCodes) > 0 {
		filter["from_ward_code"] = bson.M{"$in": customerRequest.FromWardCodes}
	}

	if customerRequest.CustomerOrderStatus != nil && *customerRequest.CustomerOrderStatus != "" {
		query, err := CustomerOrderStatusToShippingOrderQuery(*customerRequest.CustomerOrderStatus)
		if err != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Trạng thái đơn của khách hàng không hợp lệ",
			}
		}
		filter = utils.MergeMaps(filter, query)
	}

	// Nếu không có trạng thái đơn của khách hàng thì lấy tất cả trạng thái
	if customerRequest.CustomerOrderStatus == nil ||
		*customerRequest.CustomerOrderStatus == "" {
		orFilter := []bson.M{}
		readyToPickQuery, err := CustomerOrderStatusToShippingOrderQuery(enum.CustomerOrderStatus.READY_TO_PICK)
		if err != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Trạng thái đơn của khách hàng không hợp lệ",
			}
		}
		orFilter = append(orFilter, readyToPickQuery)

		pickedQuery, err := CustomerOrderStatusToShippingOrderQuery(enum.CustomerOrderStatus.PICKED)
		if err != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Trạng thái đơn của khách hàng không hợp lệ",
			}
		}
		orFilter = append(orFilter, pickedQuery)

		waitToDeliveryQuery, err := CustomerOrderStatusToShippingOrderQuery(enum.CustomerOrderStatus.WAIT_TO_DELIVERY)
		if err != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Trạng thái đơn của khách hàng không hợp lệ",
			}
		}
		orFilter = append(orFilter, waitToDeliveryQuery)

		deliveredQuery, err := CustomerOrderStatusToShippingOrderQuery(enum.CustomerOrderStatus.DELIVERED)
		if err != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Trạng thái đơn của khách hàng không hợp lệ",
			}
		}
		orFilter = append(orFilter, deliveredQuery)

		filter["$or"] = orFilter
	}

	if customerRequest.FromTime > 0 && customerRequest.ToTime > 0 {
		filter["action_time"] = bson.M{
			"$gte": customerRequest.FromTime,
			"$lte": customerRequest.ToTime,
		}
	} else if customerRequest.FromTime > 0 && customerRequest.ToTime == 0 {
		filter["action_time"] = bson.M{
			"$gte": customerRequest.FromTime,
		}
	} else if customerRequest.FromTime == 0 && customerRequest.ToTime > 0 {
		filter["action_time"] = bson.M{
			"$lte": customerRequest.ToTime,
		}
	}

	if customerRequest.FromCreatedTime > 0 && customerRequest.ToCreatedTime > 0 {
		fromTime := time.Unix(customerRequest.FromCreatedTime, 0)
		toTime := time.Unix(customerRequest.ToCreatedTime, 0)
		filter["created_time"] = bson.M{
			"$gte": primitive.NewDateTimeFromTime(fromTime),
			"$lte": primitive.NewDateTimeFromTime(toTime),
		}
	} else if customerRequest.FromCreatedTime > 0 && customerRequest.ToCreatedTime == 0 {
		fromTime := time.Unix(customerRequest.FromCreatedTime, 0)
		filter["created_time"] = bson.M{
			"$gte": primitive.NewDateTimeFromTime(fromTime),
		}
	} else if customerRequest.FromCreatedTime == 0 && customerRequest.ToCreatedTime > 0 {
		toTime := time.Unix(customerRequest.ToCreatedTime, 0)
		filter["created_time"] = bson.M{
			"$lte": primitive.NewDateTimeFromTime(toTime),
		}
	}

	sortField := primitive.M{}
	if sort == nil {
		sortField["_id"] = -1
	} else {
		sortField = sort
	}

	var result *common.APIResponse
	if customerRequest.ReadPreference != nil && *customerRequest.ReadPreference == enum.ReadPreference.SECONDARY {
		result = model.ShippingOrderDB.SecondaryInstance.Query(
			filter,
			offset,
			limit,
			&sortField)
		if getTotal {
			countResult := model.ShippingOrderDB.SecondaryInstance.Count(filter)
			result.Total = countResult.Total
		}
	} else {
		result = model.ShippingOrderDB.Query(
			filter,
			offset,
			limit,
			&sortField)
		if getTotal {
			countResult := model.ShippingOrderDB.Count(filter)
			result.Total = countResult.Total
		}
	}

	return result
}

func CountCustomerOrders(customerRequest request.ShippingOrderQuery) *common.APIResponse {
	type CountCustomerOrdersByStatusModel struct {
		Status   enum.CustomerOrderStatusValue `json:"status,omitempty"`
		Quantity int64                         `json:"quantity,omitempty"`
	}

	if customerRequest.CustomerCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã khách hàng không được để trống",
		}
	}

	filter := bson.M{
		"customer_info.code": customerRequest.CustomerCode,
	}

	if len(customerRequest.ListReferenceCode) > 0 {
		filter["reference_code"] = bson.M{
			"$in": customerRequest.ListReferenceCode,
		}
	}

	if len(customerRequest.ListTrackingCode) > 0 {
		filter["tracking_code"] = bson.M{
			"$in": customerRequest.ListTrackingCode,
		}
	}

	if len(customerRequest.ListTplServiceId) > 0 {
		filter["tpl_service_id"] = bson.M{
			"$in": customerRequest.ListTplServiceId,
		}
	}

	if len(customerRequest.ProvinceCodes) > 0 {
		filter["customer_province_code"] = bson.M{"$in": customerRequest.ProvinceCodes}
	}

	if len(customerRequest.DistrictCodes) > 0 {
		filter["customer_district_code"] = bson.M{"$in": customerRequest.DistrictCodes}
	}

	if len(customerRequest.WardCodes) > 0 {
		filter["customer_ward_code"] = bson.M{"$in": customerRequest.WardCodes}
	}

	if len(customerRequest.FromProvinceCodes) > 0 {
		filter["from_province_code"] = bson.M{"$in": customerRequest.FromProvinceCodes}
	}

	if len(customerRequest.FromDistrictCodes) > 0 {
		filter["from_district_code"] = bson.M{"$in": customerRequest.FromDistrictCodes}
	}

	if len(customerRequest.FromWardCodes) > 0 {
		filter["from_ward_code"] = bson.M{"$in": customerRequest.FromWardCodes}
	}

	if customerRequest.FromTime > 0 && customerRequest.ToTime > 0 {
		filter["action_time"] = bson.M{
			"$gte": customerRequest.FromTime,
			"$lte": customerRequest.ToTime,
		}
	} else if customerRequest.FromTime > 0 && customerRequest.ToTime == 0 {
		filter["action_time"] = bson.M{
			"$gte": customerRequest.FromTime,
		}
	} else if customerRequest.FromTime == 0 && customerRequest.ToTime > 0 {
		filter["action_time"] = bson.M{
			"$lte": customerRequest.ToTime,
		}
	}

	if customerRequest.FromCreatedTime > 0 && customerRequest.ToCreatedTime > 0 {
		fromTime := time.Unix(customerRequest.FromCreatedTime, 0)
		toTime := time.Unix(customerRequest.ToCreatedTime, 0)
		filter["created_time"] = bson.M{
			"$gte": primitive.NewDateTimeFromTime(fromTime),
			"$lte": primitive.NewDateTimeFromTime(toTime),
		}
	} else if customerRequest.FromCreatedTime > 0 && customerRequest.ToCreatedTime == 0 {
		fromTime := time.Unix(customerRequest.FromCreatedTime, 0)
		filter["created_time"] = bson.M{
			"$gte": primitive.NewDateTimeFromTime(fromTime),
		}
	} else if customerRequest.FromCreatedTime == 0 && customerRequest.ToCreatedTime > 0 {
		toTime := time.Unix(customerRequest.ToCreatedTime, 0)
		filter["created_time"] = bson.M{
			"$lte": primitive.NewDateTimeFromTime(toTime),
		}
	}

	readyToPickQuery, err := CustomerOrderStatusToShippingOrderQuery(enum.CustomerOrderStatus.READY_TO_PICK)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Trạng thái đơn của khách hàng không hợp lệ",
		}
	}
	readyToPickFilter := utils.MergeMaps(filter, readyToPickQuery)

	pickedQuery, err := CustomerOrderStatusToShippingOrderQuery(enum.CustomerOrderStatus.PICKED)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Trạng thái đơn của khách hàng không hợp lệ",
		}
	}
	pickedFilter := utils.MergeMaps(filter, pickedQuery)

	waitToDeliveryQuery, err := CustomerOrderStatusToShippingOrderQuery(enum.CustomerOrderStatus.WAIT_TO_DELIVERY)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Trạng thái đơn của khách hàng không hợp lệ",
		}
	}
	waitToDeliveryFilter := utils.MergeMaps(filter, waitToDeliveryQuery)

	deliveredQuery, err := CustomerOrderStatusToShippingOrderQuery(enum.CustomerOrderStatus.DELIVERED)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Trạng thái đơn của khách hàng không hợp lệ",
		}
	}
	deliveredFilter := utils.MergeMaps(filter, deliveredQuery)

	// TODO: If we meet bottleneck, we can use goroutine to query in parallel
	readyToPickOrders := model.ShippingOrderDB.Count(readyToPickFilter)
	pickedOrders := model.ShippingOrderDB.Count(pickedFilter)
	waitToDeliveryOrders := model.ShippingOrderDB.Count(waitToDeliveryFilter)
	deliveredOrders := model.ShippingOrderDB.Count(deliveredFilter)

	countResult := []CountCustomerOrdersByStatusModel{
		{
			Status:   enum.CustomerOrderStatus.READY_TO_PICK,
			Quantity: readyToPickOrders.Total,
		},
		{
			Status:   enum.CustomerOrderStatus.PICKED,
			Quantity: pickedOrders.Total,
		},
		{
			Status:   enum.CustomerOrderStatus.WAIT_TO_DELIVERY,
			Quantity: waitToDeliveryOrders.Total,
		},
		{
			Status:   enum.CustomerOrderStatus.DELIVERED,
			Quantity: deliveredOrders.Total,
		},
	}

	return &common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   countResult,
	}
}

func MigrateCustomerInfo() *common.APIResponse {
	var limit int64 = 1000
	customerMap := make(map[string]*model.Customer)
	filter := bson.M{}
	for {
		shippingOrderRaw := model.ShippingOrderDB.Query(filter, 0, limit, &bson.M{"_id": -1})
		if shippingOrderRaw.Status != common.APIStatus.Ok {
			break
		}
		shippingOrders := shippingOrderRaw.Data.([]*model.ShippingOrder)
		smallestId := shippingOrders[len(shippingOrders)-1].ID
		filter["_id"] = bson.M{
			"$lt": smallestId,
		}

		for _, shippingOrder := range shippingOrders {
			if shippingOrder.ShippingType == nil {
				continue
			}

			var customer *model.Customer
			if *shippingOrder.ShippingType == enum.ShippingOrderType.DELIVERY ||
				*shippingOrder.ShippingType == enum.ShippingOrderType.INTERNAL_TRANS {
				customer = &model.Customer{}
				customer.Name = shippingOrder.CustomerName
				customer.Phone = shippingOrder.CustomerPhone
				customer.Code = shippingOrder.CustomerCode
			}

			if *shippingOrder.ShippingType == enum.ShippingOrderType.PICKUP ||
				*shippingOrder.ShippingType == enum.ShippingOrderType.RETURN ||
				*shippingOrder.ShippingType == enum.ShippingOrderType.FMPO {
				customer = &model.Customer{}
				customer.Name = shippingOrder.FromCustomerName
				customer.Phone = shippingOrder.FromCustomerPhone
				customer.Code = shippingOrder.FromCustomerCode
			}

			if customer == nil || customer.Code == "" {
				continue
			}

			if shippingOrder.CustomerInfo != nil {
				continue
			}

			if _, err := strconv.Atoi(customer.Code); err == nil {
				customerMap[customer.Code] = customer
			}

			model.ShippingOrderDB.UpdateOne(bson.M{
				"_id": shippingOrder.ID,
			}, bson.M{
				"customer_info": *customer,
			})
		}
	}

	for _, customer := range customerMap {
		existCustomer := model.CustomerDB.QueryOne(bson.M{
			"code": customer.Code,
		})
		if existCustomer.Status == common.APIStatus.Ok {
			continue
		}

		customer.CustomerId = model.GenId("CUSTOMER_ID")
		customer.CustomerType = &enum.CustomerType.INTERNAL
		newKeyword, keyErr := utils.GenKeyword(customer.Name, customer.Code, customer.CustomerId, customer.Phone)
		if keyErr != nil {
			continue
		}
		customer.Keyword = newKeyword
		model.CustomerDB.Create(customer)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}
}

func CustomerOrderStatusToShippingOrderQuery(customerOrderStatus enum.CustomerOrderStatusValue) (bson.M, error) {
	filter := bson.M{}
	switch customerOrderStatus {
	case enum.CustomerOrderStatus.READY_TO_PICK:
		readyToPickStatuses := []enum.TPLCallbackStatusValue{
			enum.TPLCallbackStatus.READY_TO_PICK,
			enum.TPLCallbackStatus.PICKING,
			enum.TPLCallbackStatus.PICK_FAIL,
		}
		filter["status"] = bson.M{"$in": readyToPickStatuses}

		orderTypes := []enum.ShippingOrderTypeValue{
			enum.ShippingOrderType.PICKUP,
			enum.ShippingOrderType.FMPO,
			enum.ShippingOrderType.RETURN,
		}
		filter["type"] = bson.M{"$in": orderTypes}
		return filter, nil
	case enum.CustomerOrderStatus.PICKED:
		pickedStatuses := []enum.TPLCallbackStatusValue{
			enum.TPLCallbackStatus.PICKED,
			enum.TPLCallbackStatus.STORING,
			enum.TPLCallbackStatus.TRANSPORTING,
			enum.TPLCallbackStatus.DELIVERING,
			enum.TPLCallbackStatus.DELIVERED,
			enum.TPLCallbackStatus.LOST,
			enum.TPLCallbackStatus.COMPLETED,
		}
		filter["status"] = bson.M{"$in": pickedStatuses}

		orderTypes := []enum.ShippingOrderTypeValue{
			enum.ShippingOrderType.PICKUP,
			enum.ShippingOrderType.FMPO,
			enum.ShippingOrderType.RETURN,
		}
		filter["type"] = bson.M{"$in": orderTypes}
		return filter, nil
	case enum.CustomerOrderStatus.WAIT_TO_DELIVERY:
		deliveringStatuses := []enum.TPLCallbackStatusValue{
			enum.TPLCallbackStatus.READY_TO_PICK,
			enum.TPLCallbackStatus.PICKING,
			enum.TPLCallbackStatus.PICKED,
			enum.TPLCallbackStatus.TRANSPORTING,
			enum.TPLCallbackStatus.STORING,
			enum.TPLCallbackStatus.DELIVERING,
			enum.TPLCallbackStatus.DELIVERY_FAIL,
			enum.TPLCallbackStatus.PICK_FAIL,
		}
		filter["status"] = bson.M{"$in": deliveringStatuses}

		filter["type"] = enum.ShippingOrderType.DELIVERY
		return filter, nil
	case enum.CustomerOrderStatus.DELIVERED:
		deliveredStatuses := []enum.TPLCallbackStatusValue{
			enum.TPLCallbackStatus.DELIVERED,
			enum.TPLCallbackStatus.COD_COLLECTED,
			enum.TPLCallbackStatus.COMPLETED,
		}
		filter["status"] = bson.M{"$in": deliveredStatuses}

		filter["type"] = enum.ShippingOrderType.DELIVERY
		return filter, nil
	}

	return nil, fmt.Errorf("Không tìm thấy trạng thái đơn hàng")
}

func MigrateSellerInfo() *common.APIResponse {
	var limit int64 = 1000
	customerMap := make(map[string]*model.Customer)
	var needDeleteCustomer []string
	filter := bson.M{}
	for {
		shippingOrderRaw := model.ShippingOrderDB.Query(filter, 0, limit, &bson.M{"_id": -1})
		if shippingOrderRaw.Status != common.APIStatus.Ok {
			break
		}
		shippingOrders := shippingOrderRaw.Data.([]*model.ShippingOrder)
		smallestId := shippingOrders[len(shippingOrders)-1].ID
		filter["_id"] = bson.M{
			"$lt": smallestId,
		}

		for _, shippingOrder := range shippingOrders {
			if shippingOrder.ShippingType == nil {
				continue
			}

			if shippingOrder.CustomerCode == "" {
				continue
			}

			var customer *model.Customer
			if externalId, err := strconv.Atoi(shippingOrder.CustomerCode); err == nil &&
				*shippingOrder.ShippingType == enum.ShippingOrderType.DELIVERY &&
				strings.HasPrefix(shippingOrder.ReferenceCode, "RO") {
				_, ok := customerMap[shippingOrder.CustomerCode]
				if ok {
					continue
				}
				customer = &model.Customer{}
				customer.Name = shippingOrder.CustomerName
				customer.Phone = shippingOrder.CustomerPhone
				customer.ExternalId = int64(externalId)
				needDeleteCustomer = append(needDeleteCustomer, shippingOrder.CustomerCode)
				customerMap[shippingOrder.CustomerCode] = customer
			} else {
				continue
			}

			if customer == nil {
				continue
			}

			seller, err := client.Services.SellerClient.GetSellerById(customer.ExternalId)
			if err != nil || seller == nil {
				continue
			}

			customer.Code = seller.Code
			if seller.SellerClass == "VENDOR" {
				customer.CustomerType = &enum.CustomerType.VENDOR
			}

			if seller.SellerClass == "EXTERNAL" {
				customer.CustomerType = &enum.CustomerType.SELLER
			}

			model.ShippingOrderDB.UpdateOne(bson.M{
				"_id": shippingOrder.ID,
			}, bson.M{
				"customer_info": *customer,
			})
		}
	}

	if len(needDeleteCustomer) > 0 {
		model.CustomerDB.Delete(bson.M{
			"code": bson.M{
				"$in": needDeleteCustomer,
			},
		})
	}

	for _, customer := range customerMap {
		existCustomer := model.CustomerDB.QueryOne(bson.M{
			"code": customer.Code,
		})
		if existCustomer.Status == common.APIStatus.Ok {
			continue
		}

		customer.CustomerId = model.GenId("CUSTOMER_ID")
		newKeyword, keyErr := utils.GenKeyword(customer.Name, customer.Code, customer.CustomerId, customer.Phone)
		if keyErr != nil {
			continue
		}
		customer.Keyword = newKeyword
		model.CustomerDB.Create(customer)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}
}

func FillMissingCustomerType() *common.APIResponse {
	return model.CustomerDB.UpdateMany(bson.M{
		"customer_type": bson.M{
			"$exists": false,
		},
	}, bson.M{
		"customer_type": enum.CustomerType.INTERNAL,
	})
}

func ImportEOOrders(req request.ImportOrdersRequest, createdBy int64) *common.APIResponse {
	if len(req.Orders) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Danh sách đơn hàng không được để trống",
		}
	}
	// Validate and auto fill province, district, ward code
	validateAddressError := ValidateAndAutoFillImportEoOrders(&req)
	if len(validateAddressError) > 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Dữ liệu không hợp lệ",
			Data:    validateAddressError,
		}
	}

	// Book shipping order
	for _, order := range req.Orders {
		go BookShippingOrder(order, createdBy)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Tạo đơn hàng thành công",
	}
}

func ValidateAndAutoFillImportEoOrders(req *request.ImportOrdersRequest) []response.ImportExcelError {
	// provinceName -> districtName -> wardName -> index
	wardPath := map[string]map[string]map[string]int{}
	// provinceName -> districtName -> index
	districtPath := map[string]map[string]int{}

	needVerifyDistrict := []model.ValidatePath{}
	needVerifyWard := []model.ValidatePath{}
	var errorOrders []response.ImportExcelError
	for index, order := range req.Orders {
		if order.To == nil ||
			order.To.ProvinceName == "" ||
			order.To.DistrictName == "" {
			errorOrders = append(errorOrders, response.ImportExcelError{
				LineId:  index + 1,
				Message: "Địa chỉ nhận hàng không được để trống",
			})
			continue
		}

		if order.From == nil ||
			order.From.ProvinceName == "" ||
			order.From.DistrictName == "" {
			errorOrders = append(errorOrders, response.ImportExcelError{
				LineId:  index + 1,
				Message: "Địa chỉ gửi hàng không được để trống",
			})
			continue
		}
		order.ShippingType = &enum.ShippingOrderType.EO

		if order.From.WardName == "" {
			needVerifyDistrict = FillDistrictPath(order.From, districtPath, index, needVerifyDistrict, true, false)
		} else {
			needVerifyWard = FillWardPath(order.From, wardPath, index, needVerifyWard, true, false)
		}

		if order.To.WardName == "" {
			needVerifyDistrict = FillDistrictPath(order.To, districtPath, index, needVerifyDistrict, false, true)
		} else {
			needVerifyWard = FillWardPath(order.To, wardPath, index, needVerifyWard, false, true)
		}
	}

	// Vefify path in parallel
	needVerifyArr := append(needVerifyDistrict, needVerifyWard...)
	verifyPathJobs := make(chan *model.ValidatePath, len(needVerifyArr))
	type ValidatePathResult struct {
		Err     error
		FromIds []int
		ToIds   []int
	}
	verifyPathResults := make(chan ValidatePathResult, len(needVerifyArr))
	numberOfWorker := 4
	for w := 1; w <= numberOfWorker; w++ {
		go func(js <-chan *model.ValidatePath, rs chan<- ValidatePathResult) {
			for j := range js {
				err := ValidateImportEoAddressOrder(j)
				rs <- ValidatePathResult{
					FromIds: j.FromOrderIndex,
					ToIds:   j.ToOrderIndex,
					Err:     err,
				}
			}
		}(verifyPathJobs, verifyPathResults)
	}
	for i := range needVerifyArr {
		verifyPathJobs <- &needVerifyArr[i]
	}
	close(verifyPathJobs)
	// Gather errors
	for i := 0; i < len(needVerifyArr); i++ {
		validateResult := <-verifyPathResults
		if validateResult.Err != nil {
			if len(validateResult.ToIds) > 0 {
				for _, toId := range validateResult.ToIds {
					errorOrders = append(errorOrders, response.ImportExcelError{
						Message: "Lỗi địa chỉ nhận hàng: " + validateResult.Err.Error(),
						LineId:  toId + 1,
					})
				}
			}
			if len(validateResult.FromIds) > 0 {
				for _, fromId := range validateResult.FromIds {
					errorOrders = append(errorOrders, response.ImportExcelError{
						Message: "Lỗi địa chỉ gửi hàng: " + validateResult.Err.Error(),
						LineId:  fromId + 1,
					})
				}

			}
		}
	}
	close(verifyPathResults)
	if len(errorOrders) > 0 {
		return errorOrders
	}

	for _, verifiedPath := range needVerifyArr {
		if len(verifiedPath.ToOrderIndex) > 0 {
			for _, toId := range verifiedPath.ToOrderIndex {
				req.Orders[toId].To.ProvinceCode = verifiedPath.ProvinceCode
				req.Orders[toId].To.DistrictCode = verifiedPath.DistrictCode
				req.Orders[toId].To.WardCode = verifiedPath.WardCode
			}
		}
		if len(verifiedPath.FromOrderIndex) > 0 {
			for _, fromId := range verifiedPath.FromOrderIndex {
				req.Orders[fromId].From.ProvinceCode = verifiedPath.ProvinceCode
				req.Orders[fromId].From.DistrictCode = verifiedPath.DistrictCode
				req.Orders[fromId].From.WardCode = verifiedPath.WardCode
			}
		}
	}

	// Validate fm and lm hub
	type ValidateHubJob struct {
		OrderIndex int
		Request    *request.BookShippingOrder
		Error      error
	}
	type ValidateHubResult struct {
		Err        error
		FmHub      *model.Hub
		LmHub      *model.Hub
		OrderIndex int
	}
	validateHubJobs := make(chan ValidateHubJob, len(req.Orders))
	validateHubResults := make(chan ValidateHubResult, len(req.Orders))
	validateHubWorker := 6

	for w := 1; w <= validateHubWorker; w++ {
		go func(js <-chan ValidateHubJob, rs chan<- ValidateHubResult) {
			for j := range js {
				fmHub, lmHub, err := ValidateImportEoHub(j.Request)
				rs <- ValidateHubResult{
					Err:        err,
					FmHub:      fmHub,
					LmHub:      lmHub,
					OrderIndex: j.OrderIndex,
				}
			}
		}(validateHubJobs, validateHubResults)
	}
	for i, order := range req.Orders {
		validateHubJobs <- ValidateHubJob{
			OrderIndex: i,
			Request:    order,
		}
	}
	close(validateHubJobs)
	// Gather errors
	for i := 0; i < len(req.Orders); i++ {
		validateResult := <-validateHubResults
		if validateResult.Err != nil {
			errorOrders = append(errorOrders, response.ImportExcelError{
				Message: validateResult.Err.Error(),
				LineId:  validateResult.OrderIndex + 1,
			})
			continue
		}
		req.Orders[validateResult.OrderIndex].FmHub = validateResult.FmHub
		req.Orders[validateResult.OrderIndex].FromHubCode = validateResult.FmHub.Code
		req.Orders[validateResult.OrderIndex].LmHub = validateResult.LmHub
		req.Orders[validateResult.OrderIndex].ToHubCode = validateResult.LmHub.Code

	}
	close(validateHubResults)

	if len(errorOrders) > 0 {
		return errorOrders
	}

	errorOrders = ValidateImportCustomerInfo(req.Orders)
	if len(errorOrders) > 0 {
		return errorOrders
	}

	return nil
}

func ValidateImportEoAddressOrder(bookReq *model.ValidatePath) error {
	// Validate address
	provinceRaw := model.ProvinceMappingDB.QueryOne(bson.M{
		"dictionary": bookReq.ProvinceName,
	})
	if provinceRaw.Status != common.APIStatus.Ok {
		return fmt.Errorf("Tỉnh/Thành phố không hợp lệ: %s", bookReq.ProvinceName)
	}
	province := provinceRaw.Data.([]*model.ProvinceMapping)[0]
	districtRaw := model.DistrictMappingDB.QueryOne(bson.M{
		"dictionary":    bookReq.DistrictName,
		"province_code": province.Code,
	})
	if districtRaw.Status != common.APIStatus.Ok {
		return fmt.Errorf("Quận/Huyện không hợp lệ: %s", bookReq.DistrictName)
	}
	bookReq.ProvinceCode = province.Code
	bookReq.DistrictCode = districtRaw.Data.([]*model.DistrictMapping)[0].Code
	if bookReq.WardName != "" {
		wardRaw := model.WardMappingDB.QueryOne(bson.M{
			"dictionary":    bookReq.WardName,
			"district_code": bookReq.DistrictCode,
		})
		if wardRaw.Status != common.APIStatus.Ok {
			return fmt.Errorf("Xã/Phường không hợp lệ: %s", bookReq.WardName)
		}
		bookReq.WardCode = wardRaw.Data.([]*model.WardMapping)[0].Code
	}
	return nil
}

func ValidateImportEoHub(bookReq *request.BookShippingOrder) (*model.Hub, *model.Hub, error) {
	var lmHub, fmHub *model.Hub
	if bookReq.ReceiveAtHubCode != "" {
		hubRaw := model.HubDB.QueryOne(bson.M{
			"code": bookReq.ReceiveAtHubCode,
		})
		if hubRaw.Status != common.APIStatus.Ok {
			return nil, nil, fmt.Errorf("Hub không hợp lệ: %s", bookReq.ReceiveAtHubCode)
		}
		lmHub = hubRaw.Data.([]*model.Hub)[0]
	}

	if lmHub == nil {
		lmHub = FindNearestHubOfAddressV2(*bookReq.To)
	}

	if lmHub == nil {
		return nil, nil, fmt.Errorf("Không tìm thấy hub giao hàng")
	}
	// Nếu book dropoff thì lấy hub lên để check
	if bookReq.DropOffAtHubCode != "" {
		hubRaw := model.HubDB.QueryOne(bson.M{
			"code": bookReq.DropOffAtHubCode,
		})
		if hubRaw.Status != common.APIStatus.Ok {
			return nil, nil, fmt.Errorf("Hub không hợp lệ: %s", bookReq.FromHubCode)
		}
		fmHub = hubRaw.Data.([]*model.Hub)[0]
		return fmHub, lmHub, nil
	}

	fmHub = FindNearestHubOfAddressV2(*bookReq.From)
	if fmHub == nil {
		return nil, nil, fmt.Errorf("Không tìm thấy hub lấy hàng")
	}

	return fmHub, lmHub, nil
}

func FillWardPath(
	address *model.Address,
	path map[string]map[string]map[string]int,
	index int,
	needValidatePath []model.ValidatePath,
	isFrom bool,
	isTo bool) []model.ValidatePath {
	_, provinceOk := path[address.ProvinceName]
	if !provinceOk {
		path[address.ProvinceName] = map[string]map[string]int{}
	}
	_, districtOk := path[address.ProvinceName][address.DistrictName]
	if !districtOk {
		path[address.ProvinceName][address.DistrictName] = map[string]int{}
	}
	_, fromWardOk := path[address.ProvinceName][address.DistrictName][address.WardName]
	if !fromWardOk {
		path[address.ProvinceName][address.DistrictName][address.WardName] = -1
	}
	if value, ok := path[address.ProvinceName][address.DistrictName][address.WardName]; ok {
		if value == -1 {
			path[address.ProvinceName][address.DistrictName][address.WardName] = index
			validatePath := model.ValidatePath{
				ProvinceName: address.ProvinceName,
				DistrictName: address.DistrictName,
				WardName:     address.WardName,
			}
			if isFrom {
				validatePath.FromOrderIndex = []int{index}
			}
			if isTo {
				validatePath.ToOrderIndex = []int{index}
			}
			needValidatePath = append(needValidatePath, validatePath)
			return needValidatePath
		}
		if isFrom {
			for nvp := range needValidatePath {
				if needValidatePath[nvp].ProvinceName == address.ProvinceName &&
					needValidatePath[nvp].DistrictName == address.DistrictName &&
					needValidatePath[nvp].WardName == address.WardName {
					needValidatePath[nvp].FromOrderIndex = append(needValidatePath[nvp].FromOrderIndex, index)
				}
			}
		}
		if isTo {
			for nvp := range needValidatePath {
				if needValidatePath[nvp].ProvinceName == address.ProvinceName &&
					needValidatePath[nvp].DistrictName == address.DistrictName &&
					needValidatePath[nvp].WardName == address.WardName {
					needValidatePath[nvp].ToOrderIndex = append(needValidatePath[nvp].ToOrderIndex, index)
				}
			}
		}
	}
	return needValidatePath
}

func FillDistrictPath(
	address *model.Address,
	path map[string]map[string]int,
	index int,
	needValidatePath []model.ValidatePath,
	isFrom bool,
	isTo bool) []model.ValidatePath {
	_, provinceOk := path[address.ProvinceName]
	if !provinceOk {
		path[address.ProvinceName] = map[string]int{}
	}
	_, districtOk := path[address.ProvinceName][address.DistrictName]
	if !districtOk {
		path[address.ProvinceName][address.DistrictName] = -1
	}
	if value, ok := path[address.ProvinceName][address.DistrictName]; ok {
		if value == -1 {
			path[address.ProvinceName][address.DistrictName] = index
			validatePath := model.ValidatePath{
				ProvinceName: address.ProvinceName,
				DistrictName: address.DistrictName,
			}
			if isFrom {
				validatePath.FromOrderIndex = []int{index}
			}
			if isTo {
				validatePath.ToOrderIndex = []int{index}
			}
			needValidatePath = append(needValidatePath, validatePath)
			return needValidatePath
		}
		if isFrom {
			needValidatePath[value].FromOrderIndex = append(needValidatePath[value].FromOrderIndex, index)
		}
		if isTo {
			needValidatePath[value].ToOrderIndex = append(needValidatePath[value].ToOrderIndex, index)
		}
	}
	return needValidatePath
}

func ValidateImportCustomerInfo(orders []*request.BookShippingOrder) []response.ImportExcelError {
	customerMap := make(map[int64][]int64)
	for index, order := range orders {
		customerMap[order.CustomerInfo.CustomerId] = append(customerMap[order.CustomerInfo.CustomerId], int64(index))
	}
	checkCusIds := []int64{}
	for cusId := range customerMap {
		checkCusIds = append(checkCusIds, cusId)
	}
	customersRaw := model.CustomerDB.Query(bson.M{
		"customer_id": bson.M{
			"$in": checkCusIds,
		},
	}, 0, int64(len(checkCusIds)), nil)
	if customersRaw.Status != common.APIStatus.Ok {
		return []response.ImportExcelError{
			{
				Message: "Không tìm thấy thông tin khách hàng",
			},
		}
	}
	customers := customersRaw.Data.([]*model.Customer)
	for _, c := range customers {
		ids, ok := customerMap[c.CustomerId]
		if ok {
			for _, index := range ids {
				orders[index].CustomerInfo.CustomerId = c.CustomerId
				orders[index].CustomerInfo.Name = c.Name
				orders[index].CustomerInfo.Phone = c.Phone
				orders[index].CustomerInfo.Code = c.Code
				orders[index].CustomerInfo.CustomerType = c.CustomerType
				orders[index].CustomerInfo.AppliedFees = c.AppliedFees
			}
		}
	}

	// Loop để check xem có order nào thiếu thông tin khách hàng không
	var res []response.ImportExcelError
	type ConfigFeePlaceHolder struct {
		ConfigId int64
		Code     string
	}
	tempIntM := map[int][]int{}
	tempIntA := []int{}
	tempStrM := map[string][]int{}
	tempStrA := []string{}
	// Map Order và Biểu phí
	for i, o := range orders {
		idStr := strconv.FormatInt(o.CustomerInfo.CustomerId, 10)
		if o.CustomerInfo.Code == "" {
			res = append(res, response.ImportExcelError{
				LineId:  i + 1,
				Message: "Mã khách hàng không hợp lệ: " + idStr,
			})
			continue
		}
		if o.CustomerInfo.CustomerId == 0 {
			res = append(res, response.ImportExcelError{
				LineId:  i + 1,
				Message: "Không tìm thấy thông tin khách hàng",
			})
			continue
		}
		var configFeeCode string
		if o.DropOffAtHubCode != "" {
			configFeeCode = conf.Config.DefaultEOTransNDeliCode
		} else {
			configFeeCode = conf.Config.DefaultEOPickNDeliCode
		}
		var checkConfigId int64 = 0
		if o.CustomerInfo.AppliedFees != nil {
			for _, fee := range *o.CustomerInfo.AppliedFees {
				if o.DropOffAtHubCode != "" &&
					o.ReceiveAtHubCode != "" &&
					fee.ConfigFeeType == enum.ConfigFeeType.TRANSPORT {
					checkConfigId = fee.ConfigFeeId
					break
				}

				if o.DropOffAtHubCode == "" &&
					o.ReceiveAtHubCode != "" &&
					fee.ConfigFeeType == enum.ConfigFeeType.PICK_N_TRANSPORT {
					checkConfigId = fee.ConfigFeeId
					break
				}

				if o.DropOffAtHubCode != "" &&
					o.ReceiveAtHubCode == "" &&
					fee.ConfigFeeType == enum.ConfigFeeType.TRANSPORT_N_DELIVERY {
					checkConfigId = fee.ConfigFeeId
					break
				}

				if o.DropOffAtHubCode == "" &&
					o.ReceiveAtHubCode == "" &&
					fee.ConfigFeeType == enum.ConfigFeeType.PICK_N_DELIVERY {
					checkConfigId = fee.ConfigFeeId
					break
				}
			}
		}
		if checkConfigId != 0 {
			tempIntM[int(checkConfigId)] = append(tempIntM[int(checkConfigId)], i)
			tempIntA = append(tempIntA, int(checkConfigId))
		} else {
			tempStrM[configFeeCode] = append(tempStrM[configFeeCode], i)
			tempStrA = append(tempStrA, configFeeCode)
		}
		o.FromAddress = o.From
		o.ToAddress = o.To

	}
	if len(res) > 0 {
		return res
	}

	filter := bson.M{
		"$or": bson.A{
			bson.M{
				"config_id": bson.M{
					"$in": tempIntA,
				},
			},
			bson.M{
				"code": bson.M{
					"$in": tempStrA,
				},
			},
		},
	}

	configFeeRaw := model.ConfigFeeDB.Query(filter, 0, 200, nil)

	if configFeeRaw.Status != common.APIStatus.Ok {
		return []response.ImportExcelError{
			{
				Message: "Không tìm thấy biểu phí thỏa điều kiện",
			},
		}
	}
	configFees := configFeeRaw.Data.([]*model.ConfigFee)
	for _, configFee := range configFees {
		ids, ok := tempIntM[int(configFee.ConfigId)]
		if ok {
			for _, idx := range ids {
				orders[idx].ConfigFee = configFee
			}
		}
		ids, ok = tempStrM[configFee.Code]
		if ok {
			for _, idx := range ids {
				orders[idx].ConfigFee = configFee
			}
		}
	}
	type VerifyConfigFeeResult struct {
		Err    error
		LineId int
	}
	type VerifyConfigJob struct {
		Order  *request.BookShippingOrder
		LineId int
	}
	// Verify path in parallel
	verifyConfigFeeJobs := make(chan *VerifyConfigJob, len(orders))
	verifyConfigFeeResults := make(chan VerifyConfigFeeResult, len(orders))
	numberOfWorker := 8
	for w := 1; w <= numberOfWorker; w++ {
		go func(js <-chan *VerifyConfigJob, rs chan<- VerifyConfigFeeResult) {
			for j := range js {
				_, err := CalculateOrderFee(*j.Order, j.Order.ConfigFee)
				rs <- VerifyConfigFeeResult{
					Err:    err,
					LineId: j.LineId,
				}
			}
		}(verifyConfigFeeJobs, verifyConfigFeeResults)
	}
	for i := range orders {
		verifyConfigFeeJobs <- &VerifyConfigJob{
			Order:  orders[i],
			LineId: i + 1,
		}
	}
	close(verifyConfigFeeJobs)
	for i := 0; i < len(orders); i++ {
		validateResult := <-verifyConfigFeeResults
		if validateResult.Err != nil {
			res = append(res, response.ImportExcelError{
				Message: validateResult.Err.Error(),
				LineId:  validateResult.LineId,
			})
		}
	}

	if len(res) > 0 {
		return res
	}
	return nil
}

func UpdateOrdersNote(req []request.UpdateOrderNoteRequest, updateBy int64) *common.APIResponse {
	for _, r := range req {
		strUpdateBy := strconv.FormatInt(updateBy, 10)
		model.ShippingOrderDB.UpdateOne(bson.M{
			"reference_code": r.ReferenceCode,
		}, bson.M{
			"note":            r.Note,
			"last_updated_by": strUpdateBy,
		})
		model.HUBShippingOrderDB.UpdateMany(bson.M{
			"reference_code": r.ReferenceCode,
		}, bson.M{
			"note":            r.Note,
			"last_updated_by": strUpdateBy,
		})
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}
}

func RemoveRequireVerify(codes []string) *common.APIResponse {
	model.ShippingOrderDB.UpdateMany(bson.M{
		"reference_code": bson.M{
			"$in": codes,
		},
	}, bson.M{
		"need_verify_receiver": false,
		"verification_code":    "",
	})

	model.HUBShippingOrderDB.UpdateMany(bson.M{
		"reference_code": bson.M{
			"$in": codes,
		},
	}, bson.M{
		"need_verify_receiver": false,
		"verification_code":    "",
	})
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}
}

func CheckSplittingPickupOrder(orderPickup *model.HubShippingOrder, binCode string, hubCode string) *common.APIResponse {
	hasRemoved := false
	for _, product := range orderPickup.Products {
		if product.SKU == binCode && *product.Status == enum.ProductStatus.REMOVED {
			hasRemoved = true
			break
		}
	}
	if !hasRemoved {
		return &common.APIResponse{
			Status: common.APIStatus.NotFound,
		}
	}
	// nếu bin đã bị xóa khỏi đơn lấy thì kiểm tra đơn lấy BIN mới được tách ra từ đơn hiện tại
	hubOrderTakeRemoveBinResp := model.HUBShippingOrderDB.QueryOne(bson.M{
		"parent_reference_code": orderPickup.ReferenceCode,
		"type":                  enum.HubOrderType.PICKUP,
	})
	if hubOrderTakeRemoveBinResp.Status == common.APIStatus.Ok {
		hubOrderTakeRemoveBin := hubOrderTakeRemoveBinResp.Data.([]*model.HubShippingOrder)[0]

		checkNestedOrderResp := CheckSplittingPickupOrder(hubOrderTakeRemoveBin, binCode, hubCode)
		if checkNestedOrderResp.Status != common.APIStatus.NotFound {
			return checkNestedOrderResp
		}
		// nếu có đơn lấy thì kiểm tra xem có phiếu luân chuyển
		handOverResp := model.HandoverTicketDB.QueryOne(bson.M{
			"so_list": bson.M{
				"$elemMatch": bson.M{
					"so": hubOrderTakeRemoveBin.ReferenceCode,
					"status": bson.M{
						"$ne": "CANCEL",
					},
				},
			},
			"status": bson.M{
				"$ne": enum.HandoverStatus.CANCEL,
			},
		})
		if handOverResp.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "BIN có Hub lấy khác Hub hiện tại và chưa tạo phiếu luân chuyển về kho",
				Data: []response.PrepareCheckinResponse{
					{
						BinCode: binCode,
						Status:  enum.PrepareCheckinStatus.MISSING_TRANSFER_TO_WH,
					},
				},
			}
		} else {
			// nếu có phiếu luân chuyển thì kiểm tra xem có phiếu luân chuyển về kho không
			handOverResp := model.HandoverTicketDB.QueryOne(bson.M{
				"so_list": bson.M{
					"$elemMatch": bson.M{
						"so": hubOrderTakeRemoveBin.ReferenceCode,
						"status": bson.M{
							"$ne": "CANCEL",
						},
					},
				},
				"to_department_code": hubCode,
				"status": bson.M{
					"$ne": enum.HandoverStatus.CANCEL,
				},
			})
			if handOverResp.Status != common.APIStatus.Ok {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Bin trong phiếu luân chuyển nhưng không về kho đích",
					Data: []response.PrepareCheckinResponse{
						{
							BinCode: binCode,
							Status:  enum.PrepareCheckinStatus.MISSING_TRANSFER_TO_WH,
						},
					},
				}
			}
			handOver := handOverResp.Data.([]*model.HandoverTicket)[0]
			totalBin := 0
			isInvalid := false
			for _, so := range handOver.SOList {
				for _, product := range so.Products {
					if product.Status == nil ||
						*product.Status == enum.ProductStatus.STORING ||
						*product.Status == enum.ProductStatus.LOST ||
						*product.Status == enum.ProductStatus.REMOVED {
						if product.SKU == binCode {
							isInvalid = true
						}
						continue
					}
					totalBin++
				}
			}
			if !isInvalid {
				return &common.APIResponse{
					Status:  common.APIStatus.Ok,
					Message: "Mã bin hợp lệ",
					Data: []response.PrepareCheckinResponse{
						{
							BinCode:       binCode,
							Status:        enum.PrepareCheckinStatus.TRANSPORTING_TO_WH,
							TransportCode: handOver.Code,
							TotalBin:      totalBin,
						},
					},
				}
			}

		}
	}

	return &common.APIResponse{
		Status: common.APIStatus.NotFound,
	}
}

func PrepareCheckin(req request.PrepareCheckinRequest) *common.APIResponse {
	var hub *model.Hub
	if req.CheckinAt == enum.CheckInAt.WAREHOUSE {
		hubResp := model.HubDB.QueryOne(bson.M{
			"warehouse_reference_code": req.WarehouseCode,
		})
		if hubResp.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không tìm thấy thông tin kho",
			}
		}
		hub = hubResp.Data.([]*model.Hub)[0]
	}
	if hub == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không tìm thấy thông tin kho",
		}
	}

	locationDetail, err := client.Services.WarehouseCoreClient.GetLocationDetail(req.WarehouseCode, req.BinCode)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không tìm thấy " + req.BinCode,
		}
	}
	if !*locationDetail.IsUsed {
		return &common.APIResponse{
			Status: common.APIStatus.Invalid,
			Data: []response.PrepareCheckinResponse{
				{
					BinCode: req.BinCode,
					Status:  enum.PrepareCheckinStatus.NOT_USED,
				},
			},
		}
	}

	deliveringOrderResp := model.ShippingOrderDB.QueryOne(bson.M{
		"status": bson.M{
			"$in": []enum.TPLCallbackStatusValue{
				enum.TPLCallbackStatus.READY_TO_PICK,
				enum.TPLCallbackStatus.PICKING,
				enum.TPLCallbackStatus.PICKED,
				enum.TPLCallbackStatus.DELIVERING,
				enum.TPLCallbackStatus.PICK_FAIL,
				enum.TPLCallbackStatus.TRANSPORTING,
				enum.TPLCallbackStatus.STORING,
				enum.TPLCallbackStatus.DELIVERY_FAIL,
			},
		},
		"type":         enum.ShippingOrderType.DELIVERY,
		"baskets.code": req.BinCode,
	})
	if deliveringOrderResp.Status == common.APIStatus.Ok {
		// case: bin thuộc 1 đơn đang giao và chưa có đơn lấy bin
		//note : đơn giao hàng đang giao chắc chắn chưa tạo đơn lấy bin
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Bin thuộc đơn hàng đang giao: " + deliveringOrderResp.Data.([]*model.ShippingOrder)[0].ReferenceCode,
			Data: []response.PrepareCheckinResponse{
				{
					BinCode: req.BinCode,
					Status:  enum.PrepareCheckinStatus.DELIVERING,
				},
			},
		}

	}

	deliveredOrdersResp := model.ShippingOrderDB.Query(bson.M{
		"type":         enum.ShippingOrderType.DELIVERY,
		"baskets.code": req.BinCode,
		"status": bson.M{"$in": []enum.HubShippingOrderStatusValue{
			enum.HubShippingOrderStatus.DELIVERED,
			enum.HubShippingOrderStatus.COMPLETED,
		}},
	}, 0, 1, &primitive.M{
		"created_time": -1,
	})
	if deliveredOrdersResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không tìm thấy đơn hàng đã giao có chứa mã bin này",
		}
	}

	lastestShippingOrder := &model.ShippingOrder{}
	lastestShippingOrder = deliveredOrdersResp.Data.([]*model.ShippingOrder)[0]
	trackingCode := lastestShippingOrder.TrackingCode

	// BIN có Hub lấy là Hub hiện tại
	orderPickupAtWhResp := model.HUBShippingOrderDB.Query(bson.M{
		"reference_code":   trackingCode,
		"to_customer_code": hub.Code,
		"type":             enum.ShippingOrderType.PICKUP,
		"status": bson.M{
			"$nin": []enum.TPLCallbackStatusValue{
				enum.TPLCallbackStatus.CANCEL,
				enum.TPLCallbackStatus.COMPLETED,
			},
		},
	}, 0, 1, &primitive.M{
		"created_time": -1,
	})

	if orderPickupAtWhResp.Status == common.APIStatus.Ok {
		orderPickupAtWh := orderPickupAtWhResp.Data.([]*model.HubShippingOrder)[0]
		if *orderPickupAtWh.Status == enum.HubShippingOrderStatus.WAIT_TO_STORING {
			return &common.APIResponse{
				Status:  common.APIStatus.Ok,
				Message: "Mã bin hợp lệ",
				Data: []response.PrepareCheckinResponse{
					{
						BinCode:       req.BinCode,
						ReferenceCode: orderPickupAtWh.ReferenceCode,
						Status:        enum.PrepareCheckinStatus.PICK_AT_WH,
					},
				},
			}
		}
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "BIN có hub lấy là hub thuộc kho đích(" + string(*orderPickupAtWh.Status) + ")",
		}
	}

	orderPickupResp := model.HUBShippingOrderDB.Query(bson.M{
		"reference_code": trackingCode,
		"sub_type":       enum.ShippingOrderType.PICKUP,
		"status": bson.M{
			"$nin": []enum.TPLCallbackStatusValue{
				enum.TPLCallbackStatus.CANCEL,
				enum.TPLCallbackStatus.COMPLETED,
			},
		},
	}, 0, 50, nil)

	if orderPickupResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Đơn giao hàng đã hoàn thành nhưng chưa tạo đơn lấy bin",
		}
	}

	orderPickups := orderPickupResp.Data.([]*model.HubShippingOrder)
	for _, orderPickup := range orderPickups {
		if *orderPickup.Status == enum.HubShippingOrderStatus.LOST {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Bin bị thất lạc cần báo LOG nhập lại",
				Data: []response.PrepareCheckinResponse{
					{
						BinCode: req.BinCode,
						Status:  enum.PrepareCheckinStatus.LOST,
					},
				},
			}
		}

		if *orderPickup.Status == enum.HubShippingOrderStatus.CANCEL {
			continue
		}

		if *orderPickup.Status == enum.HubShippingOrderStatus.COMPLETED || *orderPickup.Status == enum.HubShippingOrderStatus.STORING || *orderPickup.Status == enum.HubShippingOrderStatus.DONE_TRANSPORTING {
			if *orderPickup.Type == enum.HubOrderType.PICKUP {
				checkNestedPickupOrder := CheckSplittingPickupOrder(orderPickup, req.BinCode, hub.Code)
				if checkNestedPickupOrder.Status != common.APIStatus.NotFound {
					return checkNestedPickupOrder
				}
			}
			handOverTicketResp := model.HandoverTicketDB.QueryOne(bson.M{
				"so_list": bson.M{
					"$elemMatch": bson.M{
						"so": orderPickup.ReferenceCode,
						"status": bson.M{
							"$ne": "CANCEL",
						},
					},
				},
				"status": enum.HandoverStatus.COMPLETED,
			})
			if handOverTicketResp.Status == common.APIStatus.Ok {
				handOverTicket := handOverTicketResp.Data.([]*model.HandoverTicket)[0]
				for _, so := range handOverTicket.SOList {
					for _, product := range so.Products {
						if product.SKU == req.BinCode && *product.Status == enum.ProductStatus.LOST {
							return &common.APIResponse{
								Status:  common.APIStatus.Invalid,
								Message: "Bin bị thất lạc cần báo LOG nhập lại",
								Data: []response.PrepareCheckinResponse{
									{
										BinCode: req.BinCode,
										Status:  enum.PrepareCheckinStatus.LOST,
									},
								},
							}
						}
					}
				}
			}
			if len(orderPickups) > 1 {
				// khi orderPickups có nhiều hơn 1 orderPickup và đã có 1 đơn hoàn thành thì nghĩa bin đó đã được chuyển đi
				// k cần tiếp tục keierm tra các điều kiện bên dưới đối với orderPickup này nữa
				continue
			}
		}
		if orderPickup.ToCustomerCode != hub.Code && *orderPickup.Type == enum.HubOrderType.PICKUP && len(orderPickups) == 1 {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "BIN có Hub lấy khác Hub hiện tại và chưa tạo phiếu luân chuyển về kho",
				Data: []response.PrepareCheckinResponse{
					{
						BinCode: req.BinCode,
						Status:  enum.PrepareCheckinStatus.MISSING_TRANSFER_TO_WH,
					},
				},
			}
		}

		if *orderPickup.Type == enum.HubOrderType.TRANSPORTING && orderPickup.HUBCode == hub.Code {
			handOverTicketResp := model.HandoverTicketDB.QueryOne(bson.M{
				"so_list": bson.M{
					"$elemMatch": bson.M{
						"so": orderPickup.ReferenceCode,
						"status": bson.M{
							"$ne": "CANCEL",
						},
					},
				},
				"status": bson.M{
					"$in": []enum.HandoverStatusValue{
						enum.HandoverStatus.TRANSPORTING,
						enum.HandoverStatus.CHECKING,
						enum.HandoverStatus.WAIT_TO_CHECK,
					},
				},
			})
			if handOverTicketResp.Status != common.APIStatus.Ok {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Không tìm thấy thông tin phiếu luân chuyển",
				}
			}
			handOverTicket := handOverTicketResp.Data.([]*model.HandoverTicket)[0]
			totalBin := 0
			isInvalid := false
			for _, so := range handOverTicket.SOList {
				for _, product := range so.Products {
					if product.Status == nil ||
						*product.Status == enum.ProductStatus.STORING ||
						*product.Status == enum.ProductStatus.LOST ||
						*product.Status == enum.ProductStatus.REMOVED {
						if product.SKU == req.BinCode {
							isInvalid = true
						}
						continue
					}
					totalBin++
				}
			}
			if isInvalid {
				continue
			}
			if handOverTicket.ToDepartmentCode != hub.Code {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Bin trong phiếu luân chuyển nhưng không về kho đích",
					Data: []response.PrepareCheckinResponse{
						{
							BinCode: req.BinCode,
							Status:  enum.PrepareCheckinStatus.MISSING_TRANSFER_TO_WH,
						},
					},
				}
			}

			// BIN thuộc 1 phiếu luân chuyển về kho hiện tại
			return &common.APIResponse{
				Status:  common.APIStatus.Ok,
				Message: "Mã bin hợp lệ",
				Data: []response.PrepareCheckinResponse{
					{
						BinCode:       req.BinCode,
						Status:        enum.PrepareCheckinStatus.TRANSPORTING_TO_WH,
						TransportCode: handOverTicket.Code,
						TotalBin:      totalBin,
					},
				},
			}
		}
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Invalid,
		Message: "Mã bin không hợp lệ, không thuộc các trường hợp trên",
	}
}

func CheckinShippingOrder(
	req request.CheckinShippingOrderRequest,
	checkInBy model.HrmAccount) *common.APIResponse {
	var handoverCodeList []string
	transportMap := map[string]request.CheckinTransport{}
	receiveAtWarehouse := req.ReceiveAtWarehouse
	transportingToWarehouse := []*model.TransportingToWarehouse{}
	allBinCodes := []string{}

	if len(receiveAtWarehouse) > 0 {
		for _, checkInItem := range receiveAtWarehouse {
			allBinCodes = append(allBinCodes, checkInItem.Bins...)
			hubShipingOrder := model.HUBShippingOrderDB.QueryOne(bson.M{
				"reference_code": checkInItem.ReferenceCode,
			})
			if hubShipingOrder.Status != common.APIStatus.Ok {
				continue
			}
			hubOrder := hubShipingOrder.Data.([]*model.HubShippingOrder)[0]
			hubOrder.Status = &enum.HubShippingOrderStatus.COMPLETED
			hubOrder.LastUpdatedBy = string(checkInBy.AccountID)
			removedBin := []*model.Product{}
			for _, product := range hubOrder.Products {
				if utils.CheckContainValue(checkInItem.Bins, product.SKU) {
					product.Status = &enum.ProductStatus.STORING
				} else {
					product.Status = &enum.ProductStatus.REMOVED
					removedBin = append(removedBin, product)
				}
			}
			model.HUBShippingOrderDB.UpdateOne(bson.M{
				"reference_code": checkInItem.ReferenceCode,
			}, hubOrder)

			model.ShippingOrderDB.UpdateOne(bson.M{
				"tracking_code": hubOrder.TrackingCode,
			}, bson.M{
				"status": enum.TPLCallbackStatus.COMPLETED,
			})

			if len(removedBin) > 0 {
				_ = shippingOrderQueue.PushCreateHubShippingOrder(shippingOrderQueue.RequestBodyHandover{
					HubOrder:       *hubOrder,
					RemovedBinList: removedBin,
				}, hubOrder.ReferenceCode)
			}
		}
	}

	for _, transport := range req.Transports {
		bins := []string{}
		for _, item := range transport.Items {
			bins = append(bins, item.ReferenceCode)
		}
		allBinCodes = append(allBinCodes, bins...)
		transportingToWarehouse = append(transportingToWarehouse, &model.TransportingToWarehouse{
			Code:     transport.TransportCode,
			Bins:     bins,
			TotalBin: transport.TotalBin,
		})
		handoverCodeList = append(handoverCodeList, transport.TransportCode)
		transportMap[transport.TransportCode] = transport
	}
	handoverTicketsRsp := model.HandoverTicketDB.Query(bson.M{
		"code": bson.M{
			"$in": handoverCodeList,
		},
	}, 0, int64(len(handoverCodeList)), nil)
	if handoverTicketsRsp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Lỗi khi lấy thông tin phiếu giao",
		}
	}
	handoverTickets := handoverTicketsRsp.Data.([]*model.HandoverTicket)
	toDepartmentCode := ""
	// Validate tickets
	for _, handoverTicket := range handoverTickets {
		if handoverTicket.ToDepartmentCode == "" {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không tìm thấy thông tin kho đích trong phiếu luân chuyển: " + handoverTicket.Code,
			}
		}
		if toDepartmentCode == "" {
			toDepartmentCode = handoverTicket.ToDepartmentCode
			continue
		}
		if toDepartmentCode != handoverTicket.ToDepartmentCode {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không thể checkin cùng lúc từ nhiều kho",
			}
		}
	}
	hubResp := model.HubDB.QueryOne(bson.M{
		"code": toDepartmentCode,
	})
	if hubResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Không tìm thấy thông tin kho",
		}
	}
	hub := hubResp.Data.([]*model.Hub)[0]
	if hub.WarehouseReferenceCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không tìm thấy thông tin kho",
		}
	}
	var handoverTicketIdList []int64
	note := map[string]map[string]string{}
	ignoreBins := map[string]bool{}
	for _, handoverTicket := range handoverTickets {
		if handoverTicket.Status == nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Lỗi khi lấy thông tin phiếu giao",
			}
		}
		if *handoverTicket.Status != enum.HandoverStatus.TRANSPORTING &&
			*handoverTicket.Status != enum.HandoverStatus.CHECKING &&
			*handoverTicket.Status != enum.HandoverStatus.WAIT_TO_CHECK {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Trạng thái phiếu luân chuyển không hợp lệ: " + handoverTicket.Code,
			}
		}
		handoverTicketIdList = append(handoverTicketIdList, int64(handoverTicket.TicketID))

		for i, so := range handoverTicket.SOList {
			if so.Status == nil {
				return &common.APIResponse{
					Status:  common.APIStatus.Error,
					Message: "Lỗi khi lấy thông tin đơn hàng",
				}
			}
			if *so.Status == enum.HandoverItemStatus.CANCEL {
				continue
			}
			transport, ok := transportMap[handoverTicket.Code]
			if !ok {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Không tìm thấy thông tin mã bin",
				}
			}

			for _, product := range so.Products {
				bin := transport.FindBinFromCheckinTransportItems(product.SKU)
				if bin == nil {
					request := request.PrepareCheckinRequest{
						BinCode:       product.SKU,
						WarehouseCode: hub.WarehouseReferenceCode,
						CheckinAt:     enum.CheckInAt.WAREHOUSE,
					}
					binStatusResp := PrepareCheckin(request)
					if binStatusResp.Status != common.APIStatus.Ok {
						ignoreBins[product.SKU] = true
					}
					continue
				}

				if product.Status == nil ||
					*product.Status == enum.ProductStatus.LOST ||
					*product.Status == enum.ProductStatus.STORING ||
					*product.Status == enum.ProductStatus.REMOVED {
					continue
				}
				product.Status = &enum.ProductStatus.SCANNING
				receivedQuantity := handoverTicket.SOList[i].ReceivedQuantity + int64(bin.Quantity)
				if receivedQuantity > handoverTicket.SOList[i].ScannedQuantity {
					receivedQuantity = handoverTicket.SOList[i].ScannedQuantity
				}
				handoverTicket.SOList[i].ReceivedQuantity = receivedQuantity
			}
			handoverTicket.SOList[i].Products = so.Products

			if handoverTicket.SOList[i].ReceivedQuantity ==
				handoverTicket.SOList[i].ScannedQuantity {
				handoverTicket.SOList[i].Status = &enum.HandoverItemStatus.DONE
			}
		}

		for i, so := range handoverTicket.SOList {
			if so.Status == nil {
				continue
			}
			if len(so.Products) == 1 {
				bin := so.Products[0].SKU
				if ignoreBins[bin] {
					continue
				}
			}
			if so.ReceivedQuantity < so.ScannedQuantity {
				handoverTicket.SOList[i].Note = "Nhận luân chuyển thiếu"
				if note[strconv.Itoa(handoverTicket.TicketID)] == nil {
					note[strconv.Itoa(handoverTicket.TicketID)] = map[string]string{
						so.SO: "Nhận luân chuyển thiếu",
					}
				} else {
					note[strconv.Itoa(handoverTicket.TicketID)][so.SO] = "Nhận luân chuyển thiếu"
				}
			}
		}

		model.HandoverTicketDB.UpdateOne(bson.M{
			"code": handoverTicket.Code,
		}, handoverTicket)

	}
	resp := DoneListHandoverTicket(request.DoneTransportRequest{
		ListTicketId: handoverTicketIdList,
		HubCode:      hub.Code,
		CheckInAt:    enum.CheckInAt.WAREHOUSE,
		Note:         note,
		IgnoreBins:   ignoreBins,
	}, checkInBy.AccountID, checkInBy.Fullname)

	if resp.Status == common.APIStatus.Ok {
		allBinCodes = append(allBinCodes, req.Lost...)
		allBinCodes = append(allBinCodes, req.Unmapping...)
		allBinCodes = append(allBinCodes, req.NotUsed...)
		allBinCodes = append(allBinCodes, req.Delivering...)
		allBinCodes = append(allBinCodes, req.MissingTransferToWarehouse...)
		for _, other := range req.Other {
			allBinCodes = append(allBinCodes, other.Bins...)
		}

		newCheckinSession := model.CheckinMultipleBin{
			Code:                       "CHECKIN-BINS" + "-" + strconv.Itoa(int(model.GenId("CHECKIN-BINS"))),
			CreatedBy:                  checkInBy,
			TransportingToWarehouse:    transportingToWarehouse,
			MissingTransferToWarehouse: req.MissingTransferToWarehouse,
			Delivering:                 req.Delivering,
			Lost:                       req.Lost,
			Unmapping:                  req.Unmapping,
			NotUsed:                    req.NotUsed,
			ReceiveAtWareHouse:         req.ReceiveAtWarehouse,
			Other:                      req.Other,
			Bins:                       allBinCodes,
			WarehouseCode:              req.WarehouseCode,
			CreatedAt:                  time.Now(),
		}
		model.CheckinMultipleBinDB.Create(newCheckinSession)
	}
	return resp
}
