package action

import (
	"fmt"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	tplCallbackConsumer "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action/consumer/tpl-callback"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/response"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/utils"
	"go.mongodb.org/mongo-driver/bson"
)

func UpdateAccountTotalReceiveAmount(driverId int64, hubCode string, amount float64) *common.APIResponse {
	return model.AccountDB.UpdateOne(
		bson.M{
			"account_id": driverId,
			"hub_code":   hubCode,
		},
		bson.M{
			"received_amount": amount,
		},
	)
}

func FixOrderAmount(input request.UpdateOrderFee) *common.APIResponse {
	updater := bson.M{}
	if input.FeeAmount != nil {
		updater["fee_amount"] = *input.FeeAmount
	}
	if input.CodAmount != nil {
		updater["cod_amount"] = *input.CodAmount
	}
	if input.FeeSenderAmount != nil {
		updater["fee_sender_amount"] = *input.FeeSenderAmount
	}
	if input.FeeReceiverAmount != nil {
		updater["fee_receiver_amount"] = *input.FeeReceiverAmount
	}
	if input.TotalCollectSenderAmount != nil {
		updater["total_collect_sender_amount"] = *input.TotalCollectSenderAmount
	}
	if input.TotalCollectReceiverAmount != nil {
		updater["total_collect_receiver_amount"] = *input.TotalCollectReceiverAmount
	}
	if input.FeeDebtAmount != nil {
		updater["fee_debt_amount"] = *input.FeeDebtAmount
	}
	if input.TotalDebtAmount != nil {
		updater["total_debt_amount"] = *input.TotalDebtAmount
	}
	if input.TotalAmount != nil {
		updater["total_amount"] = *input.TotalAmount
	}

	if len(updater) > 0 && input.ReferenceCode != "" {
		model.ShippingOrderDB.UpdateOne(bson.M{
			"reference_code": input.ReferenceCode,
		}, updater)

		model.HUBShippingOrderDB.UpdateMany(bson.M{
			"reference_code": input.ReferenceCode,
		}, updater)
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}
}

func MigrateParentReceiveSessionCode() *common.APIResponse {
	shippingOrdersRaw := model.ShippingOrderDB.Query(bson.M{
		"type": enum.ShippingOrderType.FMPO,
	}, 0, 3000, nil)

	shippingOrders := shippingOrdersRaw.Data.([]*model.ShippingOrder)
	for _, shippingOrder := range shippingOrders {
		parentReceiveSessionCode := strings.Split(shippingOrder.ReferenceCode, "-")[0]
		model.ShippingOrderDB.UpdateOne(bson.M{
			"reference_code": shippingOrder.ReferenceCode,
		}, bson.M{
			"parent_receive_session_code": parentReceiveSessionCode,
		})
		model.HUBShippingOrderDB.UpdateMany(bson.M{
			"reference_code": shippingOrder.ReferenceCode,
		}, bson.M{
			"parent_receive_session_code": parentReceiveSessionCode,
		})
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}
}
func SetWarehouseReferenceCode(hubId int64, warehouseReferenceCode string) *common.APIResponse {
	return model.HubDB.UpdateOneWithOption(bson.M{
		"hub_id": hubId,
	}, bson.M{
		"$set": bson.M{
			"warehouse_reference_code": warehouseReferenceCode,
		},
	})
}

func UnSetWarehouseReferenceCode(hubId int64) *common.APIResponse {
	return model.HubDB.UpdateOneWithOption(bson.M{
		"hub_id": hubId,
	}, bson.M{
		"$unset": bson.M{
			"warehouse_reference_code": "",
		},
	})
}

func DeleteCustomer(customerId int64) *common.APIResponse {
	if customerId == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Customer id is required",
		}
	}

	return model.CustomerDB.Delete(bson.M{
		"customer_id": customerId,
	})
}

func TestShippingOrderFee(req request.BookShippingOrder, configId int64) *common.APIResponse {
	configRaw := model.ConfigFeeDB.QueryOne(bson.M{
		"config_id": configId,
	})

	if configRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid ConfigId",
		}
	}

	fee, err := CalculateOrderFee(req, configRaw.Data.([]*model.ConfigFee)[0])
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Error(),
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
		Data:    []float64{fee},
	}
}

func SetUpDefaultFee(fromCode, toCode string) *common.APIResponse {
	oldConfigRaw := model.ConfigFeeDB.QueryOne(bson.M{
		"code": fromCode,
	})

	if oldConfigRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid FromCode",
		}
	}

	oldConfig := oldConfigRaw.Data.([]*model.ConfigFee)[0]
	fmt.Println(oldConfig)

	newConfigRaw := model.ConfigFeeDB.QueryOne(bson.M{
		"code": toCode,
	})
	if newConfigRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid ToCode",
		}
	}
	newConfig := newConfigRaw.Data.([]*model.ConfigFee)[0]
	fmt.Println(newConfig)

	model.ConfigFeeDB.UpdateOne(bson.M{
		"_id": newConfig.ID,
	}, bson.M{
		"code":        oldConfig.Code,
		"config_id":   oldConfig.ConfigId,
		"config_name": oldConfig.Name,
		"keyword":     oldConfig.Keyword,
	})

	model.ConfigFeeDB.UpdateOne(bson.M{
		"_id": oldConfig.ID,
	}, bson.M{
		"code":        newConfig.Code,
		"config_id":   newConfig.ConfigId,
		"config_name": newConfig.Name,
		"keyword":     newConfig.Keyword,
	})

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}
}

func ToolUpdateRoute(routeCode, status string) *common.APIResponse {
	return model.RouteDB.UpdateOne(bson.M{
		"route_code": routeCode,
	}, bson.M{
		"status": status,
	})
}

func MigrateCustomerPhone() *common.APIResponse {
	var limit int64 = 1000
	filter := bson.M{}

	// Full table scan
	for {
		customersRaw := model.CustomerDB.Query(filter, 0, limit, &bson.M{"_id": -1})
		if customersRaw.Status != common.APIStatus.Ok {
			break
		}
		customers := customersRaw.Data.([]*model.Customer)
		smallestId := customers[len(customers)-1].Id
		filter["_id"] = bson.M{
			"$lt": smallestId,
		}
		for _, customer := range customers {
			newKeyword, keyErr := utils.GenKeyword(customer.Name, customer.Code, customer.CustomerId, customer.Phone)
			if keyErr != nil {
				continue
			}
			model.CustomerDB.UpdateOne(bson.M{
				"_id": customer.Id,
			}, bson.M{
				"keyword": newKeyword,
			})
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}
}

func MigrateDonePackTime() *common.APIResponse {
	var limit int64 = 500
	filter := bson.M{}

	// Full table scan
	for {
		shippingOrdersRaw := model.ShippingOrderDB.Query(filter, 0, limit, &bson.M{"_id": -1})
		if shippingOrdersRaw.Status != common.APIStatus.Ok {
			break
		}
		shippingOrders := shippingOrdersRaw.Data.([]*model.ShippingOrder)
		smallestId := shippingOrders[len(shippingOrders)-1].ID
		filter["_id"] = bson.M{
			"$lt": smallestId,
		}
		for _, shippingOrder := range shippingOrders {
			if shippingOrder.DonePackTime > 0 {
				continue
			}
			if shippingOrder.ShippingType == nil ||
				*shippingOrder.ShippingType != enum.ShippingOrderType.DELIVERY {
				continue
			}
			if strings.HasPrefix(shippingOrder.ReferenceCode, "RONS") {
				continue
			}
			donePackTime := 0
			if !strings.Contains(shippingOrder.ReferenceCode, "-") {
				pickTicket, err := client.Services.PickingClient.GetPickTicket(shippingOrder.ReferenceCode)
				if err != nil || pickTicket == nil {
					continue
				}
				donePackTime = int(pickTicket.EndTime)
			} else {
				do, err := client.Services.WarehouseCoreClient.GetDeliveryOrder(shippingOrder.ReferenceCode)
				if err != nil || do == nil {
					continue
				}
				pickTicket, getPickTicketErr := client.Services.PickingClient.GetPickTicketById(do.ReferenceID)
				if getPickTicketErr != nil || pickTicket == nil {
					continue
				}
				donePackTime = int(pickTicket.EndTime)
			}
			if donePackTime > 0 {
				model.ShippingOrderDB.UpdateOne(bson.M{
					"_id": shippingOrder.ID,
				}, bson.M{
					"done_pack_time": donePackTime,
				})
			}

		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}

}

func ChangeHandoverSOCode(ticketCode, oldSoCode, newSoCode string) *common.APIResponse {
	if ticketCode == "" || oldSoCode == "" || newSoCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid input",
		}
	}

	getTicketResult := model.HandoverTicketDB.QueryOne(bson.M{
		"code": ticketCode,
	})

	if getTicketResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy phiếu bàn giao.",
			ErrorCode: "TICKET_NOT_FOUND",
		}
	}

	existedTicket := getTicketResult.Data.([]*model.HandoverTicket)[0]
	isFoundSO := false

	for i, existedSO := range existedTicket.SOList {
		if existedSO.SO == oldSoCode {
			isFoundSO = true
			existedTicket.SOList[i].SO = newSoCode
			break
		}
	}

	if !isFoundSO {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy mã đơn hàng trong phiếu bàn giao",
			ErrorCode: "NOT_FOUND",
		}
	}

	updateResult := model.HandoverTicketDB.UpdateOne(bson.M{
		"ticket_id": existedTicket.TicketID,
	},
		existedTicket)

	if updateResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Không thể xóa đơn hàng ra khỏi phiếu bàn giao!",
		}
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}
}

func UpdateReferenceCode(req []request.UpdateReferenceCodeRequest) *common.APIResponse {
	for _, input := range req {
		if input.OldReferenceCode == "" || input.NewReferenceCode == "" {
			continue
		}

		model.ShippingOrderDB.UpdateOne(bson.M{
			"reference_code": input.OldReferenceCode,
		}, bson.M{
			"reference_code": input.NewReferenceCode,
		})

		model.HUBShippingOrderDB.UpdateMany(bson.M{
			"reference_code": input.OldReferenceCode,
		}, bson.M{
			"reference_code": input.NewReferenceCode,
		})
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}
}

func ToolUpdateAccountStatus(accountId int, hubCode, status string) *common.APIResponse {
	return model.AccountDB.UpdateOne(bson.M{
		"account_id": accountId,
		"hub_code":   hubCode,
	}, bson.M{
		"status": status,
	})
}

func UpdateHubOrderHub(req request.UpdateHubOrder) *common.APIResponse {
	id, err := primitive.ObjectIDFromHex(req.Id)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid ID",
		}

	}
	return model.HUBShippingOrderDB.UpdateOne(bson.M{
		"_id": id,
	}, bson.M{
		"hub_code": req.HubCode,
	})
}

func RevertPreviousBinStatus(req []string, requireUpdate bool) *common.APIResponse {
	if len(req) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid input",
		}
	}
	type Response struct {
		ReferenceCode string
		HubStatus     string
		OrderStatus   string
	}

	var res []Response

	shippingOrderResp := model.ShippingOrderDB.Query(bson.M{
		"reference_code": bson.M{
			"$in": req,
		},
	}, 0, int64(len(req)), nil)
	if shippingOrderResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Can not get shipping order",
		}
	}
	shippingOrders := shippingOrderResp.Data.([]*model.ShippingOrder)
	for _, shippingOrder := range shippingOrders {
		callback, err := client.Services.TplCallbackClient.GetCallback(shippingOrder.ReferenceCode)
		if err != nil || callback == nil {
			continue
		}
		if len(callback.Logs) == 0 {
			callback.Logs = append(callback.Logs, &response.Callback{
				Status: &enum.Status.READY_TO_PICK,
			})
		}
		lastLog := callback.Logs[len(callback.Logs)-1]
		status := lastLog.Status
		if status == nil {
			continue
		}
		var hubOrderStatus enum.HubShippingOrderStatusValue
		var shippingOrderStatus enum.TPLCallbackStatusValue
		needUpdate := false
		if *status == enum.Status.READY_TO_PICK {
			hubOrderStatus = enum.HubShippingOrderStatus.READY_TO_PICK
			shippingOrderStatus = enum.TPLCallbackStatus.READY_TO_PICK
			needUpdate = true
		}
		if *status == enum.Status.PICKING {
			hubOrderStatus = enum.HubShippingOrderStatus.PICKING
			shippingOrderStatus = enum.TPLCallbackStatus.PICKING
			needUpdate = true
		}
		if *status == enum.Status.PICKED {
			hubOrderStatus = enum.HubShippingOrderStatus.WAIT_TO_STORING
			shippingOrderStatus = enum.TPLCallbackStatus.PICKED
			needUpdate = true
		}
		if *status == enum.Status.PICK_FAIL {
			hubOrderStatus = enum.HubShippingOrderStatus.PICK_FAIL
			shippingOrderStatus = enum.TPLCallbackStatus.PICK_FAIL
			needUpdate = true
		}
		if *status == enum.Status.STORING {
			hubOrderStatus = enum.HubShippingOrderStatus.STORING
			shippingOrderStatus = enum.TPLCallbackStatus.STORING
			needUpdate = true
		}

		if *status == enum.Status.TRANSPORTING {
			hubOrderStatus = enum.HubShippingOrderStatus.WAIT_TO_STORING
			shippingOrderStatus = enum.TPLCallbackStatus.TRANSPORTING
			needUpdate = true
		}

		res = append(res, Response{
			ReferenceCode: shippingOrder.ReferenceCode,
			HubStatus:     string(hubOrderStatus),
			OrderStatus:   string(shippingOrderStatus),
		})
		if needUpdate && requireUpdate {
			model.ShippingOrderDB.UpdateOne(bson.M{
				"reference_code": shippingOrder.ReferenceCode,
			}, bson.M{
				"status": shippingOrderStatus,
			})

			model.HUBShippingOrderDB.UpdateOne(bson.M{
				"reference_code": shippingOrder.ReferenceCode,
			}, bson.M{
				"status": hubOrderStatus,
			}, &options.FindOneAndUpdateOptions{
				Sort: bson.M{
					"_id": -1,
				},
			})
		}

	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
		Data:    res,
	}
}

func MigrateReferences(referenceCodes []string) *common.APIResponse {
	if len(referenceCodes) > 0 {
		shippingOrdersResp := model.ShippingOrderDB.Query(bson.M{
			"reference_code": bson.M{
				"$in": referenceCodes,
			},
		}, 0, int64(len(referenceCodes)), nil)
		if shippingOrdersResp.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Can not get shipping order",
			}
		}
		shippingOrders := shippingOrdersResp.Data.([]*model.ShippingOrder)
		findAndUpdateReferences(shippingOrders)
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "OK",
		}
	}
	var limit int64 = 1000
	filter := bson.M{}
	// Full table scan
	for {
		shippingOrdersRaw := model.ShippingOrderDB.Query(filter, 0, limit, &bson.M{"_id": -1})
		if shippingOrdersRaw.Status != common.APIStatus.Ok {
			break
		}
		shippingOrders := shippingOrdersRaw.Data.([]*model.ShippingOrder)
		smallestId := shippingOrders[len(shippingOrders)-1].ID
		filter["_id"] = bson.M{
			"$lt": smallestId,
		}
		findAndUpdateReferences(shippingOrders)
	}

	_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
		CmdType: "DONE_MIGRATE_REFERENCES",
		Title:   "DONE_MIGRATE_REFERENCES",
		Message: "DONE_MIGRATE_REFERENCES",
	})

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}
}

func MigrateShippingOrderSnapshotStatus(
	toTime time.Time) *common.APIResponse {
	id := primitive.NewObjectIDFromTimestamp(toTime)
	for _, fromStatus := range ShippingOrderSnapshotStatuses {
		countShippingOrder := model.ShippingOrderDB.Count(bson.M{
			"status": fromStatus,
			"_id": bson.M{
				"$lt": id,
			},
		})
		if countShippingOrder.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Can not count shipping order",
			}
		}
		CreateSnapshot(model.Snapshot{
			SnapshotAt: toTime,
			Type:       enum.SnapshotType.CACHE_ORDER_STATUS,
			Key:        "shipping_order_status_" + fromStatus,
			Quantity:   countShippingOrder.Total,
		})
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}
}

func MigrateHubOrderSnapshotStatus(toTime time.Time) *common.APIResponse {
	// Get all active hub, this will likely relatively small so don't need pagination
	hubResp := model.HubDB.Query(bson.M{
		"active": true,
	}, 0, 1000, nil)
	if hubResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Can not get hub",
		}
	}
	id := primitive.NewObjectIDFromTimestamp(toTime)
	hubs := hubResp.Data.([]*model.Hub)
	for _, hub := range hubs {
		// Only support status in HubOrderSnapshotStatuses global variable
		// Cause once order in those state it will likely to be in that state forever
		for _, fromStatus := range HubOrderSnapshotStatuses {
			countHubOrder := model.HUBShippingOrderDB.Count(bson.M{
				"status":   fromStatus,
				"hub_code": hub.Code,
				"_id": bson.M{
					"$lt": id,
				},
			})
			if countHubOrder.Status != common.APIStatus.Ok {
				return &common.APIResponse{
					Status:  common.APIStatus.Error,
					Message: "Can not count hub order",
				}
			}
			CreateSnapshot(model.Snapshot{
				SnapshotAt: toTime,
				Type:       enum.SnapshotType.CACHE_ORDER_STATUS,
				Key:        "hub_shipping_order_code_" + hub.Code + "_status_" + string(fromStatus),
				Quantity:   countHubOrder.Total,
			})
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}
}

func findAndUpdateReferences(shippingOrders []*model.ShippingOrder) {
	for _, shippingOrder := range shippingOrders {
		references := utils.NotEmptyStrArr(
			shippingOrder.ReferenceCode,
			shippingOrder.TrackingCode,
			shippingOrder.DeliveryOrderCode,
			shippingOrder.ParentReferenceCode,
			shippingOrder.ParentReceiveSessionCode,
		)
		references = utils.RemoveDuplicateStr(references)
		model.ShippingOrderDB.UpdateOne(bson.M{
			"reference_code": shippingOrder.ReferenceCode,
		}, bson.M{
			"references": references,
		})
		model.HUBShippingOrderDB.UpdateMany(bson.M{
			"reference_code": shippingOrder.ReferenceCode,
		}, bson.M{
			"references": references,
		})
	}

}

func ToolChangeOrderCarrier(referenceCodes []string, carrierId int, hubCode string) *common.APIResponse {
	if len(referenceCodes) == 0 || carrierId == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid input",
		}
	}
	shippingOrdersRaw := model.ShippingOrderDB.Query(bson.M{
		"reference_code": bson.M{
			"$in": referenceCodes,
		},
	}, 0, int64(len(referenceCodes)), nil)
	if shippingOrdersRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Can not get shipping order",
		}
	}
	shippingOrders := shippingOrdersRaw.Data.([]*model.ShippingOrder)
	carrierRaw := model.CarrierDB.QueryOne(bson.M{
		"carrier_id": carrierId,
	})
	if carrierRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Can not get carrier",
		}
	}
	hub := model.HubDB.QueryOne(bson.M{
		"code": hubCode,
	})
	if hub.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Can not get hub",
		}
	}
	carrier := carrierRaw.Data.([]*model.Carrier)[0]
	for _, shippingOrder := range shippingOrders {
		prevHubCode := shippingOrder.CurrentHub
		// Only support change carrier when order is just init, once it on it way, we can not change it
		if shippingOrder.Status == nil ||
			*shippingOrder.Status != enum.TPLCallbackStatus.READY_TO_PICK {
			continue
		}
		resp := model.ShippingOrderDB.UpdateOne(bson.M{
			"reference_code": shippingOrder.ReferenceCode,
		}, bson.M{
			"tpl_code":       carrier.CarrierCode,
			"tpl_name":       carrier.CarrierName,
			"tpl_service_id": carrier.CarrierId,
			"current_hub":    hubCode,
		})
		if resp.Status != common.APIStatus.Ok {
			continue
		}
		// Create new hub order base on newly updated shipping order
		CreateHubOrderByShippingOrder(&request.CreateHubOrderRequest{
			ReferenceCode: shippingOrder.ReferenceCode,
		})
		// Cancel old hub order
		model.HUBShippingOrderDB.UpdateOne(bson.M{
			"reference_code": shippingOrder.ReferenceCode,
			"hub_code":       prevHubCode,
		}, bson.M{
			"status": "CANCEL",
		})
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}
}

// DeleteExecuteQueueMessage
func DeleteExecuteQueueMessage(req sdk.APIRequest, resp sdk.APIResponder) error {
	type request struct {
		UniqueKey  string   `json:"uniqueKey,omitempty"`
		UniqueKeys []string `json:"uniqueKeys,omitempty"`
		FailCount  int      `json:"failCount,omitempty"`
		Topic      string   `json:"topic,omitempty"`
	}
	var input request
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	dbInstance := tplCallbackConsumer.GetExecuteQueueDatabase()
	if dbInstance == nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   "Không kết nối được database.",
			ErrorCode: "SERVER_ERROR",
		})
	}

	filter := bson.M{}

	if len(input.UniqueKeys) > 0 {
		filter["unique_key"] = bson.M{
			"$in": input.UniqueKeys,
		}
	}

	if input.UniqueKey != "" {
		filter["unique_key"] = input.UniqueKey
	}

	if input.FailCount > 0 {
		filter["fail_count"] = bson.M{
			"$gt": input.FailCount,
		}
	}

	if input.Topic != "" {
		filter["topic"] = input.Topic
	}

	if len(filter) == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Chưa truyền param",
			ErrorCode: "PARAM_INVALID",
		})
	}

	return resp.Respond(dbInstance.Delete(filter))
}

func UpdateHubOrderProductStatus(hubCode, referenceCode, sku string, status enum.ProductStatusValue) *common.APIResponse {
	if hubCode == "" || referenceCode == "" || sku == "" || status == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid input",
		}
	}

	getHubOrderResult := model.HUBShippingOrderDB.QueryOne(bson.M{
		"hub_code":       hubCode,
		"reference_code": referenceCode,
	})
	if getHubOrderResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không tìm thấy đơn hàng",
		}
	}

	hubOrder := getHubOrderResult.Data.([]*model.HubShippingOrder)[0]
	for idx, product := range hubOrder.Products {
		if product.SKU == sku {
			product.Status = &status
			hubOrder.Products[idx] = product
		}
	}

	afterOption := options.After
	return model.HUBShippingOrderDB.UpdateOne(bson.M{
		"_id": hubOrder.ID,
	}, bson.M{
		"products": hubOrder.Products,
	}, &options.FindOneAndUpdateOptions{
		ReturnDocument: &afterOption,
	})
}

func BulkUpdateHubBank(hubCodes []string, hubInfo *model.Hub) *common.APIResponse {
	if len(hubCodes) == 0 || hubInfo == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid input",
		}
	}

	if hubInfo.CompanyBankInfo.BankName == "" ||
		hubInfo.CompanyBankInfo.BankAccountName == "" ||
		hubInfo.CompanyBankInfo.BankAccountID == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid bank info",
		}
	}

	return model.HubDB.UpdateMany(bson.M{
		"code": bson.M{
			"$in": hubCodes,
		},
	}, bson.M{
		"company_bank_info": hubInfo.CompanyBankInfo,
	})
}
