package api

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
)

func GetMyAccount(req sdk.APIRequest, resp sdk.APIResponder) error {

	var hubCode = req.GetParam("hubCode")
	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}

	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	return resp.Respond(action.GetMyAccount(UserInfo.Account.AccountID, hubCode))
}

func GetAccount(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input request.Account
	str := req.GetParam("q")
	if str == "" {
		str = "{}"
	}
	var offset = int64(sdk.ParseInt(req.GetParam("offset"), 0))
	var limit = int64(sdk.ParseInt(req.GetParam("limit"), 100))
	var getTotal = req.GetParam("getTotal") == "true"
	err := json.Unmarshal([]byte(str), &input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.GetAccount(&input, offset, limit, getTotal))
}

func GetLogisticHubLimitInformation(req sdk.APIRequest, resp sdk.APIResponder) error {
	hubCode := req.GetParam("hubCode")
	if hubCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Hub code is not empty",
			ErrorCode: "INVALID_DATA",
		})
	}
	return resp.Respond(action.GetLogisticHubLimitInformation(hubCode))
}

func UpdateAccount(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Account
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.UpdateAccount(&input))
}

func MigrateHubReceivedAmount(req sdk.APIRequest, resp sdk.APIResponder) error {
	type testData struct {
		HubCodes   []string `json:"hubCodes,omitempty"`
		NeedUpdate bool     `json:"needUpdate,omitempty"`
	}
	var input testData
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.MigrateHubReceivedAmount(input.HubCodes, input.NeedUpdate))
}

func GetMyDepartments(req sdk.APIRequest, resp sdk.APIResponder) error {
	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}

	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	if UserInfo.Account.Username == "" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing Username.",
		})
	}

	return resp.Respond(action.GetMyDepartments(*UserInfo))
}

func CountAccountsByStatus(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input request.Account
	str := req.GetParam("q")
	if str == "" {
		str = "{}"
	}
	err := json.Unmarshal([]byte(str), &input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.CountAccountByStatus(&input))
}

func GetHubsOfDrivers(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input request.Account
	str := req.GetParam("q")
	if str == "" {
		str = "{}"
	}

	var offset = int64(sdk.ParseInt(req.GetParam("offset"), 0))
	var limit = int64(sdk.ParseInt(req.GetParam("limit"), 100))
	var getTotal = req.GetParam("getTotal") == "true"

	err := json.Unmarshal([]byte(str), &input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.GetHubsOfDrivers(&input, offset, limit, getTotal))
}
func DistinctCountByStatus(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input request.Account
	str := req.GetParam("q")
	if str == "" {
		str = "{}"
	}

	err := json.Unmarshal([]byte(str), &input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.DistinctCountByStatus(&input))
}

func UpdateAccountsLimit(req sdk.APIRequest, resp sdk.APIResponder) error {
	type requestAccounts struct {
		HubCode     string  `json:"hubCode,omitempty"`
		Accounts    []int64 `json:"accountIds,omitempty"`
		LimitAmount float64 `json:"limitAmount,omitempty"`
	}

	var input requestAccounts
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.UpdateAccountsLimit(input.HubCode, input.Accounts, input.LimitAmount))
}

func GetProductivity(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input request.HubShippingOrderQuery
	str := req.GetParam("q")
	if str == "" {
		str = "{}"
	}
	err := json.Unmarshal([]byte(str), &input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.GetProductivity(&input))
}

func GetMyProductivity(req sdk.APIRequest, resp sdk.APIResponder) error {
	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}

	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	var input request.HubShippingOrderQuery
	str := req.GetParam("q")
	if str == "" {
		str = "{}"
	}
	err := json.Unmarshal([]byte(str), &input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	input.DriverId = UserInfo.Account.AccountID
	return resp.Respond(action.GetProductivity(&input))
}

func CrawlProductivity(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.CrawlProductivity())
}
