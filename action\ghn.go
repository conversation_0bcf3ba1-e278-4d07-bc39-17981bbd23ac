package action

import (
	"fmt"
	"strconv"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/shipping"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"go.mongodb.org/mongo-driver/bson"
)

type ghn struct{}

var GHN ghn

func (ghn) BookShipping(input *request.BookShipping, saleOrder *model.SaleOrder, carrierModel *model.Carrier) (result *model.ShippingInfo, err error) {
	if shipping.TplShippingClient.GHNClient == nil {
		err = fmt.Errorf("Dịch vụ book GHN chưa được khởi tạo")
		return
	}

	warehouse, err := client.Services.WarehouseCoreClient.GetWarehouse(input.WarehouseCode)
	if err != nil {
		return
	}

	if len(carrierModel.Service) == 0 {
		err = fmt.Errorf("Không tìm thấy dịch vụ vận chuyển phù hợp")
		return
	}

	requestBooking := request.BookGHNRequest{
		Weight:          input.Weight * 1000,
		Width:           int64(input.Width),
		Height:          int64(input.Height),
		Length:          int64(input.Length),
		ReturnAddress:   warehouse.Address,
		ServiceId:       0,
		Content:         input.SO,
		RequiredNote:    "CHOXEMHANGKHONGTHU",
		ReturnPhone:     conf.Config.Supporter["delivery"].Phone,
		Note:            input.DeliveryNote,
		Quantity:        input.NbOfPackages,
		ToName:          saleOrder.CustomerInfos.Delivery.Name,
		ToPhone:         saleOrder.CustomerInfos.Delivery.Phone,
		ClientOrderCode: input.SO,
		ToAddress:       saleOrder.CustomerInfos.Delivery.Address,
		CodAmount:       saleOrder.CODAmount,
	}

	requestBooking.ToDistrictId, requestBooking.ToWardCode, err = CompareMasterDataGHN(saleOrder.CustomerInfos.Delivery.Province, saleOrder.CustomerInfos.Delivery.District, saleOrder.CustomerInfos.Delivery.Ward)

	if err != nil {
		return
	}

	if saleOrder.PaymentMethod != nil && *saleOrder.PaymentMethod == enum.PaymentMethod.BANK {
		requestBooking.CodAmount = 0
	}

	paymentMethod, err := strconv.Atoi(carrierModel.PaymentMethod)

	if err != nil {
		err = fmt.Errorf("Phương thức thanh toán không hợp lệ")
		return
	}

	requestBooking.PaymentTypeId = int64(paymentMethod)

	service, err := strconv.Atoi(carrierModel.Service)

	if err != nil {
		err = fmt.Errorf("Dịch vụ nhà vận chuyển không hợp lệ")
		return
	}

	requestBooking.ServiceTypeId = int64(service)

	for i := 0; i < int(input.NbOfPackages); i++ {
		requestBooking.Items = append(requestBooking.Items, &request.GHNItem{
			Name:     "Thuốc",
			Quantity: 1,
			Code:     input.SO,
		})
	}

	// Giá trị hàng hóa mặc định không quá 3 củ
	requestBooking.InsuranceValue = int64(saleOrder.DeliveryAmount)
	if requestBooking.InsuranceValue > int64(carrierModel.InsuranceValue) {
		requestBooking.InsuranceValue = int64(carrierModel.InsuranceValue)
	}

	result, err = shipping.TplShippingClient.GHNClient.CreateTrackingGHN(requestBooking)
	if err != nil {
		return
	}

	result.CODAmount = requestBooking.CodAmount

	return
}

func CompareMasterDataGHN(provinceName, districtName, wardName string) (districtId int64, wardId string, err error) {
	provinceMappingGHN := model.ProvinceMappingDB.QueryOne(bson.M{
		"dictionary": provinceName,
	})

	if provinceMappingGHN.Status != common.APIStatus.Ok || provinceMappingGHN.Data == nil {
		err = fmt.Errorf("Không tìm thấy thông tin ", provinceName)
		return
	}
	provinceMappingGHNModel := provinceMappingGHN.Data.([]*model.ProvinceMapping)[0]

	districtMappingGHN := model.DistrictMappingDB.QueryOne(bson.M{
		"dictionary":    districtName,
		"province_code": provinceMappingGHNModel.Code,
	})

	if districtMappingGHN.Status != common.APIStatus.Ok || districtMappingGHN.Data == nil {
		err = fmt.Errorf("Không tìm thấy thông tin ", districtName+", "+provinceName)
		return
	}

	districtMappingGHNModel := districtMappingGHN.Data.([]*model.DistrictMapping)[0]

	wardMappingGHNModel := new(model.WardMapping)

	wardMappingVTP := model.WardMappingDB.QueryOne(bson.M{
		"dictionary":    wardName,
		"district_code": districtMappingGHNModel.Code,
	})

	if wardMappingVTP.Status == common.APIStatus.Ok {
		wardMappingGHNModel = wardMappingVTP.Data.([]*model.WardMapping)[0]
	}

	return districtMappingGHNModel.GHNId, wardMappingGHNModel.GHNId, err
}
