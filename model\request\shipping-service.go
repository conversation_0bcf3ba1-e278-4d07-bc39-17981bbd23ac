package request

import (
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
)

type BookShipping struct {
	SO            string                       `json:"so"`
	CarrierId     int64                        `json:"carrierId"`
	CarrierName   string                       `json:"carrierName"`
	NbOfPackages  int64                        `json:"numPackage"`
	Weight        float64                      `json:"weight"`
	Height        float64                      `json:"height"`
	Width         float64                      `json:"width"`
	Length        float64                      `json:"length"`
	WarehouseCode string                       `json:"warehouseCode"`
	DeliveryNote  string                       `json:"deliveryNote"`
	PickupNote    string                       `json:"pickupNote"`
	HubCode       string                       `json:"hubCode"`
	SupplierId    int                          `json:"supplierId"`
	SupplierName  string                       `json:"supplierName"`
	ListSo        []string                     `json:"listSo"`
	ShippingType  *enum.ShippingOrderTypeValue `json:"type"`          // Delivery, Pickup
	ReferenceCode string                       `json:"referenceCode"` // SO123, PO345, BIN123
	From          *model.Address               `json:"from"`
	To            *model.Address               `json:"to"`
	CallbackUrl   string                       `json:"url"`
	PartnerCode   string                       `json:"partnerCode"`

	ExpectedDeliveryTime int64                  `json:"expectedDeliveryTime"`
	ExpectedPickupTime   int64                  `json:"expectedPickupTime"`
	Products             []*model.Product       `json:"products"`
	ParentReferenceCode  string                 `json:"parentReferenceCode"`
	MergeStatus          *enum.MergeStatusValue `json:"mergeStatus,omitempty" bson:"merge_status,omitempty"`

	FromHubCode  string         `json:"fromHubCode"`
	ToHubCode    string         `json:"toHubCode"`
	DonePackTime int64          `json:"donePackTime"`
	Baskets      []model.Basket `json:"baskets,omitempty" bson:"baskets,omitempty"`

	References []string `json:"references,omitempty" bson:"references,omitempty"`
}

type BookShippingOrder struct {
	ShippingType         *enum.ShippingOrderTypeValue `json:"type"`          // Delivery, Pickup
	ReferenceCode        string                       `json:"referenceCode"` // SO123, PO345, BIN123
	TrackingCode         string                       `json:"trackingCode"`
	NumPackage           int64                        `json:"numPackage"`
	HubCode              string                       `json:"hubCode"`
	From                 *model.Address               `json:"from"`
	To                   *model.Address               `json:"to"`
	CallbackUrl          string                       `json:"callbackUrl"`
	PartnerCode          string                       `json:"partnerCode"`
	Weight               float64                      `json:"weight"`
	Height               float64                      `json:"height"`
	Width                float64                      `json:"width"`
	Length               float64                      `json:"length"`
	Products             []*model.Product             `json:"products"`
	ExpectedDeliveryTime int64                        `json:"expectedDeliveryTime"`
	ExpectedPickupTime   int64                        `json:"expectedPickupTime"`
	DeliveryNote         string                       `json:"deliveryNote"`
	PickupNote           string                       `json:"pickupNote"`
	AssignRider          *RiderDetail                 `json:"assignRider"`
	CarrierId            int64                        `json:"carrierId"`
	CarrierName          string                       `json:"carrierName"`
	CarrierCode          *enum.PartnerValue           `json:"carrierCode"`
	ParentReferenceCode  string                       `json:"parentReferenceCode"`
	CODAmount            float64                      `json:"codAmount"`
	DeliveryAmount       float64                      `json:"deliveryAmount"`
	FeeCollectedOn       *enum.FeeCollectMethodValue  `json:"feeCollectedOn"`
	OrderValue           float64                      `json:"orderValue"`
	PaymentMethod        *enum.PaymentMethodValue     `json:"paymentMethod"`
	TotalAmount          float64                      `json:"totalAmount"`

	// Chọn lại nhà vận chuyển thì giữ nguyên mã cũ
	ReBook           bool   `json:"rebook"`
	OldReferenceCode string `json:"oldReferenceCode"`

	DropOffAtHubCode   string `json:"dropOffAtHubCode"`
	ReceiveAtHubCode   string `json:"receiveAtHubCode"`
	ReceiveSessionCode string `json:"receiveSessionCode"`
	IsSplitOrder       bool   `json:"isSplitOrder"`
	IsReconciled       *bool  `json:"is_reconciled"`
	CheckInNumPack     int64  `json:"checkInNumPack"`
	IsBookDropOff      bool   `json:"isBookDropOff"`

	Tags               []string        `json:"tags"`
	VoucherCode        string          `json:"voucherCode"`
	CustomerInfo       *model.Customer `json:"customerInfo"`
	DeliveryOrderCodes []string        `json:"deliveryOrderCodes"`
	DeliveryOrderCode  string          `json:"deliveryOrderCode"`
	WarehouseCode      string          `json:"warehouseCode"`
	// Book multiple PO
	ParentReceiveSessionCode string `json:"parentReceiveSessionCode,omitempty" bson:"parent_receive_session_code,omitempty"`

	Status *enum.TPLCallbackStatusValue `json:"status"`
	Note   string                       `json:"note"`
	FmHub  *model.Hub                   `json:"fmHub"`
	LmHub  *model.Hub                   `json:"lmHub"`

	FromAddress *model.Address `json:"fromAddress"`
	ToAddress   *model.Address `json:"toAddress"`

	FromHubCode string `json:"fromHubCode"`
	ToHubCode   string `json:"toHubCode"`

	FromZoneCode string `json:"fromZoneCode"`
	ToZoneCode   string `json:"toZoneCode"`

	ProductType enum.ProductTypeValue `json:"productType,omitempty" bson:"product_type,omitempty"`
	ConfigFee   *model.ConfigFee

	// OPM
	OPM             bool                    `json:"opm,omitempty"`
	PickMoney       *float64                `json:"pickMoney,omitempty"`
	AccountToken    string                  `json:"accountToken,omitempty"`
	SubAccountToken string                  `json:"subAccountToken,omitempty"`
	ProductTypes    []enum.ProductTypeValue `json:"productTypes,omitempty" bson:"product_types,omitempty"`
	// Auto cancel order
	ExpireAfter    int64                 `json:"expireAfter,omitempty" bson:"expire_after,omitempty"`
	AdditionalFees []model.AdditionalFee `json:"additionalFees,omitempty" bson:"additional_fees,omitempty"`

	References   []string               `json:"references,omitempty" bson:"references,omitempty"`
	Medship      bool                   `json:"medship,omitempty"`
	ReturnMethod enum.ReturnMethodValue `json:"returnMethod"`
}

type GetShippingService struct {
	WarehouseCode          string                       `json:"warehouseCode"`
	SaleOrderCode          string                       `json:"saleOrderCode"`
	FromProvince           string                       `json:"fromProvince"`
	FromDistrict           string                       `json:"fromDistrict"`
	FromWard               string                       `json:"fromWard"`
	FromProvinceCode       string                       `json:"fromProvinceCode"`
	FromDistrictCode       string                       `json:"fromDistrictCode"`
	FromWardCode           string                       `json:"fromWardCode"`
	FromProvinceName       string                       `json:"fromProvinceName"`
	FromDistrictName       string                       `json:"fromDistrictName"`
	FromWardName           string                       `json:"fromWardName"`
	ToProvinceCode         string                       `json:"toProvinceCode"`
	ToDistrictCode         string                       `json:"toDistrictCode"`
	ToWardCode             string                       `json:"toWardCode"`
	ToProvinceName         string                       `json:"toProvinceName"`
	ToDistrictName         string                       `json:"toDistrictName"`
	ToWardName             string                       `json:"toWardName"`
	CustomerCode           string                       `json:"customerCode"`
	CustomerType           *enum.CustomerTypeValue      `json:"customerType"`
	Province               string                       `json:"province"`
	District               string                       `json:"district"`
	Ward                   string                       `json:"ward"`
	ProvinceCode           string                       `json:"provinceCode"`
	DistrictCode           string                       `json:"districtCode"`
	WardCode               string                       `json:"wardCode"`
	CustomerID             int64                        `json:"customerId,omitempty"`
	PackageCount           int64                        `json:"packageCount"`
	Weight                 float64                      `json:"weight"`
	Price                  float64                      `json:"price"`
	Phone                  string                       `json:"phone"`
	Email                  string                       `json:"email"`
	GetShippingFee         bool                         `json:"getShippingFee"`
	EstimateDeliveringTime bool                         `json:"estimateDeliveringTime"`
	ServiceType            *enum.ShippingOrderTypeValue `json:"serviceType"`
	ReferenceCode          string                       `json:"referenceCode"`
	CarrierIds             []int64                      `json:"carrierIds"`
	IsReceiveAtLMHub       bool                         `json:"isReceiveAtLMHub"`
	Carriers               []*model.Carrier
	OPM                    bool   `json:"opm,omitempty"`
	Value                  int    `json:"value,omitempty"`
	PickMoney              int    `json:"pickMoney,omitempty"`
	FromAddress            string `json:"fromAddress"`
	ToAddress              string `json:"toAddress"`
	FromName               string `json:"fromName"`
	FromPhone              string `json:"fromPhone"`
	ToName                 string `json:"toName"`
	ToPhone                string `json:"toPhone"`
	Medship                bool   `json:"medship,omitempty"`
}

type UpdateShippingServiceOdoo struct {
	SO             string  `json:"so"`
	CarrierId      int64   `json:"carrier_id"`
	TrackingNumber string  `json:"tracking_number"`
	NbOfPackages   int64   `json:"nb_of_packages"`
	Weight         float64 `json:"weight"`
	WarehouseCode  string  `json:"warehouseCode"`
}

type CancelShippingService struct {
	SO                  string   `json:"so"`
	ReferenceCode       string   `json:"referenceCode"`
	ReferenceCodes      []string `json:"referenceCodes"`
	ParentReferenceCode string   `json:"parentReferenceCode"`
}

type ChangeCarrier struct {
	ReferenceCode string  `json:"referenceCode"`
	CarrierId     int64   `json:"carrierId"`
	HubCode       string  `json:"hubCode"`
	NumPackage    int64   `json:"numPackage"`
	Weight        float64 `json:"weight"`
}

type RiderDetail struct {
	DriverId   int    `json:"driverId"`
	DriverName string `json:"driverName"`
}
