package action

import (
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/mongo/options"

	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action/consumer/customer"

	"github.com/google/uuid"
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	shipping_order "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action/consumer/shipping-order"
	syncData "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action/consumer/tpl-callback"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/shipping"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/utils"
	"go.mongodb.org/mongo-driver/bson"
)

func BookShippingOrder(input *request.BookShippingOrder, createdBy int64) *common.APIResponse {
	if input.ShippingType == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Loại giao hàng không được để trống",
			ErrorCode: "INVALID_SHIPPING_TYPE",
		}
	}

	if *input.ShippingType == enum.ShippingOrderType.PICKUP {
		return BookPickUp(input, createdBy)
	}

	if *input.ShippingType == enum.ShippingOrderType.RETURN {
		return BookReturn(input, createdBy)
	}

	if *input.ShippingType == enum.ShippingOrderType.FMPO {
		return BookFMPO(input, createdBy)
	}

	if *input.ShippingType == enum.ShippingOrderType.EO {
		if input.CallbackUrl != "" && utils.IsUrl(input.CallbackUrl) == false {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "CallbackUrl không hợp lệ",
				ErrorCode: "INVALID_CALLBACK_URL",
			}
		}

		if input.OPM {
			return BookOPMEO(input, createdBy)
		}

		return BookEO(input, createdBy)
	}

	if *input.ShippingType == enum.ShippingOrderType.CS {
		return BookCS(input, createdBy)
	}

	// TODO: If refactor DELIVERY type goes here.

	return &common.APIResponse{
		Status:    common.APIStatus.Invalid,
		Message:   "Loại giao hàng không hợp lệ",
		ErrorCode: "INVALID_SHIPPING_TYPE",
	}

}

func BookPickUp(input *request.BookShippingOrder, createdBy int64) *common.APIResponse {
	if input.ReferenceCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Mã liên kết không hợp lệ",
		}
	}

	if input.From == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Địa chỉ gửi hàng không được để trống",
		}
	}

	if input.To == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Địa chỉ nhận hàng không được để trống",
		}
	}

	if input.NumPackage <= 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Số kiện phải lớn hơn 0",
		}
	}

	if utils.IsEmptyString(input.From.Address) || utils.IsEmptyString(input.From.Name) {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Thiếu thông tin nơi lấy hàng",
		}
	}

	if utils.IsEmptyString(input.To.Address) || utils.IsEmptyString(input.To.Name) {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Thiếu thông tin nơi giao hàng",
		}
	}

	fromAddressErr := BuildAddress(input.From)
	if fromAddressErr != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Lỗi địa chỉ gửi hàng: " + fromAddressErr.Message,
		}
	}

	toAddressErr := BuildAddress(input.To)
	if toAddressErr != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Lỗi địa chỉ nhận hàng: " + toAddressErr.Message,
		}
	}

	var hub *model.Hub
	var carrierId int64

	if input.Status != nil &&
		*input.Status == enum.TPLCallbackStatus.STORING {
		hubRaw := model.HubDB.QueryOne(bson.M{
			"warehouse_reference_code": input.From.Code,
		})
		if hubRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không tìm thấy kho lấy hàng",
			}
		}
		hub = hubRaw.Data.([]*model.Hub)[0]
	} else {
		// Find Pickup Carrier in customer carrier
		hub, carrierId = FindPickUpCarrierOfCustomer(input.NumPackage, input.Weight, sdk.ParseInt64(input.From.Code, 0))
		if hub != nil {
			goto ADD_REFERENCE
		}
		// Find the closest Hub to assign pickup order
		hub = FindNearestHubOfAddress(*input.From)
		if hub == nil {
			// Nếu không đặt lại đơn lấy thì check theo ReferenceCode
			deliveredTrackingCode := input.ReferenceCode
			// Nếu book lại thì dùng ParentReferenceCode để xác định mã tracking của đơn giao hàng thành công
			if input.ReBook && input.ParentReferenceCode != "" {
				deliveredTrackingCode = input.ParentReferenceCode
			}
			if input.ShippingType != nil && *input.ShippingType == enum.ShippingOrderType.PICKUP {
				shippingOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
					"tracking_code": deliveredTrackingCode,
				})

				if shippingOrderRaw.Status != common.APIStatus.Ok {
					return &common.APIResponse{
						Status:  common.APIStatus.Invalid,
						Message: "Hiện tại chưa có hub hỗ trợ khu vực này",
					}
				}

				shippingOrder := shippingOrderRaw.Data.([]*model.ShippingOrder)[0]

				hubRaw := model.HubDB.QueryOne(bson.M{
					"code": shippingOrder.CurrentHub,
				})

				if hubRaw.Status != common.APIStatus.Ok {
					return &common.APIResponse{
						Status:  common.APIStatus.Invalid,
						Message: "Hiện tại chưa có hub hỗ trợ khu vực này",
					}
				}

				hub = hubRaw.Data.([]*model.Hub)[0]
				input.References = utils.AppendStrIfNotExists(input.References, shippingOrder.ReferenceCode)
			}
		}

	ADD_REFERENCE:
		if hub != nil {
			deliveredTrackingCode := input.ReferenceCode
			// Nếu book lại thì dùng ParentReferenceCode để xác định mã tracking của đơn giao hàng thành công
			if input.ReBook && input.ParentReferenceCode != "" {
				deliveredTrackingCode = input.ParentReferenceCode
			}
			if input.ShippingType != nil && *input.ShippingType == enum.ShippingOrderType.PICKUP {
				shippingOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
					"tracking_code": deliveredTrackingCode,
				})

				if shippingOrderRaw.Status != common.APIStatus.Ok {
					return &common.APIResponse{
						Status:  common.APIStatus.Invalid,
						Message: "Hiện tại chưa có hub hỗ trợ khu vực này",
					}
				}

				shippingOrder := shippingOrderRaw.Data.([]*model.ShippingOrder)[0]
				input.References = utils.AppendStrIfNotExists(input.References, shippingOrder.ReferenceCode)
			}
		}
	}

	if hub == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Hiện tại chưa có hub hỗ trợ khu vực này",
		}
	}

	if carrierId == 0 {
		carrierId = hub.DefaultCarrierId
	}

	if carrierId == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Chưa cài đặt nhà vận chuyển mặc định tại hub: " + hub.Code,
		}
	}

	carrierRaw := model.CarrierDB.QueryOne(bson.M{
		"carrier_id": carrierId,
	})

	if carrierRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status: common.APIStatus.Invalid,
			Message: "Nhà vận chuyển với Id: " +
				strconv.FormatInt(hub.ListCarrierRefId[0], 10) +
				" không tồn tại",
		}
	}

	carrier := carrierRaw.Data.([]*model.Carrier)[0]
	input.CarrierId = carrier.CarrierId
	input.CarrierName = carrier.CarrierName
	input.CarrierCode = carrier.CarrierCode

	shippingOrderRaw := model.ShippingOrderDB.QueryOne(&model.ShippingOrder{
		ReferenceCode: input.ReferenceCode,
	})

	if shippingOrderRaw.Status == common.APIStatus.Ok {
		shippingOrder := shippingOrderRaw.Data.([]*model.ShippingOrder)[0]

		if *shippingOrder.Status != enum.TPLCallbackStatus.CANCEL && *shippingOrder.Status != enum.TPLCallbackStatus.CREATE_FAIL {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Đơn lấy hàng đã được tạo",
			}
		}
	}
	// Todo : Calculate estimate picking time for shipping order

	// Todo : Calculate shipping Fee

	input.HubCode = hub.Code
	// Create shipping order
	shippingOrder := buildShippingOrder(input, int(createdBy))
	createShippingOrder := model.ShippingOrderDB.Create(shippingOrder)
	if createShippingOrder.Status != common.APIStatus.Ok {
		return createShippingOrder
	}

	// Create hub shipping orders
	hubShippingOrder := buildHubOrderForPickup(&shippingOrder, hub, createdBy)
	createHubShippingResult := model.HUBShippingOrderDB.Create(hubShippingOrder)
	if createHubShippingResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không thể tạo đơn giao hàng cho hub: " + createHubShippingResult.Message,
		}
	}

	// Send Tpl callback
	actionTime := time.Now()
	createCallbackRequest := request.Callback{
		SO:              input.ReferenceCode,
		CreatedSource:   carrier.ParentCode,
		Status:          &enum.TPLCallbackStatus.READY_TO_PICK,
		Weight:          input.Weight,
		TPLCode:         shippingOrder.TrackingCode,
		ActionTime:      &actionTime,
		StatusName:      "Tạo mới vận đơn",
		ExternalTPLName: carrier.CarrierName,
		TPLStatus:       string(enum.TPLCallbackStatus.READY_TO_PICK),
		TPLStatusName:   "Tạo mới vận đơn",
		NumPackage:      input.NumPackage,
		Type:            shippingOrder.ShippingType,
	}
	if carrier.ParentCode == nil {
		createCallbackRequest.CreatedSource = carrier.CarrierCode
	}

	err := client.Services.TplCallbackClient.CreateCallback(createCallbackRequest)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Không thể gửi callback: " + err.Error(),
		}
	}

	// Todo : Check and assign rider pickup
	if input.AssignRider != nil && input.AssignRider.DriverId > 0 {
		time.Sleep(200 * time.Millisecond)
		assignRes := AssignHubShippingOrder([]string{input.ReferenceCode}, input.AssignRider.DriverName, input.HubCode, input.AssignRider.DriverId, createdBy, &enum.False)
		if assignRes.Status != common.APIStatus.Ok {
			log.Println("AssignRider get error => ", input.AssignRider)
		}
	}

	// Send callback
	if input.PartnerCode != "" &&
		conf.Config.WebHookConfig[input.PartnerCode] != nil &&
		conf.Config.WebHookConfig[input.PartnerCode].Url != "" {
		fullFromAddress := fmt.Sprintf("%s, %s, %s, %s", input.From.Address, input.From.WardName, input.From.DistrictName, input.From.ProvinceName)
		fullToAddress := fmt.Sprintf("%s, %s, %s, %s", input.To.Address, input.To.WardName, input.To.DistrictName, input.To.ProvinceName)
		callbackData := &request.Callback{
			PartnerCode:   input.PartnerCode,
			ReferenceCode: input.ReferenceCode,
			TrackingCode:  shippingOrder.TrackingCode,
			FromAddress:   fullFromAddress,
			FromName:      input.From.Name,
			ToAddress:     fullToAddress,
			ToName:        input.To.Name,
			NumPackage:    input.NumPackage,
			HubCode:       hub.Code,
			Status:        &enum.TPLCallbackStatus.READY_TO_PICK,
		}
		err := syncData.PushSendCallbackQueue(callbackData, input.ReferenceCode)
		if err != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: err.Error(),
			}
		}
	}

	if shippingOrder.FromCustomerCode != "" {
		err = customer.PushCreateCustomer(model.Customer{
			Code:         shippingOrder.FromCustomerCode,
			Name:         shippingOrder.FromCustomerName,
			Phone:        shippingOrder.FromCustomerPhone,
			CustomerType: &enum.CustomerType.INTERNAL,
		}, shippingOrder.CustomerCode)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Book vận chuyển thành công cho đơn hàng : " + input.ReferenceCode,
	}
}

func BookReturn(input *request.BookShippingOrder, createdBy int64) *common.APIResponse {
	if input.ReferenceCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã liên kết không hợp lệ",
			ErrorCode: "EMPTY_REFERENCE_CODE",
		}
	}

	if input.From == nil ||
		input.From.Address == "" ||
		input.From.Name == "" ||
		input.From.Phone == "" ||
		input.From.ProvinceName == "" ||
		input.From.ProvinceCode == "" ||
		input.From.DistrictName == "" ||
		input.From.DistrictCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Thiếu thông tin địa chỉ gửi hàng",
		}
	}

	if input.To == nil ||
		input.To.Code == "" ||
		input.To.Name == "" ||
		input.To.Phone == "" ||
		input.To.Address == "" ||
		input.To.DistrictName == "" ||
		input.To.DistrictCode == "" ||
		input.To.ProvinceName == "" ||
		input.To.ProvinceCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Thiếu thông tin địa chỉ nhận hàng",
		}
	}

	var deliveredShippingOrderRaw *common.APIResponse
	if len(input.DeliveryOrderCodes) > 0 {
		deliveredShippingOrderRaw = model.ShippingOrderDB.QueryOne(bson.M{
			"reference_code": bson.M{
				"$in": input.DeliveryOrderCodes,
			},
			"status": bson.M{
				"$in": []enum.TPLCallbackStatusValue{
					enum.TPLCallbackStatus.DELIVERED,
					enum.TPLCallbackStatus.COMPLETED,
				},
			},
		})
		if deliveredShippingOrderRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Không tìm thấy đơn giao hàng thành công",
				ErrorCode: "NOT_FOUND_SHIPPING_ORDER",
			}
		}
		input.DeliveryOrderCode =
			deliveredShippingOrderRaw.Data.([]*model.ShippingOrder)[0].ReferenceCode
	} else if input.DeliveryOrderCode != "" {
		deliveredShippingOrderRaw = model.ShippingOrderDB.QueryOne(bson.M{
			"reference_code": input.DeliveryOrderCode,
		})
	} else {
		// Check exist shipping order
		deliveredShippingOrderRaw = model.ShippingOrderDB.QueryOne(bson.M{
			"reference_code": input.ReferenceCode,
		})
	}

	if deliveredShippingOrderRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy đơn giao hàng thành công",
			ErrorCode: "NOT_FOUND_SHIPPING_ORDER",
		}
	}

	deliveredShippingOrder := deliveredShippingOrderRaw.Data.([]*model.ShippingOrder)[0]
	if deliveredShippingOrder.Status == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Trạng thái đơn giao hàng không hợp lệ",
			ErrorCode: "INVALID_SHIPPING_ORDER_STATUS",
		}
	}

	// Xac dinh ma don
	if !input.ReBook &&
		(*deliveredShippingOrder.Status == enum.TPLCallbackStatus.DELIVERED ||
			*deliveredShippingOrder.Status == enum.TPLCallbackStatus.COD_COLLECTED ||
			*deliveredShippingOrder.Status == enum.TPLCallbackStatus.COMPLETED) {
		countReturnQuery := bson.M{
			"parent_reference_code": deliveredShippingOrder.ReferenceCode,
			"status": bson.M{
				"$in": []string{
					"CANCEL",
					"CANCEL_DELIVERY",
					"COMPLETED",
				},
			},
			"type": enum.ShippingOrderType.RETURN,
		}
		input.ParentReferenceCode = deliveredShippingOrder.ReferenceCode
		if (strings.HasPrefix(deliveredShippingOrder.ReferenceCode, "SO") ||
			strings.HasPrefix(deliveredShippingOrder.ReferenceCode, "CO") ||
			strings.HasPrefix(deliveredShippingOrder.ReferenceCode, "RO") ||
			strings.HasPrefix(deliveredShippingOrder.ReferenceCode, "TO") ||
			strings.HasPrefix(deliveredShippingOrder.ReferenceCode, "LO")) &&
			(strings.Contains(deliveredShippingOrder.ReferenceCode, "-P") ||
				strings.Contains(deliveredShippingOrder.ReferenceCode, "-F")) {
			input.ParentReferenceCode = deliveredShippingOrder.ParentReferenceCode
			countReturnQuery = bson.M{
				"parent_reference_code": deliveredShippingOrder.ParentReferenceCode,
				"type":                  enum.ShippingOrderType.RETURN,
			}
		}
		// Nếu cancel hoặc đơn return cũ hoàn thành rồi book lại thì + 1 vào đuôi của mã đơn
		numOfReturnedOrder := model.ShippingOrderDB.Count(countReturnQuery).Total

		if numOfReturnedOrder == 0 {
			input.ReferenceCode += "R1"
		} else {
			input.ReferenceCode += "R" + strconv.Itoa(int(numOfReturnedOrder+1))
		}
	}
	if input.ReBook {
		input.ReferenceCode = input.OldReferenceCode
	}

	carrier, hub, err := FindSuitableCarrierAndHubForReturn(input, deliveredShippingOrder)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "NOT_FOUND_VALID_CARRIER",
		}
	}

	if carrier.ParentCode == nil || *carrier.ParentCode == "" {
		carrier.ParentCode = carrier.CarrierCode
	}
	input.CarrierCode = carrier.ParentCode
	input.CarrierName = carrier.CarrierName
	input.CarrierId = carrier.CarrierId
	input.HubCode = hub.Code

	shippingOrder := buildShippingOrder(input, int(createdBy))
	createdShippingOrder := model.ShippingOrderDB.Upsert(bson.M{
		"reference_code": shippingOrder.ReferenceCode,
	}, shippingOrder)

	if createdShippingOrder.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Đã có lỗi xảy ra khi tạo đơn hàng, thử lại sau",
		}
	}

	updaterCarrierErr := shipping.ChangeInfoCarrier(carrier)
	if updaterCarrierErr != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Đã có lỗi xảy ra khi tạo đơn hàng, thử lại sau",
		}
	}

	var trackingInfo *model.ShippingInfo
	var bookErr error
	var needCalculateShippingFee bool = false
	if carrier.IsInternal != nil && *carrier.IsInternal == false {
		trackingInfo, bookErr = BookExternalCarrier(input, carrier)
	} else {
		trackingNumber := model.GenCode("TRACKING_CODE")
		trackingInfo = &model.ShippingInfo{
			TrackingNumber: trackingNumber,
			CODAmount:      input.CODAmount,
		}

		if carrier.ParentCode == nil {
			trackingInfo.TplCode = carrier.CarrierCode
		} else {
			trackingInfo.TplCode = carrier.ParentCode
		}

		// TODO: add logic here to charge logistic fee, default needCalculateShippingFee is false
		if needCalculateShippingFee {
			trackingInfo.FeeAmount = calculateShippingFee(input.NumPackage, input.Weight, input.From.ProvinceCode, input.To.ProvinceCode, false)
			if input.CODAmount == 0 {
				input.CODAmount = trackingInfo.FeeAmount
				input.TotalAmount = trackingInfo.FeeAmount
			} else {
				input.TotalAmount = trackingInfo.FeeAmount + input.CODAmount
			}
			input.PaymentMethod = &enum.PaymentMethod.COD
		}
	}

	// Nếu book không thành công chuyển trạng thái shipping order sang CREATE_FAIL để book lại
	// Chuyển hub order sang nhà vận chuyển book thất bại để vận hành đỡ rối
	if bookErr != nil || trackingInfo == nil {
		updateFailShippingOrder := model.ShippingOrderDB.UpdateOne(bson.M{
			"reference_code": shippingOrder.ReferenceCode,
		}, bson.M{
			"status": &enum.TPLCallbackStatus.CREATE_FAIL,
		})

		updateFailHubOrder := model.HUBShippingOrderDB.UpdateOne(bson.M{
			"reference_code": shippingOrder.ReferenceCode,
			"hub_code":       shippingOrder.CurrentHub,
		}, bson.M{
			"tpl_code": shippingOrder.TplCode,
			"tpl_name": shippingOrder.TplName,
		})

		if updateFailShippingOrder.Status != common.APIStatus.Ok || updateFailHubOrder.Status != common.APIStatus.Ok {
			_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
				CmdType: "ERROR_UPDATE_SHIPPING_ORDER",
				Title:   "Update shipping order when booking shipping " + shippingOrder.ReferenceCode,
				Message: "Update shipping order messgae: " + updateFailShippingOrder.Message,
			})
		}

		// Nếu book thất bại vẫn tạo phiếu trả hàng nhưng yêu cầu Logistic phải dùng api đổi nhà vận chuyển để chỉnh sửa
		// Số kiện, số kí, nhà vận chuyển mới
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: bookErr.Error(),
			Data: []*model.ShippingOrder{
				{
					ReferenceCode: shippingOrder.ReferenceCode,
				},
			},
		}
	}

	shippingOrder.TrackingCode = trackingInfo.TrackingNumber
	shippingOrder.FeeAmount = trackingInfo.FeeAmount
	// Sender chỉ trả phí nếu có
	if shippingOrder.FeeCollectMethod != nil && *shippingOrder.FeeCollectMethod == enum.FeeCollectMethod.SENDER_PAY {
		shippingOrder.FeeSenderAmount = &shippingOrder.FeeAmount
		shippingOrder.TotalCollectSenderAmount = &shippingOrder.FeeAmount
	}

	// Receiver LUÔN LUÔN phải trả COD đơn hàng
	shippingOrder.TotalCollectReceiverAmount = &shippingOrder.CODAmount
	// Nếu xác định được Receiver trả phí vận chuyển thì công thêm phí vận chuyển vào tổng thu người nhận
	if shippingOrder.FeeCollectMethod != nil && *shippingOrder.FeeCollectMethod == enum.FeeCollectMethod.RECEIVER_PAY {
		shippingOrder.FeeReceiverAmount = &shippingOrder.FeeAmount
		totalReceiverAmount := shippingOrder.CODAmount + shippingOrder.FeeAmount
		shippingOrder.TotalCollectReceiverAmount = &totalReceiverAmount
	}

	hubOrder := buildHubOrderForReturn(&shippingOrder, hub, carrier, createdBy)
	createdHubOrder := model.HUBShippingOrderDB.Upsert(bson.M{
		"reference_code": hubOrder.ReferenceCode,
		"hub_code":       hubOrder.HUBCode,
	}, hubOrder)

	if createdHubOrder.Status != common.APIStatus.Ok {
		hubOrderJson, _ := json.Marshal(hubOrder)
		_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
			CmdType: "FAIL_TO_CREATE_HUB_ORDER",
			Title:   "Hub order: " + string(hubOrderJson) + "||" + "Error: " + createdHubOrder.Message,
			Message: "Không thể tạo hub order khi book shipping",
		})
	}

	// Only get tracking info and fee after successfully book shipping service
	updateShippingOrder := model.ShippingOrderDB.UpdateOne(bson.M{
		"reference_code": shippingOrder.ReferenceCode,
	}, bson.M{
		"current_hub":                   hubOrder.HUBCode,
		"tracking_code":                 trackingInfo.TrackingNumber,
		"fee_amount":                    trackingInfo.FeeAmount,
		"scope":                         []string{hubOrder.HUBCode},
		"total_collect_receiver_amount": shippingOrder.TotalCollectReceiverAmount,
		"fee_receiver_amount":           shippingOrder.FeeReceiverAmount,
		"total_collect_sender_amount":   shippingOrder.TotalCollectSenderAmount,
		"fee_sender_amount":             shippingOrder.FeeSenderAmount,
	})

	// Nếu fee = 0 thì phải update bằng bson.M do feeAmount là float64 chứ không phải *float64
	if trackingInfo.FeeAmount == 0 {
		updateHubOrderFee := model.HUBShippingOrderDB.UpdateOne(bson.M{
			"reference_code": hubOrder.ReferenceCode,
			"hub_code":       hubOrder.HUBCode,
		}, bson.M{
			"fee_amount": trackingInfo.FeeAmount,
		})

		if updateHubOrderFee.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Đã có lỗi xảy ra khi đặt vận chuyển, thử lại sau",
			}
		}
	}

	if updateShippingOrder.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Đã có lỗi xảy ra khi đặt vận chuyển, thử lại sau",
		}
	}

	current := time.Now()
	trackingInfo.ReferenceCode = shippingOrder.ReferenceCode
	callbackModel := request.Callback{
		SO:            input.ReferenceCode,
		CreatedSource: carrier.ParentCode,
		WarehouseCode: input.To.Code,
		Status:        &enum.TPLCallbackStatus.READY_TO_PICK,
		Weight:        input.Weight,
		TPLCode:       trackingInfo.TrackingNumber,
		TotalFee:      trackingInfo.FeeAmount,
		COD:           trackingInfo.CODAmount,
		ActionTime:    &current,
		StatusName:    "Tạo mới vận đơn",
		ExtraCallback: map[string]interface{}{
			"shippingInfo": trackingInfo,
		},
		ExternalTPLName: carrier.CarrierName,
		TPLStatus:       string(enum.TPLCallbackStatus.READY_TO_PICK),
		TPLStatusName:   "Tạo mới vận đơn",
		NumPackage:      input.NumPackage,
		Type:            &enum.ShippingOrderType.RETURN,
	}

	// Set nhầm thì gán với nvc cha luôn
	if callbackModel.CreatedSource == nil || *callbackModel.CreatedSource == "" {
		callbackModel.CreatedSource = carrier.CarrierCode
	}

	err = client.Services.TplCallbackClient.CreateCallback(callbackModel)
	if err != nil {
		log.Println("Can not update callback ", input.ReferenceCode)
	}

	if shippingOrder.FromCustomerCode != "" {
		err = customer.PushCreateCustomer(model.Customer{
			Code:         shippingOrder.FromCustomerCode,
			Name:         shippingOrder.FromCustomerName,
			Phone:        shippingOrder.FromCustomerPhone,
			CustomerType: &enum.CustomerType.INTERNAL,
		}, shippingOrder.CustomerCode)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Book vận chuyển thành công cho đơn hàng : " + input.ReferenceCode,
		Data:    []*model.ShippingInfo{trackingInfo},
	}
}

func buildShippingOrder(input *request.BookShippingOrder, createdBy int) model.ShippingOrder {
	current := time.Now()
	shippingOrder := model.ShippingOrder{
		ReferenceCode:     input.ReferenceCode,
		DeliveryOrderCode: input.DeliveryOrderCode,
		PartnerCode:       input.PartnerCode,
		BookingTime:       current.Unix(),
		PrivateNote:       input.PickupNote,
		Note:              input.DeliveryNote,

		Height:     input.Height,
		Width:      input.Width,
		Length:     input.Length,
		Weight:     input.Weight,
		NumPackage: input.NumPackage,

		VersionNo:            uuid.New().String(),
		ActionTime:           current.Unix(),
		CreatedBy:            strconv.Itoa(createdBy),
		LastUpdatedTime:      &current,
		CreatedTime:          &current,
		ExpectedPickupTime:   input.ExpectedPickupTime,
		ExpectedDeliveryTime: input.ExpectedDeliveryTime,

		Products:     input.Products,
		CurrentHub:   input.HubCode,
		TplName:      input.CarrierName,
		TplCode:      input.CarrierCode,
		TplServiceId: input.CarrierId,
		Scope:        []string{input.HubCode},

		FeeCollectMethod: input.FeeCollectedOn,
		CallbackUrl:      input.CallbackUrl,
		IsReconciled:     input.IsReconciled,
		CheckinNumPack:   input.CheckInNumPack,
		OrderValue:       &input.OrderValue,
		VoucherCode:      input.VoucherCode,
		Tags:             input.Tags,
		ProductType:      input.ProductType,
		ProductTypes:     input.ProductTypes,
	}

	if shippingOrder.Height == 0 {
		shippingOrder.Height = 30
	}

	if shippingOrder.Width == 0 {
		shippingOrder.Width = 40
	}

	if shippingOrder.Length == 0 {
		shippingOrder.Length = 50
	}

	if shippingOrder.Weight == 0 {
		shippingOrder.Weight = 1
	}

	if input.ExpireAfter > 0 {
		needAutoCancelAt := current.Add(time.Duration(input.ExpireAfter) * time.Second)
		shippingOrder.NeedAutoCancelAt = &needAutoCancelAt
	}

	switch *input.ShippingType {

	case enum.ShippingOrderType.PICKUP:
		shippingOrder.TrackingCode = model.GenCode("TRACKING_CODE")
		shippingOrder.Status = &enum.TPLCallbackStatus.READY_TO_PICK
		shippingOrder.ShippingType = &enum.ShippingOrderType.PICKUP
		shippingOrder.ParentReferenceCode = input.ParentReferenceCode
		shippingOrder.CustomerName = input.To.Name
		shippingOrder.CustomerCode = input.To.Code
		shippingOrder.CustomerWardName = input.To.WardName
		shippingOrder.CustomerWardCode = input.To.WardCode
		shippingOrder.CustomerDistrictCode = input.To.DistrictCode
		shippingOrder.CustomerDistrictName = input.To.DistrictName
		shippingOrder.CustomerProvinceCode = input.To.ProvinceCode
		shippingOrder.CustomerProvinceName = input.To.ProvinceName
		shippingOrder.CustomerShippingAddress = input.To.Address
		shippingOrder.CustomerPhone = input.To.Phone
		shippingOrder.CustomerEmail = input.To.Email

		shippingOrder.FromCustomerAddress = input.From.Address
		shippingOrder.FromCustomerCode = input.From.Code
		shippingOrder.FromCustomerName = input.From.Name
		shippingOrder.FromProvinceCode = input.From.ProvinceCode
		shippingOrder.FromProvinceName = input.From.ProvinceName
		shippingOrder.FromDistrictCode = input.From.DistrictCode
		shippingOrder.FromDistrictName = input.From.DistrictName
		shippingOrder.FromWardCode = input.From.WardCode
		shippingOrder.FromWardName = input.From.WardName
		shippingOrder.FromCustomerPhone = input.From.Phone
		shippingOrder.FromCustomerEmail = input.From.Email

		shippingOrder.Note = input.PickupNote

		if shippingOrder.CustomerCode == "" {
			shippingOrder.CustomerCode = input.WarehouseCode
		}

		if input.Status != nil && *input.Status == enum.TPLCallbackStatus.STORING {
			shippingOrder.Status = &enum.TPLCallbackStatus.STORING
		}

		if len(input.Products) > 0 {
			for _, product := range input.Products {
				shippingOrder.Baskets = append(shippingOrder.Baskets, model.Basket{
					Code: product.SKU,
				})
			}
		}

		shippingOrder.CustomerInfo = &model.Customer{
			Name:  input.From.Name,
			Code:  input.From.Code,
			Phone: input.From.Phone,
		}

	case enum.ShippingOrderType.DELIVERY:
		shippingOrder.Status = &enum.TPLCallbackStatus.INIT
		shippingOrder.ShippingType = &enum.ShippingOrderType.DELIVERY

		shippingOrder.CustomerName = input.From.Name
		shippingOrder.CustomerCode = input.From.Code
		shippingOrder.CustomerWardName = input.From.WardName
		shippingOrder.CustomerWardCode = input.From.WardCode
		shippingOrder.CustomerDistrictCode = input.From.DistrictCode
		shippingOrder.CustomerDistrictName = input.From.DistrictName
		shippingOrder.CustomerProvinceCode = input.From.ProvinceCode
		shippingOrder.CustomerProvinceName = input.From.ProvinceName
		shippingOrder.CustomerShippingAddress = input.From.Address
		shippingOrder.CustomerEmail = input.From.Email
		shippingOrder.CustomerPhone = input.From.Phone

		shippingOrder.FromCustomerAddress = input.To.Address
		shippingOrder.FromCustomerName = input.To.Name
		shippingOrder.FromCustomerCode = input.To.Code
		shippingOrder.FromProvinceCode = input.To.ProvinceCode
		shippingOrder.FromProvinceName = input.To.ProvinceName
		shippingOrder.FromDistrictCode = input.To.DistrictCode
		shippingOrder.FromDistrictName = input.To.DistrictName
		shippingOrder.FromWardCode = input.To.WardCode
		shippingOrder.FromWardName = input.To.WardName
		shippingOrder.FromCustomerPhone = input.To.Phone
		shippingOrder.FromCustomerEmail = input.To.Email

		shippingOrder.Note = input.DeliveryNote
		shippingOrder.CustomerInfo = &model.Customer{
			Name:  input.To.Name,
			Code:  input.To.Code,
			Phone: input.To.Phone,
		}

	case enum.ShippingOrderType.RETURN:
		shippingOrder.Status = &enum.TPLCallbackStatus.READY_TO_PICK
		shippingOrder.ShippingType = &enum.ShippingOrderType.RETURN
		shippingOrder.ParentReferenceCode = input.ParentReferenceCode
		shippingOrder.CustomerName = input.To.Name
		shippingOrder.CustomerCode = input.To.Code
		shippingOrder.CustomerWardName = input.To.WardName
		shippingOrder.CustomerWardCode = input.To.WardCode
		shippingOrder.CustomerDistrictCode = input.To.DistrictCode
		shippingOrder.CustomerDistrictName = input.To.DistrictName
		shippingOrder.CustomerProvinceCode = input.To.ProvinceCode
		shippingOrder.CustomerProvinceName = input.To.ProvinceName
		shippingOrder.CustomerShippingAddress = input.To.Address
		shippingOrder.CustomerPhone = input.To.Phone
		shippingOrder.CustomerEmail = input.To.Email
		shippingOrder.FromCustomerAddress = input.From.Address
		shippingOrder.FromCustomerCode = input.From.Code
		shippingOrder.FromCustomerName = input.From.Name
		shippingOrder.FromProvinceCode = input.From.ProvinceCode
		shippingOrder.FromProvinceName = input.From.ProvinceName
		shippingOrder.FromDistrictCode = input.From.DistrictCode
		shippingOrder.FromDistrictName = input.From.DistrictName
		shippingOrder.FromWardCode = input.From.WardCode
		shippingOrder.FromWardName = input.From.WardName
		shippingOrder.FromCustomerPhone = input.From.Phone
		shippingOrder.FromCustomerEmail = input.From.Email

		shippingOrder.Note = input.PickupNote

		shippingOrder.CustomerInfo = &model.Customer{
			Name:  input.From.Name,
			Code:  input.From.Code,
			Phone: input.From.Phone,
		}

	case enum.ShippingOrderType.FMPO:
		shippingOrder.TrackingCode = model.GenCode("TRACKING_CODE")
		shippingOrder.ShippingType = &enum.ShippingOrderType.FMPO
		shippingOrder.ParentReferenceCode = input.ParentReferenceCode
		shippingOrder.ReceiveSessionCode = input.ReceiveSessionCode
		shippingOrder.ParentReceiveSessionCode = input.ParentReceiveSessionCode
		shippingOrder.CustomerName = input.To.Name
		shippingOrder.CustomerCode = input.To.Code
		shippingOrder.CustomerWardName = input.To.WardName
		shippingOrder.CustomerWardCode = input.To.WardCode
		shippingOrder.CustomerDistrictCode = input.To.DistrictCode
		shippingOrder.CustomerDistrictName = input.To.DistrictName
		shippingOrder.CustomerProvinceCode = input.To.ProvinceCode
		shippingOrder.CustomerProvinceName = input.To.ProvinceName
		shippingOrder.CustomerShippingAddress = input.To.Address
		shippingOrder.CustomerPhone = input.To.Phone
		shippingOrder.CustomerEmail = input.To.Email
		shippingOrder.CODAmount = input.CODAmount
		shippingOrder.FromCustomerAddress = input.From.Address
		shippingOrder.FromCustomerCode = input.From.Code
		shippingOrder.FromCustomerName = input.From.Name
		shippingOrder.FromProvinceCode = input.From.ProvinceCode
		shippingOrder.FromProvinceName = input.From.ProvinceName
		shippingOrder.FromDistrictCode = input.From.DistrictCode
		shippingOrder.FromDistrictName = input.From.DistrictName
		shippingOrder.FromWardCode = input.From.WardCode
		shippingOrder.FromWardName = input.From.WardName
		shippingOrder.FromCustomerPhone = input.From.Phone
		shippingOrder.FromCustomerEmail = input.From.Email
		shippingOrder.FeeAmount = shippingOrder.CODAmount

		// Nếu drop off thì status sẽ là STORING
		if input.DropOffAtHubCode != "" {
			shippingOrder.Status = &enum.TPLCallbackStatus.STORING
		} else {
			shippingOrder.Status = &enum.TPLCallbackStatus.READY_TO_PICK
		}

		if input.IsBookDropOff {
			shippingOrder.Status = &enum.TPLCallbackStatus.PICKED
			shippingOrder.IsBookDropOff = &enum.True
		} else {
			shippingOrder.IsBookDropOff = &enum.False
		}

		if input.IsSplitOrder {
			shippingOrder.CODAmount = 0
			shippingOrder.SplitFromCode = input.OldReferenceCode
		}

		if shippingOrder.FeeCollectMethod != nil &&
			*shippingOrder.FeeCollectMethod == enum.FeeCollectMethod.SENDER_PAY {
			shippingOrder.TotalCollectSenderAmount = &shippingOrder.CODAmount
			shippingOrder.FeeSenderAmount = &shippingOrder.CODAmount
		}

		shippingOrder.CustomerInfo = &model.Customer{
			Name:  input.From.Name,
			Code:  input.From.Code,
			Phone: input.From.Phone,
		}

	case enum.ShippingOrderType.CS:
		shippingOrder.ShippingType = &enum.ShippingOrderType.CS
		shippingOrder.ReferenceCode = input.ReferenceCode
		shippingOrder.TrackingCode = input.TrackingCode

		if !input.ReBook {
			customerSupportCode := strconv.FormatInt(model.GenId("CUSTOMER_SUPPORT_CODE"), 10)
			shippingOrder.ReferenceCode = "CSKH" + customerSupportCode
			shippingOrder.TrackingCode = model.GenCode("TRACKING_CODE")
		}

		shippingOrder.FromCustomerCode = input.From.Code
		shippingOrder.FromCustomerName = input.From.Name
		shippingOrder.FromCustomerPhone = input.From.Phone
		shippingOrder.FromCustomerEmail = input.From.Email
		shippingOrder.FromCustomerAddress = input.From.Address
		shippingOrder.FromProvinceCode = input.From.ProvinceCode
		shippingOrder.FromProvinceName = input.From.ProvinceName
		shippingOrder.FromDistrictCode = input.From.DistrictCode
		shippingOrder.FromDistrictName = input.From.DistrictName
		shippingOrder.FromWardCode = input.From.WardCode
		shippingOrder.FromWardName = input.From.WardName

		shippingOrder.CustomerName = input.To.Name
		shippingOrder.CustomerCode = input.To.Code
		shippingOrder.CustomerPhone = input.To.Phone
		shippingOrder.CustomerEmail = input.To.Email
		shippingOrder.CustomerShippingAddress = input.To.Address
		shippingOrder.CustomerProvinceCode = input.To.ProvinceCode
		shippingOrder.CustomerProvinceName = input.To.ProvinceName
		shippingOrder.CustomerDistrictCode = input.To.DistrictCode
		shippingOrder.CustomerDistrictName = input.To.DistrictName
		shippingOrder.CustomerWardCode = input.To.WardCode
		shippingOrder.CustomerWardName = input.To.WardName

		shippingOrder.Height = 0
		shippingOrder.Width = 0
		shippingOrder.Length = 0
		shippingOrder.Weight = 0

		shippingOrder.Note = input.Note

		shippingOrder.TplServiceId = input.CarrierId
		shippingOrder.TplName = input.CarrierName
		shippingOrder.TplCode = input.CarrierCode

		shippingOrder.Action = "IN"
		shippingOrder.ActionName = "Chăm sóc khách hàng"
		shippingOrder.Status = &enum.TPLCallbackStatus.STORING

		shippingOrder.CreatedBy = strconv.Itoa(createdBy)
		shippingOrder.ActionTime = current.Unix()

		shippingOrder.CurrentHub = input.HubCode
		shippingOrder.CustomerInfo = &model.Customer{
			Name:  input.From.Name,
			Code:  input.From.Code,
			Phone: input.From.Phone,
		}

	case enum.ShippingOrderType.EO:
		shippingOrder.ReferenceCode = input.ReferenceCode
		shippingOrder.TrackingCode = input.TrackingCode

		if !input.ReBook && input.ParentReferenceCode == "" {
			eoCode := strconv.FormatInt(model.GenId("EXTERNAL_ORDER_CODE"), 10)
			shippingOrder.ReferenceCode = "BMX" + eoCode
			shippingOrder.TrackingCode = model.GenCode("TRACKING_CODE")
		}

		if input.ParentReferenceCode != "" {
			shippingOrder.ParentReferenceCode = input.ParentReferenceCode
			shippingOrder.ReferenceCode = input.ParentReferenceCode
			shippingOrder.TrackingCode = model.GenCode("TRACKING_CODE")
		}

		shippingOrder.Note = input.Note
		if input.FmHub != nil {
			shippingOrder.CurrentHub = input.FmHub.Code
			shippingOrder.FirstMileHubCode = input.FmHub.Code
			shippingOrder.FirstMileCarrierId = input.FmHub.DefaultCarrierId
		}
		if input.LmHub != nil {
			shippingOrder.LastMileHubCode = input.LmHub.Code
			shippingOrder.LastMileCarrierId = input.LmHub.DefaultCarrierId
		}

		shippingOrder.ShippingType = &enum.ShippingOrderType.EO
		shippingOrder.CustomerInfo = input.CustomerInfo
		shippingOrder.Status = &enum.TPLCallbackStatus.READY_TO_PICK

		shippingOrder.CustomerName = input.To.Name
		shippingOrder.CustomerCode = input.CustomerInfo.Code
		shippingOrder.CustomerPhone = input.To.Phone
		shippingOrder.CustomerWardName = input.To.WardName
		shippingOrder.CustomerWardCode = input.To.WardCode
		shippingOrder.CustomerDistrictCode = input.To.DistrictCode
		shippingOrder.CustomerDistrictName = input.To.DistrictName
		shippingOrder.CustomerProvinceCode = input.To.ProvinceCode
		shippingOrder.CustomerProvinceName = input.To.ProvinceName
		shippingOrder.CustomerShippingAddress = input.To.Address

		shippingOrder.FromCustomerCode = input.From.Code
		shippingOrder.FromCustomerName = input.From.Name
		shippingOrder.FromCustomerPhone = input.From.Phone
		shippingOrder.FromCustomerEmail = input.From.Email
		shippingOrder.FromCustomerAddress = input.From.Address
		shippingOrder.FromProvinceCode = input.From.ProvinceCode
		shippingOrder.FromProvinceName = input.From.ProvinceName
		shippingOrder.FromDistrictCode = input.From.DistrictCode
		shippingOrder.FromDistrictName = input.From.DistrictName
		shippingOrder.FromWardCode = input.From.WardCode
		shippingOrder.FromWardName = input.From.WardName

		shippingOrder.AccountToken = input.AccountToken
		shippingOrder.SubAccountToken = input.SubAccountToken
		shippingOrder.ParentReceiveSessionCode = input.ParentReceiveSessionCode
		if input.DropOffAtHubCode != "" {
			shippingOrder.IsBookDropOff = &enum.True
			shippingOrder.IsDropOffAtFMHub = true
			shippingOrder.Status = &enum.TPLCallbackStatus.PICKED
			shippingOrder.FromProvinceName = input.FmHub.Address.ProvinceName
			shippingOrder.FromProvinceCode = input.FmHub.Address.ProvinceCode
			shippingOrder.FromDistrictName = input.FmHub.Address.DistrictName
			shippingOrder.FromDistrictCode = input.FmHub.Address.DistrictCode
			shippingOrder.FromWardName = input.FmHub.Address.WardName
			shippingOrder.FromWardCode = input.FmHub.Address.WardCode
		}
		if input.ReceiveAtHubCode != "" {
			shippingOrder.IsReceiveAtLMHub = true
		}
		shippingOrder.CODAmount = input.CODAmount
		if input.OPM {
			shippingOrder.CODAmount = *input.PickMoney
			shippingOrder.ShippingType = &enum.ShippingOrderType.OPM
		}
	}

	shippingOrder.References = utils.AppendStrIfNotExists(shippingOrder.References,
		input.ReferenceCode,
		input.ParentReferenceCode,
		input.ParentReceiveSessionCode,
		input.TrackingCode,
	)

	return shippingOrder
}

func buildHubOrderForPickup(shippingOrder *model.ShippingOrder, hub *model.Hub, createdBy int64) (hubShippingOrder model.HubShippingOrder) {
	current := time.Now()
	// Calculate hub order lead time
	leadTime := CalculateHubOrderLeadTime(&request.CalculateLeadTimeRequest{
		From: &model.Address{
			WardCode:     shippingOrder.FromWardCode,
			DistrictCode: shippingOrder.FromDistrictCode,
			ProvinceCode: shippingOrder.FromProvinceCode,
		},
		To: &model.Address{
			WardCode:     hub.Address.WardCode,
			DistrictCode: hub.Address.DistrictCode,
			ProvinceCode: hub.Address.ProvinceCode,
		},
		Partner:     string(*shippingOrder.TplCode),
		ServiceType: &enum.LeadTimeConfigType.PICKING,
	})

	intLeadTime := leadTime.Data.([]int64)[0]
	// We use iso time because it is easier to compare date with created time
	isoLeadTime := current.Add(time.Second * time.Duration(intLeadTime))

	hubShippingOrder = model.HubShippingOrder{
		VersionNo:           uuid.New().String(),
		HUBCode:             hub.Code,
		ReferenceCode:       shippingOrder.ReferenceCode,
		TrackingCode:        shippingOrder.TrackingCode,
		ParentReferenceCode: shippingOrder.ParentReferenceCode,

		FromCustomerName:    shippingOrder.FromCustomerName,
		FromCustomerCode:    shippingOrder.FromCustomerCode,
		FromCustomerPhone:   shippingOrder.FromCustomerPhone,
		FromCustomerEmail:   shippingOrder.FromCustomerEmail,
		FromCustomerAddress: shippingOrder.FromCustomerAddress,
		FromWardCode:        shippingOrder.FromWardCode,
		FromWardName:        shippingOrder.FromWardName,
		FromDistrictCode:    shippingOrder.FromDistrictCode,
		FromDistrictName:    shippingOrder.FromDistrictName,
		FromProvinceCode:    shippingOrder.FromProvinceCode,
		FromProvinceName:    shippingOrder.FromProvinceName,
		FromLongitude:       shippingOrder.FromLongitude,
		FromLatitude:        shippingOrder.FromLatitude,

		ToCustomerName:    hub.Name,
		ToCustomerCode:    hub.Code,
		ToCustomerAddress: hub.Address.Address,
		ToCustomerPhone:   hub.Address.Phone,
		ToCustomerEmail:   hub.Address.Email,
		ToWardCode:        hub.Address.WardCode,
		ToWardName:        hub.Address.WardName,
		ToDistrictCode:    hub.Address.DistrictCode,
		ToDistrictName:    hub.Address.DistrictName,
		ToProvinceCode:    hub.Address.ProvinceCode,
		ToProvinceName:    hub.Address.ProvinceName,
		ToLongitude:       hub.Address.Longitude,
		ToLatitude:        hub.Address.Latitude,

		Type:       &enum.HubOrderType.PICKUP,
		SubType:    &enum.SubType.PICKUP,
		Status:     &enum.HubShippingOrderStatus.READY_TO_PICK,
		ActionName: "Đang lấy hàng về hub " + hub.Name,
		Action:     "PICKING", // IN, OUT, TRANSPORTING, PICKING

		Height:      shippingOrder.Height,
		Width:       shippingOrder.Width,
		Length:      shippingOrder.Length,
		Weight:      shippingOrder.Weight,
		NumPackage:  shippingOrder.NumPackage,
		PrivateNote: shippingOrder.PrivateNote,
		Note:        shippingOrder.Note,

		CreatedTime:          &current,
		ActionTime:           current.Unix(),
		CreatedBy:            strconv.FormatInt(createdBy, 10),
		ExpectedDeliveryTime: shippingOrder.ExpectedDeliveryTime,
		ExpectedPickupTime:   shippingOrder.ExpectedPickupTime,

		Products:       shippingOrder.Products,
		TplCode:        shippingOrder.TplCode,
		TplName:        shippingOrder.TplName,
		PickupLeadTime: &isoLeadTime,
		Baskets:        shippingOrder.Baskets,
		References:     shippingOrder.References,
	}

	if shippingOrder.Status != nil &&
		*shippingOrder.Status == enum.TPLCallbackStatus.STORING {
		hubShippingOrder.Status = &enum.HubShippingOrderStatus.STORING
		for _, p := range hubShippingOrder.Products {
			p.Status = &enum.ProductStatus.STORING
		}
	}

	return
}

func CancelShippingOrder(input *request.CancelShippingService, updatedBy int64) *common.APIResponse {
	if input.ParentReferenceCode != "" {
		input.ReferenceCode = input.ParentReferenceCode + "-1"
	}

	if input == nil || input.ReferenceCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã đơn không hợp lệ",
			ErrorCode: "EMPTY_REFERENCE_CODE",
		}
	}

	shippingOrderRaw := model.ShippingOrderDB.QueryOne(&model.ShippingOrder{
		ReferenceCode: input.ReferenceCode,
	})

	if shippingOrderRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã đơn không hợp lệ",
			ErrorCode: "INVALID_REFERENCE_CODE",
		}
	}

	shippingOrder := shippingOrderRaw.Data.([]*model.ShippingOrder)[0]
	if shippingOrder.Status != nil && *shippingOrder.Status == enum.TPLCallbackStatus.CANCEL {
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Hủy vận chuyển thành công.",
		}
	}

	if shippingOrder.ShippingType != nil &&
		*shippingOrder.ShippingType == enum.ShippingOrderType.CS {
		return CancelCustomerSupportOrder(shippingOrder, updatedBy)
	}

	if shippingOrder.Status == nil ||
		(shippingOrder.ShippingType != nil &&
			*shippingOrder.ShippingType != enum.ShippingOrderType.FMPO &&
			*shippingOrder.ShippingType != enum.ShippingOrderType.EO &&
			*shippingOrder.Status != enum.TPLCallbackStatus.READY_TO_PICK &&
			*shippingOrder.Status != enum.TPLCallbackStatus.PICK_FAIL &&
			*shippingOrder.Status != enum.TPLCallbackStatus.CREATE_FAIL &&
			*shippingOrder.Status != enum.TPLCallbackStatus.LOST) {

		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Chỉ có thể hủy đơn khi đơn ở trạng thái chờ lấy hàng hoặc đang lấy hàng",
			ErrorCode: "INVALID_SHIPPING_ORDER_STATUS",
		}
	}

	if shippingOrder.ShippingType != nil &&
		*shippingOrder.ShippingType == enum.ShippingOrderType.FMPO {
		if shippingOrder.IsBookDropOff != nil && *shippingOrder.IsBookDropOff &&
			*shippingOrder.Status != enum.TPLCallbackStatus.PICKED {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Chỉ có thể hủy đơn Dropoff khi đơn chờ nhập kho",
				ErrorCode: "INVALID_SHIPPING_ORDER_STATUS",
			}
		}

		if (shippingOrder.IsBookDropOff == nil || !*shippingOrder.IsBookDropOff) &&
			*shippingOrder.Status != enum.TPLCallbackStatus.READY_TO_PICK &&
			*shippingOrder.Status != enum.TPLCallbackStatus.PICK_FAIL {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Chỉ có thể hủy đơn first mile khi đơn chờ lấy hàng hoặc lấy hàng thất bại",
				ErrorCode: "INVALID_SHIPPING_ORDER_STATUS",
			}
		}

	}

	if shippingOrder.ShippingType != nil &&
		*shippingOrder.ShippingType == enum.ShippingOrderType.EO {
		if (shippingOrder.IsDropOffAtFMHub ||
			(shippingOrder.IsBookDropOff != nil && *shippingOrder.IsBookDropOff)) &&
			*shippingOrder.Status != enum.TPLCallbackStatus.PICKED {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Đơn dropoff chỉ có thể hủy khi chờ nhập kho",
				ErrorCode: "INVALID_SHIPPING_ORDER_STATUS",
			}
		}

		if !shippingOrder.IsDropOffAtFMHub &&
			*shippingOrder.Status != enum.TPLCallbackStatus.READY_TO_PICK {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Đơn pickup chỉ có thể hủy khi ở trạng thái chờ lấy hàng",
				ErrorCode: "INVALID_SHIPPING_ORDER_STATUS",
			}
		}
	}

	handoverTicketRaw := model.HandoverTicketDB.QueryOne(bson.M{
		"so_list.so": input.ReferenceCode,
		"status": bson.M{
			"$nin": []string{"CANCEL", "COMPLETED"},
		},
		"so_list.status": &enum.HandoverItemStatus.CANCEL,
	})
	if handoverTicketRaw.Status != common.APIStatus.NotFound {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Đơn hàng đang được bàn giao, không thể hủy giao hàng.",
			ErrorCode: "EXISTED_HANDOVER_TICKET",
		}
	}

	carrierRaw := model.CarrierDB.QueryOne(bson.M{
		"carrier_id": shippingOrder.TplServiceId,
	})

	if carrierRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy thông tin nhà vận chuyển",
			ErrorCode: "GET_CARRIER_ERROR",
		}
	}

	carrier := carrierRaw.Data.([]*model.Carrier)[0]
	if shippingOrder.AccountToken != "" {
		carrier.ExtraData.AccessToken = shippingOrder.AccountToken
	}
	if shippingOrder.SubAccountToken != "" {
		carrier.ExtraData.SubAccountToken = shippingOrder.SubAccountToken
	}
	err := shipping.ChangeInfoCarrier(carrier)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Error(),
		}
	}

	var cancel3plErr error
	if carrier.IsInternal != nil && !*carrier.IsInternal {
		if carrier.ParentCode == nil && *carrier.ParentCode == "" {
			carrier.ParentCode = carrier.CarrierCode
		}
		switch *carrier.ParentCode {
		case enum.Partner.GHTK:
			cancel3plErr = shipping.TplShippingClient.GHTKClient.CancelGHTK(input.ReferenceCode)
			break
		case enum.Partner.VIETTEL_POST:
			trackingCode, _ := strconv.Atoi(shippingOrder.TrackingCode)
			cancelVTPRequest := request.UpdateTrackingVTP{
				Type:        &enum.VTPUpdateTrackingStatus.CANCEL_ORDER,
				OrderNumber: int64(trackingCode),
				Note:        "cancel",
			}
			cancel3plErr = shipping.TplShippingClient.VTPClient.CancelVTP(cancelVTPRequest)
			break
		}
	}

	if cancel3plErr != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   cancel3plErr.Error(),
			ErrorCode: "CANCEL_SHIPPING_ORDER_NOT_ALLOW",
		}
	}

	updateShippingOrderResult := model.ShippingOrderDB.UpdateOne(&model.ShippingOrder{
		ReferenceCode: input.ReferenceCode,
	}, &model.ShippingOrder{
		Status:        &enum.TPLCallbackStatus.CANCEL,
		LastUpdatedBy: strconv.FormatInt(updatedBy, 10),
	})

	if updateShippingOrderResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không thể cập nhật trạng thái đơn hàng, thử lại sau",
			ErrorCode: updateShippingOrderResult.ErrorCode,
		}
	}

	if shippingOrder.ShippingType != nil &&
		*shippingOrder.ShippingType != enum.ShippingOrderType.OPM {
		updateHubShippingOrderResult := model.HUBShippingOrderDB.UpdateOne(&model.HubShippingOrder{
			ReferenceCode: input.ReferenceCode,
			HUBCode:       shippingOrder.CurrentHub,
		}, &model.HubShippingOrder{
			Status:        &enum.HubShippingOrderStatus.CANCEL,
			LastUpdatedBy: strconv.FormatInt(updatedBy, 10),
		})

		if updateHubShippingOrderResult.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Không thể cập nhật trạng thái đơn hàng tại hub, thử lại sau",
				ErrorCode: updateHubShippingOrderResult.ErrorCode,
			}
		}
	}

	actionTime := time.Now()
	// After cancel locally push callback update
	createCallbackRequest := request.Callback{
		SO:              input.ReferenceCode,
		StatusName:      "Hủy vận chuyển",
		TPLStatusName:   "Hủy vận chuyển",
		ExternalTPLName: carrier.CarrierName,
		CreatedSource:   carrier.ParentCode,
		ActionTime:      &actionTime,
		Status:          &enum.TPLCallbackStatus.CANCEL,
		TPLStatus:       string(enum.TPLCallbackStatus.CANCEL),
	}

	errCb := client.Services.TplCallbackClient.CreateCallback(createCallbackRequest)
	if errCb != nil {
		log.Println("Can not create callback cancel ", input.ReferenceCode)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Hủy vận chuyển thành công",
	}
}

func CancelShippingOrders(input *request.CancelShippingService, updatedBy int64) *common.APIResponse {
	if len(input.ReferenceCodes) > 0 {
		model.ShippingOrderDB.UpdateMany(bson.M{
			"reference_code": bson.M{
				"$in": input.ReferenceCodes,
			},
		}, bson.M{
			"status": "STORING",
		})
	}

	for _, referenceCode := range input.ReferenceCodes {
		resp := CancelShippingOrder(&request.CancelShippingService{
			ReferenceCode: referenceCode,
		}, updatedBy)

		if resp.Status != common.APIStatus.Ok {
			return resp
		}
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Hủy vận chuyển thành công",
	}
}

func FindSuitableCarrierAndHubForReturn(input *request.BookShippingOrder, deliveredShippingOrder *model.ShippingOrder) (
	*model.Carrier, *model.Hub, error) {

	if input.CarrierId != 0 {
		carrierRaw := model.CarrierDB.QueryOne(&model.Carrier{CarrierId: input.CarrierId})
		if carrierRaw.Status != common.APIStatus.Ok || carrierRaw.Data == nil {
			return nil, nil, errors.New("Không tìm thấy dịch vụ vận chuyển")
		}
		carrier := carrierRaw.Data.([]*model.Carrier)[0]

		if carrier.Active != nil && !*carrier.Active {
			return nil, nil, errors.New(carrier.CarrierName + " chưa sẵn sàng để đặt")
		}

		// Nếu chọn nvc nội bộ yêu cầu chọn thêm hub code do nvc mặc định có thể dùng chung giữa các hub
		if carrier.IsInternal != nil && *carrier.IsInternal {
			if input.HubCode == "" {
				return nil, nil, errors.New("Yêu cầu mã hub nếu chọn nhà vận chuyển nội bộ")
			}

			hubRaw := model.HubDB.QueryOne(bson.M{
				"code": input.HubCode,
			})

			if hubRaw.Status != common.APIStatus.Ok {
				return nil, nil, errors.New("Nhà vận chuyển được chọn chưa trờ thành nhà vận chuyển mặc định của hub nào")
			}

			hub := hubRaw.Data.([]*model.Hub)[0]
			return carrier, hub, nil

		} else {
			if carrier.ParentCode == nil || *carrier.ParentCode == "" {
				return nil, nil, errors.New("Chọn nhà vận chuyển con để có thể đặt đơn")
			}

			hubRaw := model.HubDB.QueryOne(bson.M{
				"warehouse_reference_code": input.To.Code,
			})

			if hubRaw.Status != common.APIStatus.Ok {
				return nil, nil, errors.New("Không xác định được hub với mã kho là: " + input.To.Code)
			}

			hub := hubRaw.Data.([]*model.Hub)[0]

			return carrier, hub, nil
		}

	}

	customerId, err := strconv.ParseInt(input.From.Code, 10, 64)
	if err != nil {
		return nil, nil, errors.New("Mã dịnh danh khách hàng không hợp lệ")
	}

	// TODO: Hiện tại chỉ config địa chỉ giao hàng tại kho nên chưa thể giao hàng return tại hub bất kì
	availableCarrier := GetAvailableShippingCarrier(request.GetShippingService{
		CustomerID:   customerId,
		PackageCount: input.NumPackage,
		Weight:       input.Weight,
		// From customer address
		Province:     input.From.ProvinceName,
		ProvinceCode: input.From.ProvinceCode,
		District:     input.From.DistrictName,
		DistrictCode: input.From.DistrictCode,
		WardCode:     input.From.WardCode,
		Ward:         input.From.WardName,
		// To warehouse
		WarehouseCode: input.To.Code,
	})

	if availableCarrier.Status == common.APIStatus.Ok {
		avlCarriers := availableCarrier.Data.([]*model.Carrier)
		allowedCarriersMap, err := GetAllowedExternalCarrierHubConfig()
		if err != nil {
			return nil, nil, fmt.Errorf("GetAllowedExternalCarrierHubConfig: %w", err)
		}
		var carrier = avlCarriers[0]
		if _, ok := allowedCarriersMap[input.To.Code]; ok && carrier.IsInternal != nil && !*carrier.IsInternal {
			if len(allowedCarriersMap[input.To.Code]) == 0 {
				return nil, nil, fmt.Errorf("không thể tìm thấy nhà vận chuyển phù hợp với kho: %s", input.To.Code)
			}
			isAllowed := false
			// kiểm tra xem liệu một carrier có được cho phép trả hàng về kho hay không
			for _, avlCarrier := range avlCarriers {
				if avlCarrier.ParentCode == nil && utils.StringSliceContain(allowedCarriersMap[input.To.Code], string(*avlCarrier.CarrierCode)) {
					carrier = avlCarrier
					isAllowed = true
					break
				}
				if utils.StringSliceContain(allowedCarriersMap[input.To.Code], string(*avlCarrier.ParentCode)) {
					carrier = avlCarrier
					isAllowed = true
					break
				}
			}
			if !isAllowed {
				carrierRaw := model.CarrierDB.Query(bson.M{
					"default_external_carrier_of": input.To.Code,
				}, 0, 1, nil)
				if carrierRaw.Status != common.APIStatus.Ok {
					return nil, nil, fmt.Errorf("không thể tìm thấy nhà vận chuyển phù hợp với kho: %s", input.To.Code)
				}
				carrier = carrierRaw.Data.([]*model.Carrier)[0]
			}
		}
		// Chỉ dùng config đặt nhà vận chuyển để book carrier 3pl vì không xác định được chính xác hub (ngoài hub nguồn) mà chỉ dựa vào carrier code
		// Do nvc mặc định của hub có thể bị trùng
		if carrier.IsInternal != nil && *carrier.IsInternal == false {
			hubRaw := model.HubDB.QueryOne(bson.M{
				"warehouse_reference_code": input.To.Code,
			})

			if hubRaw.Status != common.APIStatus.Ok {
				return nil, nil, errors.New("Không xác định được hub với mã kho là: " + input.To.Code)
			}

			hub := hubRaw.Data.([]*model.Hub)[0]
			return carrier, hub, nil
		}
	}

	// Nếu đặt vận chuyển là nhà vận chuyển nội bộ check theo những điều kiện sau:
	// + Check thử config khu vực xem có hub nào cover địa chỉ lấy hàng không?
	hub := FindNearestHubOfAddress(*input.From)
	if hub != nil {
		defaultCarrierRaw := model.CarrierDB.QueryOne(bson.M{
			"carrier_id": hub.DefaultCarrierId,
		})

		if defaultCarrierRaw.Status != common.APIStatus.Ok {
			return nil, nil, errors.New("Không tìm thấy nhà vận chuyển mặc định với id: " + strconv.FormatInt(hub.DefaultCarrierId, 10))
		}

		return defaultCarrierRaw.Data.([]*model.Carrier)[0], hub, nil
	}

	// Nếu vẫn không xác định được hub và carrier thì nvc nào đi giao sẽ book return nhà vận chuyển đó đi lấy
	deliveredByCarrierRaw := model.CarrierDB.QueryOne(bson.M{
		"carrier_id": deliveredShippingOrder.TplServiceId,
	})

	if deliveredByCarrierRaw.Status != common.APIStatus.Ok {
		return nil, nil, errors.New("Không xác định được đơn vị vận chuyển đã giao thành công")
	}

	deliveredByCarrier := deliveredByCarrierRaw.Data.([]*model.Carrier)[0]
	// Nếu đặt vận chuyển bên ngoài hub mặc định sẽ là hub ở kho
	if deliveredByCarrier.IsInternal != nil && *deliveredByCarrier.IsInternal == false {
		hubRaw := model.HubDB.QueryOne(bson.M{
			"warehouse_reference_code": input.To.Code,
		})

		if hubRaw.Status != common.APIStatus.Ok {
			return nil, nil, errors.New("Không xác định được hub với mã kho là: " + input.To.Code)
		}

		hub := hubRaw.Data.([]*model.Hub)[0]
		return deliveredByCarrier, hub, nil
	}
	// Nếu nhà vận chuyển nội bộ thì lấy nhà vận chuyển mặc định làm carrier
	usedToAtHubRaw := model.HubDB.QueryOne(bson.M{
		"code": deliveredShippingOrder.CurrentHub,
	})

	if deliveredByCarrier.IsInternal != nil && *deliveredByCarrier.IsInternal == false {
		return nil, nil, errors.New("Không xác định được hub đã giao hàng thành công")
	}

	usedToAtHub := usedToAtHubRaw.Data.([]*model.Hub)[0]

	if usedToAtHub.DefaultCarrierId == 0 {
		return nil, nil, errors.New("Hub: " + usedToAtHub.Name + " chưa có nhà vận chuyển mặc định")
	}

	defaultCarrierRaw := model.CarrierDB.QueryOne(bson.M{
		"carrier_id": usedToAtHub.DefaultCarrierId,
	})

	if defaultCarrierRaw.Status != common.APIStatus.Ok {
		return nil, nil, errors.New("Không tìm thấy nhà vận chuyển với id:" + strconv.FormatInt(usedToAtHub.DefaultCarrierId, 10))
	}

	defaultCarrier := defaultCarrierRaw.Data.([]*model.Carrier)[0]

	return defaultCarrier, usedToAtHub, nil
}

func buildHubOrderForReturn(shippingOrder *model.ShippingOrder, hub *model.Hub, carrier *model.Carrier, createdBy int64) model.HubShippingOrder {
	current := time.Now()
	hubShippingOrder := model.HubShippingOrder{
		VersionNo:           uuid.New().String(),
		ReferenceCode:       shippingOrder.ReferenceCode,
		TrackingCode:        shippingOrder.TrackingCode,
		FromCustomerName:    shippingOrder.FromCustomerName,
		FromCustomerCode:    shippingOrder.FromCustomerCode,
		FromCustomerPhone:   shippingOrder.FromCustomerPhone,
		FromCustomerEmail:   shippingOrder.FromCustomerEmail,
		FromCustomerAddress: shippingOrder.FromCustomerAddress,
		FromWardCode:        shippingOrder.FromWardCode,
		FromWardName:        shippingOrder.FromWardName,
		FromDistrictCode:    shippingOrder.FromDistrictCode,
		FromDistrictName:    shippingOrder.FromDistrictName,
		FromProvinceCode:    shippingOrder.FromProvinceCode,
		FromProvinceName:    shippingOrder.FromProvinceName,
		FromLongitude:       shippingOrder.FromLongitude,
		FromLatitude:        shippingOrder.FromLatitude,

		Type:    &enum.HubOrderType.PICKUP,
		SubType: &enum.SubType.RETURN,
		Status:  &enum.HubShippingOrderStatus.READY_TO_PICK,
		Action:  "PICKING",

		Height:      shippingOrder.Height,
		Width:       shippingOrder.Width,
		Length:      shippingOrder.Length,
		Weight:      shippingOrder.Weight,
		NumPackage:  shippingOrder.NumPackage,
		PrivateNote: shippingOrder.PrivateNote,
		Note:        shippingOrder.Note,

		CreatedTime: &current,
		ActionTime:  current.Unix(),
		CreatedBy:   strconv.FormatInt(createdBy, 10),
		Products:    shippingOrder.Products,
		TplCode:     shippingOrder.TplCode,
		TplName:     shippingOrder.TplName,

		ParentReferenceCode:        shippingOrder.ParentReferenceCode,
		FeeAmount:                  shippingOrder.FeeAmount,
		TotalCollectReceiverAmount: shippingOrder.TotalCollectReceiverAmount,
		FeeReceiverAmount:          shippingOrder.FeeReceiverAmount,
		TotalCollectSenderAmount:   shippingOrder.TotalCollectSenderAmount,
		TotalDebtAmount:            shippingOrder.TotalDebtAmount,
		FeeDebtAmount:              shippingOrder.FeeDebtAmount,
		FeeSenderAmount:            shippingOrder.FeeSenderAmount,
		References:                 shippingOrder.References,
	}

	if carrier.IsInternal != nil && *carrier.IsInternal == false {
		hubShippingOrder.HUBCode = hub.Code
		hubShippingOrder.ToCustomerPhone = hub.Address.Phone
		hubShippingOrder.ToCustomerAddress = shippingOrder.CustomerShippingAddress
		hubShippingOrder.ToCustomerCode = shippingOrder.CustomerCode
		hubShippingOrder.ToCustomerName = shippingOrder.CustomerName
		hubShippingOrder.ToWardCode = shippingOrder.CustomerWardCode
		hubShippingOrder.ToWardName = shippingOrder.CustomerWardName
		hubShippingOrder.ToDistrictCode = shippingOrder.CustomerDistrictCode
		hubShippingOrder.ToDistrictName = shippingOrder.CustomerDistrictName
		hubShippingOrder.ToProvinceCode = shippingOrder.CustomerProvinceCode
		hubShippingOrder.ToProvinceName = shippingOrder.CustomerProvinceName
		// Initial hub order status when bool 3pl is WAIT_TO_STORING
		hubShippingOrder.Status = &enum.HubShippingOrderStatus.WAIT_TO_STORING
	} else {
		hubShippingOrder.HUBCode = hub.Code
		hubShippingOrder.ToCustomerAddress = hub.Address.Address
		hubShippingOrder.ToCustomerCode = hub.Code
		hubShippingOrder.ToCustomerName = hub.Name
		hubShippingOrder.ToWardCode = hub.Address.WardCode
		hubShippingOrder.ToWardName = hub.Address.WardName
		hubShippingOrder.ToDistrictCode = hub.Address.DistrictCode
		hubShippingOrder.ToDistrictName = hub.Address.DistrictName
		hubShippingOrder.ToProvinceCode = hub.Address.ProvinceCode
		hubShippingOrder.ToProvinceName = hub.Address.ProvinceName
		hubShippingOrder.ToCustomerPhone = hub.Address.Phone
	}

	return hubShippingOrder
}

func BookExternalCarrier(
	input *request.BookShippingOrder,
	carrier *model.Carrier,
) (*model.ShippingInfo, error) {

	if len(carrier.Service) == 0 {
		err := errors.New("not found valid carrier")
		return nil, err
	}

	var trackingInfo *model.ShippingInfo
	var book3plErr error
	switch *carrier.ParentCode {
	case enum.Partner.GHTK:
		trackingInfo, book3plErr = GHTK.BookShippingOrder(input, carrier)
	case enum.Partner.VIETTEL_POST:
		trackingInfo, book3plErr = VTP.BookShippingOrder(input, carrier)
	default:
		return nil, errors.New("this external carrier is not supported")
	}

	if book3plErr != nil {
		return nil, book3plErr
	}

	trackingInfo.TplCode = carrier.ParentCode
	return trackingInfo, book3plErr
}

func ChangeCarrier(req *request.ChangeCarrier, updateBy int64) *common.APIResponse {
	if req.ReferenceCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã liên kết không được để trống",
		}
	}

	if req.CarrierId == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "ID nhà vận chuyển mới không được để trống",
		}
	}

	if req.HubCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã hub không được để trống",
		}
	}

	shippingOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
		"reference_code": req.ReferenceCode,
	})

	if shippingOrderRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không tìm thấy mã liên kết " + req.ReferenceCode,
		}
	}

	shippingOrder := shippingOrderRaw.Data.([]*model.ShippingOrder)[0]

	if shippingOrder.ShippingType == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Loại đơn không xác định",
		}
	}

	if shippingOrder.Status == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Trạng thái đơn không xác định",
		}
	}

	carrierRaw := model.CarrierDB.QueryOne(bson.M{
		"carrier_id": req.CarrierId,
	})

	if carrierRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không tìm thấy nhà vận chuyển với ID: " + strconv.FormatInt(req.CarrierId, 10),
		}
	}
	carrier := carrierRaw.Data.([]*model.Carrier)[0]

	if *shippingOrder.ShippingType == enum.ShippingOrderType.CS {
		if *shippingOrder.Status != enum.TPLCallbackStatus.STORING &&
			*shippingOrder.Status != enum.TPLCallbackStatus.DELIVERY_FAIL {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Chỉ có thể đổi nhà vận chuyển khi đơn ở trạng thái lưu kho hoặc giao hàng thất bại",
			}
		}

		cancelResult := CancelCustomerSupportOrder(shippingOrder, updateBy)
		if cancelResult.Status != common.APIStatus.Ok {
			return cancelResult
		}

		rebookCsResult := BookCS(&request.BookShippingOrder{
			From: &model.Address{
				Address:      shippingOrder.FromCustomerAddress,
				WardCode:     shippingOrder.FromWardCode,
				WardName:     shippingOrder.FromWardName,
				DistrictCode: shippingOrder.FromDistrictCode,
				DistrictName: shippingOrder.FromDistrictName,
				ProvinceCode: shippingOrder.FromProvinceCode,
				ProvinceName: shippingOrder.FromProvinceName,
				Phone:        shippingOrder.FromCustomerPhone,
				Name:         shippingOrder.FromCustomerName,
				Code:         shippingOrder.FromCustomerCode,
			},
			To: &model.Address{
				Address:      shippingOrder.CustomerShippingAddress,
				WardCode:     shippingOrder.CustomerWardCode,
				WardName:     shippingOrder.CustomerWardName,
				DistrictCode: shippingOrder.CustomerDistrictCode,
				DistrictName: shippingOrder.CustomerDistrictName,
				ProvinceCode: shippingOrder.CustomerProvinceCode,
				ProvinceName: shippingOrder.CustomerProvinceName,
				Phone:        shippingOrder.CustomerPhone,
				Name:         shippingOrder.CustomerName,
				Code:         shippingOrder.CustomerCode,
			},
			CarrierId:     carrier.CarrierId,
			CarrierName:   carrier.CarrierName,
			CarrierCode:   carrier.CarrierCode,
			Note:          shippingOrder.Note,
			HubCode:       req.HubCode,
			ReBook:        true,
			ReferenceCode: shippingOrder.ReferenceCode,
			TrackingCode:  shippingOrder.TrackingCode,
			ShippingType:  &enum.ShippingOrderType.CS,
			CustomerInfo: &model.Customer{
				Name:  shippingOrder.CustomerName,
				Phone: shippingOrder.CustomerPhone,
				Code:  shippingOrder.CustomerCode,
			},
		}, updateBy)

		return rebookCsResult
	}

	if *shippingOrder.ShippingType == enum.ShippingOrderType.EO &&
		shippingOrder.IsBookDropOff != nil && *shippingOrder.IsBookDropOff {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không thể đổi nhà vận chuyển cho đơn dropoff EO",
		}
	}

	if shippingOrder.Status != nil &&
		*shippingOrder.Status != enum.TPLCallbackStatus.READY_TO_PICK &&
		*shippingOrder.Status != enum.TPLCallbackStatus.CREATE_FAIL &&
		*shippingOrder.Status != enum.TPLCallbackStatus.CANCEL &&
		*shippingOrder.Status != enum.TPLCallbackStatus.PICK_FAIL {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Chỉ có thể đổi nhà vận chuyển khi đơn ở trạng thái chờ lấy hàng, tạo thất bại hoặc đã hủy",
		}
	}

	// Chỉ cần hủy khi đã book thành công
	if shippingOrder.Status != nil &&
		(*shippingOrder.Status == enum.TPLCallbackStatus.READY_TO_PICK ||
			*shippingOrder.Status == enum.TPLCallbackStatus.PICK_FAIL) {
		cancelErr := CancelShippingOrder(&request.CancelShippingService{
			ReferenceCode: req.ReferenceCode,
		}, updateBy)

		if cancelErr.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: cancelErr.Message,
			}
		}
	}

	if *shippingOrder.ShippingType == enum.ShippingOrderType.RETURN {
		bookReturnReq := &request.BookShippingOrder{
			From: &model.Address{
				Name:         shippingOrder.FromCustomerName,
				Code:         shippingOrder.FromCustomerCode,
				Phone:        shippingOrder.FromCustomerPhone,
				Address:      shippingOrder.FromCustomerAddress,
				WardName:     shippingOrder.FromWardName,
				WardCode:     shippingOrder.FromWardCode,
				DistrictName: shippingOrder.FromDistrictName,
				DistrictCode: shippingOrder.FromDistrictCode,
				ProvinceName: shippingOrder.FromProvinceName,
				ProvinceCode: shippingOrder.FromProvinceCode,
			},
			To: &model.Address{
				Name:         shippingOrder.CustomerName,
				Code:         shippingOrder.CustomerCode,
				Phone:        shippingOrder.CustomerPhone,
				Address:      shippingOrder.CustomerShippingAddress,
				WardName:     shippingOrder.CustomerWardName,
				WardCode:     shippingOrder.CustomerWardCode,
				DistrictName: shippingOrder.CustomerDistrictName,
				DistrictCode: shippingOrder.CustomerDistrictCode,
				ProvinceName: shippingOrder.CustomerProvinceName,
				ProvinceCode: shippingOrder.CustomerProvinceCode,
			},
			CarrierId:         carrier.CarrierId,
			ReferenceCode:     shippingOrder.ParentReferenceCode,
			ShippingType:      &enum.ShippingOrderType.RETURN,
			Weight:            req.Weight,
			NumPackage:        req.NumPackage,
			HubCode:           req.HubCode,
			Products:          shippingOrder.Products,
			ReBook:            true,
			OldReferenceCode:  req.ReferenceCode,
			DeliveryOrderCode: shippingOrder.DeliveryOrderCode,
			FeeCollectedOn:    shippingOrder.FeeCollectMethod,
			References:        shippingOrder.References,
		}

		if shippingOrder.DeliveryOrderCode != "" {
			bookReturnReq.ReferenceCode = shippingOrder.DeliveryOrderCode
		}

		bookShipping := BookReturn(bookReturnReq, updateBy)

		// Nếu book không thành công chuyển status sang CREATE_FAIL để có thể book lại
		if bookShipping.Status != common.APIStatus.Ok {
			res := model.ShippingOrderDB.UpdateOne(bson.M{
				"reference_code": req.ReferenceCode,
			}, bson.M{
				"status": &enum.TPLCallbackStatus.CREATE_FAIL,
			})

			if res.Status != common.APIStatus.Ok {
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "UPDATE_FAIL_SHIPPING_ORDER",
					Title:   "Không thể cập nhật đơn book thất bại, mã liên kết: " + req.ReferenceCode,
					Message: "Không thể cập nhật đơn book thất bại",
				})
			}

			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: bookShipping.Message,
			}
		}
	}

	if *shippingOrder.ShippingType == enum.ShippingOrderType.FMPO {
		bookReq := &request.BookShippingOrder{
			From: &model.Address{
				Name:         shippingOrder.FromCustomerName,
				Code:         shippingOrder.FromCustomerCode,
				Phone:        shippingOrder.FromCustomerPhone,
				Address:      shippingOrder.FromCustomerAddress,
				WardName:     shippingOrder.FromWardName,
				WardCode:     shippingOrder.FromWardCode,
				DistrictName: shippingOrder.FromDistrictName,
				DistrictCode: shippingOrder.FromDistrictCode,
				ProvinceName: shippingOrder.FromProvinceName,
				ProvinceCode: shippingOrder.FromProvinceCode,
			},
			To: &model.Address{
				Name:         shippingOrder.CustomerName,
				Code:         shippingOrder.CustomerCode,
				Phone:        shippingOrder.CustomerPhone,
				Address:      shippingOrder.CustomerShippingAddress,
				WardName:     shippingOrder.CustomerWardName,
				WardCode:     shippingOrder.CustomerWardCode,
				DistrictName: shippingOrder.CustomerDistrictName,
				DistrictCode: shippingOrder.CustomerDistrictCode,
				ProvinceName: shippingOrder.CustomerProvinceName,
				ProvinceCode: shippingOrder.CustomerProvinceCode,
			},
			ReferenceCode:            shippingOrder.ReferenceCode,
			ExpectedPickupTime:       shippingOrder.ExpectedPickupTime,
			ParentReferenceCode:      shippingOrder.ParentReferenceCode,
			ReceiveSessionCode:       shippingOrder.ReceiveSessionCode,
			ShippingType:             &enum.ShippingOrderType.FMPO,
			Weight:                   shippingOrder.Weight,
			NumPackage:               shippingOrder.NumPackage,
			HubCode:                  req.HubCode,
			Products:                 shippingOrder.Products,
			ReBook:                   true,
			OldReferenceCode:         req.ReferenceCode,
			FeeCollectedOn:           shippingOrder.FeeCollectMethod,
			VoucherCode:              shippingOrder.VoucherCode,
			ParentReceiveSessionCode: shippingOrder.ParentReceiveSessionCode,
			References:               shippingOrder.References,
		}

		if shippingOrder.OrderValue != nil {
			bookReq.OrderValue = *shippingOrder.OrderValue
		}
		bookShipping := BookFMPO(bookReq, updateBy)

		// Nếu book không thành công chuyển status sang CREATE_FAIL để có thể book lại
		if bookShipping.Status != common.APIStatus.Ok {
			res := model.ShippingOrderDB.UpdateOne(bson.M{
				"reference_code": req.ReferenceCode,
			}, bson.M{
				"status": &enum.TPLCallbackStatus.CREATE_FAIL,
			})

			if res.Status != common.APIStatus.Ok {
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "UPDATE_FAIL_SHIPPING_ORDER",
					Title:   "Không thể cập nhật đơn book thất bại, mã liên kết: " + req.ReferenceCode,
					Message: "Không thể cập nhật đơn book thất bại",
				})
			}

			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: bookShipping.Message,
			}
		}
	}

	if *shippingOrder.ShippingType == enum.ShippingOrderType.EO {
		bookReq := &request.BookShippingOrder{
			From: &model.Address{
				Name:         shippingOrder.FromCustomerName,
				Code:         shippingOrder.FromCustomerCode,
				Phone:        shippingOrder.FromCustomerPhone,
				Address:      shippingOrder.FromCustomerAddress,
				WardName:     shippingOrder.FromWardName,
				WardCode:     shippingOrder.FromWardCode,
				DistrictName: shippingOrder.FromDistrictName,
				DistrictCode: shippingOrder.FromDistrictCode,
				ProvinceName: shippingOrder.FromProvinceName,
				ProvinceCode: shippingOrder.FromProvinceCode,
			},
			To: &model.Address{
				Name:         shippingOrder.CustomerName,
				Code:         shippingOrder.CustomerCode,
				Phone:        shippingOrder.CustomerPhone,
				Address:      shippingOrder.CustomerShippingAddress,
				WardName:     shippingOrder.CustomerWardName,
				WardCode:     shippingOrder.CustomerWardCode,
				DistrictName: shippingOrder.CustomerDistrictName,
				DistrictCode: shippingOrder.CustomerDistrictCode,
				ProvinceName: shippingOrder.CustomerProvinceName,
				ProvinceCode: shippingOrder.CustomerProvinceCode,
			},
			ReferenceCode:      shippingOrder.ReferenceCode,
			TrackingCode:       shippingOrder.TrackingCode,
			ExpectedPickupTime: shippingOrder.ExpectedPickupTime,
			ShippingType:       &enum.ShippingOrderType.EO,
			Weight:             shippingOrder.Weight,
			NumPackage:         shippingOrder.NumPackage,
			HubCode:            req.HubCode,
			Products:           shippingOrder.Products,
			ReBook:             true,
			OldReferenceCode:   req.ReferenceCode,
			FeeCollectedOn:     shippingOrder.FeeCollectMethod,
			VoucherCode:        shippingOrder.VoucherCode,
			CustomerInfo:       shippingOrder.CustomerInfo,
			CODAmount:          shippingOrder.CODAmount,
			Note:               shippingOrder.Note,
			IsBookDropOff:      false,
			ReceiveAtHubCode:   shippingOrder.LastMileHubCode,
			References:         shippingOrder.References,
		}
		if shippingOrder.IsBookDropOff != nil {
			bookReq.IsBookDropOff = *shippingOrder.IsBookDropOff
		}

		if shippingOrder.OrderValue != nil {
			bookReq.OrderValue = *shippingOrder.OrderValue
		}

		bookShipping := BookEO(bookReq, updateBy)
		if bookShipping.Status != common.APIStatus.Ok {
			res := model.ShippingOrderDB.UpdateOne(bson.M{
				"reference_code": req.ReferenceCode,
			}, bson.M{
				"status": &enum.TPLCallbackStatus.CREATE_FAIL,
			})

			if res.Status != common.APIStatus.Ok {
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "UPDATE_FAIL_SHIPPING_ORDER",
					Title:   "Không thể cập nhật đơn book thất bại, mã liên kết: " + req.ReferenceCode,
					Message: "Không thể cập nhật đơn book thất bại",
				})
			}

			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: bookShipping.Message,
			}
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Đổi nhà vận chuyển thành công",
	}
}

func MigrateCategory(testOrders []string, massUpdate bool) *common.APIResponse {
	if len(testOrders) > 0 {
		for _, to := range testOrders {
			orderRaw := model.ShippingOrderDB.QueryOne(bson.M{
				"reference_code": to,
			})
			if orderRaw.Status != common.APIStatus.Ok {
				continue
			}
			o := orderRaw.Data.([]*model.ShippingOrder)[0]
			if len(o.Products) == 0 {
				continue
			}
			var newProducts []*model.Product
			for _, p := range o.Products {
				if p.Quantity != 0 {
					p.Category = "SKU"
					newProducts = append(newProducts, p)
				}
			}

			soErr := model.ShippingOrderDB.UpdateOne(bson.M{
				"reference_code": o.ReferenceCode,
			}, bson.M{
				"products": newProducts,
			})

			hsoErr := model.HUBShippingOrderDB.UpdateMany(bson.M{
				"reference_code": o.ReferenceCode,
			}, bson.M{
				"products": newProducts,
			})

			if soErr.Status != common.APIStatus.Ok || hsoErr.Status != common.APIStatus.Ok {
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "FAIL_UPDATE_PRODUCT_CATEGORY",
					Title:   "FAIL_UPDATE_PRODUCT_CATEGORY",
					Message: "Đã có lỗi xảy ra khi cập nhật category của sản phẩm: " + o.ReferenceCode,
				})
			}

		}
	}

	if massUpdate {
		ordersRaw := model.ShippingOrderDB.Query(bson.M{
			"type": "RETURN",
		}, 0, 1000, nil)

		orders := ordersRaw.Data.([]*model.ShippingOrder)
		for _, o := range orders {
			if len(o.Products) == 0 {
				continue
			}

			var newProducts []*model.Product
			for _, p := range o.Products {
				if p.Quantity != 0 {
					p.Category = "SKU"
					newProducts = append(newProducts, p)
				}
			}

			soErr := model.ShippingOrderDB.UpdateOne(bson.M{
				"reference_code": o.ReferenceCode,
			}, bson.M{
				"products": newProducts,
			})

			hsoErr := model.HUBShippingOrderDB.UpdateMany(bson.M{
				"reference_code": o.ReferenceCode,
			}, bson.M{
				"products": newProducts,
			})

			if soErr.Status != common.APIStatus.Ok || hsoErr.Status != common.APIStatus.Ok {
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "FAIL_UPDATE_PRODUCT_CATEGORY",
					Title:   "FAIL_UPDATE_PRODUCT_CATEGORY",
					Message: "Đã có lỗi xảy ra khi cập nhật category của sản phẩm: " + o.ReferenceCode,
				})
			}
		}

	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Ok",
	}
}

func MigrateSubType(testOrders []string, massUpdate bool) *common.APIResponse {
	if len(testOrders) > 0 {
		shippingOrderRaw := model.ShippingOrderDB.Query(bson.M{
			"reference_code": bson.M{
				"$in": testOrders,
			},
		}, 0, int64(len(testOrders)), nil)

		if shippingOrderRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.NotFound,
				Message: "Not found",
			}
		}

		shippingOrders := shippingOrderRaw.Data.([]*model.ShippingOrder)
		for _, shippingOrder := range shippingOrders {
			if shippingOrder.ShippingType == nil || *shippingOrder.ShippingType == enum.ShippingOrderType.DELIVERY {
				continue
			}

			var subType string
			if *shippingOrder.ShippingType == enum.ShippingOrderType.PICKUP {
				subType = "PICKUP"
			} else {
				subType = "RETURN"
			}

			_ = model.HUBShippingOrderDB.UpdateMany(bson.M{
				"reference_code": shippingOrder.ReferenceCode,
			}, bson.M{
				"sub_type": subType,
			})
		}
	}

	if massUpdate {
		var limit int64 = 1000
		filter := bson.M{}
		for {
			shippingOrderRaw := model.ShippingOrderDB.Query(filter, 0, limit, &bson.M{"_id": -1})
			if shippingOrderRaw.Status != common.APIStatus.Ok {
				break
			}
			shippingOrders := shippingOrderRaw.Data.([]*model.ShippingOrder)
			smallestId := shippingOrders[len(shippingOrders)-1].ID
			filter["_id"] = bson.M{
				"$lt": smallestId,
			}

			for _, shippingOrder := range shippingOrders {
				if shippingOrder.ShippingType == nil || *shippingOrder.ShippingType == enum.ShippingOrderType.DELIVERY {
					continue
				}

				var subType string
				if *shippingOrder.ShippingType == enum.ShippingOrderType.PICKUP {
					subType = "PICKUP"
				} else {
					subType = "RETURN"
				}

				_ = model.HUBShippingOrderDB.UpdateMany(bson.M{
					"reference_code": shippingOrder.ReferenceCode,
				}, bson.M{
					"sub_type": subType,
				})
			}
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Ok",
	}
}

func buildHubOrderForFM(shippingOrder *model.ShippingOrder, hub *model.Hub, input *request.BookShippingOrder, createdBy int64) model.HubShippingOrder {
	current := time.Now()
	hubShippingOrder := model.HubShippingOrder{
		VersionNo:     uuid.New().String(),
		ReferenceCode: shippingOrder.ReferenceCode,
		TrackingCode:  shippingOrder.TrackingCode,
		HUBCode:       hub.Code,

		ToCustomerAddress: shippingOrder.CustomerShippingAddress,
		ToCustomerPhone:   shippingOrder.CustomerPhone,
		ToCustomerCode:    shippingOrder.CustomerCode,
		ToCustomerEmail:   shippingOrder.CustomerEmail,
		ToCustomerName:    shippingOrder.CustomerName,
		ToProvinceCode:    shippingOrder.CustomerProvinceCode,
		ToProvinceName:    shippingOrder.CustomerProvinceName,
		ToDistrictCode:    shippingOrder.CustomerDistrictCode,
		ToDistrictName:    shippingOrder.CustomerDistrictName,
		ToWardCode:        shippingOrder.CustomerWardCode,
		ToWardName:        shippingOrder.CustomerWardName,

		Type:    &enum.HubOrderType.PICKUP,
		SubType: &enum.SubType.FMPO,
		Action:  "PICKING",

		Height:      shippingOrder.Height,
		Width:       shippingOrder.Width,
		Length:      shippingOrder.Length,
		Weight:      shippingOrder.Weight,
		NumPackage:  shippingOrder.NumPackage,
		PrivateNote: shippingOrder.PrivateNote,
		Note:        shippingOrder.Note,

		CreatedTime: &current,
		ActionTime:  current.Unix(),
		CreatedBy:   strconv.FormatInt(createdBy, 10),
		Products:    shippingOrder.Products,
		TplCode:     shippingOrder.TplCode,
		TplName:     shippingOrder.TplName,

		ParentReferenceCode:      shippingOrder.ParentReferenceCode,
		ReceiveSessionCode:       shippingOrder.ReceiveSessionCode,
		ParentReceiveSessionCode: shippingOrder.ParentReceiveSessionCode,

		FeeAmount:                  shippingOrder.FeeAmount,
		TotalCollectReceiverAmount: shippingOrder.TotalCollectReceiverAmount,
		FeeReceiverAmount:          shippingOrder.FeeReceiverAmount,
		TotalCollectSenderAmount:   shippingOrder.TotalCollectSenderAmount,
		FeeSenderAmount:            shippingOrder.FeeSenderAmount,
		TotalDebtAmount:            shippingOrder.TotalDebtAmount,
		FeeDebtAmount:              shippingOrder.FeeDebtAmount,
		ExpectedPickupTime:         shippingOrder.ExpectedPickupTime,
		ExpectedDeliveryTime:       shippingOrder.ExpectedDeliveryTime,
		CODAmount:                  shippingOrder.CODAmount,
		OrderValue:                 shippingOrder.OrderValue,
		References:                 shippingOrder.References,
	}

	// Nếu là drop off thì from hub -> kho
	// Nếu là pickup thì from customer -> kho
	if input.DropOffAtHubCode != "" {
		hubShippingOrder.FromCustomerAddress = hub.Address.Address
		hubShippingOrder.FromCustomerEmail = hub.Address.Email
		hubShippingOrder.FromCustomerPhone = hub.Address.Phone
		hubShippingOrder.FromCustomerName = hub.Name
		hubShippingOrder.FromWardCode = hub.Address.WardCode
		hubShippingOrder.FromWardName = hub.Address.WardName
		hubShippingOrder.FromDistrictCode = hub.Address.DistrictCode
		hubShippingOrder.FromDistrictName = hub.Address.DistrictName
		hubShippingOrder.FromProvinceCode = hub.Address.ProvinceCode
		hubShippingOrder.FromProvinceName = hub.Address.ProvinceName
		hubShippingOrder.Status = &enum.HubShippingOrderStatus.STORING
		hubShippingOrder.Type = &enum.HubOrderType.TRANSPORTING
	} else {
		hubShippingOrder.FromCustomerAddress = shippingOrder.FromCustomerAddress
		hubShippingOrder.FromCustomerEmail = shippingOrder.FromCustomerEmail
		hubShippingOrder.FromCustomerPhone = shippingOrder.FromCustomerPhone
		hubShippingOrder.FromCustomerName = shippingOrder.FromCustomerName
		hubShippingOrder.FromWardCode = shippingOrder.FromWardCode
		hubShippingOrder.FromWardName = shippingOrder.FromWardName
		hubShippingOrder.FromDistrictCode = shippingOrder.FromDistrictCode
		hubShippingOrder.FromDistrictName = shippingOrder.FromDistrictName
		hubShippingOrder.FromProvinceCode = shippingOrder.FromProvinceCode
		hubShippingOrder.FromProvinceName = shippingOrder.FromProvinceName
		hubShippingOrder.Status = &enum.HubShippingOrderStatus.READY_TO_PICK
	}

	if input.IsBookDropOff {
		hubShippingOrder.Status = &enum.HubShippingOrderStatus.WAIT_TO_STORING
	}

	if input.IsSplitOrder {
		hubShippingOrder.Type = &enum.HubOrderType.TRANSPORTING
	}

	return hubShippingOrder
}

func BookFMPO(input *request.BookShippingOrder, createdBy int64) *common.APIResponse {
	if input.ParentReferenceCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã liên kết không hợp lệ",
			ErrorCode: "EMPTY_REFERENCE_CODE",
		}
	}

	if input.From == nil ||
		input.From.Address == "" ||
		input.From.Name == "" ||
		input.From.Phone == "" ||
		input.From.Code == "" ||
		input.From.ProvinceCode == "" ||
		input.From.DistrictCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Thiếu thông tin địa chỉ gửi hàng",
			ErrorCode: "EMPTY_FROM_ADDRESS",
		}
	}

	if input.To == nil ||
		input.To.Code == "" ||
		input.To.Name == "" ||
		input.To.Phone == "" ||
		input.To.Address == "" ||
		input.To.DistrictCode == "" ||
		input.To.ProvinceCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Thiếu thông tin địa chỉ nhận hàng",
			ErrorCode: "EMPTY_TO_ADDRESS",
		}
	}

	fromAddressErr := BuildAddress(input.From)
	if fromAddressErr != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Lỗi địa chỉ gửi hàng: " + fromAddressErr.Message,
			ErrorCode: "INVALID_FROM_ADDRESS",
		}
	}

	toAddressErr := BuildAddress(input.To)
	if toAddressErr != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Lỗi địa chỉ nhận hàng: " + toAddressErr.Message,
			ErrorCode: "INVALID_TO_ADDRESS",
		}
	}

	isValidVoucher := false
	needCreateCustomerVoucher := false
	needReduceUsedVoucher := false
	var voucher *model.Voucher
	var customerVoucher *model.CustomerVoucher
	if input.VoucherCode != "" && !input.ReBook && !input.IsSplitOrder {
		checkVoucher := model.VoucherDB.QueryOne(bson.M{"code": input.VoucherCode})
		if checkVoucher.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Mã giảm giá không hợp lệ",
				ErrorCode: "INVALID_VOUCHER_CODE",
			}
		}
		voucher = checkVoucher.Data.([]*model.Voucher)[0]
		if voucher.IsActive == nil || !*voucher.IsActive {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Mã giảm giá không còn hoat động",
				ErrorCode: "VOUCHER_NOT_ACTIVE",
			}
		}

		now := time.Now()
		if voucher.ValidFromDate != nil && now.Before(*voucher.ValidFromDate) {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Mã giảm giá không hợp lệ",
				ErrorCode: "VOUCHER_NOT_ACTIVE",
			}
		}

		if voucher.ValidToDate != nil && now.After(*voucher.ValidToDate) {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Mã giảm giá không hợp lệ",
				ErrorCode: "VOUCHER_EXPIRED",
			}
		}

		customerVoucherRaw := model.CustomerVoucherDB.QueryOne(bson.M{
			"customer_code": input.From.Code,
			"voucher_code":  input.VoucherCode,
		})

		if customerVoucherRaw.Status == common.APIStatus.Ok {
			customerVoucher = customerVoucherRaw.Data.([]*model.CustomerVoucher)[0]
			if customerVoucher.RemainAmount <= 0 {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   "Đã hết sử dụng mã giảm giá tối đa",
					ErrorCode: "EXCEED_MAX_VOUCHER_USAGE",
				}
			}
		}

		isValidVoucher = true

		// Chưa có thì insert mới
		if customerVoucherRaw.Status != common.APIStatus.Ok {
			needCreateCustomerVoucher = true
		} else {
			needReduceUsedVoucher = true
		}
	}

	if input.ReBook && input.VoucherCode != "" {
		isValidVoucher = true
	}

	var fromHUB *model.Hub
	var toHUB *model.Hub
	toHubRaw := model.HubDB.QueryOne(bson.M{
		"warehouse_reference_code": input.To.Code,
	})
	if toHubRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã kho nhận hàng không hợp lệ",
			ErrorCode: "INVALID_TO_HUB_CODE",
		}
	}
	toHUB = toHubRaw.Data.([]*model.Hub)[0]

	var carrier *model.Carrier
	// Nếu drop off thì dùng hub và nvc mặc định của hub để đặt đơn
	if input.DropOffAtHubCode != "" {
		hubRaw := model.HubDB.QueryOne(bson.M{
			"code": input.DropOffAtHubCode,
		})
		if hubRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Mã hub không hợp lệ",
				ErrorCode: "INVALID_HUB_CODE",
			}
		}
		fromHUB = hubRaw.Data.([]*model.Hub)[0]
	} else if input.HubCode != "" && input.ReBook {
		hubRaw := model.HubDB.QueryOne(bson.M{
			"code": input.HubCode,
		})
		if hubRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Mã hub không hợp lệ",
				ErrorCode: "INVALID_HUB_CODE",
			}
		}
		fromHUB = hubRaw.Data.([]*model.Hub)[0]
	} else {
		if input.NumPackage == 0 {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Số lượng kiện phải lớn hơn 0",
				ErrorCode: "INVALID_NUM_PACK",
			}
		}

		fromHUB = FindNearestHubOfAddress(*input.From)
		if fromHUB == nil {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Không tìm thấy khu vực hỗ trợ lấy hàng",
				ErrorCode: "NOT_FOUND_VALID_PICKUP_CARRIER",
			}
		}
	}

	// TODO: Hiện tại chưa hỗ trợ đặt đơn cho 3pl
	carrierRaw := model.CarrierDB.QueryOne(bson.M{
		"carrier_id": fromHUB.DefaultCarrierId,
	})
	if carrierRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Nhà vận chuyển mặc định không hợp lệ",
			ErrorCode: "INVALID_DEFAULT_CARRIER",
		}
	}
	carrier = carrierRaw.Data.([]*model.Carrier)[0]
	if carrier.IsInternal != nil && !*carrier.IsInternal ||
		carrier.ParentCode == nil || *carrier.ParentCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Nhà vận chuyển mặc định không hợp lệ",
			ErrorCode: "INVALID_DEFAULT_CARRIER",
		}
	}

	// Đếm số lượng đơn PO cùng mã đã được đặt
	// Reference code: PO123FM1-1
	// Receive session code: PO123-1 mã đơn này sinh ra để liên kết với luồng cũ của warehouse
	// Parent reference code: PO123
	// trong đó: PO123 sẽ là mã đơn seller đặt
	// FM1 là số lần đặt lại của PO123
	// -1 là số lần tách đơn từ vế trước, đặt đơn luôn bắt đầu bằng -1
	var numberOfOrder int64
	if input.IsSplitOrder {
		splitRefCode := strings.Split(input.OldReferenceCode, "-")
		numberOfOrder = model.ShippingOrderDB.Count(bson.M{
			"receive_session_code": input.ReceiveSessionCode,
		}).Total
		input.ReferenceCode = splitRefCode[0] + "-" + strconv.Itoa(int(numberOfOrder+1))
		if len(splitRefCode) == 3 {
			input.ReferenceCode = splitRefCode[0] + "-" + splitRefCode[1] + "-" + strconv.Itoa(int(numberOfOrder+1))
		}
	} else {
		input.ReferenceCode = input.ParentReferenceCode + "-1"
	}

	input.CarrierCode = carrier.ParentCode
	input.CarrierName = carrier.CarrierName
	input.CarrierId = carrier.CarrierId
	input.HubCode = fromHUB.Code
	calculateFeeShippingType := enum.ShippingOrderType.FMPO
	if strings.HasPrefix(input.ParentReferenceCode, "PO") {
		calculateFeeShippingType = enum.ShippingOrderType.PO
	}
	if strings.HasPrefix(input.ParentReferenceCode, "PGH") {
		calculateFeeShippingType = enum.ShippingOrderType.PGH
	}

	// Tính phí vận chuyển
	// Chỉ tính phí cho đơn book đầu tiên còn đơn split thì không cần tính
	if input.FeeCollectedOn != nil &&
		*input.FeeCollectedOn == enum.FeeCollectMethod.SENDER_PAY &&
		!isValidVoucher &&
		!input.IsSplitOrder {
		if input.NumPackage <= 0 {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Số lượng kiện phải lớn hơn 0",
				ErrorCode: "INVALID_NUM_PACK",
			}
		}

		query := bson.M{
			"code": input.From.Code,
		}
		if input.IsBookDropOff {
			query["applied_fees.config_fee_type"] = enum.ConfigFeeType.TRANSPORT
		} else {
			query["applied_fees.config_fee_type"] = enum.ConfigFeeType.PICK_N_TRANSPORT
		}

		useDefaultFee := true
		// Check customer có công thức tính chưa
		customerRaw := model.CustomerDB.QueryOne(query)
		if customerRaw.Status == common.APIStatus.Ok {
			customer := customerRaw.Data.([]*model.Customer)[0]
			var configFeeId int64
			for _, fee := range *customer.AppliedFees {
				if input.IsBookDropOff {
					if fee.ConfigFeeType == enum.ConfigFeeType.TRANSPORT {
						configFeeId = fee.ConfigFeeId
					}
				} else {
					if fee.ConfigFeeType == enum.ConfigFeeType.PICK_N_TRANSPORT {
						configFeeId = fee.ConfigFeeId
					}
				}
			}
			configFeeRaw := model.ConfigFeeDB.QueryOne(bson.M{
				"config_id": configFeeId,
			})
			if configFeeRaw.Status != common.APIStatus.Ok {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Không tìm thấy cấu hình phí vận chuyển",
				}
			}
			configFee := configFeeRaw.Data.([]*model.ConfigFee)[0]

			feeAmount, err := CalculateOrderFee(request.BookShippingOrder{
				NumPackage:   input.NumPackage,
				Weight:       input.Weight,
				OrderValue:   input.OrderValue,
				ShippingType: &calculateFeeShippingType,
				From: &model.Address{
					Code:         fromHUB.Code,
					ProvinceCode: fromHUB.Address.ProvinceCode,
					DistrictCode: fromHUB.Address.DistrictCode,
					WardCode:     fromHUB.Address.WardCode,
				},
				To: &model.Address{
					Code:         toHUB.Code,
					ProvinceCode: toHUB.Address.ProvinceCode,
					DistrictCode: toHUB.Address.DistrictCode,
					WardCode:     toHUB.Address.WardCode,
				},
			}, configFee)
			if err != nil {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Biểu phí sai, liên hệ admin để được để được hỗ trợ",
				}
			}
			input.CODAmount = feeAmount
			useDefaultFee = false
		}

		if useDefaultFee {
			configCode := ""
			if input.IsBookDropOff {
				configCode = conf.Config.DefaultFMDropOffFeeCode
			} else {
				configCode = conf.Config.DefaultFMPickUpFeeCode
			}

			configFeeRaw := model.ConfigFeeDB.QueryOne(bson.M{
				"code": configCode,
			})

			if configFeeRaw.Status != common.APIStatus.Ok {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Không tìm thấy cấu hình phí vận chuyển",
				}
			}
			configFee := configFeeRaw.Data.([]*model.ConfigFee)[0]
			feeAmount, err := CalculateOrderFee(request.BookShippingOrder{
				NumPackage:   input.NumPackage,
				Weight:       input.Weight,
				OrderValue:   input.OrderValue,
				ShippingType: &calculateFeeShippingType,
				From: &model.Address{
					Code:         fromHUB.Code,
					ProvinceCode: fromHUB.Address.ProvinceCode,
					DistrictCode: fromHUB.Address.DistrictCode,
					WardCode:     fromHUB.Address.WardCode,
				},
				To: &model.Address{
					Code:         toHUB.Code,
					ProvinceCode: toHUB.Address.ProvinceCode,
					DistrictCode: toHUB.Address.DistrictCode,
					WardCode:     toHUB.Address.WardCode,
				},
			}, configFee)
			if err != nil {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Không tìm thấy cấu hình phí vận chuyển hợp lệ cho đơn",
				}
			}
			input.CODAmount = feeAmount
		}
	}

	if !input.ReBook && !input.IsSplitOrder {
		input.ReceiveSessionCode = input.ReferenceCode
	}

	shippingOrder := buildShippingOrder(input, int(createdBy))
	createdShippingOrder := model.ShippingOrderDB.Upsert(bson.M{
		"reference_code": shippingOrder.ReferenceCode,
	}, shippingOrder)
	if createdShippingOrder.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Đã có lỗi xảy ra khi tạo đơn hàng, thử lại sau",
		}
	}

	// Đơn FM ban đầu sẽ không biết phí vận chuyển
	hubOrder := buildHubOrderForFM(&shippingOrder, fromHUB, input, createdBy)
	createdHubOrder := model.HUBShippingOrderDB.Upsert(bson.M{
		"reference_code": hubOrder.ReferenceCode,
		"hub_code":       hubOrder.HUBCode,
	}, hubOrder)

	if createdHubOrder.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Đã có lỗi xảy ra khi tạo đơn hàng, thử lại sau",
		}
	}

	current := time.Now()
	callbackModel := request.Callback{
		SO:              input.ReferenceCode,
		CreatedSource:   carrier.ParentCode,
		WarehouseCode:   input.To.Code,
		Status:          &enum.TPLCallbackStatus.READY_TO_PICK,
		Weight:          input.Weight,
		TPLCode:         shippingOrder.TrackingCode,
		TotalFee:        shippingOrder.FeeAmount,
		COD:             shippingOrder.CODAmount,
		ActionTime:      &current,
		StatusName:      "Tạo mới vận đơn",
		ExternalTPLName: carrier.CarrierName,
		TPLStatus:       string(enum.TPLCallbackStatus.READY_TO_PICK),
		TPLStatusName:   "Tạo mới vận đơn",
		NumPackage:      input.NumPackage,
		Type:            &enum.ShippingOrderType.FMPO,
	}

	if input.DropOffAtHubCode != "" && !input.IsBookDropOff {
		callbackModel.Status = &enum.TPLCallbackStatus.STORING
		callbackModel.StatusName = "Đã nhập kho"
		callbackModel.TPLStatusName = "Đã nhập kho"
	}

	// Set nhầm thì gán với nvc cha luôn
	if callbackModel.CreatedSource == nil || *callbackModel.CreatedSource == "" {
		callbackModel.CreatedSource = carrier.CarrierCode
	}

	err := client.Services.TplCallbackClient.CreateCallback(callbackModel)
	if err != nil {
		log.Println("Can not update callback ", input.ReferenceCode)
	}

	if needCreateCustomerVoucher && voucher != nil {
		model.CustomerVoucherDB.Create(&model.CustomerVoucher{
			CustomerCode: input.From.Code,
			VoucherCode:  input.VoucherCode,
			RemainAmount: voucher.MaxNumOfReusePerCustomer - 1,
		})
	}

	if needReduceUsedVoucher && customerVoucher != nil {
		model.CustomerVoucherDB.UpdateOne(bson.M{
			"customer_code": input.From.Code,
			"voucher_code":  input.VoucherCode,
		}, bson.M{
			"remain_amount": customerVoucher.RemainAmount - 1,
		})
	}

	// Nếu khách hàng chưa tồn tại thì thêm mới nếu tồn tại r thì update
	if input.From.Code != "" {
		newCus := &model.Customer{
			Code:       input.From.Code,
			CustomerId: model.GenId("CUSTOMER_ID"),
			Phone:      input.From.Phone,
			Name:       input.From.Name,
		}

		// Nếu có info khách thì lấy còn không thì lấy theo thông tin người gửi hàng
		if input.CustomerInfo != nil {
			if input.CustomerInfo.Code != "" {
				newCus.Code = input.CustomerInfo.Code
			}
			newCus.Name = input.CustomerInfo.Name
			newCus.Phone = input.CustomerInfo.Phone
			newCus.CustomerType = input.CustomerInfo.CustomerType
		}

		newKeyword, keyErr := utils.GenKeyword(newCus.Name, newCus.Code, newCus.Phone)
		if keyErr == nil {
			newCus.Keyword = newKeyword
		}

		existCustomer := model.CustomerDB.QueryOne(bson.M{
			"code": newCus.Code,
		})
		if existCustomer.Status != common.APIStatus.Ok {
			model.CustomerDB.Create(newCus)
		}
	}

	if input.DropOffAtHubCode != "" {
		go client.Services.WarehouseCoreClient.UpdateFMCode(input.ParentReceiveSessionCode, input.DropOffAtHubCode)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Book vận chuyển thành công cho đơn hàng : " + input.ReferenceCode,
		Data:    []model.ShippingOrder{shippingOrder},
	}
}

func ImportFee(input request.ImportFeeRequest, updatedBy int64) *common.APIResponse {
	// Validate again
	if len(input.Lines) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Danh sách đơn không được để trống",
			ErrorCode: "EMPTY_LINE_ITEM",
		}
	}

	if len(input.Lines) > 1000 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Số lượng đơn không vượt quá 1000",
			ErrorCode: "EXCEED_MAX_LIMIT",
		}
	}

	referenceCodes := make([]string, 0, len(input.Lines))
	referenceCodesM := map[string]bool{}
	for _, line := range input.Lines {
		if line.ReferenceCode == "" {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Mã đơn không được để trống",
				ErrorCode: "REFERENCE_CODE_REQUIRED",
			}
		}
		referenceCodes = append(referenceCodes, line.ReferenceCode)
		// Trùng mã
		if referenceCodesM[line.ReferenceCode] {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Nhập trùng mã đơn: " + line.ReferenceCode,
				ErrorCode: "DUP_REFERENCE_CODE",
			}
		}
		referenceCodesM[line.ReferenceCode] = true
	}
	shippingOrdersRaw := model.ShippingOrderDB.Query(bson.M{
		"reference_code": bson.M{
			"$in": referenceCodes,
		},
	}, 0, int64(len(input.Lines)), nil)
	if shippingOrdersRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy danh sách đơn hàng",
			ErrorCode: "NOT_FOUND_SHIPPING_ORDERS",
		}
	}
	shippingOrders := shippingOrdersRaw.Data.([]*model.ShippingOrder)
	// Chỉ cần check case query thiếu đơn do reference_code đã unique
	if len(shippingOrders) < len(input.Lines) {
		missingOrdersSlice := make([]string, 0, len(shippingOrders))
		for _, o := range shippingOrders {
			missingOrdersSlice = append(missingOrdersSlice, o.ReferenceCode)
		}

		missingOrders := utils.GetRemovedElement(referenceCodes, missingOrdersSlice)
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy danh sách đơn hàng",
			ErrorCode: "NOT_FOUND_SHIPPING_ORDERS",
			Data:      missingOrders,
		}
	}

	_ = shipping_order.PushUpdateFee(input, "")

	return &common.APIResponse{Status: common.APIStatus.Ok}
}

func buildHubOrderForCS(shippingOrder model.ShippingOrder) model.HubShippingOrder {
	current := time.Now()
	return model.HubShippingOrder{
		ReferenceCode: shippingOrder.ReferenceCode,
		TrackingCode:  shippingOrder.TrackingCode,
		Note:          shippingOrder.Note,

		FromCustomerCode:    shippingOrder.FromCustomerCode,
		FromCustomerName:    shippingOrder.FromCustomerName,
		FromCustomerPhone:   shippingOrder.FromCustomerPhone,
		FromCustomerAddress: shippingOrder.FromCustomerAddress,
		FromCustomerEmail:   shippingOrder.FromCustomerEmail,
		FromProvinceName:    shippingOrder.FromProvinceName,
		FromDistrictName:    shippingOrder.FromDistrictName,
		FromWardName:        shippingOrder.FromWardName,
		FromProvinceCode:    shippingOrder.FromProvinceCode,
		FromDistrictCode:    shippingOrder.FromDistrictCode,
		FromWardCode:        shippingOrder.FromWardCode,

		ToCustomerCode:    shippingOrder.CustomerCode,
		ToCustomerName:    shippingOrder.CustomerName,
		ToCustomerPhone:   shippingOrder.CustomerPhone,
		ToCustomerAddress: shippingOrder.CustomerShippingAddress,
		ToCustomerEmail:   shippingOrder.CustomerEmail,
		ToProvinceName:    shippingOrder.CustomerProvinceName,
		ToDistrictName:    shippingOrder.CustomerDistrictName,
		ToWardName:        shippingOrder.CustomerWardName,
		ToProvinceCode:    shippingOrder.CustomerProvinceCode,
		ToDistrictCode:    shippingOrder.CustomerDistrictCode,
		ToWardCode:        shippingOrder.CustomerWardCode,

		SubType:    &enum.SubType.CS,
		Type:       &enum.HubOrderType.CS,
		Status:     &enum.HubShippingOrderStatus.STORING,
		ActionTime: current.Unix(),
		CreatedBy:  shippingOrder.CreatedBy,

		HUBCode: shippingOrder.CurrentHub,

		TplName: shippingOrder.TplName,
		TplCode: shippingOrder.TplCode,
	}
}

func BookCS(input *request.BookShippingOrder, createdBy int64) *common.APIResponse {
	shippingOrder := buildShippingOrder(input, int(createdBy))

	hubShippingOrder := buildHubOrderForCS(shippingOrder)

	shippingOrderResp := model.ShippingOrderDB.Upsert(bson.M{
		"reference_code": shippingOrder.ReferenceCode,
	}, shippingOrder)
	if shippingOrderResp.Status != common.APIStatus.Ok {
		shippingOrderJson, _ := json.Marshal(shippingOrder)
		_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
			CmdType: "CREATE_SHIPPING_ORDER_ERROR",
			Title:   "FAIL TO CREATE SHIPPING ORDER",
			Message: string(shippingOrderJson) + "\n" +
				"Error: " + shippingOrderResp.Message,
		})
		return shippingOrderResp
	}
	hubShippingOrderResp := model.HUBShippingOrderDB.Upsert(bson.M{
		"reference_code": hubShippingOrder.ReferenceCode,
		"hub_code":       hubShippingOrder.HUBCode,
	}, hubShippingOrder)
	if hubShippingOrderResp.Status != common.APIStatus.Ok {
		hubShippingOrderJson, _ := json.Marshal(hubShippingOrder)
		_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
			CmdType: "CREATE_SHIPPING_ORDER_ERROR",
			Title:   "FAIL TO CREATE SHIPPING ORDER",
			Message: string(hubShippingOrderJson) + "\n" +
				"Error: " + hubShippingOrderResp.Message,
		})
		return hubShippingOrderResp
	}

	current := time.Now()
	callbackModel := request.Callback{
		SO:              shippingOrder.ReferenceCode,
		CreatedSource:   shippingOrder.TplCode,
		WarehouseCode:   "CS",
		Status:          &enum.TPLCallbackStatus.STORING,
		Weight:          input.Weight,
		TPLCode:         shippingOrder.TrackingCode,
		ActionTime:      &current,
		StatusName:      "Chờ gán tài xế",
		ExternalTPLName: shippingOrder.TplName,
		TPLStatus:       string(enum.TPLCallbackStatus.STORING),
		TPLStatusName:   "Chờ gán tài xế",
		NumPackage:      input.NumPackage,
		Type:            &enum.ShippingOrderType.CS,
	}

	err := client.Services.TplCallbackClient.CreateCallback(callbackModel)
	if err != nil {
		log.Println("Can not update callback ", input.ReferenceCode)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Book vận chuyển thành công cho đơn hàng : " + input.ReferenceCode,
	}
}

func CancelCustomerSupportOrder(shippingOrder *model.ShippingOrder, updatedBy int64) *common.APIResponse {

	if *shippingOrder.Status == enum.TPLCallbackStatus.CANCEL {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Đơn hàng đã bị hủy trước đó",
			ErrorCode: "SHIPPING_ORDER_IS_CANCELLED",
		}
	}

	if *shippingOrder.Status != enum.TPLCallbackStatus.STORING &&
		*shippingOrder.Status != enum.TPLCallbackStatus.DELIVERY_FAIL {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Đơn hàng phải ở trạng thái đang nhập kho hoặc giao hàng thất bại để hủy",
			ErrorCode: "INVALID_SHIPPING_ORDER_STATUS",
		}
	}

	updateShippingOrderResult := model.ShippingOrderDB.UpdateOne(&model.ShippingOrder{
		ReferenceCode: shippingOrder.ReferenceCode,
	}, &model.ShippingOrder{
		Status:        &enum.TPLCallbackStatus.CANCEL,
		LastUpdatedBy: strconv.FormatInt(updatedBy, 10),
	})

	if updateShippingOrderResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không thể cập nhật trạng thái đơn hàng, thử lại sau",
			ErrorCode: updateShippingOrderResult.ErrorCode,
		}
	}

	updateHubShippingOrderResult := model.HUBShippingOrderDB.UpdateOne(&model.HubShippingOrder{
		ReferenceCode: shippingOrder.ReferenceCode,
		HUBCode:       shippingOrder.CurrentHub,
	}, &model.HubShippingOrder{
		Status:        &enum.HubShippingOrderStatus.CANCEL,
		LastUpdatedBy: strconv.FormatInt(updatedBy, 10),
	})

	if updateHubShippingOrderResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không thể cập nhật trạng thái đơn hàng tại hub, thử lại sau",
			ErrorCode: updateHubShippingOrderResult.ErrorCode,
		}
	}

	actionTime := time.Now()
	// After cancel locally push callback update
	createCallbackRequest := request.Callback{
		SO:              shippingOrder.ReferenceCode,
		StatusName:      "Hủy vận chuyển",
		TPLStatusName:   "Hủy vận chuyển",
		ExternalTPLName: shippingOrder.TplName,
		CreatedSource:   shippingOrder.TplCode,
		ActionTime:      &actionTime,
		Status:          &enum.TPLCallbackStatus.CANCEL,
		TPLStatus:       string(enum.TPLCallbackStatus.CANCEL),
	}

	errCb := client.Services.TplCallbackClient.CreateCallback(createCallbackRequest)
	if errCb != nil {
		log.Println("Can not create callback cancel ", shippingOrder.ReferenceCode)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Hủy vận chuyển thành công",
	}
}

func BookEO(input *request.BookShippingOrder, createdBy int64) *common.APIResponse {
	if input.From == nil ||
		input.From.Address == "" ||
		input.From.Name == "" ||
		input.From.Phone == "" ||
		input.From.ProvinceCode == "" ||
		input.From.DistrictCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Thiếu thông tin địa chỉ gửi hàng",
			ErrorCode: "EMPTY_FROM_ADDRESS",
		}
	}

	if input.To == nil ||
		input.To.Name == "" ||
		input.To.Phone == "" ||
		input.To.Address == "" ||
		input.To.DistrictCode == "" ||
		input.To.ProvinceCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Thiếu thông tin địa chỉ nhận hàng",
			ErrorCode: "EMPTY_TO_ADDRESS",
		}
	}

	if input.CustomerInfo == nil ||
		input.CustomerInfo.CustomerType == nil ||
		*input.CustomerInfo.CustomerType == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Thiếu thông tin khách hàng",
			ErrorCode: "EMPTY_CUSTOMER_INFO",
		}
	}

	if input.CustomerInfo.CitizenIdCode != "" &&
		input.CustomerInfo.Code == "" {
		input.CustomerInfo.Code = input.CustomerInfo.CitizenIdCode
	}

	fromAddressErr := BuildAddress(input.From)
	if fromAddressErr != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Lỗi địa chỉ gửi hàng: " + fromAddressErr.Message,
			ErrorCode: "INVALID_FROM_ADDRESS",
		}
	}

	toAddressErr := BuildAddress(input.To)
	if toAddressErr != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Lỗi địa chỉ nhận hàng: " + toAddressErr.Message,
			ErrorCode: "INVALID_TO_ADDRESS",
		}
	}

	if input.Weight <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Khối lượng không được để trống",
			ErrorCode: "EMPTY_WEIGHT",
		}
	}

	if input.NumPackage <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Số lượng kiện không được để trống",
			ErrorCode: "EMPTY_NUM_PACK",
		}
	}

	if input.FeeCollectedOn == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Chưa chọn hình thức thu phí",
			ErrorCode: "EMPTY_FEE_COLLECTED_ON",
		}
	}

	if input.CarrierId != 0 {
		carrierResp := model.CarrierDB.QueryOne(bson.M{
			"carrier_id": input.CarrierId,
		})
		if carrierResp.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Nhà vận chuyển không hợp lệ",
				ErrorCode: "INVALID_CARRIER_ID",
			}
		}
		carrier := carrierResp.Data.([]*model.Carrier)[0]
		if carrier.IsInternal != nil && !*carrier.IsInternal {
			if carrier.ExtraData == nil ||
				carrier.ExtraData.AccessToken == "" {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Nhà vận chuyển không hợp lệ",
				}
			}
			return BookExternalEO(input, carrier, createdBy)
		}
	}

	var configFeeCode string
	if input.DropOffAtHubCode != "" &&
		input.ReceiveAtHubCode != "" {
		configFeeCode = conf.Config.DefaultEOTransportFeeCode
	} else if input.DropOffAtHubCode == "" &&
		input.ReceiveAtHubCode != "" {
		configFeeCode = conf.Config.DefaultEOPickNTransFeeCode
	} else if input.DropOffAtHubCode != "" &&
		input.ReceiveAtHubCode == "" {
		configFeeCode = conf.Config.DefaultEOTransNDeliCode
	} else if input.DropOffAtHubCode == "" &&
		input.ReceiveAtHubCode == "" {
		configFeeCode = conf.Config.DefaultEOPickNDeliCode
	}

	if input.ReBook {
		if input.IsBookDropOff {
			configFeeCode = conf.Config.DefaultEOTransNDeliCode
		} else {
			configFeeCode = conf.Config.DefaultEOPickNDeliCode
		}
	}

	var fmHub = input.FmHub
	var lmHub = input.LmHub
	if input.FmHub == nil {
		if input.ReBook {
			fmHubRaw := model.HubDB.QueryOne(bson.M{
				"code": input.HubCode,
			})
			if fmHubRaw.Status != common.APIStatus.Ok {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   "Mã hub không hợp lệ",
					ErrorCode: "INVALID_HUB_CODE",
				}
			}
			fmHub = fmHubRaw.Data.([]*model.Hub)[0]
		} else {
			if input.DropOffAtHubCode != "" {
				fmHubRaw := model.HubDB.QueryOne(bson.M{
					"code": input.DropOffAtHubCode,
				})
				if fmHubRaw.Status != common.APIStatus.Ok {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   "Mã hub không hợp lệ",
						ErrorCode: "INVALID_HUB_CODE",
					}
				}
				fmHub = fmHubRaw.Data.([]*model.Hub)[0]
			} else {
				fmHub = FindNearestHubOfAddressV2(*input.From)
				if fmHub == nil {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   "Không tìm thấy khu vực hỗ trợ lấy hàng",
						ErrorCode: "NOT_FOUND_FIRST_MILE_HUB",
					}
				}
			}
		}
		input.FmHub = fmHub
	}

	if input.LmHub == nil {
		if input.ReceiveAtHubCode != "" {
			lmHubRaw := model.HubDB.QueryOne(bson.M{
				"code": input.ReceiveAtHubCode,
			})
			if lmHubRaw.Status != common.APIStatus.Ok {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   "Mã hub không hợp lệ",
					ErrorCode: "INVALID_HUB_CODE",
				}
			}
			lmHub = lmHubRaw.Data.([]*model.Hub)[0]
		} else {
			lmHub = FindNearestHubOfAddressV2(*input.To)
			if lmHub == nil {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   "Không tìm thấy khu vực hỗ trợ giao hàng",
					ErrorCode: "NOT_FOUND_LAST_MILE_HUB",
				}
			}
		}
		input.LmHub = lmHub
	}

	// Calculate fee
	var customerRaw = &common.APIResponse{
		Status: common.APIStatus.NotFound,
	}
	if input.CustomerInfo.Code != "" {
		customerRaw = model.CustomerDB.QueryOne(bson.M{
			"code":          input.CustomerInfo.Code,
			"customer_type": input.CustomerInfo.CustomerType,
		})
	}

	needCreateCustomer := false
	if (input.CustomerInfo.CitizenIdCode != "" ||
		input.CustomerInfo.Code != "") &&
		input.CustomerInfo.Phone != "" &&
		input.CustomerInfo.Name != "" &&
		*input.CustomerInfo.CustomerType == enum.CustomerType.EXTERNAL {
		needCreateCustomer = true
	}

	if customerRaw.Status != common.APIStatus.Ok && needCreateCustomer {
		customer.PushCreateCustomer(input.CustomerInfo, input.CustomerInfo.Code)
	} else if customerRaw.Status == common.APIStatus.Ok {
		customer := customerRaw.Data.([]*model.Customer)[0]
		var checkConfigId int64 = 0
		if customer.AppliedFees != nil {
			for _, fee := range *customer.AppliedFees {
				if input.DropOffAtHubCode != "" &&
					input.ReceiveAtHubCode != "" &&
					fee.ConfigFeeType == enum.ConfigFeeType.TRANSPORT {
					checkConfigId = fee.ConfigFeeId
					break
				}

				if input.DropOffAtHubCode == "" &&
					input.ReceiveAtHubCode != "" &&
					fee.ConfigFeeType == enum.ConfigFeeType.PICK_N_TRANSPORT {
					checkConfigId = fee.ConfigFeeId
					break
				}

				if input.DropOffAtHubCode != "" &&
					input.ReceiveAtHubCode == "" &&
					fee.ConfigFeeType == enum.ConfigFeeType.TRANSPORT_N_DELIVERY {
					checkConfigId = fee.ConfigFeeId
					break
				}

				if input.DropOffAtHubCode == "" &&
					input.ReceiveAtHubCode == "" &&
					fee.ConfigFeeType == enum.ConfigFeeType.PICK_N_DELIVERY {
					checkConfigId = fee.ConfigFeeId
					break
				}
			}
		}
		if checkConfigId != 0 {
			configRaw := model.ConfigFeeDB.QueryOne(bson.M{
				"config_id": checkConfigId,
			})
			if configRaw.Status == common.APIStatus.Ok {
				configFeeCode = configRaw.Data.([]*model.ConfigFee)[0].Code
			}
		}
	}

	configFeeRaw := model.ConfigFeeDB.QueryOne(bson.M{
		"code": configFeeCode,
		"is_deleted": bson.M{
			"$ne": true,
		},
	})
	if configFeeRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy cấu hình phí vận chuyển",
			ErrorCode: "NOT_FOUND_CONFIG_FEE",
		}
	}

	configFee := configFeeRaw.Data.([]*model.ConfigFee)[0]
	input.FromHubCode = fmHub.Code
	input.ToHubCode = lmHub.Code

	input.FromAddress = input.From
	input.ToAddress = input.To
	feeAmount, err := CalculateOrderFee(*input, configFee)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy cấu hình phí vận chuyển hợp lệ cho đơn",
			ErrorCode: "NOT_FOUND_VALID_CONFIG_FEE",
		}
	}
	var carrier *model.Carrier
	carrierRaw := model.CarrierDB.QueryOne(bson.M{
		"carrier_id": fmHub.DefaultCarrierId,
	})
	if carrierRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Nhà vận chuyển mặc định không hợp lệ",
			ErrorCode: "INVALID_DEFAULT_CARRIER",
		}
	}
	carrier = carrierRaw.Data.([]*model.Carrier)[0]
	if carrier.IsInternal != nil && !*carrier.IsInternal ||
		carrier.ParentCode == nil || *carrier.ParentCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Nhà vận chuyển mặc định không hợp lệ",
			ErrorCode: "INVALID_DEFAULT_CARRIER",
		}
	}
	input.CarrierCode = carrier.ParentCode
	input.CarrierName = carrier.CarrierName
	input.CarrierId = carrier.CarrierId

	shippingOrder := buildShippingOrder(input, int(createdBy))
	shippingOrder.FeeAmount = feeAmount
	shippingOrder.CODAmount = input.CODAmount
	if input.FeeCollectedOn != nil &&
		*input.FeeCollectedOn == enum.FeeCollectMethod.SENDER_PAY {
		shippingOrder.FeeSenderAmount = &feeAmount
		shippingOrder.TotalCollectSenderAmount = &feeAmount
	}
	shippingOrder.TotalCollectReceiverAmount = &input.CODAmount
	if input.FeeCollectedOn != nil &&
		*input.FeeCollectedOn == enum.FeeCollectMethod.RECEIVER_PAY {
		shippingOrder.FeeReceiverAmount = &feeAmount
		sum := feeAmount + input.CODAmount
		shippingOrder.TotalCollectReceiverAmount = &sum
	}

	if input.FeeCollectedOn != nil &&
		*input.FeeCollectedOn == enum.FeeCollectMethod.DEBT {
		shippingOrder.FeeDebtAmount = &feeAmount
		shippingOrder.TotalDebtAmount = &feeAmount
	}

	createdShippingOrder := model.ShippingOrderDB.Upsert(bson.M{
		"reference_code": shippingOrder.ReferenceCode,
	}, shippingOrder)
	if createdShippingOrder.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Đã có lỗi xảy ra khi tạo đơn hàng, thử lại sau",
		}
	}

	hubOrder := buildHubOrderForEO(&shippingOrder, fmHub, input, createdBy)

	createdHubOrder := model.HUBShippingOrderDB.Upsert(bson.M{
		"reference_code": hubOrder.ReferenceCode,
		"hub_code":       hubOrder.HUBCode,
	}, hubOrder)

	if createdHubOrder.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Đã có lỗi xảy ra khi tạo đơn hàng, thử lại sau",
		}
	}

	current := time.Now()
	callbackModel := request.Callback{
		SO:              shippingOrder.ReferenceCode,
		CreatedSource:   carrier.ParentCode,
		WarehouseCode:   shippingOrder.CurrentHub,
		Status:          &enum.TPLCallbackStatus.READY_TO_PICK,
		Weight:          shippingOrder.Weight,
		TPLCode:         shippingOrder.TrackingCode,
		TotalFee:        shippingOrder.FeeAmount,
		COD:             shippingOrder.CODAmount,
		ActionTime:      &current,
		StatusName:      "Tạo mới vận đơn",
		ExternalTPLName: carrier.CarrierName,
		TPLStatus:       string(enum.TPLCallbackStatus.READY_TO_PICK),
		TPLStatusName:   "Tạo mới vận đơn",
		NumPackage:      shippingOrder.NumPackage,
		Type:            &enum.ShippingOrderType.EO,
	}

	if input.DropOffAtHubCode != "" {
		callbackModel.Status = &enum.TPLCallbackStatus.PICKED
		callbackModel.TPLStatus = string(enum.TPLCallbackStatus.PICKED)
		callbackModel.StatusName = "Chờ nhập hub"
		callbackModel.TPLStatusName = "Chờ nhập hub"
	}

	// Set nhầm thì gán với nvc cha luôn
	if callbackModel.CreatedSource == nil || *callbackModel.CreatedSource == "" {
		callbackModel.CreatedSource = carrier.CarrierCode
	}

	err = client.Services.TplCallbackClient.CreateCallback(callbackModel)
	if err != nil {
		log.Println("Can not update callback ", input.ReferenceCode)
	}

	if input.PartnerCode != "" &&
		conf.Config.WebHookConfig[input.PartnerCode] != nil &&
		conf.Config.WebHookConfig[input.PartnerCode].Url != "" {
		fullFromAddress := fmt.Sprintf("%s, %s, %s, %s", input.From.Address, input.From.WardName, input.From.DistrictName, input.From.ProvinceName)
		fullToAddress := fmt.Sprintf("%s, %s, %s, %s", input.To.Address, input.To.WardName, input.To.DistrictName, input.To.ProvinceName)
		callbackData := &request.Callback{
			PartnerCode:   input.PartnerCode,
			ReferenceCode: shippingOrder.ReferenceCode,
			TrackingCode:  shippingOrder.TrackingCode,
			FromAddress:   fullFromAddress,
			FromName:      input.From.Name,
			ToAddress:     fullToAddress,
			ToName:        input.To.Name,
			NumPackage:    input.NumPackage,
			Status:        &enum.TPLCallbackStatus.READY_TO_PICK,
			CallbackUrl:   shippingOrder.CallbackUrl,
		}
		err := syncData.PushSendCallbackQueue(callbackData, input.ReferenceCode)
		if err != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: err.Error(),
			}
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Book vận chuyển thành công cho đơn hàng : " + input.ReferenceCode,
		Data:    []model.ShippingOrder{shippingOrder},
	}
}

func buildHubOrderForEO(shippingOrder *model.ShippingOrder,
	fmHub *model.Hub,
	input *request.BookShippingOrder,
	createdBy int64) model.HubShippingOrder {
	current := time.Now()
	hubShippingOrder := model.HubShippingOrder{
		VersionNo:     uuid.New().String(),
		ReferenceCode: shippingOrder.ReferenceCode,
		TrackingCode:  shippingOrder.TrackingCode,
		HUBCode:       fmHub.Code,

		ToCustomerAddress: shippingOrder.CustomerShippingAddress,
		ToCustomerPhone:   shippingOrder.CustomerPhone,
		ToCustomerCode:    shippingOrder.CustomerCode,
		ToCustomerEmail:   shippingOrder.CustomerEmail,
		ToCustomerName:    shippingOrder.CustomerName,
		ToProvinceCode:    shippingOrder.CustomerProvinceCode,
		ToProvinceName:    shippingOrder.CustomerProvinceName,
		ToDistrictCode:    shippingOrder.CustomerDistrictCode,
		ToDistrictName:    shippingOrder.CustomerDistrictName,
		ToWardCode:        shippingOrder.CustomerWardCode,
		ToWardName:        shippingOrder.CustomerWardName,

		Height:      shippingOrder.Height,
		Width:       shippingOrder.Width,
		Length:      shippingOrder.Length,
		Weight:      shippingOrder.Weight,
		NumPackage:  shippingOrder.NumPackage,
		PrivateNote: shippingOrder.PrivateNote,
		Note:        shippingOrder.Note,

		CreatedTime: &current,
		ActionTime:  current.Unix(),
		CreatedBy:   strconv.FormatInt(createdBy, 10),
		Products:    shippingOrder.Products,
		TplCode:     shippingOrder.TplCode,
		TplName:     shippingOrder.TplName,

		FeeAmount:                  shippingOrder.FeeAmount,
		TotalCollectReceiverAmount: shippingOrder.TotalCollectReceiverAmount,
		FeeReceiverAmount:          shippingOrder.FeeReceiverAmount,
		TotalCollectSenderAmount:   shippingOrder.TotalCollectSenderAmount,
		FeeSenderAmount:            shippingOrder.FeeSenderAmount,
		TotalDebtAmount:            shippingOrder.TotalDebtAmount,
		FeeDebtAmount:              shippingOrder.FeeDebtAmount,
		ExpectedPickupTime:         shippingOrder.ExpectedPickupTime,
		ExpectedDeliveryTime:       shippingOrder.ExpectedDeliveryTime,
		CODAmount:                  shippingOrder.CODAmount,
		OrderValue:                 shippingOrder.OrderValue,

		ParentReceiveSessionCode: shippingOrder.ParentReceiveSessionCode,

		SubType:      &enum.SubType.EO,
		Action:       "PICKING",
		ProductType:  shippingOrder.ProductType,
		ProductTypes: shippingOrder.ProductTypes,
		References:   shippingOrder.References,
	}

	if input.DropOffAtHubCode != "" {
		hubShippingOrder.FromCustomerAddress = fmHub.Address.Address
		hubShippingOrder.FromCustomerEmail = fmHub.Address.Email
		hubShippingOrder.FromCustomerPhone = fmHub.Address.Phone
		hubShippingOrder.FromCustomerName = fmHub.Name
		hubShippingOrder.FromWardCode = fmHub.Address.WardCode
		hubShippingOrder.FromWardName = fmHub.Address.WardName
		hubShippingOrder.FromDistrictCode = fmHub.Address.DistrictCode
		hubShippingOrder.FromDistrictName = fmHub.Address.DistrictName
		hubShippingOrder.FromProvinceCode = fmHub.Address.ProvinceCode
		hubShippingOrder.FromProvinceName = fmHub.Address.ProvinceName
		hubShippingOrder.Status = &enum.HubShippingOrderStatus.WAIT_TO_STORING
		hubShippingOrder.Type = &enum.HubOrderType.TRANSPORTING
	} else {
		hubShippingOrder.FromCustomerAddress = shippingOrder.FromCustomerAddress
		hubShippingOrder.FromCustomerEmail = shippingOrder.FromCustomerEmail
		hubShippingOrder.FromCustomerPhone = shippingOrder.FromCustomerPhone
		hubShippingOrder.FromCustomerName = shippingOrder.FromCustomerName
		hubShippingOrder.FromWardCode = shippingOrder.FromWardCode
		hubShippingOrder.FromWardName = shippingOrder.FromWardName
		hubShippingOrder.FromDistrictCode = shippingOrder.FromDistrictCode
		hubShippingOrder.FromDistrictName = shippingOrder.FromDistrictName
		hubShippingOrder.FromProvinceCode = shippingOrder.FromProvinceCode
		hubShippingOrder.FromProvinceName = shippingOrder.FromProvinceName
		hubShippingOrder.Status = &enum.HubShippingOrderStatus.READY_TO_PICK
		hubShippingOrder.Type = &enum.HubOrderType.PICKUP
	}

	return hubShippingOrder
}

func FindSuitableCarrierForEO(input *request.BookShippingOrder) []*model.Carrier {
	if input.OPM {
		carriersRaw := model.CarrierDB.Query(bson.M{
			"parent_code": bson.M{
				"$ne": "",
			},
			"is_opm": &enum.True,
		}, 0, 100, nil)

		if carriersRaw.Status != common.APIStatus.Ok {
			return nil
		}

		carriers := carriersRaw.Data.([]*model.Carrier)
		return carriers
	}

	if input.Medship {
		var externalCarrrier []*model.Carrier
		externalCarrieRaw := model.CarrierDB.Query(bson.M{
			"extra_data.is_fast": true,
		}, 0, 100, nil)
		if externalCarrieRaw.Status == common.APIStatus.Ok {
			externalCarrrier = externalCarrieRaw.Data.([]*model.Carrier)
		}
		result := []*model.Carrier{
			{
				CarrierCode: &enum.Partner.NVCT,
				CarrierName: "Nhân viên công ty thuốc sỉ",
				IsInternal:  &enum.True,
			},
		}
		result = append(result, externalCarrrier...)
		return result
	}

	// TODO: Find default carrier of hub
	return []*model.Carrier{
		{
			CarrierCode: &enum.Partner.NVCT,
			CarrierName: "Nhân viên công ty thuốc sỉ",
			IsInternal:  &enum.True,
		},
	}
}

func AutoCancelOrderWorker() {
	isOk := utils.CheckJobLockAvailable(enum.AUTO_CANCEL_ORDER_JOB, conf.Config.RepeatCancelOrderJob)
	if !isOk {
		return
	}

	now := time.Now()
	filterExpiredOrder := bson.M{
		"status": bson.M{
			"$nin": []string{
				string(enum.TPLCallbackStatus.CANCEL),
				string(enum.TPLCallbackStatus.DELIVERED),
				string(enum.TPLCallbackStatus.COMPLETED),
			},
		},
		"need_auto_cancel_at": bson.M{
			"$lte": now,
		},
	}
	var limit int64 = 1000
	for {
		shippingOrdersRaw := model.ShippingOrderDB.SecondaryInstance.Query(filterExpiredOrder, 0, limit, &bson.M{"_id": -1})
		if shippingOrdersRaw.Status != common.APIStatus.Ok {
			break
		}
		shippingOrders := shippingOrdersRaw.Data.([]*model.ShippingOrder)
		smallestId := shippingOrders[len(shippingOrders)-1].ID
		filterExpiredOrder["_id"] = bson.M{
			"$lt": smallestId,
		}
		referenceCodes := make([]string, 0, len(shippingOrders))
		for _, o := range shippingOrders {
			referenceCodes = append(referenceCodes, o.ReferenceCode)
		}
		CancelShippingOrders(&request.CancelShippingService{
			ReferenceCodes: referenceCodes,
		}, 0)
	}
}

func BookOPMEO(input *request.BookShippingOrder, createdBy int64) *common.APIResponse {
	if input.From == nil ||
		input.From.Address == "" ||
		input.From.Name == "" ||
		input.From.Phone == "" ||
		input.From.ProvinceCode == "" ||
		input.From.DistrictCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Thiếu thông tin địa chỉ gửi hàng",
			ErrorCode: "EMPTY_FROM_ADDRESS",
		}
	}

	if input.To == nil ||
		input.To.Name == "" ||
		input.To.Phone == "" ||
		input.To.Address == "" ||
		input.To.DistrictCode == "" ||
		input.To.ProvinceCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Thiếu thông tin địa chỉ nhận hàng",
			ErrorCode: "EMPTY_TO_ADDRESS",
		}
	}

	if input.CustomerInfo == nil ||
		input.CustomerInfo.CustomerType == nil ||
		*input.CustomerInfo.CustomerType == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Thiếu thông tin khách hàng",
			ErrorCode: "EMPTY_CUSTOMER_INFO",
		}
	}

	if input.CarrierId <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Thiếu thông tin nhà vận chuyển",
			ErrorCode: "EMPTY_CARRIER_ID",
		}
	}

	if input.PickMoney == nil ||
		*input.PickMoney <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Thiếu thông tin tiền thu hộ người gửi",
			ErrorCode: "INVALID_PICK_MONEY",
		}
	}

	fromAddressErr := BuildAddress(input.From)
	if fromAddressErr != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Lỗi địa chỉ gửi hàng: " + fromAddressErr.Message,
			ErrorCode: "INVALID_FROM_ADDRESS",
		}
	}

	toAddressErr := BuildAddress(input.To)
	if toAddressErr != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Lỗi địa chỉ nhận hàng: " + toAddressErr.Message,
			ErrorCode: "INVALID_TO_ADDRESS",
		}
	}

	carrierRaw := model.CarrierDB.QueryOne(bson.M{
		"carrier_id": input.CarrierId,
	})
	if carrierRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Nhà vận chuyển không hợp lệ",
			ErrorCode: "INVALID_CARRIER_ID",
		}
	}
	carrier := carrierRaw.Data.([]*model.Carrier)[0]
	// TODO: Hiện tại chưa hỗ trợ nhà vận chuyển ngoài cho nvc nội bộ
	if (carrier.IsInternal != nil && *carrier.IsInternal) ||
		// Chỉ cho phép book nhà vận chuyển con
		(carrier.ParentCode == nil || *carrier.ParentCode == "") {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Nhà vận chuyển không hợp lệ",
			ErrorCode: "INVALID_CARRIER_ID",
		}
	}

	if carrier.IsInternal != nil &&
		!*carrier.IsInternal &&
		input.AccountToken == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Thiếu thông tin tài khoản",
			ErrorCode: "EMPTY_ACCOUNT_TOKEN",
		}
	}

	if input.AccountToken == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Thiếu thông tin tài khoản",
			ErrorCode: "EMPTY_ACCOUNT_TOKEN",
		}
	}
	if carrier.ExtraData == nil {
		carrier.ExtraData = &model.ExtraInfo{}
	}
	carrier.ExtraData.AccessToken = input.AccountToken
	carrier.ExtraData.SubAccountToken = input.SubAccountToken
	input.CarrierName = carrier.CarrierName
	input.CarrierId = carrier.CarrierId
	input.CarrierCode = carrier.ParentCode

	err := shipping.ChangeInfoCarrier(carrier)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Thông tin tài khoản không hợp lệ",
			ErrorCode: "INVALID_ACCOUNT_TOKEN",
		}
	}
	shippingOrder := buildShippingOrder(input, int(createdBy))
	input.ReferenceCode = shippingOrder.ReferenceCode
	var trackingInfo *model.ShippingInfo
	var book3plErr error
	switch *carrier.ParentCode {
	case enum.Partner.GHTK:
		trackingInfo, book3plErr = GHTK.BookOPMOrder(input, carrier)
	default:
		trackingInfo, book3plErr = nil, errors.New("this external carrier is not supported")
	}

	if trackingInfo == nil || book3plErr != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Đã có lỗi xảy ra khi tạo đơn hàng, thử lại sau",
			ErrorCode: "BOOK_3PL_ERROR",
		}
	}

	shippingOrder.TrackingCode = trackingInfo.TrackingNumber
	shippingOrder.FeeAmount = trackingInfo.FeeAmount

	trackingInfo.ReferenceCode = shippingOrder.ReferenceCode

	createdShippingOrder := model.ShippingOrderDB.Upsert(bson.M{
		"reference_code": shippingOrder.ReferenceCode,
	}, shippingOrder)

	if createdShippingOrder.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Đã có lỗi xảy ra khi tạo đơn hàng, thử lại sau",
		}
	}

	current := time.Now()
	callbackModel := request.Callback{
		SO:              shippingOrder.ReferenceCode,
		CreatedSource:   carrier.ParentCode,
		WarehouseCode:   shippingOrder.CurrentHub,
		Status:          &enum.TPLCallbackStatus.READY_TO_PICK,
		Weight:          shippingOrder.Weight,
		TPLCode:         shippingOrder.TrackingCode,
		TotalFee:        shippingOrder.FeeAmount,
		COD:             shippingOrder.CODAmount,
		ActionTime:      &current,
		StatusName:      "Tạo mới vận đơn",
		ExternalTPLName: carrier.CarrierName,
		TPLStatus:       string(enum.TPLCallbackStatus.READY_TO_PICK),
		TPLStatusName:   "Tạo mới vận đơn",
		NumPackage:      shippingOrder.NumPackage,
		Type:            &enum.ShippingOrderType.EO,
	}

	err = client.Services.TplCallbackClient.CreateCallback(callbackModel)
	if err != nil {
		log.Println("Can not update callback ", input.ReferenceCode)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Book vận chuyển thành công cho đơn hàng : " + input.ReferenceCode,
		Data: []*model.ShippingInfo{
			trackingInfo,
		},
	}
}

func BookExternalEO(
	input *request.BookShippingOrder,
	carrier *model.Carrier,
	createdBy int64) *common.APIResponse {
	shippingOrderDraft := buildShippingOrder(input, int(createdBy))
	shippingOrderDraft.TplCode = carrier.ParentCode
	shippingOrderDraft.TplName = carrier.CarrierName
	shippingOrderDraft.TplServiceId = carrier.CarrierId
	shippingOrderDraft.Status = &enum.TPLCallbackStatus.INIT
	shippingOrderResp := model.ShippingOrderDB.Create(shippingOrderDraft)
	if shippingOrderResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Đã có lỗi xảy ra khi tạo đơn hàng, thử lại sau",
		}
	}
	input.ReferenceCode = shippingOrderDraft.ReferenceCode
	var resp *model.ShippingInfo
	var err error
	if carrier.ParentCode != nil &&
		*carrier.ParentCode == enum.Partner.BE {
		resp, err = Be.BookShippingOrder(input, carrier)
		if err != nil {
			model.ShippingOrderDB.UpdateOne(bson.M{
				"reference_code": shippingOrderDraft.ReferenceCode,
			}, bson.M{
				"status": string(enum.TPLCallbackStatus.CREATE_FAIL),
			})
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   err.Error(),
				ErrorCode: "BOOK_3PL_ERROR",
			}
		}
	} else {
		// TODO: Add more carrier here
		resp, err = Ahamove.BookShippingOrder(input, carrier)
		if err != nil {
			model.ShippingOrderDB.UpdateOne(bson.M{
				"reference_code": shippingOrderDraft.ReferenceCode,
			}, bson.M{
				"status": string(enum.TPLCallbackStatus.CREATE_FAIL),
			})
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Đã có lỗi xảy ra khi tạo đơn hàng, thử lại sau",
				ErrorCode: "BOOK_3PL_ERROR",
			}
		}
	}
	afterOption := options.After
	updateResp := model.ShippingOrderDB.UpdateOneWithOption(bson.M{
		"reference_code": shippingOrderDraft.ReferenceCode,
	}, bson.M{
		"$set": bson.M{
			"tracking_code": resp.TrackingNumber,
			"fee_amount":    resp.FeeAmount,
			"status":        string(enum.TPLCallbackStatus.READY_TO_PICK),
			"extra_info":    resp.ExtraInfo,
		},
	}, &options.FindOneAndUpdateOptions{
		ReturnDocument: &afterOption,
	})

	if updateResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Không thể cập nhật trạng thái đơn hàng",
		}
	}
	current := time.Now()
	callbackModel := request.Callback{
		SO:              shippingOrderDraft.ReferenceCode,
		CreatedSource:   carrier.ParentCode,
		Status:          &enum.TPLCallbackStatus.READY_TO_PICK,
		Weight:          shippingOrderDraft.Weight,
		TPLCode:         shippingOrderDraft.TrackingCode,
		TotalFee:        shippingOrderDraft.FeeAmount,
		COD:             shippingOrderDraft.CODAmount,
		ActionTime:      &current,
		StatusName:      "Tạo mới vận đơn",
		ExternalTPLName: carrier.CarrierName,
		TPLStatus:       string(enum.TPLCallbackStatus.READY_TO_PICK),
		TPLStatusName:   "Tạo mới vận đơn",
		NumPackage:      shippingOrderDraft.NumPackage,
		Type:            &enum.ShippingOrderType.EO,
	}

	err = client.Services.TplCallbackClient.CreateCallback(callbackModel)
	if err != nil {
		log.Println("Can not update callback ", input.ReferenceCode)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Book vận chuyển thành công cho đơn hàng : " + input.ReferenceCode,
		Data:    updateResp.Data,
	}
}
