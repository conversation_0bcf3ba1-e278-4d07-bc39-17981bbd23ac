package api

import (
	"encoding/json"
	"log"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
)

// CreateConfig api
func CreateConfig(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Config
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.CreateConfig(&input))
}

// UpdateConfig api
func UpdateConfig(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Config
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.UpdateConfig(&input))
}

// GetConfig api
func GetConfig(req sdk.APIRequest, resp sdk.APIResponder) error {
	// get config by provinceCode
	var str = req.GetParam("provinceCode")
	if str != "" {
		return resp.Respond(action.GetConfig(str))
	}

	var (
		q        = req.GetParam("q")
		offset   = int64(sdk.ParseInt(req.GetParam("offset"), 0))
		limit    = int64(sdk.ParseInt(req.GetParam("limit"), 100))
		getTotal = req.GetParam("getTotal") == "true"
	)
	if q == "" {
		q = "{}"
	}
	var input model.Config
	err := json.Unmarshal([]byte(q), &input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.GetListConfig(input, offset, limit, getTotal))
}

// DeleteConfig api
func DeleteConfig(req sdk.APIRequest, resp sdk.APIResponder) error {
	type myRequest struct {
		Id            string `json:"id"`
		ProvinceCode  string `json:"provinceCode"`
		WarehouseCode string `json:"warehouseCode"`
	}
	var input myRequest
	log.Println("Delete Callback GetContentText => ", req.GetContentText())
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	if input.Id != "" {
		return resp.Respond(action.DeleteConfigById(input.Id))
	}
	return resp.Respond(action.DeleteConfig(input.ProvinceCode, input.WarehouseCode))
}

func GetFailReasons(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.GetFailReasons())
}
