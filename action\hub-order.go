package action

import (
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action/consumer/productivity"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action/consumer/bin"
	handOverTransfer "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action/consumer/hand-over-transfer"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action/consumer/reconcile"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action/consumer/retry_queue"
	shippingAddress "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action/consumer/shipping-address"
	shippingOrderQueue "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action/consumer/shipping-order"
	syncData "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action/consumer/tpl-callback"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/response"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var (
	// Cached hub order statuses. WARNING: DO NOT MODIFY THIS SLICE
	HubOrderSnapshotStatuses = []string{
		"COMPLETED",
		"LOST",
	}
)

// CreateHubShippingOrder func
func CreateHubShippingOrder(input *model.HubShippingOrder) *common.APIResponse {
	if validate(input.HUBCode) != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã hub không được để trống",
		}
	}

	if validate(input.ReferenceCode) != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã phiếu giao không được để trống",
		}
	}

	if validate(input.TplCode) != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã nhà vận chuyển không được để trống",
		}
	}

	if validate(input.TrackingCode) != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã tracking không được để trống",
		}
	}

	createShipping := model.HUBShippingOrderDB.Create(input)

	if createShipping.Status != common.APIStatus.Ok {
		return createShipping
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Data:    createShipping.Data,
		Message: "Tạo phiếu giao thành công",
	}
}

// UpdateStatusHubShippingOrder func
func UpdateStatusHubShippingOrder(input *model.HubShippingOrder, account *model.ActionSource) *common.APIResponse {
	if input.ReferenceCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã phiếu giao không được để trống",
		}
	}

	if input.Status == nil || *input.Status == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Trạng thái cập nhật không hợp lệ",
		}
	}

	hubShippingOrder := model.HUBShippingOrderDB.QueryOne(bson.M{
		"reference_code": input.ReferenceCode,
		"hub_code":       input.HUBCode,
	})

	if hubShippingOrder.Status != common.APIStatus.Ok || hubShippingOrder.Data == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Không tìm thấy thông tin phiếu giao hàng",
		}
	}

	shippingOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
		"reference_code": input.ReferenceCode,
	})

	if shippingOrderRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không tìm thấy đơn giao hàng",
		}
	}

	hubShippingOrderInfo := hubShippingOrder.Data.([]*model.HubShippingOrder)[0]
	shippingOrder := shippingOrderRaw.Data.([]*model.ShippingOrder)[0]

	if hubShippingOrderInfo.Status == nil || *hubShippingOrderInfo.Status == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Trạng thái ban đầu của đơn giao hàng đang trống",
		}
	}

	isValid := IsValidStatusTransition(*hubShippingOrderInfo.Status, *input.Status)

	if !isValid {
		return &common.APIResponse{
			Status: common.APIStatus.Invalid,
			Message: "Không thể chuyển trạng thái từ " +
				string(*hubShippingOrderInfo.Status) + " sang " +
				string(*input.Status),
		}
	}

	// Kiểm tra yêu cầu trả hàng khi đơn chưa xuất kho
	if *shippingOrder.Status == enum.TPLCallbackStatus.READY_TO_PICK &&
		*hubShippingOrderInfo.Status == enum.HubShippingOrderStatus.STORING &&
		*input.Status == enum.HubShippingOrderStatus.RETURN {
		whs, err := client.Services.WarehouseCoreClient.GetAllWarehouse()

		if err != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Lỗi kết nối đến warehouse",
			}
		}

		isHubWareHouse := false
		for _, wh := range whs {
			if wh.IsMergeDO && wh.MainHubCode == input.HUBCode {
				isHubWareHouse = true
				break
			}
		}

		isHubOrderF, err := regexp.Match("-F$", []byte(hubShippingOrderInfo.ReferenceCode))
		if err != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Mã đơn không hợp lệ",
			}
		}

		isHubOrderP, err := regexp.Match("-P.?$", []byte(hubShippingOrderInfo.ReferenceCode))
		if err != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Mã đơn không hợp lệ",
			}
		}

		isSOPExpired := false
		if isHubOrderP {
			if hubShippingOrderInfo.MergeStatus != nil && *hubShippingOrderInfo.MergeStatus == enum.MergeStatus.EXPIRED {
				isSOPExpired = true
			}
		}

		// Không chặn đơn hubWarehouse có status cho gộp ở đơn -F và đơn -P quá hạn
		// not [a & (b | c)] = !a | (!b & !c)
		if !isHubWareHouse || (!isHubOrderF && !isSOPExpired) {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không thể trả đơn hàng chưa xuất kho",
			}
		}
	}

	needUpdateReason := false
	// Kiểm tra đơn đang giao bị chuyển thành giao thất bại
	if *hubShippingOrderInfo.Status == enum.HubShippingOrderStatus.DELIVERING && *input.Status == enum.HubShippingOrderStatus.DELIVERY_FAIL {
		deliveryFailReason := &model.FailReason{}

		deliveryFailReason.ReasonType = enum.FailReasonType.DELIVERY_FAIL
		deliveryFailReason.ReasonCode = input.ReasonCode
		deliveryFailReason.CreatedAt = time.Now()
		deliveryFailReason.Images = input.Images
		if input.RescheduledDeliveryTime != 0 &&
			input.RescheduledDeliveryTime < time.Now().Unix() {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				ErrorCode: "INVALID_RESCHEDULED_DELIVERY_TIME",
				Message:   "Thời gian hẹn lại không được nhỏ hơn thời gian hiện tại",
			}
		}

		// Update Hub Shipping Order FailReason array
		deliveryFailReason.FailTime = len(hubShippingOrderInfo.FailReasons) + 1
		deliveryFailReason.FormattedReason = FormatFailReason(input.ReasonCode, input.Reason, len(hubShippingOrderInfo.FailReasons)+1, "")
		hubShippingOrderInfo.FailReasons = append(hubShippingOrderInfo.FailReasons, *deliveryFailReason)

		// Update Shipping Order FailReason array
		deliveryFailReason.FailTime = len(shippingOrder.FailReasons) + 1
		deliveryFailReason.FormattedReason = FormatFailReason(input.ReasonCode, input.Reason, len(shippingOrder.FailReasons)+1, "")
		shippingOrder.FailReasons = append(shippingOrder.FailReasons, *deliveryFailReason)

		hubShippingOrderInfo.RescheduledDeliveryTime = input.RescheduledDeliveryTime
		hubShippingOrderInfo.ExpectedDeliveryTime = input.RescheduledDeliveryTime
		needUpdateReason = true
	}
	// Kiểm tra đơn đang lấy bị chuyển thành lấy thất bại
	if *hubShippingOrderInfo.Status == enum.HubShippingOrderStatus.PICKING && *input.Status == enum.HubShippingOrderStatus.PICK_FAIL {
		pickFailReason := &model.FailReason{}

		pickFailReason.ReasonType = enum.FailReasonType.PICK_FAIL
		pickFailReason.ReasonCode = input.ReasonCode
		pickFailReason.CreatedAt = time.Now()
		pickFailReason.Images = input.Images
		if input.RescheduledPickupTime != 0 &&
			input.RescheduledPickupTime <= time.Now().Unix() {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				ErrorCode: "INVALID_RESCHEDULED_PICKUP_TIME",
				Message:   "Thời gian hẹn lại không được nhỏ hơn thời gian hiện tại",
			}
		}

		// Update Hub Shipping Order FailReason array
		pickFailReason.FailTime = len(hubShippingOrderInfo.FailReasons) + 1
		pickFailReason.FormattedReason = FormatFailReason(input.ReasonCode, input.Reason, len(hubShippingOrderInfo.FailReasons)+1, "")
		hubShippingOrderInfo.FailReasons = append(hubShippingOrderInfo.FailReasons, *pickFailReason)

		// Update Shipping Order FailReason array
		pickFailReason.FailTime = len(shippingOrder.FailReasons) + 1
		pickFailReason.FormattedReason = FormatFailReason(input.ReasonCode, input.Reason, len(shippingOrder.FailReasons)+1, "")
		shippingOrder.FailReasons = append(shippingOrder.FailReasons, *pickFailReason)
		hubShippingOrderInfo.RescheduledPickupTime = input.RescheduledPickupTime
		hubShippingOrderInfo.ExpectedPickupTime = input.RescheduledPickupTime

		needUpdateReason = true
	}

	afterOption := options.After

	// Cập nhật mảng lí do thất bại cho ShippingOrder
	if needUpdateReason {
		updateShippingOrderStatus := model.ShippingOrderDB.UpdateOne(
			bson.M{
				"reference_code": input.ReferenceCode,
			},
			shippingOrder,
			&options.FindOneAndUpdateOptions{
				Upsert:         &enum.False,
				ReturnDocument: &afterOption,
			})

		if updateShippingOrderStatus.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Không thể cập nhật lí do thất bại của đơn hàng: " + input.ReferenceCode,
				ErrorCode: updateShippingOrderStatus.ErrorCode,
			}
		}
	}

	// Nếu tài xế tiến hành giao đơn
	getHub := model.HubDB.QueryOne(bson.M{"code": hubShippingOrderInfo.HUBCode})
	if getHub.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: getHub.Message,
		}
	}
	hub := getHub.Data.([]*model.Hub)[0]
	if hubShippingOrderInfo.CODAmount > 0 &&
		hubShippingOrderInfo.Type != nil &&
		*hubShippingOrderInfo.Type != enum.HubOrderType.PICKUP &&
		shippingOrder.ShippingType != nil &&
		*shippingOrder.ShippingType != enum.ShippingOrderType.EO &&
		hub.LimitStatus == "ACTIVE" {

		var accountAmountUpdate float64
		var hubAmountUpdate float64
		var needCheckAccountAmount bool
		var needCheckHubAmount bool
		var needPass bool
		switch *input.Status {
		case enum.HubShippingOrderStatus.DELIVERING:
			needCheckAccountAmount = true
			if hubShippingOrderInfo.SubType != nil &&
				*hubShippingOrderInfo.SubType == enum.SubType.EO {
				needCheckHubAmount = false
			}
			break
		case enum.HubShippingOrderStatus.DELIVERED:
			// Cộng tiền nhận cho tài xế
			accountAmountUpdate = hubShippingOrderInfo.CODAmount
			hubAmountUpdate = hubShippingOrderInfo.CODAmount
			// Từ STORING -> DELIVERED của điều phối giao hàng
			if *hubShippingOrderInfo.Status == enum.HubShippingOrderStatus.STORING {
				needCheckAccountAmount = true
				needCheckHubAmount = true
				hubShippingOrderInfo.DriverID = account.Account.AccountID
			}
			break
		case enum.HubShippingOrderStatus.COD_COLLECTED:
			// Trừ tiền nhận tài xế và cộng cho HUB
			accountAmountUpdate = -1 * hubShippingOrderInfo.CODAmount
			break
		default:
			needPass = true
			break
		}
		if !needPass {
			// Tài xế lấy đơn giao - hoặc là giao hàng tại chỗ
			if needCheckAccountAmount {
				getAccount := model.AccountDB.QueryOne(
					bson.M{
						"account_id": hubShippingOrderInfo.DriverID,
						"hub_code":   hubShippingOrderInfo.HUBCode})
				if getAccount.Status != common.APIStatus.Ok {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						ErrorCode: "NOT_FOUND_EMPLOYEE",
						Message:   "Không tìm thấy nhân viên",
					}
				}

				logisticAccount := getAccount.Data.([]*model.Account)[0]
				// Tiền nắm giữ lớn hơn hạn mức hiện tại
				if logisticAccount.ReceivedAmount >= logisticAccount.LimitAmount {
					return limitErrorResponse("Vượt quá hạn mức của bạn, chỉ có thể nhận đơn giao 0 VNĐ. Vui lòng nhận đơn trong hạn mức cho phép hoặc nộp tiền và chờ đối soát để có thể nhận đơn.")
				}

				// Giao hàng tại chỗ - cần phải kiểm tra hạn mức của hub
				if needCheckHubAmount {
					loc, _ := time.LoadLocation(conf.Config.TimeZoneAsiaBangkok)
					hubLimit := hub.LimitAmountByWeekday[time.Now().In(loc).Weekday().String()]
					// Xét tiền hub đang giữ >= hạn mức hub trước
					if hub.TotalReceivedAmount >= hubLimit {
						return limitErrorResponse("Vượt quá hạn mức của Hub, chỉ có thể giao hàng cho đơn 0 VNĐ")
					}
					// Xét tài khoản điều phối giao hàng
					if logisticAccount.LimitAmount < 1 {
						return &common.APIResponse{
							Status:    common.APIStatus.Invalid,
							ErrorCode: "INVALID_LIMIT_TO_ASSIGN_ORDER",
							Message:   "Tài khoản này không có hạn mức để nhận đơn hàng",
						}
					}
				}
			}

			defer func() {
				if accountAmountUpdate != 0 {
					_ = model.AccountDB.IncreOne(
						bson.M{
							"account_id": hubShippingOrderInfo.DriverID,
							"hub_code":   hubShippingOrderInfo.HUBCode,
						},
						"received_amount",
						int(accountAmountUpdate),
					)
				}
				if hubAmountUpdate != 0 {
					_ = model.HubDB.UpdateOneWithOption(bson.M{
						"code":         hubShippingOrderInfo.HUBCode,
						"limit_status": "ACTIVE",
					}, bson.M{
						"$inc": bson.D{
							{"total_received_amount", int(hubAmountUpdate)},
						},
					})
				}
			}()
		}
	}

	if *input.Status == enum.HubShippingOrderStatus.LOST && hubShippingOrderInfo.Status != nil && *hubShippingOrderInfo.Status == enum.HubShippingOrderStatus.STORING {
		hubShippingOrderInfo.ReceivePackage = hubShippingOrderInfo.NumPackage
	}

	// If change status from PICKING to WAIT_TO_STORING
	if *hubShippingOrderInfo.Status == enum.HubShippingOrderStatus.PICKING &&
		*input.Status == enum.HubShippingOrderStatus.WAIT_TO_STORING {
		if input.ExtraInfo == nil || input.ExtraInfo["pop"] == nil {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				ErrorCode: "MISSING_POP",
				Message:   "Thiếu thông tin hình ảnh lấy hàng thành công.",
			}
		}
		popByte, _ := json.Marshal(input.ExtraInfo["pop"])
		var pop []string
		err := json.Unmarshal(popByte, &pop)
		if err != nil {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				ErrorCode: "INVALID_POP",
				Message:   "Định dạng pop không hợp lệ",
			}
		}
		for _, ele := range pop {
			if isOk, _ := regexp.Match("^(?:http(s)?:\\/\\/)?[\\w.-]+(?:\\.[\\w\\.-]+)+[\\w\\-\\._~:/?#[\\]@!\\$&'\\(\\)\\*\\+,;=.]+$", []byte(ele)); !isOk {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					ErrorCode: "INVALID_POP",
					Message:   "Link không đúng định dạng.",
				}
			}
		}

		if hubShippingOrderInfo.ExtraInfo == nil {
			hubShippingOrderInfo.ExtraInfo = map[string]interface{}{}
		}

		hubShippingOrderInfo.ExtraInfo["pop"] = input.ExtraInfo["pop"]
		current := time.Now()
		if shippingOrder.CallbackUrl != "" {
			callback := request.Callback{
				Status:        &enum.TPLCallbackStatus.PICKED,
				ReferenceCode: shippingOrder.ReferenceCode,
				PoCode:        shippingOrder.ParentReferenceCode,
				WarehouseCode: shippingOrder.CustomerCode,
				TrackingCode:  shippingOrder.TrackingCode,
				ActionTime:    &current,
				PartnerCode:   "INTERNAL",
				HubCode:       shippingOrder.CurrentHub,
				CallbackUrl:   shippingOrder.CallbackUrl,
				NumPackage:    shippingOrder.NumPackage,
			}
			partner := client.GetWebhookClient("INTERNAL")
			err = partner.SendCallbackToPartner(callback)
			if err != nil {
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "ERROR_SEND_NOTI",
					Title:   fmt.Sprintf("ERROR_SEND_NOTI: " + err.Error()),
					Message: "Không thể gửi callback đã lấy hàng" + "\n" + err.Error(),
				})
			}
		}

		if shippingOrder.FeeCollectMethod != nil &&
			*shippingOrder.FeeCollectMethod == enum.FeeCollectMethod.SENDER_PAY {
			hubShippingOrderInfo.DeliveredTime = time.Now().Unix()
			cloneHubShippingOrderInfo := *hubShippingOrderInfo
			cloneHubShippingOrderInfo.Status = &enum.HubShippingOrderStatus.WAIT_TO_STORING
			_, _ = CreateReconcile(&cloneHubShippingOrderInfo, string(enum.ReconcileTypeRequest.RIDER_HUB))
		}

		if shippingOrder.ShippingType != nil &&
			*shippingOrder.ShippingType == enum.ShippingOrderType.EO {
			splitEO := strings.Split(shippingOrder.ReferenceCode, "BMX")
			if len(splitEO) == 2 {
				orderNumStr := strings.Split(splitEO[1], "-")
				orderId, err := strconv.ParseInt(orderNumStr[0], 10, 64)
				if err != nil {
					return &common.APIResponse{
						Status:  common.APIStatus.Invalid,
						Message: "Đơn hàng không tồn tại",
					}
				}

				client.Services.AccountingClient.CreateBill(request.CreateBillRequest{
					OrderId:           orderId,
					CustomerName:      shippingOrder.FromCustomerName,
					ExternalOrderCode: shippingOrder.ReferenceCode,
					IsAuto:            true,
					FeeLogistic: &request.FeeLogisticRequest{
						LogisticFeeService: int64(shippingOrder.FeeAmount),
						CollectionFee:      int64(shippingOrder.CODAmount),
					},
				})
			}
		}

		if shippingOrder.FeeCollectMethod != nil &&
			*shippingOrder.FeeCollectMethod == enum.FeeCollectMethod.DEBT &&
			shippingOrder.ShippingType != nil &&
			*shippingOrder.ShippingType != enum.ShippingOrderType.EO {
			reconcile.PushReconcileOrder(reconcile.AddOrderToReconcileSession{
				ReferenceCode: shippingOrder.ReferenceCode,
				CustomerCode:  shippingOrder.FromCustomerCode,
			}, shippingOrder.ReferenceCode)
		}

		// Nếu cung cấp chữ kí thì validate format rồi gán cho hub order
		if input.ExtraInfo["signature"] != nil {
			signatureByte, _ := json.Marshal(input.ExtraInfo["signature"])
			var signature []model.Signature
			signErr := json.Unmarshal(signatureByte, &signature)
			if signErr != nil {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					ErrorCode: "INVALID_SIGNATURE",
					Message:   "Định dạng chữ kí không hợp lệ",
				}
			}
			hubShippingOrderInfo.ExtraInfo["signature"] = input.ExtraInfo["signature"]
		}
	}
	// If change status from PICK_FAIL to READY_TO_PICK
	// or change status from DELIVERY_FAIL to STORING
	if (*hubShippingOrderInfo.Status == enum.HubShippingOrderStatus.PICK_FAIL &&
		*input.Status == enum.HubShippingOrderStatus.READY_TO_PICK) ||
		(*hubShippingOrderInfo.Status == enum.HubShippingOrderStatus.DELIVERY_FAIL &&
			*input.Status == enum.HubShippingOrderStatus.STORING) {
		filter := bson.M{
			"driver_id":   0,
			"driver_name": "",
		}

		updateHubShippingOrder := model.HUBShippingOrderDB.UpdateOne(
			bson.M{
				"reference_code": input.ReferenceCode,
				"hub_code":       input.HUBCode,
			}, filter)

		if updateHubShippingOrder.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Lỗi khi cập nhật đơn: " + updateHubShippingOrder.Message,
			}
		}
		hubShippingOrderInfo.DriverID = 0
		hubShippingOrderInfo.DriverName = ""
	}

	// If change status to DELIVERED, require pod by default
	if *input.Status == enum.HubShippingOrderStatus.DELIVERED {
		if hubShippingOrderInfo.MergeStatus != nil && *hubShippingOrderInfo.MergeStatus == enum.MergeStatus.WAIT_TO_MERGE {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				ErrorCode: "INVALID_STATUS",
				Message:   "Đơn hàng đang được gộp, không thể chuyển trạng thái thành công",
			}
		}

		if hubShippingOrderInfo.NeedVerifyReceiver && input.VerificationCode == "" {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				ErrorCode: "INVALID_VERIFICATION_CODE",
				Message:   "Thiếu thông tin mã xác thực giao hàng",
			}
		} else if hubShippingOrderInfo.NeedVerifyReceiver &&
			input.VerificationCode != hubShippingOrderInfo.VerificationCode {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				ErrorCode: "INVALID_VERIFICATION_CODE",
				Message:   "Mã xác thực giao hàng không chính xác, vui lòng kiểm tra lại",
			}
		}

		if input.ExtraInfo == nil || input.ExtraInfo["pod"] == nil {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				ErrorCode: "MISSING_POD",
				Message:   "Thiếu thông tin hình ảnh giao hàng thành công.",
			}
		}

		podByte, _ := json.Marshal(input.ExtraInfo["pod"])
		var pod []string
		err := json.Unmarshal(podByte, &pod)
		if err != nil {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				ErrorCode: "INVALID_POD",
				Message:   "Định dạng pod không hợp lệ",
			}
		}
		for _, ele := range pod {
			if isOk, _ := regexp.Match("^(?:http(s)?:\\/\\/)?[\\w.-]+(?:\\.[\\w\\.-]+)+[\\w\\-\\._~:/?#[\\]@!\\$&'\\(\\)\\*\\+,;=.]+$", []byte(ele)); !isOk {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					ErrorCode: "INVALID_POD",
					Message:   "Link không đúng định dạng.",
				}
			}
		}

		if hubShippingOrderInfo.ExtraInfo == nil {
			hubShippingOrderInfo.ExtraInfo = map[string]interface{}{}
		}

		// Nếu cung cấp chữ kí thì validate format rồi gán cho hub order
		if input.ExtraInfo["signature"] != nil {
			signatureByte, _ := json.Marshal(input.ExtraInfo["signature"])
			var signature []model.Signature
			signErr := json.Unmarshal(signatureByte, &signature)
			if signErr != nil {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					ErrorCode: "INVALID_SIGNATURE",
					Message:   "Định dạng chữ kí không hợp lệ",
				}
			}
			hubShippingOrderInfo.ExtraInfo["signature"] = input.ExtraInfo["signature"]
		}

		// Assign driver
		hubShippingOrderInfo.DriverID = account.Account.AccountID
		hubShippingOrderInfo.DriverName = account.Account.Fullname
		hubShippingOrderInfo.ExtraInfo["pod"] = input.ExtraInfo["pod"]

		// If change status from STORING to DELIVERED
		if *hubShippingOrderInfo.Status == enum.HubShippingOrderStatus.STORING {
			// Check employee permission
			isValidRole := false
			for _, role := range account.UserRole {
				if role.DepartmentCode != nil && *role.DepartmentCode == input.HUBCode {
					isValidRole = true
				}
			}
			if !isValidRole {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   "Chức vụ của nhân viên không hợp lệ",
					ErrorCode: "INVALID_ROLE",
				}
			}

			// Assign driver
			hubShippingOrderInfo.DriverID = account.Account.AccountID
			hubShippingOrderInfo.DriverName = account.Account.Fullname

			if hubShippingOrderInfo.Type != nil &&
				*hubShippingOrderInfo.Type == enum.HubOrderType.PICKUP {
				// Update status shipping order
				updateShippingOrderStatus := model.ShippingOrderDB.UpdateOne(bson.M{
					"reference_code": input.ReferenceCode,
				}, bson.M{
					"status": input.Status,
				})

				if updateShippingOrderStatus.Status != common.APIStatus.Ok {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   "Không thể cập nhật trạng thái đơn hàng: " + input.ReferenceCode,
						ErrorCode: updateShippingOrderStatus.ErrorCode,
					}
				}
			}
		}
	}

	// Assign driver at warehouse directly
	if *shippingOrder.ShippingType == enum.ShippingOrderType.DELIVERY {
		if *input.Status == enum.HubShippingOrderStatus.DELIVERING || // case: assign driver
			(*input.Status == enum.HubShippingOrderStatus.DELIVERED && // case customers pick up at hub
				*hubShippingOrderInfo.Status == enum.HubShippingOrderStatus.STORING) {
			hubQuery := model.HubDB.QueryOne(bson.M{
				"code": input.HUBCode,
			})
			if hubQuery.Status == common.APIStatus.Ok {
				hub := hubQuery.Data.([]*model.Hub)[0]
				if hub.WarehouseReferenceCode != "" {
					// Notify out Wms
					handOverTransfer.Push(request.CompleteHandOverRequest{
						SO: input.ReferenceCode,
					}, input.ReferenceCode)
				}
			}

			// Assign driver
			hubShippingOrderInfo.DriverID = account.Account.AccountID
			hubShippingOrderInfo.DriverName = account.Account.Fullname

			if hubShippingOrderInfo.Type != nil &&
				*hubShippingOrderInfo.Type == enum.HubOrderType.PICKUP {
				// Update status shipping order
				updateShippingOrderStatus := model.ShippingOrderDB.UpdateOne(bson.M{
					"reference_code": input.ReferenceCode,
				}, bson.M{
					"status": input.Status,
				})

				if updateShippingOrderStatus.Status != common.APIStatus.Ok {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   "Không thể cập nhật trạng thái đơn hàng: " + input.ReferenceCode,
						ErrorCode: updateShippingOrderStatus.ErrorCode,
					}
				}
			}
		}
	}

	// If change status from DELIVERING to DELIVERED
	// or change status from PICK_FAIL  to STORING
	if ((*hubShippingOrderInfo.Status == enum.HubShippingOrderStatus.DELIVERING &&
		*input.Status == enum.HubShippingOrderStatus.DELIVERED) ||
		(*hubShippingOrderInfo.Status == enum.HubShippingOrderStatus.DELIVERY_FAIL &&
			*input.Status == enum.HubShippingOrderStatus.STORING)) &&
		// Check trip is exist or not, old data may not have trip
		hubShippingOrderInfo.TripId != 0 {
		tripRaw := model.TripDB.QueryOne(bson.M{
			"trip_id": hubShippingOrderInfo.TripId,
		})
		if tripRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không tìm thấy chuyến của tài xế",
			}
		}
		trip := tripRaw.Data.([]*model.Trip)[0]
		isCompleteTrip := &enum.TripStatus.COMPLETE
		for _, address := range trip.Addresses {
			if address.Code == hubShippingOrderInfo.ReferenceCode {
				if *hubShippingOrderInfo.Status == enum.HubShippingOrderStatus.DELIVERY_FAIL {
					address.Status = &enum.AddressStatus.DELIVERY_FAIL
				} else {
					address.Status = &enum.AddressStatus.DELIVERED
				}
			}

			// if address contain nil, draft status means trip is not complete yet
			if address.Status == nil ||
				address.Status == &enum.AddressStatus.DRAFT {
				isCompleteTrip = &enum.TripStatus.IN_PROCESS
			}
		}

		updateTripResult := model.TripDB.UpdateOne(bson.M{
			"trip_id": hubShippingOrderInfo.TripId,
		}, bson.M{
			"status":    isCompleteTrip,
			"addresses": trip.Addresses,
		})

		if updateTripResult.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không thể cập nhật chuyến cho tài xế",
			}
		}

	}

	current := time.Now()

	hubShippingOrderInfo.Status = input.Status
	hubShippingOrderInfo.Reason = input.Reason
	hubShippingOrderInfo.ActionTime = current.Unix()

	callback := request.Callback{
		ActionTime:              &current,
		Reason:                  input.Reason,
		SO:                      input.ReferenceCode,
		ReferenceCode:           input.ReferenceCode,
		FromName:                hubShippingOrderInfo.FromCustomerName,
		ToName:                  hubShippingOrderInfo.ToCustomerName,
		DriverId:                int(hubShippingOrderInfo.DriverID),
		DriverName:              hubShippingOrderInfo.DriverName,
		RescheduledPickupTime:   input.RescheduledPickupTime,
		RescheduledDeliveryTime: input.RescheduledDeliveryTime,
	}

	if hubShippingOrderInfo.ExtraInfo["pod"] != nil {
		callback.ExtraCallback = hubShippingOrderInfo.ExtraInfo
	}

	if hubShippingOrderInfo.ExtraInfo["pop"] != nil {
		callback.ExtraCallback = hubShippingOrderInfo.ExtraInfo
	}

	var notificationTitle, notificationDescription, targetScreen string
	switch *input.Status {
	case enum.HubShippingOrderStatus.WAIT_TO_STORING:
		callback.Status = &enum.TPLCallbackStatus.PICKED
		callback.StatusName = "Lấy hàng thành công"
		callback.TPLStatus = string(enum.TPLCallbackStatus.PICKED)
		callback.TPLStatusName = "Lấy hàng thành công"
		notificationTitle = "Lấy hàng thành công."
		notificationDescription = fmt.Sprintf("Chúc mừng bạn đã lấy đơn hàng %s thành công. Vui lòng mang hàng về nhập kho.", input.ReferenceCode)
		targetScreen = "OrderDetail"
		break
	case enum.HubShippingOrderStatus.STORING:
		callback.Status = &enum.TPLCallbackStatus.STORING
		callback.StatusName = "Đã nhập kho/HUB " + hub.Name + "."
		callback.TPLStatus = string(enum.TPLCallbackStatus.STORING)
		callback.TPLStatusName = "Đã nhập kho/HUB " + hub.Name + "."
		callback.StatusName = "Đã nhập kho/HUB " + hub.Name + "."
		break
	case enum.HubShippingOrderStatus.READY_TO_PICK:
		callback.Status = &enum.TPLCallbackStatus.READY_TO_PICK
		callback.StatusName = "Chuẩn bị lấy hàng"
		callback.TPLStatus = string(enum.TPLCallbackStatus.PICKING)
		callback.TPLStatusName = "Chuẩn bị lấy hàng"
		break
	case enum.HubShippingOrderStatus.PICKING:
		callback.Status = &enum.TPLCallbackStatus.PICKING
		callback.StatusName = "Đang lấy hàng"
		callback.TPLStatus = string(enum.TPLCallbackStatus.PICKING)
		callback.TPLStatusName = "Đang lấy hàng"
		break
	case enum.HubShippingOrderStatus.DELIVERING:
		callback.Status = &enum.TPLCallbackStatus.DELIVERING
		callback.StatusName = "Đang giao hàng."
		callback.TPLStatus = string(enum.TPLCallbackStatus.DELIVERING)
		callback.TPLStatusName = "Đang giao hàng."
		break
	case enum.HubShippingOrderStatus.DELIVERED:
		callback.Status = &enum.TPLCallbackStatus.DELIVERED
		callback.StatusName = "Giao hàng thành công."
		callback.TPLStatus = string(enum.TPLCallbackStatus.DELIVERED)
		callback.TPLStatusName = "Giao hàng thành công."
		callback.ExtraCallback = hubShippingOrderInfo.ExtraInfo
		// Update delivered time directly to db
		hubShippingOrderInfo.DeliveredTime = current.Unix()
		notificationTitle = "Giao hàng thành công."
		notificationDescription = fmt.Sprintf("Chúc mừng bạn đã giao đơn %s thành công. Hãy tiếp tục giao những đơn hàng khác nhé!", input.ReferenceCode)
		targetScreen = "OrderDetail"

		if shippingOrder.ShippingType != nil &&
			*shippingOrder.ShippingType == enum.ShippingOrderType.CS {
			callback.Status = &enum.TPLCallbackStatus.COMPLETED
			callback.StatusName = "Hoàn thành nhiệm vụ."
			callback.TPLStatus = string(enum.TPLCallbackStatus.COMPLETED)
			callback.TPLStatusName = "Hoàn thành nhiệm vụ."
			// Update completed time directly to db
			hubShippingOrderInfo.DeliveredTime = current.Unix()
			hubShippingOrderInfo.Status = &enum.HubShippingOrderStatus.COMPLETED
			notificationTitle = "Hoàn thành nhiệm vụ."
			notificationDescription = fmt.Sprintf("Chúc mừng bạn đã hoàn thành nhiệm vụ %s", input.ReferenceCode)
			targetScreen = "RiderCS"
		}
		break
	case enum.HubShippingOrderStatus.DELIVERY_FAIL:
		callback.Status = &enum.TPLCallbackStatus.DELIVERY_FAIL
		callback.StatusName = "Giao hàng thất bại."
		callback.TPLStatus = string(enum.TPLCallbackStatus.DELIVERY_FAIL)
		callback.TPLStatusName = "Giao hàng thất bại."
		notificationTitle = "Giao hàng thất bại."
		notificationDescription = fmt.Sprintf("Thật tiếc đơn hàng %s không được giao thành công. Vui lòng mang hàng về và nhập kho để giao lại lần sau.", input.ReferenceCode)
		targetScreen = "OrderDetail"
		callback.ReasonCode = input.ReasonCode
		callback.Images = input.Images
		break
	case enum.HubShippingOrderStatus.PICK_FAIL:
		callback.Status = &enum.TPLCallbackStatus.PICK_FAIL
		callback.StatusName = "Lấy hàng thất bại."
		callback.TPLStatus = string(enum.TPLCallbackStatus.DELIVERY_FAIL)
		callback.TPLStatusName = "Lấy hàng thất bại."
		notificationTitle = "Lấy hàng thất bại."
		notificationDescription = fmt.Sprintf("Thật tiếc đơn hàng %s không được lấy thành công. Cảm ơn bạn!", input.ReferenceCode)
		targetScreen = "OrderDetail"
		callback.ReasonCode = input.ReasonCode
		callback.Images = input.Images
		break
	case enum.HubShippingOrderStatus.RETURN:
		callback.Status = &enum.TPLCallbackStatus.RETURN
		callback.StatusName = "Trả hàng."
		callback.TPLStatus = string(enum.TPLCallbackStatus.RETURN)
		callback.TPLStatusName = "Trả hàng."
		break
	case enum.HubShippingOrderStatus.RETURNING:
		callback.Status = &enum.TPLCallbackStatus.RETURNING
		callback.StatusName = "Đang trả hàng."
		callback.TPLStatus = string(enum.TPLCallbackStatus.RETURNING)
		callback.TPLStatusName = "Đang trả hàng."
		break
	case enum.HubShippingOrderStatus.DAMAGE:
		callback.Status = &enum.TPLCallbackStatus.DAMAGE
		callback.StatusName = "Hàng bị hư hỏng."
		callback.TPLStatus = string(enum.TPLCallbackStatus.DAMAGE)
		callback.TPLStatusName = "Hàng bị hư hỏng."
		break
	case enum.HubShippingOrderStatus.CANCEL_DELIVERY:
		callback.Status = &enum.TPLCallbackStatus.CANCEL_DELIVERY
		callback.StatusName = "Hủy giao hàng."
		callback.TPLStatus = string(enum.TPLCallbackStatus.CANCEL_DELIVERY)
		callback.TPLStatusName = "Hủy giao hàng."
		break
	case enum.HubShippingOrderStatus.LOST:
		callback.Status = &enum.TPLCallbackStatus.LOST
		callback.StatusName = "Mất hàng."
		callback.TPLStatusName = "Mất hàng."
		callback.TPLStatus = string(enum.TPLCallbackStatus.LOST)
		callback.Reason = "Mất hàng."
		break
	}

	if shippingOrder.ShippingType == nil {
		callback.Type = &enum.ShippingOrderType.DELIVERY
	} else {
		callback.Type = shippingOrder.ShippingType
	}

	listHubOrderProductivityTypes := listHubShippingOrderTypes()
	if ContainsHubOrderTypes(listHubOrderProductivityTypes, hubShippingOrderInfo.Type, hubShippingOrderInfo.SubType) &&
		(*hubShippingOrderInfo.Status == enum.HubShippingOrderStatus.DELIVERED ||
			*hubShippingOrderInfo.Status == enum.HubShippingOrderStatus.WAIT_TO_STORING ||
			*hubShippingOrderInfo.Status == enum.HubShippingOrderStatus.PICK_FAIL ||
			*hubShippingOrderInfo.Status == enum.HubShippingOrderStatus.DELIVERY_FAIL) &&
		hubShippingOrderInfo.DriverID != 0 {
		driverProductivity := productivity.DriverProductivity{
			DriverID:      hubShippingOrderInfo.DriverID,
			ReferenceCode: hubShippingOrderInfo.ReferenceCode,
			HubCode:       hubShippingOrderInfo.HUBCode,
			Type:          hubShippingOrderInfo.Type,
			SubType:       hubShippingOrderInfo.SubType,
			Status:        hubShippingOrderInfo.Status,
			ActionTime:    current.Unix(),
		}

		if *hubShippingOrderInfo.Status == enum.HubShippingOrderStatus.DELIVERED {
			driverProductivity.ProductivityAction = enum.ProductivityAction.DELIVERED
		}

		if *hubShippingOrderInfo.Status == enum.HubShippingOrderStatus.WAIT_TO_STORING {
			driverProductivity.ProductivityAction = enum.ProductivityAction.PICKED
		}

		if *hubShippingOrderInfo.Status == enum.HubShippingOrderStatus.PICK_FAIL {
			driverProductivity.ProductivityAction = enum.ProductivityAction.PICK_FAIL
		}

		if *hubShippingOrderInfo.Status == enum.HubShippingOrderStatus.DELIVERY_FAIL {
			driverProductivity.ProductivityAction = enum.ProductivityAction.DELIVERY_FAIL
		}

		AddProductivityLog(driverProductivity, *shippingOrder)
	}

	if *input.Status != enum.HubShippingOrderStatus.COD_COLLECTED {
		// old callback
		if callback.Status != nil &&
			*callback.Status != enum.TPLCallbackStatus.PICKING &&
			*callback.Status != enum.TPLCallbackStatus.PICKED &&
			*callback.Status != enum.TPLCallbackStatus.PICK_FAIL &&
			*callback.Status != enum.TPLCallbackStatus.READY_TO_PICK &&
			*callback.Status != enum.TPLCallbackStatus.LOST &&
			*callback.Type != enum.ShippingOrderType.CS &&
			*callback.Type != enum.ShippingOrderType.EO {
			_ = syncData.PushCreateTPLCallbackQueue(callback, callback.SO)
		} else {
			// new callback
			shippingOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
				"reference_code": hubShippingOrderInfo.ReferenceCode,
			})
			if shippingOrderRaw.Status != common.APIStatus.Ok {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Đơn giao hàng không tồn tại",
				}
			}
			shippingOrder := shippingOrderRaw.Data.([]*model.ShippingOrder)[0]
			now := time.Now()
			// update shipping order status
			updater := bson.M{
				"status":            callback.Status,
				"action_time":       now.Unix(),
				"last_updated_time": &now,
				"checkin_num_pack":  shippingOrder.NumPackage,
			}

			if callback.RescheduledPickupTime != 0 {
				updater["rescheduled_pickup_time"] = callback.RescheduledPickupTime
			}

			if callback.RescheduledDeliveryTime != 0 {
				updater["rescheduled_delivery_time"] = callback.RescheduledDeliveryTime
			}

			if input.Reason != "" {
				updater["reason"] = input.Reason
			}

			if hubShippingOrderInfo.ExtraInfo != nil {
				updater["extra_info"] = utils.MergeMaps(shippingOrder.ExtraInfo, hubShippingOrderInfo.ExtraInfo)
			}

			if hubShippingOrderInfo.Status != nil &&
				*hubShippingOrderInfo.Status == enum.HubShippingOrderStatus.WAIT_TO_STORING {
				updater["picked_time"] = now.Unix()
			}

			updateResult := model.ShippingOrderDB.UpdateOne(bson.M{
				"reference_code": hubShippingOrderInfo.ReferenceCode,
			}, updater)

			if updateResult.Status != common.APIStatus.Ok {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Không thể cập nhật trạng thái đơn hàng",
				}
			}

			if shippingOrder.ShippingType != nil &&
				*shippingOrder.ShippingType == enum.ShippingOrderType.EO &&
				*callback.Status == enum.TPLCallbackStatus.PICKED &&
				hubShippingOrderInfo.IsAutoAssign != nil &&
				*hubShippingOrderInfo.IsAutoAssign {
				if shippingOrder.FirstMileHubCode != "" &&
					shippingOrder.LastMileHubCode != "" &&
					shippingOrder.LastMileHubCode == shippingOrder.FirstMileHubCode &&
					shippingOrder.CurrentHub == input.HUBCode {
					hubShippingOrderInfo.Type = &enum.HubOrderType.DELIVERY
					hubShippingOrderInfo.Status = &enum.HubShippingOrderStatus.WAIT_TO_DELIVERY
				} else {
					hubShippingOrderInfo.Type = &enum.HubOrderType.TRANSPORTING
				}
			}

			if shippingOrder.PartnerCode != "" {
				callback.PartnerCode = shippingOrder.PartnerCode
				callback.NumPackage = shippingOrder.NumPackage
				err := syncData.PushSendCallbackQueue(callback, input.ReferenceCode)
				if err != nil {
					return &common.APIResponse{
						Status:  common.APIStatus.Invalid,
						Message: err.Error(),
					}
				}
			}

			_ = syncData.PushCreateTPLCallbackQueue(callback, callback.SO)
		}
	}

	updateShipping := model.HUBShippingOrderDB.UpdateOne(
		bson.M{
			"reference_code": input.ReferenceCode,
			"hub_code":       input.HUBCode,
		},
		hubShippingOrderInfo,
		&options.FindOneAndUpdateOptions{
			Upsert:         &enum.False,
			ReturnDocument: &afterOption,
		})

	if updateShipping.Status != common.APIStatus.Ok {
		return updateShipping
	}

	if notificationTitle != "" {
		defer func() {
			driver := GetAccountInfo(hubShippingOrderInfo.DriverID, "", 0, 0)[0]
			if driver != nil && driver.AccountID > 0 {
				_ = client.Services.NotificationClient.SendNotification(
					notificationTitle,
					notificationDescription,
					[]string{driver.Username},
					targetScreen,
					[]string{hubShippingOrderInfo.ReferenceCode})

			}
		}()
	}

	hubOrder := updateShipping.Data.([]*model.HubShippingOrder)[0]
	if *input.Status == enum.HubShippingOrderStatus.DELIVERED {
		if hubOrder.ToAddressID != 0 {
			_ = model.ShippingAddressDB.UpdateOne(bson.M{"_id": hubOrder.ToAddressID}, bson.M{"nearest_order": hubOrder.ReferenceCode})
		}
		if *hubOrder.Type != enum.HubOrderType.PICKUP &&
			*hubOrder.Type != enum.HubOrderType.CS {
			_, isNotOk := CreateReconcile(hubOrder, string(enum.ReconcileTypeRequest.RIDER_HUB))
			if isNotOk {
				retry_queue.PushRetryReconcile(retry_queue.RetryReconcile{
					HubOrder:      hubOrder,
					ReconcileType: string(enum.ReconcileTypeRequest.RIDER_HUB),
				}, hubOrder.ReferenceCode)
			}
		}
	}
	if *input.Status == enum.HubShippingOrderStatus.COD_COLLECTED {
		_ = UpdateReconcileCodCollected(hubOrder)
	}
	if *input.Status == enum.HubShippingOrderStatus.WAIT_TO_STORING &&
		hubOrder.FromAddressId != 0 {
		_ = model.ShippingAddressDB.UpdateOne(bson.M{"_id": hubOrder.FromAddressId}, bson.M{"nearest_order": hubOrder.ReferenceCode})
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật thành công",
		Data:    updateShipping.Data,
	}
}

// UpdateHubShippingOrder func
func UpdateHubShippingOrder(input *model.HubShippingOrder, updateBy int64) *common.APIResponse {
	if input.ReferenceCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã phiếu giao không được để trống",
		}
	}

	if input.HUBCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã hub không được để trống",
		}
	}

	now := time.Now()
	notificationTitle := ""
	notificationDescription := ""
	var targetScreen = ""
	if input.Status != nil {
		switch *input.Status {
		case enum.HubShippingOrderStatus.DELIVERED:
			notificationTitle = "Giao hàng thành công."
			notificationDescription = fmt.Sprintf("Chúc mừng bạn đã giao đơn %s thành công. Hãy tiếp tục giao những đơn hàng khác nhé!", input.ReferenceCode)
			targetScreen = "JourneyDelivery"
			input.DeliveredTime = now.Unix()
			break
		case enum.HubShippingOrderStatus.COMPLETED:
			input.CompletedTime = now.Unix()
			break
		case enum.HubShippingOrderStatus.DELIVERY_FAIL:
			notificationTitle = "Giao hàng thất bại."
			notificationDescription = fmt.Sprintf("Thật tiếc đơn hàng %s không được giao thành công. Vui lòng mang hàng về và nhập kho để giao lại lần sau.", input.ReferenceCode)
			targetScreen = "JourneyDelivery"
			break
		}
	}

	input.LastUpdatedBy = strconv.FormatInt(updateBy, 10)
	afterOption := options.After
	updateShipping := model.HUBShippingOrderDB.UpdateOne(
		bson.M{
			"reference_code": input.ReferenceCode,
			"hub_code":       input.HUBCode,
		},
		input,
		&options.FindOneAndUpdateOptions{
			Upsert:         &enum.False,
			ReturnDocument: &afterOption,
		})

	if updateShipping.Status != common.APIStatus.Ok {
		return updateShipping
	}

	hubOrder := updateShipping.Data.([]*model.HubShippingOrder)[0]
	if input.Status != nil &&
		*input.Status == enum.HubShippingOrderStatus.DELIVERED &&
		hubOrder.ToAddressID != 0 {
		_ = model.ShippingAddressDB.UpdateOne(bson.M{"_id": hubOrder.ToAddressID}, bson.M{"nearest_order": hubOrder.ReferenceCode})
	}

	if notificationTitle != "" {
		defer func() {
			driver := GetAccountInfo(input.DriverID, "", 0, 0)[0]
			if driver != nil && driver.AccountID > 0 {
				_ = client.Services.NotificationClient.SendNotification(
					notificationTitle,
					notificationDescription,
					[]string{driver.Username},
					targetScreen,
					[]string{hubOrder.ReferenceCode})
			}
		}()
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật thành công",
	}
}

// DeleteHubOrder func
func DeleteHubOrder(input *model.HubShippingOrder) *common.APIResponse {
	return model.HUBShippingOrderDB.Delete(bson.M{
		"reference_code": input.ReferenceCode,
		"hub_code":       input.HUBCode,
	})
}

// AssignHubShippingOrder func
func AssignHubShippingOrder(
	listReferenceCode []string,
	driverName, hubCode string,
	driverId int, accountId int64,
	isAutoAssign *bool) *common.APIResponse {
	if len(listReferenceCode) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Danh sách phiếu không được để trống.",
			ErrorCode: "EMPTY_REFERENCE_CODE",
		}
	}

	if driverId <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Nhân viên không được để trống.",
			ErrorCode: "EMPTY_DRIVER_ID",
		}
	}

	employee := GetAccountInfo(int64(driverId), "", 0, 0)[0]
	if employee == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Nhân viên không hợp lệ",
			ErrorCode: "INVALID_DRIVER_ID",
		}
	}

	roles, err := client.Services.HrmClient.QueryEmployeeRoles(model.HrmAccount{
		Type:     "EMPLOYEE",
		Username: employee.Username,
	})

	if err != nil || roles == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Chức vụ của nhân viên không hợp lệ",
			ErrorCode: "INVALID_ROLES",
		}
	}

	isValidRole := false
	for _, role := range roles.UserRole {
		if role.DepartmentCode != nil && *role.DepartmentCode == hubCode {
			isValidRole = true
		}
	}
	if !isValidRole {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Chức vụ của nhân viên không hợp lệ",
			ErrorCode: "INVALID_ROLES",
		}
	}

	var pickupReferenceCode []string
	var deliveryReferenceCode []string
	var returnReferenceCode []string
	var customerSupportReferenceCode []string
	var hubOrders []*model.HubShippingOrder
	var productivityHubOrders []*model.HubShippingOrder
	var now = time.Now()

	for _, ref := range listReferenceCode {
		hubOrderQuery := model.HUBShippingOrderDB.QueryOne(bson.M{
			"reference_code": ref,
			"hub_code":       hubCode,
		})

		if hubOrderQuery.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Không tìm thấy đơn: " + ref,
				ErrorCode: "NOT_FOUND_HUB_ORDER",
			}
		}

		hubOrder := hubOrderQuery.Data.([]*model.HubShippingOrder)[0]
		if hubOrder.Type == nil {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Kiểu giao hàng của đơn: " + ref + " đang trống",
				ErrorCode: "INVALID_HUB_ORDER_TYPE",
			}
		}

		if hubOrder.MergeStatus != nil && *hubOrder.MergeStatus == enum.MergeStatus.WAIT_TO_MERGE {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Đơn: " + ref + " đang chờ được gộp",
				ErrorCode: "NEED_MERGE_ORDER",
			}
		}

		if hubOrder.SubType != nil && *hubOrder.SubType == enum.SubType.INTERNAL_TRANS {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Không thể gán tài xế cho đơn luân chuyển nội bội",
				ErrorCode: "INVALID_HUB_ORDER_TYPE",
			}
		}

		if *hubOrder.Type != enum.HubOrderType.PICKUP &&
			hubOrder.SubType != nil &&
			*hubOrder.SubType == enum.SubType.FMPO {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Không thể gán tài xế giao hàng cho đơn lấy hàng PO/PGH",
				ErrorCode: "INVALID_HUB_ORDER_TYPE",
			}
		}

		if *hubOrder.Type == enum.HubOrderType.PICKUP {
			if hubOrder.Status != nil &&
				(*hubOrder.Status == enum.HubShippingOrderStatus.STORING ||
					*hubOrder.Status == enum.HubShippingOrderStatus.DONE_TRANSPORTING ||
					*hubOrder.Status == enum.HubShippingOrderStatus.COMPLETED) {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   "Không thể gán tài xế cho đơn lấy đã lưu kho",
					ErrorCode: "INVALID_HUB_ORDER_TYPE",
				}
			}
			pickupReferenceCode = append(pickupReferenceCode, ref)
			hubOrder.ProductivityAction = &enum.ProductivityAction.ASSIGN_PICK
			hubOrder.Status = &enum.HubShippingOrderStatus.READY_TO_PICK
			productivityHubOrders = append(productivityHubOrders, hubOrder)
		} else if *hubOrder.Type == enum.HubOrderType.RETURN {
			returnReferenceCode = append(returnReferenceCode, ref)
		} else if *hubOrder.Type == enum.HubOrderType.TRANSPORTING ||
			*hubOrder.Type == enum.HubOrderType.DELIVERY {
			deliveryReferenceCode = append(deliveryReferenceCode, ref)
			hubOrder.ProductivityAction = &enum.ProductivityAction.ASSIGN_DELIVERY
			hubOrder.Status = &enum.HubShippingOrderStatus.WAIT_TO_DELIVERY
			productivityHubOrders = append(productivityHubOrders, hubOrder)
			// TODO: Currently, only support route opt for TRANSPORTING and DELIVERY, add PICKUP latter
			hubOrders = append(hubOrders, hubOrder)
		} else if *hubOrder.Type == enum.HubOrderType.CS {
			customerSupportReferenceCode = append(customerSupportReferenceCode, ref)
		}

		verifyErr := VerifyShippingAddress(hubOrder)
		if verifyErr != nil {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   verifyErr.Message,
				ErrorCode: "INVALID_ADDRESS",
			}
		}

		getHub := model.HubDB.QueryOne(bson.M{"code": hubOrder.HUBCode})
		if getHub.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: getHub.Message,
			}
		}
		hub := getHub.Data.([]*model.Hub)[0]
		if hubOrder.CODAmount > 0 &&
			*hubOrder.Type != enum.HubOrderType.PICKUP &&
			hub.LimitStatus == "ACTIVE" {

			checkExist := model.AccountDB.QueryOne(bson.M{
				"account_id": driverId,
				"hub_code":   hubCode,
			})
			if checkExist.Status != common.APIStatus.Ok {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					ErrorCode: "NOT_FOUND_EMPLOYEE",
					Message:   "Không tìm thấy nhân viên",
				}
			}
			loc, _ := time.LoadLocation(conf.Config.TimeZoneAsiaBangkok)
			limitAmount := hub.LimitAmountByWeekday[time.Now().In(loc).Weekday().String()]
			account := checkExist.Data.([]*model.Account)[0]
			if hub.TotalReceivedAmount >= limitAmount {
				return limitErrorResponse("Vượt quá hạn mức của HUB, chỉ có thể gán tài xế cho đơn 0 VND")
			}
			// Assign sẽ trừ cộng thêm hạn mức hiện tại
			if account.LimitAmount < 1 {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					ErrorCode: "INVALID_LIMIT_TO_ASSIGN_ORDER",
					Message:   "Tài khoản này không có hạn mức để nhận đơn hàng",
				}
			}
		}
	}

	if len(deliveryReferenceCode) > 0 {
		updateResult := model.HUBShippingOrderDB.UpdateMany(
			bson.M{
				"reference_code": bson.M{"$in": deliveryReferenceCode},
				"hub_code":       hubCode,
			},
			bson.M{
				"driver_id":         driverId,
				"driver_name":       driverName,
				"status":            string(enum.HubShippingOrderStatus.WAIT_TO_DELIVERY),
				"updated_by":        accountId,
				"last_updated_time": now,
			})

		if updateResult.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Không thể gán tài xế:" + updateResult.Message,
				ErrorCode: "INVALID_DRIVER_ASSIGNMENT",
			}
		}

		_ = syncData.PushAssignTPLCallbackQueue(&request.AssignCallback{
			ListReferenceCode: listReferenceCode,
			AccountId:         driverId,
			AccountName:       driverName,
		}, hubCode)

		// SendNotification func
		defer func() {
			driver := GetAccountInfo(int64(driverId), "", 0, 0)[0]
			if driver != nil && driver.AccountID > 0 {
				_ = client.Services.NotificationClient.SendNotification(
					"Đơn giao hàng mới.",
					fmt.Sprintf("Bạn có %d đơn giao hàng mới. Vui lòng kiểm tra và nhận hàng đi giao.", len(deliveryReferenceCode)),
					[]string{driver.Username},
					"JourneyDelivery",
					deliveryReferenceCode)
			}
		}()

		_, prepareErr := PrepareTripForHubOrder(hubOrders, hubCode, driverId)
		if prepareErr != nil {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Cant create trip for driver",
				ErrorCode: "CANT_CREATE_TRIP",
			}
		}
	}

	if len(pickupReferenceCode) > 0 {
		updateResult := model.HUBShippingOrderDB.UpdateMany(
			bson.M{
				"reference_code": bson.M{"$in": pickupReferenceCode},
				"hub_code":       hubCode,
			},
			bson.M{
				"driver_id":         driverId,
				"driver_name":       driverName,
				"status":            string(enum.HubShippingOrderStatus.READY_TO_PICK),
				"updated_by":        accountId,
				"last_updated_time": now,
				"is_auto_assign":    isAutoAssign,
			})

		if updateResult.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Không thể gán tài xế:" + updateResult.Message,
				ErrorCode: "INVALID_DRIVER_ASSIGNMENT",
			}
		}
		//Send pickup notify
		defer func() {
			driver := GetAccountInfo(int64(driverId), "", 0, 0)[0]
			if driver != nil && driver.AccountID > 0 {
				_ = client.Services.NotificationClient.SendNotification(
					"Đơn lấy hàng mới.",
					fmt.Sprintf("Bạn có %d đơn lấy hàng mới. Vui lòng xác nhận và di chuyển tới điểm lấy hàng.", len(pickupReferenceCode)),
					[]string{driver.Username},
					"JourneyPick",
					pickupReferenceCode)
			}
		}()

		callbackData := &request.AssignCallback{
			ListReferenceCode: pickupReferenceCode,
			AccountId:         driverId,
			AccountName:       driverName,
		}

		err := syncData.PushAssignShipperCallbackQueue(callbackData, hubCode)
		if err != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không thể hoàn thành gán tài xế",
			}
		}
	}

	if len(returnReferenceCode) > 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Gán tài xế cho đơn trả hàng hiện chưa được hỗ trợ",
		}
	}

	if len(customerSupportReferenceCode) > 0 {
		updateResult := model.HUBShippingOrderDB.UpdateMany(
			bson.M{
				"reference_code": bson.M{"$in": customerSupportReferenceCode},
				"hub_code":       hubCode,
			},
			bson.M{
				"driver_id":         driverId,
				"driver_name":       driverName,
				"status":            string(enum.HubShippingOrderStatus.WAIT_TO_DELIVERY),
				"updated_by":        accountId,
				"last_updated_time": time.Now(),
			})

		if updateResult.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Không thể gán tài xế:" + updateResult.Message,
				ErrorCode: "INVALID_DRIVER_ASSIGNMENT",
			}
		}

		// SendNotification func
		defer func() {
			driver := GetAccountInfo(int64(driverId), "", 0, 0)[0]
			if driver != nil && driver.AccountID > 0 {
				_ = client.Services.NotificationClient.SendNotification(
					"Đơn chăm sóc khách hàng mới",
					fmt.Sprintf("Bạn có %d nhiệm vụ chăm sóc khách hàng mới.", len(customerSupportReferenceCode)),
					[]string{driver.Username},
					"RiderCS",
					customerSupportReferenceCode)
			}
		}()
	}

	if len(productivityHubOrders) > 0 {
		for _, hubOrder := range productivityHubOrders {
			_ = productivity.PushCalculateProductivity(productivity.DriverProductivity{
				DriverID:           int64(driverId),
				ReferenceCode:      hubOrder.ReferenceCode,
				HubCode:            hubOrder.HUBCode,
				Status:             hubOrder.Status,
				Type:               hubOrder.Type,
				SubType:            hubOrder.SubType,
				ActionTime:         now.Unix(),
				ProductivityAction: *hubOrder.ProductivityAction,
			}, hubOrder.ReferenceCode)
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Gán tài xế thành công",
	}
}

func VerifyShippingAddress(hubOrder *model.HubShippingOrder) *common.Error {
	if hubOrder.Type == nil {
		return &common.Error{
			Message: "Loại đơn giao hàng không được để trống",
		}
	}

	address := &model.ShippingAddress{}

	if *hubOrder.Type == enum.HubOrderType.PICKUP || *hubOrder.Type == enum.HubOrderType.RETURN {
		countAddress := model.ShippingAddressDB.Count(bson.M{
			"ward_code":     hubOrder.FromWardCode,
			"customer_code": hubOrder.FromCustomerCode,
		})

		if countAddress.Total > 0 {
			getLocation := model.ShippingAddressDB.Query(&bson.M{
				"ward_code":     hubOrder.FromWardCode,
				"customer_code": hubOrder.FromCustomerCode,
			}, 0, countAddress.Total, nil)

			if getLocation.Status == common.APIStatus.Ok {
				savedLocation := getLocation.Data.([]*model.ShippingAddress)
				for _, loc := range savedLocation {
					if utils.ConvertToRawText(loc.CustomerAddress) == utils.ConvertToRawText(hubOrder.FromCustomerAddress) {
						hubOrder.FromLatitude = loc.Latitude
						hubOrder.FromLongitude = loc.Longitude
						if loc.Verified != nil {
							hubOrder.NeedVerifyAddress = *loc.Verified
						}
						hubOrder.FromAddressId = loc.ID
						break
					}
				}
			}
		}

		if hubOrder.FromLongitude == 0 || hubOrder.FromLatitude == 0 {
			fullAddress := fmt.Sprintf("%s, %s, %s, %s", hubOrder.FromCustomerAddress, hubOrder.FromWardName, hubOrder.FromDistrictName, hubOrder.FromProvinceName)
			hubOrder.FromLatitude, hubOrder.FromLongitude, _ = client.Services.GeocodeClient.GetLatLng(fullAddress)
		}

		address.CustomerCode = hubOrder.FromCustomerCode
		address.CustomerName = hubOrder.FromCustomerName
		address.CustomerAddress = hubOrder.FromCustomerAddress
		address.CustomerEmail = hubOrder.FromCustomerEmail
		address.CustomerPhone = hubOrder.FromCustomerPhone
		address.WardCode = hubOrder.FromWardCode
		address.WardName = hubOrder.FromWardName
		address.DistrictName = hubOrder.FromDistrictName
		address.DistrictCode = hubOrder.FromDistrictCode
		address.ProvinceCode = hubOrder.FromProvinceCode
		address.ProvinceName = hubOrder.FromProvinceName
		address.Latitude = hubOrder.FromLatitude
		address.Longitude = hubOrder.FromLongitude
		address.HubOrderType = hubOrder.Type
		address.NearestOrder = hubOrder.ReferenceCode
	}

	if *hubOrder.Type == enum.HubOrderType.TRANSPORTING ||
		*hubOrder.Type == enum.HubOrderType.DELIVERY ||
		*hubOrder.Type == enum.HubOrderType.CS {
		countAddress := model.ShippingAddressDB.Count(bson.M{
			"ward_code":     hubOrder.ToWardCode,
			"customer_code": hubOrder.ToCustomerCode,
		})

		if countAddress.Total > 0 {
			getLocation := model.ShippingAddressDB.Query(&bson.M{
				"ward_code":     hubOrder.ToWardCode,
				"customer_code": hubOrder.ToCustomerCode,
			}, 0, countAddress.Total, nil)

			if getLocation.Status == common.APIStatus.Ok {
				savedLocation := getLocation.Data.([]*model.ShippingAddress)
				for _, loc := range savedLocation {
					if utils.ConvertToRawText(loc.CustomerAddress) == utils.ConvertToRawText(hubOrder.ToCustomerAddress) {
						hubOrder.ToLatitude = loc.Latitude
						hubOrder.ToLongitude = loc.Longitude
						if loc.Verified != nil {
							hubOrder.NeedVerifyAddress = *loc.Verified
						}
						hubOrder.ToAddressID = loc.ID
						break
					}
				}
			}
		}

		if hubOrder.ToLongitude == 0 || hubOrder.ToLatitude == 0 {
			fullAddress := fmt.Sprintf("%s, %s, %s, %s", hubOrder.ToCustomerAddress, hubOrder.ToWardName, hubOrder.ToDistrictName, hubOrder.ToProvinceName)
			hubOrder.ToLatitude, hubOrder.ToLongitude, _ = client.Services.GeocodeClient.GetLatLng(fullAddress)
		}

		address.CustomerCode = hubOrder.ToCustomerCode
		address.CustomerName = hubOrder.ToCustomerName
		address.CustomerAddress = hubOrder.ToCustomerAddress
		address.CustomerEmail = hubOrder.ToCustomerEmail
		address.CustomerPhone = hubOrder.ToCustomerPhone
		address.WardCode = hubOrder.ToWardCode
		address.WardName = hubOrder.ToWardName
		address.DistrictName = hubOrder.ToDistrictName
		address.DistrictCode = hubOrder.ToDistrictCode
		address.ProvinceCode = hubOrder.ToProvinceCode
		address.ProvinceName = hubOrder.ToProvinceName
		address.Latitude = hubOrder.ToLatitude
		address.Longitude = hubOrder.ToLongitude
		address.HubOrderType = hubOrder.Type
		address.NearestOrder = hubOrder.ReferenceCode
	}

	updateHubOrder := model.HUBShippingOrderDB.UpdateOne(bson.M{
		"reference_code": hubOrder.ReferenceCode,
		"hub_code":       hubOrder.HUBCode,
	}, hubOrder)

	if updateHubOrder.Status != common.APIStatus.Ok {
		return &common.Error{
			Message: updateHubOrder.Message,
		}
	}

	err := shippingAddress.Push(address, hubOrder.ReferenceCode)
	if err != nil {
		return &common.Error{
			Message: err.Error(),
		}
	}

	return nil
}

// CountHubShippingOrderByStatus func
func CountHubShippingOrderByStatus(query *request.HubShippingOrderQuery) *common.APIResponse {
	type CountShippingOrderByStatusModel struct {
		Status   string `json:"status,omitempty" bson:"_id,omitempty"`
		Quantity int64  `json:"quantity" bson:"total,omitempty"`
	}

	filter := bson.D{}
	now := time.Now()
	if query.ReferenceCode != "" {
		filter = append(filter, bson.E{
			Key:   "reference_code",
			Value: query.ReferenceCode,
		})
	}

	if len(query.References) > 0 {
		filter = append(filter, bson.E{
			Key: "references",
			Value: bson.M{
				"$in": query.References,
			},
		})
	}

	if len(query.ListReferenceCode) > 0 {
		filter = append(filter, bson.E{
			Key: "reference_code",
			Value: bson.M{
				"$in": query.ListReferenceCode,
			},
		})
	}

	if query.ParentReferenceCode != "" {
		filter = append(filter, bson.E{
			Key:   "parent_reference_code",
			Value: query.ParentReferenceCode,
		})
	}

	if query.HubCode != "" {
		filter = append(filter, bson.E{
			Key:   "hub_code",
			Value: query.HubCode,
		})
	}

	if query.MergeStatus != nil {
		filter = append(filter, bson.E{
			Key:   "merge_status",
			Value: query.MergeStatus,
		})
	}

	if len(query.MergeStatuses) > 0 {
		var t []bson.M
		for _, status := range query.MergeStatuses {
			if *status == enum.MergeStatus.DEFAULT {
				t = append(t, bson.M{
					"merge_status": bson.M{
						"$exists": false,
					},
				})
			} else {
				t = append(t, bson.M{
					"merge_status": *status,
				})
			}
		}
		filter = append(filter, bson.E{
			Key:   "$or",
			Value: t,
		})
	}

	if len(query.Tags) > 0 {
		filter = append(filter, bson.E{
			Key: "tags",
			Value: bson.M{
				"$in": query.Tags,
			},
		})
	}

	if len(query.Skus) > 0 {
		filter = append(filter, bson.E{
			Key: "products.sku",
			Value: bson.M{
				"$in": query.Skus,
			},
		})
	}

	if query.DriverId > 0 {
		filter = append(filter, bson.E{
			Key:   "driver_id",
			Value: query.DriverId,
		})
	}

	if query.TplCode != nil && *query.TplCode != "" {
		filter = append(filter, bson.E{
			Key:   "tpl_code",
			Value: *query.TplCode,
		})
	}

	if len(query.ListTplCode) > 0 {
		filter = append(filter, bson.E{
			Key: "tpl_code",
			Value: bson.M{
				"$in": query.ListTplCode,
			},
		})
	}

	if len(query.Types) > 0 {
		filter = append(filter, bson.E{
			Key: "type",
			Value: bson.M{
				"$in": query.Types,
			},
		})
	}

	if query.TrackingCode != "" {
		filter = append(filter, bson.E{
			Key:   "tracking_code",
			Value: query.TrackingCode,
		})
	}

	if len(query.ListTrackingCode) > 0 {
		filter = append(filter, bson.E{
			Key: "tracking_code",
			Value: bson.M{
				"$in": query.ListTrackingCode,
			},
		})
	}

	if query.ProvinceCode != "" {
		filter = append(filter, bson.E{
			Key:   "to_province_code",
			Value: query.ProvinceCode,
		})
	}

	if query.DistrictCode != "" {
		filter = append(filter, bson.E{
			Key:   "to_district_code",
			Value: query.DistrictCode,
		})
	}

	if query.WardCode != "" {
		filter = append(filter, bson.E{
			Key:   "to_ward_code",
			Value: query.WardCode,
		})
	}

	if len(query.ProvinceCodes) > 0 {
		filter = append(filter, bson.E{
			Key: "to_province_code",
			Value: bson.M{
				"$in": query.ProvinceCodes,
			},
		})

	}

	if len(query.DistrictCodes) > 0 {
		filter = append(filter, bson.E{
			Key: "to_district_code",
			Value: bson.M{
				"$in": query.DistrictCodes,
			},
		})
	}

	if len(query.WardCodes) > 0 {
		filter = append(filter, bson.E{
			Key: "to_ward_code",
			Value: bson.M{
				"$in": query.WardCodes,
			},
		})
	}

	if len(query.FromProvinceCodes) > 0 {
		filter = append(filter, bson.E{
			Key: "from_province_code",
			Value: bson.M{
				"$in": query.FromProvinceCodes,
			},
		})
	}

	if len(query.FromDistrictCodes) > 0 {
		filter = append(filter, bson.E{
			Key: "from_district_code",
			Value: bson.M{
				"$in": query.FromDistrictCodes,
			},
		})
	}

	if len(query.FromWardCodes) > 0 {
		filter = append(filter, bson.E{
			Key: "from_ward_code",
			Value: bson.M{
				"$in": query.FromWardCodes,
			},
		})
	}

	if query.FromTime > 0 && query.ToTime > 0 {
		filter = append(filter, bson.E{
			Key: "action_time",
			Value: bson.M{
				"$gte": query.FromTime,
				"$lte": query.ToTime,
			},
		})
	} else if query.FromTime > 0 && query.ToTime == 0 {
		filter = append(filter, bson.E{
			Key: "action_time",
			Value: bson.M{
				"$gte": query.FromTime,
			},
		})
	} else if query.FromTime == 0 && query.ToTime > 0 {
		filter = append(filter, bson.E{
			Key: "action_time",
			Value: bson.M{
				"$lte": query.ToTime,
			},
		})
	}

	if query.FromDeliveredTime > 0 && query.ToDeliveredTime > 0 {
		filter = append(filter, bson.E{
			Key: "delivered_time",
			Value: bson.M{
				"$gte": query.FromDeliveredTime,
				"$lte": query.ToDeliveredTime,
			},
		})
	} else if query.FromDeliveredTime > 0 && query.ToDeliveredTime == 0 {
		filter = append(filter, bson.E{
			Key: "delivered_time",
			Value: bson.M{
				"$gte": query.FromDeliveredTime,
			},
		})
	} else if query.FromDeliveredTime == 0 && query.ToDeliveredTime > 0 {
		filter = append(filter, bson.E{
			Key: "delivered_time",
			Value: bson.M{
				"$lte": query.ToDeliveredTime,
			},
		})
	}

	if query.FromCompletedTime > 0 && query.ToCompletedTime > 0 {
		filter = append(filter, bson.E{
			Key: "completed_time",
			Value: bson.M{
				"$gte": query.FromCompletedTime,
				"$lte": query.ToCompletedTime,
			},
		})
	} else if query.FromCompletedTime > 0 && query.ToCompletedTime == 0 {
		filter = append(filter, bson.E{
			Key: "completed_time",
			Value: bson.M{
				"$gte": query.FromCompletedTime,
			},
		})
	} else if query.FromCompletedTime == 0 && query.ToCompletedTime > 0 {
		filter = append(filter, bson.E{
			Key: "completed_time",
			Value: bson.M{
				"$lte": query.ToCompletedTime,
			},
		})
	}

	if query.FromCreatedTime > 0 && query.ToCreatedTime > 0 {
		fromCreatedTime := time.Unix(query.FromCreatedTime, 0)
		toTimeCreated := time.Unix(query.ToCreatedTime, 0)
		filter = append(filter, bson.E{
			Key: "created_time",
			Value: bson.M{
				"$gte": primitive.NewDateTimeFromTime(fromCreatedTime),
				"$lte": primitive.NewDateTimeFromTime(toTimeCreated),
			},
		})
	} else if query.FromCreatedTime > 0 && query.ToCreatedTime == 0 {
		fromCreatedTime := time.Unix(query.FromCreatedTime, 0)
		filter = append(filter, bson.E{
			Key: "created_time",
			Value: bson.M{
				"$gte": primitive.NewDateTimeFromTime(fromCreatedTime),
			},
		})
	} else if query.FromCreatedTime == 0 && query.ToCreatedTime > 0 {
		toTimeCreated := time.Unix(query.ToCreatedTime, 0)
		filter = append(filter, bson.E{
			Key: "created_time",
			Value: bson.M{
				"$lte": primitive.NewDateTimeFromTime(toTimeCreated),
			},
		})
	}

	if query.FromWarningLevel > 0 && query.ToWarningLevel > 0 {
		filter = append(filter, bson.E{
			Key: "late_lead_time_level",
			Value: bson.M{
				"$gte": query.FromWarningLevel,
				"$lte": query.ToWarningLevel,
			},
		})
	} else if query.FromWarningLevel > 0 && query.ToWarningLevel == 0 {
		filter = append(filter, bson.E{
			Key: "late_lead_time_level",
			Value: bson.M{
				"$gte": query.FromWarningLevel,
			},
		})
	} else if query.FromWarningLevel == 0 && query.ToWarningLevel > 0 {
		filter = append(filter, bson.E{
			Key: "late_lead_time_level",
			Value: bson.M{
				"$lte": query.ToWarningLevel,
			},
		})
	}

	if query.TransportCode != "" {
		filter = append(filter, bson.E{
			Key:   "extra_info.handoverCode",
			Value: query.TransportCode,
		})
	}

	if len(query.HubOrderTypes) > 0 {
		var t []bson.M
		for _, hubOrderType := range query.HubOrderTypes {
			if hubOrderType.SubType == "" {
				t = append(t, bson.M{
					"type": hubOrderType.Type,
					"sub_type": bson.M{
						"$exists": false,
					},
				})
			} else {
				t = append(t, bson.M{
					"type":     hubOrderType.Type,
					"sub_type": hubOrderType.SubType,
				})
			}
		}

		filter = append(filter, bson.E{
			Key:   "$or",
			Value: t,
		})
	}

	if query.LeadTimeQuery != nil {
		//Còn hạn: now <  deliveryLeadtime
		//Còn hạn 6h: now < deliveryLeadtime - 6h => deliveryLeadtime > now + 6h
		//Qúa hạn dưới 48h: deliveryLeadtime < now < deliveryLeadtime + 48h
		//Quá hạn 48h: deliveryLeadtime + 48h < now => deliveryLeadtime < now - 48h
		//Quá hạn 7 ngày: deliveryLeadtime  + 7d < now => deliveryLeadtime < now - 7d

		leadTimeFilter := bson.M{}
		if query.LeadTimeQuery.ToMultiplier > 0 {
			leadTimeFilter["$gte"] = now.Add(time.Second * time.Duration(-query.LeadTimeQuery.ToAddition))
		}
		if query.LeadTimeQuery.FromMultiplier > 0 {
			leadTimeFilter["$lte"] = now.Add(time.Second * time.Duration(-query.LeadTimeQuery.FromAddition))
		}
		filter = append(filter, bson.E{
			Key:   "delivery_lead_time",
			Value: leadTimeFilter,
		})
	}

	statuses := utils.EnumToStringSlice(*enum.HubShippingOrderStatus)

	if query.Status != nil {
		statuses = []string{string(*query.Status)}
	}

	if len(query.Statuses) > 0 {
		statuses = []string{}
		for _, status := range query.Statuses {
			statuses = append(statuses, string(*status))
		}
	}

	result := make([]*CountShippingOrderByStatusModel, len(statuses))
	wg := new(sync.WaitGroup)
	wg.Add(len(statuses))

	for i, status := range statuses {
		go func(index int, status string, result []*CountShippingOrderByStatusModel, wg *sync.WaitGroup) {
			defer wg.Done()
			copyFilter := make(bson.D, len(filter))
			copy(copyFilter, filter)
			copyFilter = append(copyFilter, bson.E{
				Key:   "status",
				Value: status,
			})
			// If filter satisfies the condition, we can use snapshot to get the count
			// Note that snapshot is only available for certain statuses at the moment in the past
			if len(copyFilter) == 2 &&
				utils.BsonDContains(copyFilter, "status") &&
				utils.BsonDContains(copyFilter, "hub_code") &&
				utils.StringSliceContain(HubOrderSnapshotStatuses, status) {
				snapshotResp := model.SnapshotDB.QueryOne(bson.M{
					"type": enum.SnapshotType.CACHE_ORDER_STATUS,
					"key":  fmt.Sprintf("hub_shipping_order_code_%s_status_%s", query.HubCode, status),
				})
				if snapshotResp.Status == common.APIStatus.Ok {
					snapshot := snapshotResp.Data.([]*model.Snapshot)[0]
					lastSnapshotTime := snapshot.SnapshotAt
					obj := primitive.NewObjectIDFromTimestamp(lastSnapshotTime)
					countResult := model.HUBShippingOrderDB.Count(bson.M{
						"status":   status,
						"hub_code": query.HubCode,
						"_id": bson.M{
							"$gt": obj,
						},
					})
					result[index] = &CountShippingOrderByStatusModel{
						Status:   status,
						Quantity: snapshot.Quantity + countResult.Total,
					}
					return
				}
			}

			if query.ReadPreference != nil && *query.ReadPreference == enum.ReadPreference.SECONDARY {
				countResult := model.HUBShippingOrderDB.SecondaryInstance.Count(copyFilter)
				result[index] = &CountShippingOrderByStatusModel{
					Status:   status,
					Quantity: countResult.Total,
				}
			} else {
				countResult := model.HUBShippingOrderDB.Count(copyFilter)
				result[index] = &CountShippingOrderByStatusModel{
					Status:   status,
					Quantity: countResult.Total,
				}
			}

		}(i, status, result, wg)
	}

	wg.Wait()

	if len(result) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy phiếu nào.",
			ErrorCode: "SHIPPING_ORDER_NOT_FOUND",
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Thông tin số lượng phiếu theo trạng thái.",
		Data:    result,
	}
}

// GetHubShippingOrder func
func GetHubShippingOrder(query *request.HubShippingOrderQuery, sort *request.FilterSort, offset, limit int64, getTotal bool) *common.APIResponse {
	filter := bson.M{}
	now := time.Now()
	if query.ReferenceCode != "" {
		filter["reference_code"] = query.ReferenceCode
	}

	if len(query.References) > 0 {
		filter["references"] = bson.M{
			"$in": query.References,
		}
	}

	if query.ParentReferenceCode != "" {
		filter["parent_reference_code"] = query.ParentReferenceCode
	}

	if query.ParentReceiveSessionCode != "" {
		filter["parent_receive_session_code"] = query.ParentReceiveSessionCode
	}

	if len(query.Tags) > 0 {
		if len(query.Tags) == 1 {
			filter["tags"] = query.Tags[0]
		} else {
			filter["tags"] = bson.M{
				"$in": query.Tags,
			}
		}

		filter["tags"] = bson.M{
			"$in": query.Tags,
		}
	}

	if query.MergeStatus != nil {
		filter["merge_status"] = *query.MergeStatus
	}

	if len(query.MergeStatuses) > 0 {
		filter["$or"] = []bson.M{}
		for _, status := range query.MergeStatuses {
			if *status == enum.MergeStatus.DEFAULT {
				filter["$or"] = append(filter["$or"].([]bson.M),
					bson.M{"merge_status": bson.M{
						"$exists": false,
					}})
				continue
			}
			filter["$or"] = append(filter["$or"].([]bson.M), bson.M{"merge_status": *status})
		}
	}

	if len(query.ListReferenceCode) > 0 {
		if len(query.ListReferenceCode) == 1 {
			filter["reference_code"] = query.ListReferenceCode[0]
		} else {
			filter["reference_code"] = bson.M{
				"$in": query.ListReferenceCode,
			}
		}
		filter["reference_code"] = bson.M{
			"$in": query.ListReferenceCode,
		}
	}

	if query.HubCode != "" {
		filter["hub_code"] = query.HubCode
	}

	if query.Status != nil && *query.Status != "" {
		filter["status"] = *query.Status
	}

	if len(query.Statuses) > 0 {
		if len(query.Statuses) == 1 {
			filter["status"] = query.Statuses[0]
		} else {
			filter["status"] = bson.M{
				"$in": query.Statuses,
			}
		}
		filter["status"] = bson.M{
			"$in": query.Statuses,
		}
	}

	if query.DriverId > 0 {
		filter["driver_id"] = query.DriverId
	}

	if query.TplCode != nil && *query.TplCode != "" {
		filter["tpl_code"] = *query.TplCode
	}

	if len(query.ListTplCode) > 0 {
		if len(query.ListTplCode) == 1 {
			filter["tpl_code"] = query.ListTplCode[0]
		} else {
			filter["tpl_code"] = bson.M{
				"$in": query.ListTplCode,
			}
		}
		filter["tpl_code"] = bson.M{
			"$in": query.ListTplCode,
		}
	}

	if query.TrackingCode != "" {
		filter["tracking_code"] = query.TrackingCode
	}

	if len(query.ListTrackingCode) > 0 {
		if len(query.ListTrackingCode) == 1 {
			filter["tracking_code"] = query.ListTrackingCode[0]
		} else {
			filter["tracking_code"] = bson.M{
				"$in": query.ListTrackingCode,
			}
		}
		filter["tracking_code"] = bson.M{
			"$in": query.ListTrackingCode,
		}
	}

	if len(query.Types) > 0 {
		if len(query.Types) == 1 {
			filter["type"] = query.Types[0]
		} else {
			filter["type"] = bson.M{
				"$in": query.Types,
			}
		}
		filter["type"] = bson.M{
			"$in": query.Types,
		}
	}

	if len(query.HubOrderTypes) > 0 {
		var t []bson.M
		for _, hubOrderType := range query.HubOrderTypes {
			if hubOrderType.SubType == "" {
				t = append(t, bson.M{
					"type": hubOrderType.Type,
					"sub_type": bson.M{
						"$exists": false,
					},
				})
			} else {
				t = append(t, bson.M{
					"type":     hubOrderType.Type,
					"sub_type": hubOrderType.SubType,
				})
			}
		}
		if len(t) == 1 {
			filter["type"] = t[0]["type"]
			filter["sub_type"] = t[0]["sub_type"]
		} else {
			filter["$or"] = t
		}
	}

	if query.ProvinceCode != "" {
		filter["to_province_code"] = query.ProvinceCode
	}

	if query.DistrictCode != "" {
		filter["to_district_code"] = query.DistrictCode
	}

	if query.WardCode != "" {
		filter["to_ward_code"] = query.WardCode
	}

	if len(query.ProvinceCodes) > 0 {
		filter["to_province_code"] = bson.M{"$in": query.ProvinceCodes}
	}

	if len(query.DistrictCodes) > 0 {
		filter["to_district_code"] = bson.M{"$in": query.DistrictCodes}
	}

	if len(query.WardCodes) > 0 {
		filter["to_ward_code"] = bson.M{"$in": query.WardCodes}
	}

	if len(query.FromProvinceCodes) > 0 {
		filter["from_province_code"] = bson.M{"$in": query.FromProvinceCodes}
	}

	if len(query.FromDistrictCodes) > 0 {
		filter["from_district_code"] = bson.M{"$in": query.FromDistrictCodes}
	}

	if len(query.FromWardCodes) > 0 {
		filter["from_ward_code"] = bson.M{"$in": query.FromWardCodes}
	}

	if query.FromTime > 0 && query.ToTime > 0 {
		filter["action_time"] = bson.M{
			"$gte": query.FromTime,
			"$lte": query.ToTime,
		}
	} else if query.FromTime > 0 && query.ToTime == 0 {
		filter["action_time"] = bson.M{
			"$gte": query.FromTime,
		}
	} else if query.FromTime == 0 && query.ToTime > 0 {
		filter["action_time"] = bson.M{
			"$lte": query.ToTime,
		}
	}

	if query.FromDeliveredTime > 0 && query.ToDeliveredTime > 0 {
		filter["delivered_time"] = bson.M{
			"$gte": query.FromDeliveredTime,
			"$lte": query.ToDeliveredTime,
		}
	} else if query.FromDeliveredTime > 0 && query.ToDeliveredTime == 0 {
		filter["delivered_time"] = bson.M{
			"$gte": query.FromDeliveredTime,
		}
	} else if query.FromDeliveredTime == 0 && query.ToDeliveredTime > 0 {
		filter["delivered_time"] = bson.M{
			"$lte": query.ToDeliveredTime,
		}
	}

	if query.FromCompletedTime > 0 && query.ToCompletedTime > 0 {
		filter["completed_time"] = bson.M{
			"$gte": query.FromCompletedTime,
			"$lte": query.ToCompletedTime,
		}
	} else if query.FromCompletedTime > 0 && query.ToCompletedTime == 0 {
		filter["completed_time"] = bson.M{
			"$gte": query.FromCompletedTime,
		}
	} else if query.FromCompletedTime == 0 && query.ToCompletedTime > 0 {
		filter["completed_time"] = bson.M{
			"$lte": query.ToCompletedTime,
		}
	}

	if query.FromCreatedTime > 0 && query.ToCreatedTime > 0 {
		fromCreatedTime := time.Unix(query.FromCreatedTime, 0)
		toTimeCreated := time.Unix(query.ToCreatedTime, 0)
		filter["created_time"] = bson.M{
			"$gte": primitive.NewDateTimeFromTime(fromCreatedTime),
			"$lte": primitive.NewDateTimeFromTime(toTimeCreated),
		}
	} else if query.FromCreatedTime > 0 && query.ToCreatedTime == 0 {
		fromCreatedTime := time.Unix(query.FromCreatedTime, 0)
		filter["created_time"] = bson.M{
			"$gte": primitive.NewDateTimeFromTime(fromCreatedTime),
		}
	} else if query.FromCreatedTime == 0 && query.ToCreatedTime > 0 {
		toTimeCreated := time.Unix(query.ToCreatedTime, 0)
		filter["created_time"] = bson.M{
			"$lte": primitive.NewDateTimeFromTime(toTimeCreated),
		}
	}

	if query.FromWarningLevel > 0 && query.ToWarningLevel > 0 {
		fromWarningLevel := query.FromWarningLevel
		toWarningLevel := query.ToWarningLevel
		filter["late_lead_time_level"] = bson.M{
			"$gte": fromWarningLevel,
			"$lte": toWarningLevel,
		}
	} else if query.FromWarningLevel > 0 && query.ToWarningLevel == 0 {
		fromWarningLevel := query.FromWarningLevel
		filter["late_lead_time_level"] = bson.M{
			"$gte": fromWarningLevel,
		}
	} else if query.FromWarningLevel == 0 && query.ToWarningLevel > 0 {
		toWarningLevel := query.ToWarningLevel
		filter["late_lead_time_level"] = bson.M{
			"$lte": toWarningLevel,
		}
	}

	if query.TransportCode != "" {
		filter["extra_info.handoverCode"] = query.TransportCode
	}

	if len(query.Skus) > 0 {
		filter["products.sku"] = bson.M{
			"$in": query.Skus,
		}
	}

	// Check cả ReferenceCode HOẶC ParentReferenceCode do khi nhập kho ở hub nguồn có thể  trả theo mã
	// SOBD123 (trả cả kiện) và SOBE123R1 (book trả hàng ở CS) và tại 1 thời điểm chỉ có 1 mã đơn đang trả hàng về kho
	if query.CheckInReferenceCode != "" {
		filter["$or"] = []bson.M{
			{
				"reference_code": query.CheckInReferenceCode,
			},
			{
				"parent_reference_code": query.CheckInReferenceCode,
			},
		}
	}

	if query.LeadTimeQuery != nil {
		//Còn hạn: now <  deliveryLeadtime
		//Còn hạn 6h: now < deliveryLeadtime - 6h => deliveryLeadtime > now + 6h
		//Qúa hạn dưới 48h: deliveryLeadtime < now < deliveryLeadtime + 48h
		//Quá hạn 48h: deliveryLeadtime + 48h < now => deliveryLeadtime < now - 48h
		//Quá hạn 7 ngày: deliveryLeadtime  + 7d < now => deliveryLeadtime < now - 7d

		leadTimeFilter := bson.M{}
		if query.LeadTimeQuery.ToMultiplier > 0 {
			leadTimeFilter["$gte"] = now.Add(time.Second * time.Duration(-query.LeadTimeQuery.ToAddition))
		}
		if query.LeadTimeQuery.FromMultiplier > 0 {
			leadTimeFilter["$lte"] = now.Add(time.Second * time.Duration(-query.LeadTimeQuery.FromAddition))
		}
		filter["delivery_lead_time"] = leadTimeFilter
	}

	var sortField = bson.D{}

	if sort.ActionTime != nil {
		sortField = append(sortField, bson.E{
			Key:   "action_time",
			Value: sort.ActionTime,
		})
	}

	if sort.ProvinceCode != nil {
		sortField = append(sortField, bson.E{
			Key:   "to_province_code",
			Value: sort.ProvinceCode,
		})
	}

	if sort.DistrictCode != nil {
		sortField = append(sortField, bson.E{
			Key:   "to_district_code",
			Value: sort.DistrictCode,
		})
	}

	if sort.WardCode != nil {
		sortField = append(sortField, bson.E{
			Key:   "to_ward_code",
			Value: sort.WardCode,
		})
	}

	if sort.DeliveredTime != nil {
		sortField = append(sortField, bson.E{
			Key:   "delivered_time",
			Value: sort.DeliveredTime,
		})
	}

	if sort.CompletedTime != nil {
		sortField = append(sortField, bson.E{
			Key:   "completed_time",
			Value: sort.CompletedTime,
		})
	}

	if sort.Zone != nil {
		if *sort.Zone == enum.HubOrderType.PICKUP {
			sortField = append(sortField, bson.E{
				Key:   "from_province_code",
				Value: 1,
			}, bson.E{
				Key:   "from_district_code",
				Value: 1,
			}, bson.E{
				Key:   "from_ward_code",
				Value: 1,
			})
		} else {
			sortField = append(sortField, bson.E{
				Key:   "to_province_code",
				Value: 1,
			}, bson.E{
				Key:   "to_district_code",
				Value: 1,
			}, bson.E{
				Key:   "to_ward_code",
				Value: 1,
			})
		}
	}

	if sort.ProvinceCode == nil && sort.DistrictCode == nil && sort.WardCode == nil &&
		sort.DeliveredTime == nil && sort.CompletedTime == nil && sort.Zone == nil &&
		sort.ActionTime == nil {
		sortField = append(sortField, bson.E{
			Key:   "_id",
			Value: -1,
		})
	}

	var result *common.APIResponse

	if query.ReadPreference != nil && *query.ReadPreference == enum.ReadPreference.SECONDARY {
		result = model.HUBShippingOrderDB.SecondaryInstance.QueryWithOptions(filter, &options.FindOptions{
			Limit: &limit,
			Skip:  &offset,
			Sort:  sortField,
		})

		if getTotal {
			countResult := model.HUBShippingOrderDB.SecondaryInstance.Count(filter)
			result.Total = countResult.Total
		}
	} else {
		result = model.HUBShippingOrderDB.QueryWithOptions(filter, &options.FindOptions{
			Limit: &limit,
			Skip:  &offset,
			Sort:  sortField,
		})

		if getTotal {
			countResult := model.HUBShippingOrderDB.Count(filter)
			result.Total = countResult.Total
		}
	}

	if result.Data != nil {
		listHubShippingOrder := result.Data.([]*model.HubShippingOrder)
		// Check sort by distance option, rider option
		if sort.Distance != nil && *sort.Distance {
			// if mobile cann't get current driver geocode return the unsorted hub order
			if len(query.Depots) == 0 {
				return result
			}
			sortedOrders, err := SortHubOrderByDistance(listHubShippingOrder, query.Depots)
			// If can't sort hub order return the unsorted hub order
			if err == nil {
				result.Data = sortedOrders
			}
		}
	}

	return result
}

func GetMyTrip(query *request.HubShippingOrderQuery, sort *request.FilterSort, offset, limit int64, getTotal bool) *common.APIResponse {
	if query.HubCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "HubCode is required",
		}
	}

	if query.DriverId == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "DriverId is required",
		}
	}

	if query.CustomerCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "CustomerCode is required",
		}
	}

	if query.TripType == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "TripType is required",
		}
	}

	filter := bson.M{
		"driver_id": query.DriverId,
		"hub_code":  query.HubCode,
	}

	if *query.TripType == enum.TripType.FIRST_MILE_TRIP {
		pickOrderStatus := []enum.HubShippingOrderStatusValue{
			enum.HubShippingOrderStatus.PICKING,
			enum.HubShippingOrderStatus.READY_TO_PICK,
		}
		filter["status"] = bson.M{
			"$in": pickOrderStatus,
		}
		filter["type"] = enum.HubOrderType.PICKUP
		filter["from_customer_code"] = query.CustomerCode

		return model.HUBShippingOrderDB.SecondaryInstance.Query(filter, offset, limit, nil)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Invalid,
		Message: "TripType is invalid",
	}
}

// CountHubOrderWithinDay func
func CountHubOrderWithinDay(input *request.HubShippingOrderQuery, accountId int64) *common.APIResponse {
	type DataResponse struct {
		NumPickupOrder   int64 `json:"numPickupOrder"`
		NumDeliveryOrder int64 `json:"numDeliveryOrder"`
	}

	pickupEnums := []enum.HubShippingOrderStatusValue{
		enum.HubShippingOrderStatus.PICKING,
		enum.HubShippingOrderStatus.READY_TO_PICK,
	}
	deliveryEnums := []enum.HubShippingOrderStatusValue{
		enum.HubShippingOrderStatus.WAIT_TO_DELIVERY,
		enum.HubShippingOrderStatus.DELIVERING,
	}

	numPickUpOrders := CountHubOrderInDay(pickupEnums, accountId, []string{"expected_pickup_time", "rescheduled_pickup_time"}, input)
	numDeliveryOrder := CountHubOrderInDay(deliveryEnums, accountId, []string{"expected_delivery_time", "rescheduled_delivery_time"}, input)

	var response []DataResponse
	response = append(response, DataResponse{
		numPickUpOrders,
		numDeliveryOrder,
	})

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Data:    response,
		Message: "OK",
	}
}

// StatisticHubOrderWithinDay func
func StatisticHubOrderWithinDay(input *request.HubShippingOrderQuery) *common.APIResponse {
	type DataResponse struct {
		NumPickupOrder       int64 `json:"numPickupOrder"`
		NumDeliveryOrder     int64 `json:"numDeliveryOrder"`
		NumDonePickupOrder   int64 `json:"numDonePickupOrder"`
		NumDoneDeliveryOrder int64 `json:"numDoneDeliveryOrder"`
	}

	donePickupEnums := []enum.HubShippingOrderStatusValue{
		enum.HubShippingOrderStatus.WAIT_TO_STORING,
		enum.HubShippingOrderStatus.STORING,
		enum.HubShippingOrderStatus.DONE_TRANSPORTING,
		enum.HubShippingOrderStatus.COMPLETED,
	}

	pickingEnums := []enum.HubShippingOrderStatusValue{
		enum.HubShippingOrderStatus.READY_TO_PICK,
		enum.HubShippingOrderStatus.PICKING,
		enum.HubShippingOrderStatus.PICK_FAIL,
	}

	doneDeliveryEnums := []enum.HubShippingOrderStatusValue{
		enum.HubShippingOrderStatus.DELIVERED,
		enum.HubShippingOrderStatus.COD_COLLECTED,
		enum.HubShippingOrderStatus.COMPLETED,
	}

	deliveringEnums := []enum.HubShippingOrderStatusValue{
		enum.HubShippingOrderStatus.WAIT_TO_STORING,
		enum.HubShippingOrderStatus.STORING,
		enum.HubShippingOrderStatus.WAIT_TO_DELIVERY,
		enum.HubShippingOrderStatus.DELIVERING,
		enum.HubShippingOrderStatus.DELIVERY_FAIL,
	}

	numPickingOrder := CountHubOrderInDay(pickingEnums, 0, []string{"expected_pickup_time", "rescheduled_pickup_time"}, input)
	numDonePickUpOrder := CountHubOrderInDay(donePickupEnums, 0, []string{"expected_pickup_time", "rescheduled_pickup_time"}, input)
	numDeliveringOrder := CountHubOrderInDay(deliveringEnums, 0, []string{"expected_delivery_time", "rescheduled_delivery_time"}, input)
	numDoneDeliveryOrder := CountHubOrderInDay(doneDeliveryEnums, 0, []string{"expected_delivery_time", "rescheduled_delivery_time"}, input)

	totalPickupOrder := numDonePickUpOrder + numPickingOrder
	totalDeliveryOrder := numDoneDeliveryOrder + numDeliveringOrder

	var response []DataResponse
	response = append(response, DataResponse{
		totalPickupOrder,
		totalDeliveryOrder,
		numDonePickUpOrder,
		numDoneDeliveryOrder,
	})

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Data:    response,
		Message: "OK",
	}
}

func CountHubOrderInDay(listStatus []enum.HubShippingOrderStatusValue, accountId int64, fields []string, input *request.HubShippingOrderQuery) int64 {
	now := time.Now()
	year, month, day := now.Date()
	specificDate := time.Date(year, month, day, 0, 0, 0, 0, now.Location())

	startOfDay := specificDate.Unix()
	endOfDay := specificDate.Add(24 * time.Hour).Unix()

	var filter = bson.M{}
	if accountId != 0 {
		filter["driver_id"] = accountId
	}
	if len(listStatus) > 0 {
		filter["status"] = bson.M{
			"$in": listStatus,
		}
	}

	if input.HubCode != "" {
		filter["hub_code"] = input.HubCode
	}
	if len(fields) > 0 {
		orBson := []bson.M{}
		for _, f := range fields {
			orBson = append(orBson, bson.M{f: bson.M{"$gte": startOfDay, "$lte": endOfDay}})
		}
		filter["$or"] = orBson
	}

	countResult := model.HUBShippingOrderDB.Count(filter)
	return countResult.Total
}

func AuditHubOrderStatus() *common.APIResponse {
	hubOrderQuery := model.HUBShippingOrderDB.Query(&bson.M{
		"status": bson.M{
			"$in": []string{
				"STORING",
				"WAIT_TO_DELIVERY",
				"DELIVERING",
				"DELIVERED",
				"COD_COLLECTED",
			},
		},
		"action": "IN",
	}, 0, 10000, nil)

	type ErrorItem struct {
		ReferenceCode           string `json:"referenceCode,omitempty"`
		HubOrderStatus          string `json:"hubStatus"`
		ShippingOrderStatus     string `json:"shippingOrderStatus"`
		ShippingOrderCurrentHub string `json:"shippingOrderCurrentHub"`
		HubCode                 string `json:"hubCode"`
	}

	var errorItems []*ErrorItem
	var mux sync.Mutex
	if hubOrderQuery.Status == common.APIStatus.Ok {
		hubOrders := hubOrderQuery.Data.([]*model.HubShippingOrder)
		var wg sync.WaitGroup
		for _, hubOrder := range hubOrders {
			wg.Add(1)
			go func(order *model.HubShippingOrder) {
				defer wg.Done()
				shippingOrderQuery := model.ShippingOrderDB.QueryOne(&bson.M{
					"reference_code": order.ReferenceCode,
				})

				if shippingOrderQuery.Status == common.APIStatus.Ok {
					shippingOrder := shippingOrderQuery.Data.([]*model.ShippingOrder)[0]
					if string(*shippingOrder.Status) != string(*order.Status) {
						mux.Lock()
						defer mux.Unlock()
						errorItems = append(errorItems, &ErrorItem{
							order.ReferenceCode,
							string(*order.Status),
							string(*shippingOrder.Status),
							shippingOrder.CurrentHub,
							order.HUBCode,
						})
					}
				}
			}(hubOrder)
		}
		wg.Wait()
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Ok",
		Data:    errorItems,
	}
}

func MigrateDeliveredAndCompletedTime() *common.APIResponse {
	hubQuery := model.HubDB.Query(bson.M{}, 0, 1000, &bson.M{"_id": -1})
	if hubQuery.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Ok",
		}
	}

	var wg sync.WaitGroup
	hubs := hubQuery.Data.([]*model.Hub)
	for _, hub := range hubs {
		if !*hub.Active || hub.Code == "HUBMALO" {
			continue
		}
		wg.Add(1)
		go func(hubInfo *model.Hub) {
			defer wg.Done()
			var offset int64 = 0
		LoadMore:
			hubOrderQuery := model.HUBShippingOrderDB.Query(&bson.M{
				"hub_code": hubInfo.Code,
				"status": bson.M{
					"$in": []string{
						"DELIVERED",
						"COD_COLLECTED",
						"COMPLETED",
					},
				},
				"action": "IN",
				"delivered_time": bson.M{
					"$exists": false,
				},
			}, offset, 200, &bson.M{"_id": -1})

			if hubOrderQuery.Status == common.APIStatus.Ok {
				hubOrders := hubOrderQuery.Data.([]*model.HubShippingOrder)
				for _, hubOrder := range hubOrders {
					fmt.Println("Check order " + hubOrder.ReferenceCode)
					shippingOrderQuery := model.ShippingOrderDB.QueryOne(&bson.M{
						"reference_code": hubOrder.ReferenceCode,
						"delivered_time": bson.M{
							"$exists": true,
						},
					})

					if shippingOrderQuery.Status == common.APIStatus.Ok {
						shippingOrder := shippingOrderQuery.Data.([]*model.ShippingOrder)[0]
						switch *hubOrder.Status {
						case enum.HubShippingOrderStatus.DELIVERED:
							hubOrder.DeliveredTime = shippingOrder.DeliveredTime
							break
						case enum.HubShippingOrderStatus.COD_COLLECTED:
							hubOrder.DeliveredTime = shippingOrder.DeliveredTime
							break
						case enum.HubShippingOrderStatus.COMPLETED:
							hubOrder.DeliveredTime = shippingOrder.DeliveredTime
							hubOrder.CompletedTime = shippingOrder.LastUpdatedTime.Unix()
							break
						}

						fmt.Println("Update [ " + hubOrder.HUBCode + "]  - order " + hubOrder.ReferenceCode)
						if hubOrder.DeliveredTime == 0 {
							continue
						}
						_ = model.HUBShippingOrderDB.UpdateOne(&bson.M{
							"hub_code":       hubOrder.HUBCode,
							"reference_code": hubOrder.ReferenceCode,
						}, hubOrder)
					}
				}
				offset = offset + 200
				goto LoadMore
			}
		}(hub)
		wg.Wait()
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Ok",
	}
}

func MigrateSOCheckPointTime() *common.APIResponse {
	var offset int64 = 0
LoadMore:
	shippingOrderQuery := model.ShippingOrderDB.Query(&bson.M{
		"status": bson.M{
			"$in": []string{
				"STORING",
				"DELIVERING",
				"DELIVERED",
				"COD_COLLECTED",
				"COMPLETED",
				"RETURN",
				"RETURNING",
				"RETURNED",
			},
		},
	}, offset, 200, &bson.M{"_id": -1})

	if shippingOrderQuery.Status == common.APIStatus.Ok {
		shippingOrders := shippingOrderQuery.Data.([]*model.ShippingOrder)
		for _, shippingOrder := range shippingOrders {
			//fmt.Println("Check order " + shippingOrder.ReferenceCode)
			// Call saleOrder get handover time
			callback, err := client.Services.TplCallbackClient.GetCallback(shippingOrder.ReferenceCode)
			if err != nil {
				continue
			}

			if callback.Logs != nil {
				for _, l := range callback.Logs {
					if l.Status != nil && *l.Status == enum.Status.PICKED {
						if shippingOrder.PickedTime == 0 {
							shippingOrder.PickedTime = l.ActionTime.Unix()
						}
					}

					if l.Status != nil && *l.Status == enum.Status.STORING {
						if shippingOrder.PickedTime == 0 {
							shippingOrder.PickedTime = l.ActionTime.Unix()
						}
						shippingOrder.StoredAtLMHubTime = l.ActionTime.Unix()
					}
				}
			}

			_ = model.ShippingOrderDB.UpdateOne(bson.M{"reference_code": shippingOrder.ReferenceCode}, shippingOrder)
		}
		offset = offset + 200
		goto LoadMore
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Ok",
	}
}

func IsValidStatusTransition(from enum.HubShippingOrderStatusValue, to enum.HubShippingOrderStatusValue) bool {
	fromStatus := conf.Config.HOStatusTransition[string(from)]
	if fromStatus == nil {
		return false
	}
	return CheckItemInArray(string(to), fromStatus)
}

func AdjustReceivedPackage(referenceCode string, currentHub string, numberOfPackage int64, referenceType enum.ReferenceCodeTypeValue, action enum.AdjustReceivePackageValue) *common.APIResponse {

	if referenceType == enum.ReferenceCodeType.TRACKING_CODE {
		return AdjustByTrackingCode(referenceCode, currentHub, numberOfPackage)
	}

	if referenceType == enum.ReferenceCodeType.BIN_CODE {
		if action == "" || action == enum.AdjustReceivePackage.CHECKIN_BIN {
			return AdjustByBinCode(referenceCode, currentHub, numberOfPackage)
		}

		if action == enum.AdjustReceivePackage.FIND_LOST_BIN {
			return AdjustLostBinCode(referenceCode, currentHub, numberOfPackage)
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Loại nhập hàng không hợp lệ",
	}
}

func AdjustByTrackingCode(referenceCode string, currentHub string, numberOfPackage int64) *common.APIResponse {
	hubOrderRaw := model.HUBShippingOrderDB.QueryOne(bson.M{
		"reference_code": referenceCode,
		"hub_code":       currentHub,
	})
	if hubOrderRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Đơn giao hàng ở " + currentHub + " không tồn tại",
		}
	}

	hubOrder := hubOrderRaw.Data.([]*model.HubShippingOrder)[0]
	if hubOrder.Status == nil || *hubOrder.Status != enum.HubShippingOrderStatus.WAIT_TO_STORING {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Trạng thái đơn giao hàng không hợp lệ",
		}
	}

	if hubOrder.Type == nil || *hubOrder.Type != enum.HubOrderType.PICKUP {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Loại đơn không hợp lệ",
		}
	}

	newReceivedPackage := hubOrder.ReceivePackage + numberOfPackage
	if newReceivedPackage > hubOrder.NumPackage {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Thừa kiện",
		}
	}

	if newReceivedPackage < 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Số lượng kiện không được nhỏ hơn 0",
		}
	}

	afterOption := options.After
	updateResult := model.HUBShippingOrderDB.UpdateOne(bson.M{
		"reference_code": referenceCode,
		"hub_code":       currentHub,
	}, bson.M{
		"receive_ package": newReceivedPackage,
	}, &options.FindOneAndUpdateOptions{
		Upsert:         &enum.False,
		ReturnDocument: &afterOption,
	})

	if updateResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: updateResult.Message,
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật số kiện nhận thành công",
		Data:    updateResult.Data.([]*model.HubShippingOrder),
	}
}

func AdjustByBinCode(binCode string, currentHub string,
	numberOfPackage int64) *common.APIResponse {

	hubOrderRaw := model.HUBShippingOrderDB.QueryOne(bson.M{
		"hub_code":     currentHub,
		"status":       &enum.HubShippingOrderStatus.WAIT_TO_STORING,
		"type":         &enum.HubOrderType.PICKUP,
		"products.sku": binCode,
	})

	if hubOrderRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Đơn giao hàng ở " + currentHub + " không tồn tại",
		}
	}

	hubOrder := hubOrderRaw.Data.([]*model.HubShippingOrder)[0]

	for _, product := range hubOrder.Products {
		if product.SKU == binCode {
			// Only checkin when status equal nil (for old data) or WAIT_TO_STORING
			if product.Status == nil || *product.Status == enum.ProductStatus.WAIT_TO_STORING {
				product.Status = &enum.ProductStatus.SCANNING
				break
			}

			if *product.Status == enum.ProductStatus.SCANNING {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Bin đã quét",
				}
			} else {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Trạng thái của bin không hợp lệ.",
				}
			}
		}
	}

	newReceivedPackage := hubOrder.ReceivePackage + numberOfPackage
	if newReceivedPackage > hubOrder.NumPackage {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Thừa kiện",
		}
	}

	if newReceivedPackage < 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Số lượng kiện không được nhỏ hơn 0",
		}
	}

	afterOption := options.After
	updateResult := model.HUBShippingOrderDB.UpdateOne(bson.M{
		"reference_code": hubOrder.ReferenceCode,
		"hub_code":       currentHub,
	}, bson.M{
		"receive_ package": newReceivedPackage,
		"products":         hubOrder.Products,
	}, &options.FindOneAndUpdateOptions{
		Upsert:         &enum.False,
		ReturnDocument: &afterOption,
	})

	if updateResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: updateResult.Message,
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật số kiện nhận thành công",
		Data:    updateResult.Data.([]*model.HubShippingOrder),
	}
}

// CancelCheckInPickup reset received packages to 0
func ResetCheckInPickup(session *request.CheckInPickup, action enum.CheckInActionValue) *common.APIResponse {
	if len(session.HubOrders) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã liên kết không được để trống",
		}
	}

	var errs []*common.Error

	for _, hubOrder := range session.HubOrders {
		hubOrderRaw := model.HUBShippingOrderDB.QueryOne(bson.M{
			"reference_code": hubOrder.ReferenceCode,
			"hub_code":       session.HubCode,
		})

		if hubOrderRaw.Status != common.APIStatus.Ok {
			errs = append(errs, &common.Error{
				Message: "Đơn giao hàng " + hubOrder.ReferenceCode + " ở hub " + session.HubCode + " không tồn tại",
			})
			continue
		}

		hubOrder := hubOrderRaw.Data.([]*model.HubShippingOrder)[0]
		// Chỉ có thể nhập hub khi chờ nhập kho hoặc bị mất
		if hubOrder.Status == nil ||
			*hubOrder.Status != enum.HubShippingOrderStatus.WAIT_TO_STORING &&
				*hubOrder.Status != enum.HubShippingOrderStatus.LOST {
			errs = append(errs, &common.Error{
				Message: "Trạng thái đơn giao hàng " + hubOrder.ReferenceCode + " không hợp lệ",
			})
			continue
		}

		if hubOrder.Type == nil {
			errs = append(errs, &common.Error{
				Message: "Loại đơn không hợp lệ " + hubOrder.ReferenceCode,
			})
			continue
		}

		updater := bson.M{}
		updater["receive_ package"] = 0

		if len(hubOrder.Products) > 0 {
			for _, product := range hubOrder.Products {
				if product.Status != nil && *product.Status == enum.ProductStatus.REMOVED {
					continue
				}
				if action == enum.CheckInAction.RESET_LOST {
					product.Status = &enum.ProductStatus.LOST
				} else {
					product.Status = &enum.ProductStatus.WAIT_TO_STORING
				}
			}
			updater["products"] = hubOrder.Products
		}

		updateResult := model.HUBShippingOrderDB.UpdateOne(bson.M{
			"reference_code": hubOrder.ReferenceCode,
			"hub_code":       hubOrder.HUBCode,
		}, updater)

		if updateResult.Status != common.APIStatus.Ok {
			errs = append(errs, &common.Error{
				Message: updateResult.Message,
			})
		}

	}

	if len(errs) > 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Đã có lỗi xảy ra khi hủy nhập hub",
			Data:    errs,
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Hủy nhập hub thành công",
	}
}

func DoneCheckInPickup(items *request.CheckInPickup, checkInBy int) *common.APIResponse {
	type errHubOrderStruct struct {
		ReferenceCode string `json:"referenceCode"`
		ErrorMessage  string `json:"error"`
	}

	var errHubOrder []errHubOrderStruct
	var currentSubType *enum.SubTypeValue
	var externalHubOrder *model.HubShippingOrder
	var hubOrdersExtend []model.HubShippingOrder
	hubResp := model.HubDB.QueryOne(bson.M{
		"code": items.HubCode,
	})
	if hubResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Hub không tồn tại",
		}
	}
	hub := hubResp.Data.([]*model.Hub)[0]

	for _, item := range items.HubOrders {
		hubOrderRaw := model.HUBShippingOrderDB.QueryOne(bson.M{
			"reference_code": item.ReferenceCode,
			"hub_code":       items.HubCode,
		})

		if hubOrderRaw.Status != common.APIStatus.Ok {
			errHubOrder = append(errHubOrder, errHubOrderStruct{
				ErrorMessage:  "Đơn giao hàng " + item.ReferenceCode + " không tồn tại",
				ReferenceCode: item.ReferenceCode,
			})
			continue
		}

		hubOrder := hubOrderRaw.Data.([]*model.HubShippingOrder)[0]
		if *hubOrder.SubType == enum.SubType.EO {
			if item.ReceivedPackages <= 0 {
				errHubOrder = append(errHubOrder, errHubOrderStruct{
					ErrorMessage:  "Phải có ít nhất một kiện",
					ReferenceCode: item.ReferenceCode,
				})
				continue
			}

			if checkInBy == 0 {
				errHubOrder = append(errHubOrder, errHubOrderStruct{
					ErrorMessage:  "Người checkin không được để trống",
					ReferenceCode: item.ReferenceCode,
				})
				continue
			}

			externalHubOrder = hubOrder
			continue
		}

		if hubOrder.Status == nil || *hubOrder.Status != enum.HubShippingOrderStatus.WAIT_TO_STORING {
			errHubOrder = append(errHubOrder, errHubOrderStruct{
				ErrorMessage:  "Đơn hàng: " + item.ReferenceCode + " có trạng thái không hợp lệ.",
				ReferenceCode: item.ReferenceCode,
			})
			continue
		}

		if hubOrder.ReceivePackage <= 0 {
			errHubOrder = append(errHubOrder, errHubOrderStruct{
				ErrorMessage:  "Phải có ít nhất một kiện",
				ReferenceCode: item.ReferenceCode,
			})
			continue
		}

		if hubOrder.NumPackage > hubOrder.ReceivePackage && item.Reason == "" {
			errHubOrder = append(errHubOrder, errHubOrderStruct{
				ErrorMessage:  "Lí do thiếu kiện của đơn: " + item.ReferenceCode + " không được để trống.",
				ReferenceCode: item.ReferenceCode,
			})
			continue
		}

		if hubOrder.Type == nil ||
			*hubOrder.Type != enum.HubOrderType.PICKUP ||
			hubOrder.SubType == nil {
			errHubOrder = append(errHubOrder, errHubOrderStruct{
				ErrorMessage:  "Loại đơn không hợp lệ",
				ReferenceCode: item.ReferenceCode,
			})
			continue
		}

		if currentSubType == nil {
			currentSubType = hubOrder.SubType
		}

		if currentSubType != nil &&
			*currentSubType != *hubOrder.SubType {
			errHubOrder = append(errHubOrder, errHubOrderStruct{
				ErrorMessage:  "Không thể nhập cùng lúc nhiều loại đơn",
				ReferenceCode: item.ReferenceCode,
			})
			continue
		}

		current := time.Now()
		afterOption := options.After
		shippingOrder := model.ShippingOrderDB.UpdateOne(bson.M{
			"reference_code": item.ReferenceCode,
		}, bson.M{
			"status":                enum.TPLCallbackStatus.STORING,
			"last_updated_time":     &current,
			"action_time":           current.Unix(),
			"stored_first_hub_time": current.Unix(),
		}, &options.FindOneAndUpdateOptions{
			ReturnDocument: &afterOption,
		})

		if shippingOrder.Status != common.APIStatus.Ok {
			errHubOrder = append(errHubOrder, errHubOrderStruct{
				ErrorMessage:  "Không thể cập nhật đơn: " + item.ReferenceCode,
				ReferenceCode: item.ReferenceCode,
			})
			continue
		}

		order := shippingOrder.Data.([]*model.ShippingOrder)[0]
		updater := bson.M{
			"status":            enum.HubShippingOrderStatus.STORING,
			"reason":            item.Reason,
			"action":            "IN",
			"last_updated_time": &current,
			"action_time":       current.Unix(),
		}

		// Nếu loại đơn là RETURN thì scan được bao nhiêu cập nhật số kiện bấy nhiêu do chỉ có mã return
		if order.ShippingType != nil && *order.ShippingType == enum.ShippingOrderType.RETURN {
			updater["num_package"] = hubOrder.ReceivePackage
		}

		// Chỉ đếm product khi loại đơn là pickup
		if len(hubOrder.Products) > 0 && order.ShippingType != nil && *order.ShippingType == enum.ShippingOrderType.PICKUP {
			numPack := 0
			var weight float64 = 0
			needExtendPickUp := false
			for _, product := range hubOrder.Products {
				// when user does not scan bin code and done checkin bin which means it was removed
				if product.Status == nil || *product.Status == enum.ProductStatus.WAIT_TO_STORING {
					product.Status = &enum.ProductStatus.REMOVED
					needExtendPickUp = true
				} else {
					product.Status = &enum.ProductStatus.STORING
					weight += product.Weight
					numPack++
				}
			}

			if needExtendPickUp {
				// append list order can't pickup
				hubOrdersExtend = append(hubOrdersExtend, *hubOrder)
			}
			updater["products"] = hubOrder.Products
			updater["num_package"] = numPack

			if weight == 0 {
				weight = 1
			}
			updater["weight"] = weight
		}

		updateHubOrderResult := model.HUBShippingOrderDB.UpdateOne(bson.M{
			"reference_code": item.ReferenceCode,
			"hub_code":       items.HubCode,
		}, updater)

		if updateHubOrderResult.Status != common.APIStatus.Ok {
			errHubOrder = append(errHubOrder, errHubOrderStruct{
				ErrorMessage:  "Không thể cập nhật đơn: " + item.ReferenceCode,
				ReferenceCode: item.ReferenceCode,
			})
			continue
		}

		callbackData := request.Callback{
			ActionTime:    &current,
			SO:            item.ReferenceCode,
			ReferenceCode: item.ReferenceCode,
			Status:        &enum.TPLCallbackStatus.STORING,
			StatusName:    "Đã nhập kho " + hub.Name,
			TPLStatus:     string(enum.TPLCallbackStatus.STORING),
			TPLStatusName: "Đã nhập kho " + hub.Name,
			Type:          &enum.ShippingOrderType.PICKUP,
		}

		_ = syncData.PushCreateTPLCallbackQueue(callbackData, callbackData.SO)

		// Cập nhật hoàn thành ShippingOrder nếu là đơn lấy BIN (type PICKUP)
		if hubOrder.Type != nil &&
			*hubOrder.Type == enum.HubOrderType.PICKUP &&
			order.ShippingType != nil &&
			*order.ShippingType == enum.ShippingOrderType.PICKUP {
			if order.CustomerCode != "" {
				hubRaw := model.HubDB.QueryOne(bson.M{"code": items.HubCode})
				if hubRaw.Status != common.APIStatus.Ok {
					errHubOrder = append(errHubOrder, errHubOrderStruct{
						ErrorMessage:  "Không tìm thấy Hub: " + items.HubCode,
						ReferenceCode: item.ReferenceCode,
					})
					continue
				}
				hub := hubRaw.Data.([]*model.Hub)[0]
				if hub.WarehouseReferenceCode == order.CustomerCode {
					updateHubOrderRequest := &request.UpdateHubOrder{
						HubCode:       hubOrder.HUBCode,
						ReferenceCode: item.ReferenceCode,
						Status:        &enum.HubShippingOrderStatus.STORING,
						Products:      hubOrder.Products,
					}

					_ = bin.PushCompleteBin(updateHubOrderRequest, item.ReferenceCode)
				}
			}
		}
	}

	if len(hubOrdersExtend) > 0 {
		_ = shippingOrderQueue.Push(shippingOrderQueue.RequestBody{
			BodyData: hubOrdersExtend,
		}, items.HubCode)
	}

	needCreateReconcile := false
	if externalHubOrder != nil {
		current := time.Now()
		numPack := items.HubOrders[0].ReceivedPackages
		shippingOrderUpdater := bson.M{
			"status":                enum.TPLCallbackStatus.STORING,
			"last_updated_time":     &current,
			"action_time":           current.Unix(),
			"num_package":           numPack,
			"stored_first_hub_time": current.Unix(),
		}
		hubOrderUpdater := bson.M{
			"status":            enum.HubShippingOrderStatus.STORING,
			"last_updated_time": &current,
			"action_time":       current.Unix(),
			"num_package":       numPack,
		}

		storedShippingOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
			"reference_code": externalHubOrder.ReferenceCode,
		})

		if storedShippingOrderRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Đợn hàng không tồn tại",
			}
		}
		storedShippingOrder := storedShippingOrderRaw.Data.([]*model.ShippingOrder)[0]
		storedShippingOrder.NumPackage = numPack
		//needCreateReconcile := false

		if storedShippingOrder.FirstMileHubCode != "" &&
			storedShippingOrder.LastMileHubCode != "" &&
			storedShippingOrder.LastMileHubCode == storedShippingOrder.FirstMileHubCode &&
			storedShippingOrder.CurrentHub == externalHubOrder.HUBCode {
			hubOrderUpdater["type"] = &enum.HubOrderType.DELIVERY
		} else {
			hubOrderUpdater["type"] = &enum.HubOrderType.TRANSPORTING
		}

		// Đơn drop off mới cần phải tỉnh lại phí khi checkin
		if storedShippingOrder.IsDropOffAtFMHub {
			needCreateReconcile = true
			var configFee *model.ConfigFee
			bookRequest := shippingOrderToBookShippingReq(storedShippingOrder)
			if storedShippingOrder.CustomerInfo != nil &&
				storedShippingOrder.CustomerInfo.Code != "" {
				customerRaw := model.CustomerDB.QueryOne(bson.M{
					"code": storedShippingOrder.CustomerInfo.Code,
				})
				if customerRaw.Status == common.APIStatus.Ok {
					customer := customerRaw.Data.([]*model.Customer)[0]
					var configFeeId int64
					if customer.AppliedFees != nil {
						for _, appliedFee := range *customer.AppliedFees {
							if storedShippingOrder.IsReceiveAtLMHub && appliedFee.ConfigFeeType == enum.ConfigFeeType.TRANSPORT {
								configFeeId = appliedFee.ConfigFeeId
								break
							}
							if !storedShippingOrder.IsReceiveAtLMHub && appliedFee.ConfigFeeType == enum.ConfigFeeType.TRANSPORT_N_DELIVERY {
								configFeeId = appliedFee.ConfigFeeId
								break
							}
						}
						if configFeeId != 0 {
							configFeeRaw := model.ConfigFeeDB.QueryOne(bson.M{
								"config_id": configFeeId,
							})
							if configFeeRaw.Status != common.APIStatus.Ok {
								return &common.APIResponse{
									Status:  common.APIStatus.Invalid,
									Message: "Biểu phí gắn với khách hàng không tồn tại",
								}
							}
							configFee = configFeeRaw.Data.([]*model.ConfigFee)[0]
						}
					}
				}
			}

			if configFee == nil {
				configFeeCode := ""
				if storedShippingOrder.IsReceiveAtLMHub {
					configFeeCode = conf.Config.DefaultEOTransportFeeCode
				} else {
					configFeeCode = conf.Config.DefaultEOTransNDeliCode
				}
				if configFeeCode == "" {
					return &common.APIResponse{
						Status:  common.APIStatus.Invalid,
						Message: "Biểu phí mặc định không tồn tại",
					}
				}
				configFeeRaw := model.ConfigFeeDB.QueryOne(bson.M{
					"code": configFeeCode,
				})
				if configFeeRaw.Status != common.APIStatus.Ok {
					return &common.APIResponse{
						Status:  common.APIStatus.Invalid,
						Message: "Biểu phí mặc định không tồn tại",
					}
				}
				configFee = configFeeRaw.Data.([]*model.ConfigFee)[0]
			}

			feeAmount, err := CalculateOrderFee(bookRequest, configFee)
			if err != nil {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: err.Error(),
				}
			}

			if storedShippingOrder.FeeCollectMethod != nil &&
				*storedShippingOrder.FeeCollectMethod == enum.FeeCollectMethod.SENDER_PAY {
				shippingOrderUpdater["total_collect_sender_amount"] = feeAmount
				shippingOrderUpdater["fee_sender_amount"] = feeAmount

				hubOrderUpdater["total_collect_sender_amount"] = feeAmount
				hubOrderUpdater["fee_sender_amount"] = feeAmount
			}

			if storedShippingOrder.FeeCollectMethod != nil &&
				*storedShippingOrder.FeeCollectMethod == enum.FeeCollectMethod.RECEIVER_PAY {
				shippingOrderUpdater["total_collect_receiver_amount"] = feeAmount + storedShippingOrder.CODAmount
				shippingOrderUpdater["fee_receiver_amount"] = feeAmount

				hubOrderUpdater["total_collect_receiver_amount"] = feeAmount + storedShippingOrder.CODAmount
				hubOrderUpdater["fee_receiver_amount"] = feeAmount
			}

			if storedShippingOrder.FeeCollectMethod != nil &&
				*storedShippingOrder.FeeCollectMethod == enum.FeeCollectMethod.DEBT {
				shippingOrderUpdater["total_debt_amount"] = feeAmount
				shippingOrderUpdater["fee_debt_amount"] = feeAmount

				hubOrderUpdater["total_debt_amount"] = feeAmount
				hubOrderUpdater["fee_debt_amount"] = feeAmount
			}

			shippingOrderUpdater["fee_amount"] = feeAmount
			shippingOrderUpdater["delivered_time"] = current.Unix()

			hubOrderUpdater["fee_amount"] = feeAmount
			hubOrderUpdater["delivered_time"] = current.Unix()
			hubOrderUpdater["driver_id"] = checkInBy
		}

		var lmHub *model.Hub
		if storedShippingOrder.IsReceiveAtLMHub {
			lmHubRaw := model.HubDB.QueryOne(bson.M{
				"code": storedShippingOrder.LastMileHubCode,
			})
			if lmHubRaw.Status == common.APIStatus.Ok {
				lmHub = lmHubRaw.Data.([]*model.Hub)[0]
			}
		} else {
			lmHub = FindNearestHubOfAddressV2(model.Address{
				ProvinceCode: storedShippingOrder.CustomerProvinceCode,
				DistrictCode: storedShippingOrder.CustomerDistrictCode,
				WardCode:     storedShippingOrder.CustomerWardCode,
			})
		}

		if lmHub == nil || lmHub.DefaultCarrierId == 0 {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không tìm thấy hub giao hàng",
			}
		}

		shippingOrderUpdater["last_mile_hub_code"] = lmHub.Code
		carrierRaw := model.CarrierDB.QueryOne(bson.M{
			"carrier_id": lmHub.DefaultCarrierId,
		})
		if carrierRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không tìm thấy nhà vận chuyển mặc định của hub: " + lmHub.Code,
			}
		}
		carrier := carrierRaw.Data.([]*model.Carrier)[0]
		shippingOrderUpdater["tpl_code"] = carrier.ParentCode
		if carrier.ParentCode == nil {
			shippingOrderUpdater["tpl_code"] = carrier.CarrierCode
		}
		shippingOrderUpdater["tpl_service_id"] = carrier.CarrierId
		shippingOrderUpdater["tpl_name"] = carrier.CarrierName

		hubOrderUpdater["tpl_code"] = shippingOrderUpdater["tpl_code"]
		hubOrderUpdater["tpl_name"] = shippingOrderUpdater["tpl_name"]

		shippingOrderResp := model.ShippingOrderDB.UpdateOne(bson.M{
			"reference_code": externalHubOrder.ReferenceCode,
		}, shippingOrderUpdater)
		if shippingOrderResp.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: shippingOrderResp.Message,
			}
		}
		returnAfter := options.After
		hubOrderResp := model.HUBShippingOrderDB.UpdateOne(bson.M{
			"hub_code":       items.HubCode,
			"reference_code": externalHubOrder.ReferenceCode,
		}, hubOrderUpdater, &options.FindOneAndUpdateOptions{
			ReturnDocument: &returnAfter,
		})
		if hubOrderResp.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: hubOrderResp.Message,
			}
		}
		updatedHubShippingOrder := hubOrderResp.Data.([]*model.HubShippingOrder)[0]
		if needCreateReconcile {
			// Nếu phí thu ở người nhận hàng thì không cần tạo phiên đối soát khi checkin người gửi
			if storedShippingOrder.FeeCollectMethod != nil &&
				*storedShippingOrder.FeeCollectMethod == enum.FeeCollectMethod.SENDER_PAY {
				_, _ = CreateReconcile(updatedHubShippingOrder, string(enum.ReconcileTypeRequest.RIDER_HUB))
			}
			splitEO := strings.Split(updatedHubShippingOrder.ReferenceCode, "BMX")
			if len(splitEO) == 2 {
				orderNumStr := strings.Split(splitEO[1], "-")
				orderId, err := strconv.ParseInt(orderNumStr[0], 10, 64)
				if err != nil {
					return &common.APIResponse{
						Status:  common.APIStatus.Invalid,
						Message: "Đơn hàng không tồn tại",
					}
				}
				customerName := storedShippingOrder.FromCustomerName
				if storedShippingOrder.CustomerInfo != nil &&
					storedShippingOrder.CustomerInfo.Name != "" {
					customerName = storedShippingOrder.CustomerInfo.Name
				}

				client.Services.AccountingClient.CreateBill(request.CreateBillRequest{
					OrderId:           orderId,
					CustomerName:      customerName,
					ExternalOrderCode: storedShippingOrder.ReferenceCode,
					IsAuto:            true,
					FeeLogistic: &request.FeeLogisticRequest{
						LogisticFeeService: int64(updatedHubShippingOrder.FeeAmount),
						CollectionFee:      int64(updatedHubShippingOrder.CODAmount),
					},
				})
			}
		}

		callbackData := request.Callback{
			ActionTime:    &current,
			SO:            updatedHubShippingOrder.ReferenceCode,
			ReferenceCode: updatedHubShippingOrder.ReferenceCode,
			Status:        &enum.TPLCallbackStatus.STORING,
			StatusName:    "Đã nhập kho " + hub.Name,
			TPLStatus:     string(enum.TPLCallbackStatus.STORING),
			TPLStatusName: "Đã nhập kho " + hub.Name,
			Type:          &enum.ShippingOrderType.EO,
		}

		_ = syncData.PushCreateTPLCallbackQueue(callbackData, callbackData.SO)

	}
	if len(errHubOrder) > 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Đã có lỗi xuất hiện khi nhập kho",
			Data:    errHubOrder,
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Nhập hub thành công.",
	}
}

func IdentifyLateLeadTimeHubOrder() {
	isOk := utils.CheckJobLockAvailable(enum.SYNC_LEADTIME_JOB, conf.Config.RepeatLateLeadTimeJob)
	if !isOk {
		return
	}
	// TODO: Add DELIVERY type latter
	now := time.Now()
	filter := bson.M{
		"type": &enum.HubOrderType.PICKUP,
		// update warning late level if order havent out hub
		"status": bson.M{
			"$in": []string{
				"READY_TO_PICK",
				"PICKING",
				"WAIT_TO_STORING",
				"STORING",
				"PICK_FAIL",
			},
		},
		"tpl_code": bson.M{
			"$exists": true,
		},
		// If late_lead_time_level exceed max warning level we dont need to update db
		"$or": bson.A{
			bson.M{
				"late_lead_time_level": bson.M{
					"$lt": conf.Config.MaxWarningLevel,
				},
			},
			bson.M{
				"late_lead_time_level": bson.M{
					"$exists": false,
				},
			},
		},
	}

	// Reduce buffer size and network time
	project := bson.M{
		"created_time":         1,
		"late_lead_time_level": 1,
		"pickup_lead_time":     1,
		"from_ward_code":       1,
		"from_district_code":   1,
		"from_province_code":   1,
		"to_ward_code":         1,
		"to_district_code":     1,
		"to_province_code":     1,
		"tpl_code":             1,
		"reference_code":       1,
		"hub_code":             1,
	}

	type queryResult struct {
		// Update using _id is much faster
		Id                primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
		CreatedTime       *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
		LateLeadTimeLevel int                `json:"lateLeadTimeLevel,omitempty" bson:"late_lead_time_level,omitempty"`
		PickupLeadTime    *time.Time         `json:"pickupLeadTime,omitempty" bson:"pickup_lead_time,omitempty"`
		FromWardCode      string             `json:"fromWardCode,omitempty" bson:"from_ward_code,omitempty"`
		FromDistrictCode  string             `json:"fromDistrictCode,omitempty" bson:"from_district_code,omitempty"`
		FromProvinceCode  string             `json:"fromProvinceCode,omitempty" bson:"from_province_code,omitempty"`
		ToWardCode        string             `json:"toWardCode,omitempty" bson:"to_ward_code,omitempty"`
		ToDistrictCode    string             `json:"toDistrictCode,omitempty" bson:"to_district_code,omitempty"`
		ToProvinceCode    string             `json:"toProvinceCode,omitempty" bson:"to_province_code,omitempty"`
		TplCode           *enum.PartnerValue `json:"tplCode,omitempty" bson:"tpl_code,omitempty"`
		ReferenceCode     string             `json:"referenceCode,omitempty" bson:"reference_code,omitempty"`
		HUBCode           string             `json:"HUBCode,omitempty" bson:"hub_code,omitempty"`
	}

	matchStage := bson.D{{"$match", filter}}
	projectStage := bson.D{{"$project", project}}

	var results []queryResult
	aggregateResult := model.HUBShippingOrderDB.Aggregate(mongo.Pipeline{matchStage, projectStage}, &results)

	if aggregateResult.Status != common.APIStatus.Ok || len(results) == 0 {
		return
	}

	for _, hubOrder := range results {
		updater := bson.M{}
		needUpdatePickUpLeadTime := false
		needUpdateLeadTimeLevel := false

		if hubOrder.PickupLeadTime == nil {
			// If dont have lead time ,caluculate it and assign to hub order
			leadTimeRaw := CalculateHubOrderLeadTime(&request.CalculateLeadTimeRequest{
				From: &model.Address{
					WardCode:     hubOrder.FromWardCode,
					DistrictCode: hubOrder.FromDistrictCode,
					ProvinceCode: hubOrder.FromProvinceCode,
				},
				To: &model.Address{
					WardCode:     hubOrder.ToWardCode,
					DistrictCode: hubOrder.ToDistrictCode,
					ProvinceCode: hubOrder.ToProvinceCode,
				},
				Partner:     string(*hubOrder.TplCode),
				ServiceType: &enum.LeadTimeConfigType.PICKING,
			})
			fmt.Println("leadTimeRaw.Status: ", leadTimeRaw.Status)
			if leadTimeRaw.Status == common.APIStatus.Ok && leadTimeRaw.Data != nil {
				leadTime := leadTimeRaw.Data.([]int64)
				pickUpTime := hubOrder.CreatedTime.Add(time.Duration(leadTime[0]) * time.Second)
				hubOrder.PickupLeadTime = &pickUpTime
				needUpdatePickUpLeadTime = true
			}
		}

		if now.Sub(*hubOrder.PickupLeadTime) > 0 {
			diff := now.Sub(*hubOrder.PickupLeadTime)
			levelDiff := int(diff.Seconds() / conf.Config.StepWarningLevel)
			hubOrder.LateLeadTimeLevel = levelDiff
			needUpdateLeadTimeLevel = true
		}

		if needUpdatePickUpLeadTime {
			updater["pickup_lead_time"] = hubOrder.PickupLeadTime
		}

		if needUpdateLeadTimeLevel {
			updater["late_lead_time_level"] = hubOrder.LateLeadTimeLevel
		}

		if !needUpdateLeadTimeLevel && !needUpdatePickUpLeadTime {
			continue
		}

		updateResult := model.HUBShippingOrderDB.UpdateOne(bson.M{
			"_id": hubOrder.Id,
		}, updater)

		if updateResult.Status != common.APIStatus.Ok {
			mess, _ := json.Marshal(hubOrder)
			_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
				CmdType: "UPDATE_LEADTIME_ERROR",
				Title:   fmt.Sprintf("UPDATE_LEADTIME_ERROR %v - %v", hubOrder.ReferenceCode, hubOrder.HUBCode),
				Message: string(mess) + "\n" + updateResult.Message,
			})
		}
	}
}

func MigrateReconcileRiderHUB(referenceCodes []string) *common.APIResponse {
	hubOrderQuery := model.HUBShippingOrderDB.Query(bson.M{
		"status": "DELIVERED",
		"reference_code": bson.M{
			"$in": referenceCodes,
		},
		"action": "IN",
	}, 0, 1000, &bson.M{
		"_id": 1,
	})

	if hubOrderQuery.Status != common.APIStatus.Ok {
		return hubOrderQuery
	}

	hubOrders := hubOrderQuery.Data.([]*model.HubShippingOrder)
	for _, hubOrder := range hubOrders {
		result, _ := CreateReconcile(hubOrder, string(enum.ReconcileTypeRequest.RIDER_HUB))
		if result.Status != common.APIStatus.Ok {
			return result
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Successfully",
	}
}

func IdentifyLateLeadTimeHubOrderTest(hubOrder model.HubShippingOrder) *common.APIResponse {
	now := time.Now()
	filter := bson.M{
		"type": &enum.HubOrderType.PICKUP,
		// update warning late level if order havent out hub
		"status": bson.M{
			"$in": []string{
				"READY_TO_PICK",
				"PICKING",
				"WAIT_TO_STORING",
				"STORING",
				"PICK_FAIL",
			},
		},
		"tpl_code": bson.M{
			"$exists": true,
		},
		// If late_lead_time_level exceed max warning level we dont need to update db
		"$or": bson.A{
			bson.M{
				"late_lead_time_level": bson.M{
					"$lt": conf.Config.MaxWarningLevel,
				},
			},
			bson.M{
				"late_lead_time_level": bson.M{
					"$exists": false,
				},
			},
		},
		// Test goes here
		"reference_code": hubOrder.ReferenceCode,
		"hub_code":       hubOrder.HUBCode,
	}

	// Reduce buffer size and network time
	project := bson.M{
		"created_time":         1,
		"late_lead_time_level": 1,
		"pickup_lead_time":     1,
		"from_ward_code":       1,
		"from_district_code":   1,
		"from_province_code":   1,
		"to_ward_code":         1,
		"to_district_code":     1,
		"to_province_code":     1,
		"tpl_code":             1,
		"reference_code":       1,
		"hub_code":             1,
	}

	type queryResult struct {
		// Update using _id is much faster
		Id                primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
		CreatedTime       *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
		LateLeadTimeLevel int                `json:"lateLeadTimeLevel,omitempty" bson:"late_lead_time_level,omitempty"`
		PickupLeadTime    *time.Time         `json:"pickupLeadTime,omitempty" bson:"pickup_lead_time,omitempty"`
		FromWardCode      string             `json:"fromWardCode,omitempty" bson:"from_ward_code,omitempty"`
		FromDistrictCode  string             `json:"fromDistrictCode,omitempty" bson:"from_district_code,omitempty"`
		FromProvinceCode  string             `json:"fromProvinceCode,omitempty" bson:"from_province_code,omitempty"`
		ToWardCode        string             `json:"toWardCode,omitempty" bson:"to_ward_code,omitempty"`
		ToDistrictCode    string             `json:"toDistrictCode,omitempty" bson:"to_district_code,omitempty"`
		ToProvinceCode    string             `json:"toProvinceCode,omitempty" bson:"to_province_code,omitempty"`
		TplCode           *enum.PartnerValue `json:"tplCode,omitempty" bson:"tpl_code,omitempty"`
		ReferenceCode     string             `json:"referenceCode,omitempty" bson:"reference_code,omitempty"`
		HUBCode           string             `json:"HUBCode,omitempty" bson:"hub_code,omitempty"`
	}

	matchStage := bson.D{{"$match", filter}}
	projectStage := bson.D{{"$project", project}}

	var results []queryResult
	aggregateResult := model.HUBShippingOrderDB.Aggregate(mongo.Pipeline{matchStage, projectStage}, &results)

	if aggregateResult.Status != common.APIStatus.Ok || len(results) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: aggregateResult.Message,
		}
	}

	for _, hubOrder := range results {
		updater := bson.M{}
		needUpdatePickUpLeadTime := false
		needUpdateLeadTimeLevel := false

		if hubOrder.PickupLeadTime == nil {
			// If dont have lead time ,caluculate it and assign to hub order
			leadTimeRaw := CalculateHubOrderLeadTime(&request.CalculateLeadTimeRequest{
				From: &model.Address{
					WardCode:     hubOrder.FromWardCode,
					DistrictCode: hubOrder.FromDistrictCode,
					ProvinceCode: hubOrder.FromProvinceCode,
				},
				To: &model.Address{
					WardCode:     hubOrder.ToWardCode,
					DistrictCode: hubOrder.ToDistrictCode,
					ProvinceCode: hubOrder.ToProvinceCode,
				},
				Partner:     string(*hubOrder.TplCode),
				ServiceType: &enum.LeadTimeConfigType.PICKING,
			})
			if leadTimeRaw.Status == common.APIStatus.Ok && leadTimeRaw.Data != nil {
				leadTime := leadTimeRaw.Data.([]int64)
				pickUpTime := hubOrder.CreatedTime.Add(time.Duration(leadTime[0]) * time.Second)
				hubOrder.PickupLeadTime = &pickUpTime
				needUpdatePickUpLeadTime = true
			}
		}

		if now.Sub(*hubOrder.PickupLeadTime) > 0 {
			diff := now.Sub(*hubOrder.PickupLeadTime)
			levelDiff := int(diff.Seconds() / conf.Config.StepWarningLevel)
			hubOrder.LateLeadTimeLevel = levelDiff
			needUpdateLeadTimeLevel = true
		}

		if needUpdatePickUpLeadTime {
			updater["pickup_lead_time"] = hubOrder.PickupLeadTime
		}

		if needUpdateLeadTimeLevel {
			updater["late_lead_time_level"] = hubOrder.LateLeadTimeLevel
		}

		if !needUpdateLeadTimeLevel && !needUpdatePickUpLeadTime {
			continue
		}

		updateResult := model.HUBShippingOrderDB.UpdateOne(bson.M{
			"_id": hubOrder.Id,
		}, updater)

		if updateResult.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: updateResult.Message,
			}
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Nhập hub thành công.",
	}
}

func UpdateAddress(input *request.UpdateAddressRequest, updatedBy int64) *common.APIResponse {
	if input.ReferenceCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã liên kết không được để trống",
		}
	}

	if input.HubCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã hub không được để trống",
		}
	}

	if input.To == nil && input.From == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Địa chỉ không được để trống",
		}
	}

	// Only update address in FM and LM hub
	hubOrderRaw := model.HUBShippingOrderDB.QueryOne(bson.M{
		"reference_code": input.ReferenceCode,
		"hub_code":       input.HubCode,
	})
	if hubOrderRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không tìm thấy đơn tại hub" + input.HubCode,
		}
	}

	hubOrder := hubOrderRaw.Data.([]*model.HubShippingOrder)[0]

	hubOrderUpdater := bson.M{
		"last_updated_by": strconv.FormatInt(updatedBy, 10),
	}

	shippingOrderUpdater := bson.M{
		"last_updated_by": strconv.FormatInt(updatedBy, 10),
	}

	// Check pickup address
	if input.From != nil {
		if input.From.Address == "" ||
			input.From.ProvinceCode == "" ||
			input.From.DistrictCode == "" ||
			input.From.WardCode == "" {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Địa chỉ nhận hàng không được để trống",
			}
		}

		err := BuildAddress(input.From)
		if err != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: err.Message,
			}
		}

		hub := FindNearestHubOfAddress(model.Address{
			Address:      hubOrder.FromCustomerAddress,
			WardName:     hubOrder.FromWardName,
			WardCode:     hubOrder.FromWardCode,
			DistrictName: hubOrder.FromDistrictName,
			DistrictCode: hubOrder.FromDistrictCode,
			ProvinceName: hubOrder.FromProvinceName,
			ProvinceCode: hubOrder.FromProvinceCode,
		})

		if hub == nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Chưa có hub quản lí khu vực này",
			}
		}

		if hub.Code != hubOrder.HUBCode {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Chỉ có thể đổi địa chỉ mới nằm trong vùng quản lí của hub",
			}
		}

		shippingOrderUpdater["from_customer_address"] = input.From.Address
		shippingOrderUpdater["from_ward_code"] = input.From.WardCode
		shippingOrderUpdater["from_ward_name"] = input.From.WardName
		shippingOrderUpdater["from_district_code"] = input.From.DistrictCode
		shippingOrderUpdater["from_district_name"] = input.From.DistrictName
		shippingOrderUpdater["from_province_code"] = input.From.ProvinceCode
		shippingOrderUpdater["from_province_name"] = input.From.ProvinceName

		hubOrderUpdater["from_customer_address"] = input.From.Address
		hubOrderUpdater["from_ward_code"] = input.From.WardCode
		hubOrderUpdater["from_ward_name"] = input.From.WardName
		hubOrderUpdater["from_district_code"] = input.From.DistrictCode
		hubOrderUpdater["from_district_name"] = input.From.DistrictName
		hubOrderUpdater["from_province_code"] = input.From.ProvinceCode
		hubOrderUpdater["from_province_name"] = input.From.ProvinceName
	}

	// Check delivery address
	if input.To != nil {
		if input.To.Address == "" ||
			input.To.ProvinceCode == "" ||
			input.To.DistrictCode == "" ||
			input.To.WardCode == "" {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Địa chỉ lấy hàng không được để trống",
			}
		}

		err := BuildAddress(input.To)
		if err != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: err.Message,
			}
		}

		hub := FindNearestHubOfAddress(model.Address{
			Address:      hubOrder.ToCustomerAddress,
			WardName:     hubOrder.ToWardName,
			WardCode:     hubOrder.ToWardCode,
			DistrictName: hubOrder.ToDistrictName,
			DistrictCode: hubOrder.ToDistrictCode,
			ProvinceName: hubOrder.ToProvinceName,
			ProvinceCode: hubOrder.ToProvinceCode,
		})

		if hub == nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Chưa có hub quản lí khu vực này",
			}
		}

		if hub.Code != hubOrder.HUBCode {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Chỉ có thể đổi địa chỉ mới nằm trong vùng quản lí của hub",
			}
		}

		shippingOrderUpdater["customer_shipping_address"] = input.To.Address
		shippingOrderUpdater["customer_ward_code"] = input.To.WardCode
		shippingOrderUpdater["customer_ward_name"] = input.To.WardName
		shippingOrderUpdater["customer_district_code"] = input.To.DistrictCode
		shippingOrderUpdater["customer_district_name"] = input.To.DistrictName
		shippingOrderUpdater["customer_province_code"] = input.To.ProvinceCode
		shippingOrderUpdater["customer_province_name"] = input.To.ProvinceName

		hubOrderUpdater["to_customer_address"] = input.To.Address
		hubOrderUpdater["to_ward_code"] = input.To.WardCode
		hubOrderUpdater["to_ward_name"] = input.To.WardName
		hubOrderUpdater["to_district_code"] = input.To.DistrictCode
		hubOrderUpdater["to_district_name"] = input.To.DistrictName
		hubOrderUpdater["to_province_code"] = input.To.ProvinceCode
		hubOrderUpdater["to_province_name"] = input.To.ProvinceName
	}

	shippingOrder := model.ShippingOrderDB.UpdateOne(bson.M{
		"reference_code": input.ReferenceCode,
	}, shippingOrderUpdater)
	if shippingOrder.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã liên kết không hợp lệ",
		}
	}

	hubOrders := model.HUBShippingOrderDB.UpdateMany(bson.M{
		"reference_code": input.ReferenceCode,
	}, hubOrderUpdater)
	if hubOrders.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không tìm thấy đơn tại hub để cập nhật địa chỉ",
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật địa chỉ thành công",
	}

}

func FormatFailReason(reasonCode string, reasonDetail string, failTime int, lang enum.SupportedLangValue) string {
	if lang == "" {
		lang = enum.SupportedLang.VN
	}

	dictionary := map[enum.SupportedLangValue]string{}

	dictionary[enum.SupportedLang.VN] = "Lần " + strconv.Itoa(failTime) + ": " + enum.VietnamReasonCode[reasonCode]
	dictionary[enum.SupportedLang.ENG] = strconv.Itoa(failTime) + ": " + enum.EnglishReasonCode[reasonCode]

	if reasonCode == string(enum.FailReason.ANOTHER_REASON_DELIVERY) ||
		reasonCode == string(enum.FailReason.ANOTHER_REASON_PICKUP) ||
		reasonCode == string(enum.FailReason.ANOTHER_REASON_CS) {
		dictionary[enum.SupportedLang.VN] = "Lần " + strconv.Itoa(failTime) + ": " + enum.VietnamReasonCode[reasonCode] + " " + reasonDetail
		dictionary[enum.SupportedLang.ENG] = strconv.Itoa(failTime) + ": " + enum.EnglishReasonCode[reasonCode] + " " + reasonDetail
	}

	return dictionary[lang]
}

func AdjustLostBinCode(binCode string, currentHub string, numberOfPackage int64) *common.APIResponse {
	hubOrderRaw := model.HUBShippingOrderDB.QueryOne(bson.M{
		"hub_code": currentHub,
		"status":   &enum.HubShippingOrderStatus.LOST,
		"products": bson.M{
			"$elemMatch": bson.M{
				"sku":    binCode,
				"status": enum.ProductStatus.LOST,
			},
		},
	})

	if hubOrderRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không tìm thấy bin " + binCode + " bị mất tại hub " + currentHub,
		}
	}

	hubOrder := hubOrderRaw.Data.([]*model.HubShippingOrder)[0]

	for _, product := range hubOrder.Products {
		if product.SKU == binCode {
			if product.Status != nil && *product.Status == enum.ProductStatus.LOST {
				product.Status = &enum.ProductStatus.SCANNING
				break
			}

			if *product.Status == enum.ProductStatus.SCANNING {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Bin đã quét",
				}
			} else {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Trạng thái của bin không hợp lệ.",
				}
			}
		}
	}

	newReceivedPackage := hubOrder.ReceivePackage + numberOfPackage
	if newReceivedPackage > hubOrder.NumPackage {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Thừa kiện",
		}
	}

	if newReceivedPackage < 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Số lượng kiện không được nhỏ hơn 0",
		}
	}

	afterOption := options.After
	updateResult := model.HUBShippingOrderDB.UpdateOne(bson.M{
		"reference_code": hubOrder.ReferenceCode,
		"hub_code":       currentHub,
	}, bson.M{
		"receive_ package": newReceivedPackage,
		"products":         hubOrder.Products,
	}, &options.FindOneAndUpdateOptions{
		Upsert:         &enum.False,
		ReturnDocument: &afterOption,
	})

	if updateResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: updateResult.Message,
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật số kiện nhận thành công",
		Data:    updateResult.Data.([]*model.HubShippingOrder),
	}
}

func ExtendPickUp(session *request.CheckInPickup) *common.APIResponse {
	if len(session.HubOrders) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã liên kết không được để trống",
		}
	}

	type errHubOrderStruct struct {
		ReferenceCode string `json:"referenceCode"`
		ErrorMessage  string `json:"error"`
	}

	var errHubOrder []errHubOrderStruct

	for _, o := range session.HubOrders {
		hubOrderRaw := model.HUBShippingOrderDB.QueryOne(bson.M{
			"reference_code": o.ReferenceCode,
			"hub_code":       session.HubCode,
		})

		if hubOrderRaw.Status != common.APIStatus.Ok {
			errHubOrder = append(errHubOrder, errHubOrderStruct{
				ErrorMessage:  "Đơn giao hàng " + o.ReferenceCode + " không tồn tại",
				ReferenceCode: o.ReferenceCode,
			})
			continue
		}

		hubOrder := hubOrderRaw.Data.([]*model.HubShippingOrder)[0]
		if hubOrder.ReceivePackage <= 0 {
			errHubOrder = append(errHubOrder, errHubOrderStruct{
				ErrorMessage:  "Phải có ít nhất một kiện",
				ReferenceCode: o.ReferenceCode,
			})
			continue
		}

		current := time.Now()
		afterOption := options.After

		// TODO: Get hub INFO => check Hub Firstmile
		getHub := model.HubDB.QueryOne(bson.M{"code": hubOrder.HUBCode})
		hub := getHub.Data.([]*model.Hub)[0]

		shippingOrder := model.ShippingOrderDB.UpdateOne(bson.M{
			"reference_code": o.ReferenceCode,
		}, bson.M{
			"status":            enum.TPLCallbackStatus.STORING,
			"last_updated_time": &current,
			"action_time":       current.Unix(),
		}, &options.FindOneAndUpdateOptions{
			ReturnDocument: &afterOption,
		})

		if shippingOrder.Status != common.APIStatus.Ok {
			errHubOrder = append(errHubOrder, errHubOrderStruct{
				ErrorMessage:  "Không thể cập nhật đơn: " + o.ReferenceCode,
				ReferenceCode: o.ReferenceCode,
			})
			continue
		}

		updater := bson.M{
			"status":            enum.HubShippingOrderStatus.STORING,
			"action":            "IN",
			"last_updated_time": &current,
			"action_time":       current.Unix(),
		}

		// Nếu bin đã SCANNING thì chuyển sang STORING
		// Nếu vẫn còn bin LOST thì tạo đơn mới với danh sách bin là những bin ở trạng thái LOST
		var lostBin []*model.Product
		var failProducts []string

		numPack := 0
		var weight float64 = 0
		for _, p := range hubOrder.Products {
			if p.Status != nil && *p.Status == enum.ProductStatus.LOST {
				// Đẩy bin vẫn bị lost vào danh sách
				lostBin = append(lostBin, p)
				// Cập nhật bin bị LOST thành REMOVED, do phía trên là pass by value nên sẽ k ảnh hưởng đến array lostBin
				p.Status = &enum.ProductStatus.REMOVED
			}

			if p.Status != nil && *p.Status == enum.ProductStatus.SCANNING {
				p.Status = &enum.ProductStatus.STORING
				weight += p.Weight
				numPack++
				// Reset Location
				if hub.WarehouseReferenceCode != "" {
					_, err := client.Services.WarehouseCoreClient.ResetLocationWarehouse(hub.WarehouseReferenceCode, p.SKU)
					// Kiểm tra sản phẩm có tồn tại bên WH
					if err != nil {
						failProducts = append(failProducts, p.SKU)
					}
				}
			}
		}

		// TODO: Push to CreatePickupLostBin queue:
		if len(lostBin) > 0 {
			_ = shippingOrderQueue.PushCreateHubShippingOrder(shippingOrderQueue.RequestBodyHandover{
				HubOrder:       *hubOrder,
				RemovedBinList: lostBin,
			}, o.ReferenceCode)
		}

		updater["products"] = hubOrder.Products
		updater["num_package"] = numPack
		if weight == 0 {
			weight = 1
		}
		updater["weight"] = weight

		updateHubOrderResult := model.HUBShippingOrderDB.UpdateOne(bson.M{
			"reference_code": o.ReferenceCode,
			"hub_code":       session.HubCode,
		}, updater)

		if updateHubOrderResult.Status != common.APIStatus.Ok {
			errHubOrder = append(errHubOrder, errHubOrderStruct{
				ErrorMessage:  "Không thể cập nhật đơn: " + o.ReferenceCode,
				ReferenceCode: o.ReferenceCode,
			})
			continue
		}

		// TODO: Complete ShippingOrder
		var referenceCodes []string
		referenceCodes = append(referenceCodes, hubOrder.ReferenceCode)
		if hub.WarehouseReferenceCode != "" {
			err := client.Services.TransportingClient.CompleteShippingOrder(referenceCodes)
			if err != nil {
				err = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "UNABLE_COMPLETE_SHIPPING_ORDER",
					Title:   "CANT COMPLETE SHIPPING ORDER",
					Message: fmt.Sprintf("%v", failProducts),
				})
			}
		}

		callbackData := request.Callback{
			ActionTime:    &current,
			SO:            o.ReferenceCode,
			ReferenceCode: o.ReferenceCode,
			Status:        &enum.TPLCallbackStatus.STORING,
			StatusName:    "Đã nhập kho " + hub.Name,
			TPLStatus:     string(enum.TPLCallbackStatus.STORING),
			TPLStatusName: "Đã nhập kho " + hub.Name,
			Type:          &enum.ShippingOrderType.PICKUP,
		}

		_ = syncData.PushCreateTPLCallbackQueue(callbackData, callbackData.SO)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Đơn nhập kho thành công!",
	}
}

func CheckinPoPackage(
	parentReferenceCode, receiveSessionCode,
	hubCode, warehouseCode, desWhCode,
	referenceCode, note string,
	numPack, checkInBy int) *common.APIResponse {
	if parentReferenceCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã PO không được để trống",
		}
	}

	if receiveSessionCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã phiên nhận không được để trống",
		}
	}

	if numPack <= 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Phải có ít nhất một kiện",
		}
	}

	if desWhCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã kho nhận không được để trống",
		}
	}

	desHub := model.HubDB.QueryOne(bson.M{
		"warehouse_reference_code": desWhCode,
	})
	if desHub.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không xác định được hub nhận hàng",
		}
	}
	// Nếu checkin tại kho thì tính phí wh - hub
	// Nếu checkin tại hub thì tính phú hub - wh
	if warehouseCode != "" {
		hubRaw := model.HubDB.QueryOne(bson.M{
			"warehouse_reference_code": warehouseCode,
		})

		if hubRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không tìm thấy hub gán với mã kho: " + warehouseCode,
			}
		}

		hub := hubRaw.Data.([]*model.Hub)[0]
		hubCode = hub.Code
	}

	if referenceCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã đơn không được để trống",
		}
	}

	employees := GetAccountInfo(int64(checkInBy), "", 0, 0)
	if employees == nil || len(employees) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Nhân viên không hợp lệ",
			ErrorCode: "INVALID_DRIVER_ID",
		}
	}
	employee := employees[0]

	hubOrderRaw := model.HUBShippingOrderDB.QueryOne(bson.M{
		"reference_code": referenceCode,
	})

	// Nếu có thì cập nhật kiện
	if hubOrderRaw.Status == common.APIStatus.Ok {
		hubOrder := hubOrderRaw.Data.([]*model.HubShippingOrder)[0]
		if hubOrder != nil {
			if warehouseCode != "" && hubOrder.ToCustomerCode != warehouseCode {
				warehouseCode = ""
			}

			hubResp := model.HubDB.QueryOne(bson.M{
				"code": hubOrder.HUBCode,
			})
			if hubResp.Status != common.APIStatus.Ok {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Không tìm thấy hub",
				}
			}
			hub := hubResp.Data.([]*model.Hub)[0]

			current := time.Now()
			callbackPartner := request.Callback{
				Status:        &enum.TPLCallbackStatus.STORING,
				ReferenceCode: hubOrder.ReferenceCode,
				PoCode:        hubOrder.ParentReferenceCode,
				TrackingCode:  hubOrder.TrackingCode,
				ActionTime:    &current,
				PartnerCode:   "INTERNAL",
				HubCode:       hubCode,
				NumPackage:    int64(numPack),
				WarehouseCode: hubOrder.ToCustomerCode,
			}
			callbackData := request.Callback{
				ActionTime:    &current,
				SO:            hubOrder.ReferenceCode,
				ReferenceCode: hubOrder.ReferenceCode,
				Status:        &enum.TPLCallbackStatus.STORING,
				StatusName:    "Đã nhập kho " + hub.Name,
				TPLStatus:     string(enum.TPLCallbackStatus.STORING),
				TPLStatusName: "Đã nhập kho " + hub.Name,
				Type:          &enum.ShippingOrderType.FMPO,
			}
			shippingOrderUpdater := bson.M{
				"status":                enum.TPLCallbackStatus.STORING,
				"receive_session_code":  receiveSessionCode,
				"last_updated_time":     &current,
				"action_time":           current.Unix(),
				"num_package":           numPack,
				"checkin_num_pack":      numPack,
				"stored_first_hub_time": current.Unix(),
				"note":                  note,
			}
			hubOrderUpdater := bson.M{
				"status":               enum.HubShippingOrderStatus.STORING,
				"receive_session_code": receiveSessionCode,
				"last_updated_time":    &current,
				"action_time":          current.Unix(),
				"num_package":          numPack,
				"note":                 note,
			}
			returnAfter := options.After
			needCreateReconcile := false
			storedShippingOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
				"reference_code": hubOrder.ReferenceCode,
			})

			if storedShippingOrderRaw.Status != common.APIStatus.Ok {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Đợn hàng không tồn tại",
				}
			}
			storedShippingOrder := storedShippingOrderRaw.Data.([]*model.ShippingOrder)[0]

			if storedShippingOrder.IsBookDropOff != nil &&
				*storedShippingOrder.IsBookDropOff {
				hubOrderUpdater["driver_id"] = checkInBy
				hubOrderUpdater["driver_name"] = employee.Fullname
			}

			// Nếu lấy hàng tại kho, phí sẽ bằng 0 do logistic không thực hiện vận chuyển
			if warehouseCode != "" {
				shippingOrderUpdater["status"] = enum.TPLCallbackStatus.DELIVERED
				callbackPartner.Status = &enum.TPLCallbackStatus.DELIVERED
				callbackData.Status = &enum.TPLCallbackStatus.DELIVERED
				hubOrderUpdater["status"] = enum.HubShippingOrderStatus.COD_COLLECTED
				hubOrderUpdater["delivered_time"] = current.Unix()
				needCreateReconcile = true
				// Nếu lấy hàng tại kho VÀ là đơn người gửi trả hàng thì chỉ chuyển hub order sang DELIVERED VÀ không tạo đối soát
				// Do đối soát đã được tạo từ trước
				if storedShippingOrder.FeeCollectMethod != nil &&
					*storedShippingOrder.FeeCollectMethod == enum.FeeCollectMethod.SENDER_PAY {
					hubOrderUpdater["status"] = enum.HubShippingOrderStatus.DELIVERED
					needCreateReconcile = false
				}
			}

			// Nếu không checkin tại wh mà người gửi trả tiền phí thì thu COD
			if warehouseCode == "" &&
				storedShippingOrder.FeeCollectMethod != nil &&
				*storedShippingOrder.FeeCollectMethod == enum.FeeCollectMethod.SENDER_PAY &&
				storedShippingOrder.IsBookDropOff != nil &&
				*storedShippingOrder.IsBookDropOff {
				storedShippingOrder.NumPackage = int64(numPack)
				var configFee *model.ConfigFee
				bookRequest := shippingOrderToBookShippingReq(storedShippingOrder)
				customerRaw := model.CustomerDB.QueryOne(bson.M{
					"code":                         storedShippingOrder.FromCustomerCode,
					"applied_fees.config_fee_type": enum.ConfigFeeType.TRANSPORT,
				})
				if customerRaw.Status != common.APIStatus.Ok {
					configFeeRaw := model.ConfigFeeDB.QueryOne(bson.M{
						"code": conf.Config.DefaultFMDropOffFeeCode,
					})
					if configFeeRaw.Status != common.APIStatus.Ok {
						return &common.APIResponse{
							Status:  common.APIStatus.Invalid,
							Message: "Biểu phí mặc định không tồn tại",
						}
					}
					configFee = configFeeRaw.Data.([]*model.ConfigFee)[0]
				} else {
					customer := customerRaw.Data.([]*model.Customer)[0]
					var configFeeId int64
					for _, appliedFee := range *customer.AppliedFees {
						if appliedFee.ConfigFeeType == enum.ConfigFeeType.TRANSPORT {
							configFeeId = appliedFee.ConfigFeeId
							break
						}
					}
					configFeeRaw := model.ConfigFeeDB.QueryOne(bson.M{
						"config_id": configFeeId,
					})
					if configFeeRaw.Status != common.APIStatus.Ok {
						return &common.APIResponse{
							Status:  common.APIStatus.Invalid,
							Message: "Biểu phí mặc định không tồn tại",
						}
					}
					configFee = configFeeRaw.Data.([]*model.ConfigFee)[0]
				}
				feeAmount, err := CalculateOrderFee(bookRequest, configFee)
				if err != nil {
					return &common.APIResponse{
						Status:  common.APIStatus.Invalid,
						Message: err.Error(),
					}
				}
				shippingOrderUpdater["fee_amount"] = feeAmount
				shippingOrderUpdater["total_collect_sender_amount"] = feeAmount
				shippingOrderUpdater["cod_amount"] = feeAmount
				shippingOrderUpdater["fee_sender_amount"] = feeAmount
				shippingOrderUpdater["delivered_time"] = current.Unix()

				hubOrderUpdater["fee_amount"] = feeAmount
				hubOrderUpdater["total_collect_sender_amount"] = feeAmount
				hubOrderUpdater["cod_amount"] = feeAmount
				hubOrderUpdater["fee_sender_amount"] = feeAmount
				hubOrderUpdater["delivered_time"] = current.Unix()

				needCreateReconcile = true
			}

			if storedShippingOrder.FeeCollectMethod != nil &&
				*storedShippingOrder.FeeCollectMethod == enum.FeeCollectMethod.DEBT &&
				storedShippingOrder.IsBookDropOff != nil &&
				*storedShippingOrder.IsBookDropOff &&
				*storedShippingOrder.Status == enum.TPLCallbackStatus.PICKED {
				reconcile.PushReconcileOrder(reconcile.AddOrderToReconcileSession{
					ReferenceCode: storedShippingOrder.ReferenceCode,
					CustomerCode:  storedShippingOrder.FromCustomerCode,
				}, storedShippingOrder.ReferenceCode)
			}

			updateShippingOrderResult := model.ShippingOrderDB.UpdateOne(bson.M{
				"reference_code": hubOrder.ReferenceCode,
			}, shippingOrderUpdater)
			if updateShippingOrderResult.Status != common.APIStatus.Ok {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Không thể cập nhật số kiện",
				}
			}

			updateHubOrderResult := model.HUBShippingOrderDB.UpdateOne(bson.M{
				"reference_code": hubOrder.ReferenceCode,
				"hub_code":       hubCode,
			}, hubOrderUpdater, &options.FindOneAndUpdateOptions{
				ReturnDocument: &returnAfter,
			})

			if updateHubOrderResult.Status != common.APIStatus.Ok {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Không thể cập nhật số kiện",
				}
			}

			if storedShippingOrder.FeeCollectMethod != nil &&
				*storedShippingOrder.FeeCollectMethod == enum.FeeCollectMethod.DEBT &&
				storedShippingOrder.IsBookDropOff != nil &&
				*storedShippingOrder.IsBookDropOff &&
				*storedShippingOrder.Status == enum.TPLCallbackStatus.STORING &&
				storedShippingOrder.NumPackage != int64(numPack) {
				updatedShippingOrder := updateShippingOrderResult.Data.([]*model.ShippingOrder)[0]
				reconcile.PushUpdateOrderFee(reconcile.UpdateOrderFeeSession{
					StoredShippingOrder: *updatedShippingOrder,
				}, storedShippingOrder.ReferenceCode)
			}

			if needCreateReconcile &&
				storedShippingOrder.FeeCollectMethod != nil &&
				*storedShippingOrder.FeeCollectMethod != enum.FeeCollectMethod.DEBT {
				reconcileOrder := updateHubOrderResult.Data.([]*model.HubShippingOrder)[0]
				callbackData.StatusName = "Giao hàng thành công cho kho: " + warehouseCode
				callbackData.TPLStatusName = "Giao hàng thành công cho kho: " + warehouseCode
				_, _ = CreateReconcile(reconcileOrder, string(enum.ReconcileTypeRequest.RIDER_HUB))
			}

			if storedShippingOrder.CallbackUrl != "" {
				partner := client.GetWebhookClient("INTERNAL")
				callbackPartner.CallbackUrl = storedShippingOrder.CallbackUrl
				_ = partner.SendCallbackToPartner(callbackPartner)
			}
			if note != "" {
				callbackData.StatusName += ". Ghi chú: " + note
				callbackData.TPLStatusName += ". Ghi chú: " + note
			}

			_ = syncData.PushCreateTPLCallbackQueue(callbackData, callbackData.SO)
			return &common.APIResponse{
				Status:  common.APIStatus.Ok,
				Message: "Checkin thành công",
			}
		}

	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Checkin thành công",
	}
}

func UpdateNumPack(referenceCode string, numPack int64, weight float64) *common.APIResponse {
	if referenceCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã liên kết không được để trống",
		}
	}

	if numPack <= 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Số kiện phải lớn hơn 0",
		}
	}

	shippingOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
		"reference_code": referenceCode,
	})
	if shippingOrderRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không tìm thấy đơn lấy hàng",
		}
	}

	shippingOrder := shippingOrderRaw.Data.([]*model.ShippingOrder)[0]

	updater := bson.M{
		"num_package":      numPack,
		"checkin_num_pack": numPack,
		"weight":           weight,
	}

	if shippingOrder.ShippingType != nil &&
		*shippingOrder.ShippingType == enum.ShippingOrderType.FMPO &&
		shippingOrder.FeeCollectMethod != nil &&
		*shippingOrder.FeeCollectMethod == enum.FeeCollectMethod.SENDER_PAY &&
		(shippingOrder.IsBookDropOff == nil ||
			!*shippingOrder.IsBookDropOff) &&
		shippingOrder.NumPackage != numPack {

		shippingOrder.NumPackage = numPack
		shippingOrder.Weight = weight
		shippingOrder.CheckinNumPack = numPack
		if strings.HasPrefix(shippingOrder.ReferenceCode, "PO") {
			shippingOrder.ShippingType = &enum.ShippingOrderType.PO
		}
		if strings.HasPrefix(shippingOrder.ReferenceCode, "PGH") {
			shippingOrder.ShippingType = &enum.ShippingOrderType.PGH
		}
		bookReq := shippingOrderToBookShippingReq(shippingOrder)
		resp := EstimateShippingOrderFee(bookReq)
		if resp.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không thể cập nhật số kiện, do biểu phí không hợp lệ",
			}
		}

		fee := resp.Data.([]response.Fee)[0].FeeAmount
		updater["cod_amount"] = fee
		updater["fee_amount"] = fee
		updater["fee_sender_amount"] = fee
		updater["total_collect_sender_amount"] = fee
	}

	if shippingOrder.ShippingType != nil &&
		*shippingOrder.ShippingType == enum.ShippingOrderType.EO &&
		(shippingOrder.IsBookDropOff == nil ||
			!*shippingOrder.IsBookDropOff) &&
		shippingOrder.NumPackage != numPack {
		shippingOrder.NumPackage = numPack
		shippingOrder.Weight = weight
		shippingOrder.CheckinNumPack = numPack

		bookReq := shippingOrderToBookShippingReq(shippingOrder)
		resp := EstimateShippingOrderFee(bookReq)
		if resp.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không thể cập nhật số kiện, do biểu phí không hợp lệ",
			}
		}

		fee := resp.Data.([]response.Fee)[0].FeeAmount
		updater["fee_amount"] = fee
		if shippingOrder.FeeCollectMethod != nil &&
			*shippingOrder.FeeCollectMethod == enum.FeeCollectMethod.SENDER_PAY {
			updater["fee_sender_amount"] = fee
			updater["total_collect_sender_amount"] = fee
		}

		if shippingOrder.FeeCollectMethod != nil &&
			*shippingOrder.FeeCollectMethod == enum.FeeCollectMethod.RECEIVER_PAY {
			updater["fee_receiver_amount"] = fee
			updater["total_collect_receiver_amount"] = fee + shippingOrder.CODAmount
		}
	}

	updateShippingOrder := model.ShippingOrderDB.UpdateOne(bson.M{
		"reference_code": referenceCode,
	}, updater)

	if updateShippingOrder.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không thể cập nhật số kiện",
		}
	}

	updateHubOrder := model.HUBShippingOrderDB.UpdateOne(bson.M{
		"reference_code": referenceCode,
		"hub_code":       shippingOrder.CurrentHub,
	}, updater)

	if updateHubOrder.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không thể cập nhật số kiện",
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật số kiện thành công",
	}
}

func CrawlWithinDay() {
	//isJobLockAvailable := utils.CheckJobLockAvailable(enum.CRAWL_PRODUCTIVITY, conf.Config.RepeatSyncAccountTimeJob)
	//if !isJobLockAvailable {
	//	return
	//}

	filter := bson.M{}
	now := time.Now()
	var RANGE = 3
	successPickupEnums, pickupEnums, successDeliveryEnums, deliveringEnums := listStatusProductivity()
	filter["account_id"] = 28239
	filter["hub_code"] = "HUBCANTHO"
	y, m, d := now.Date()
	bound := time.Date(y, m, d-RANGE, 0, 0, 0, 0, now.Location()) // from range day to today
	// list hub order types required
	listHubOrderTypes := listHubShippingOrderTypes()
	for {
		accountRaw := model.AccountDB.Query(filter, 0, 50, &bson.M{"_id": -1})
		if accountRaw.Status != common.APIStatus.Ok {
			break
		}

		accounts := accountRaw.Data.([]*model.Account)
		smallestId := accounts[len(accounts)-1].ID
		filter["_id"] = bson.M{
			"$lt": smallestId,
		}
		//map[HubCode][ReferenceCode]
		for _, account := range accounts {
			if account.Status != string(enum.AccountStatus.ACTIVE) {
				continue
			}
			filterDriver := bson.M{}
			filterDriver["hub_code"] = account.HubCode
			filterDriver["$or"] = []bson.M{
				{
					"logs.action_time": bson.M{
						"$gte": bound.Unix(),
					},
					"logs.driver_id": account.AccountID,
				},
				{
					"driver_id": account.AccountID,
					"action_time": bson.M{
						"$gte": bound.Unix(),
					},
				},
			}
			hubOrdersRaw := model.HUBShippingOrderDB.Query(filterDriver, 0, 2000, nil)
			if hubOrdersRaw.Status != common.APIStatus.Ok {
				continue
			}
			hubOrders := hubOrdersRaw.Data.([]*model.HubShippingOrder)
			// [hubCode][driverId][year][month][day]
			listData := map[string]map[int64]map[int]map[time.Month]map[int][]*model.HubShippingOrder{}

			for _, hubOrder := range hubOrders {
				if !ContainsHubOrderTypes(listHubOrderTypes, hubOrder.Type, hubOrder.SubType) {
					continue
				}

				if *hubOrder.Type == enum.HubOrderType.PICKUP {
					fmt.Println(hubOrder.ReferenceCode)
				}

				needUpdate := false
				if len(hubOrder.Logs) == 0 && hubOrder.DriverID != 0 {
					if *hubOrder.Type != enum.HubOrderType.PICKUP {
						if ContainsStatus(successDeliveryEnums, *hubOrder.Status) {
							hubOrder.Logs = append(hubOrder.Logs, model.HubShippingOrder{
								ReferenceCode:      hubOrder.ReferenceCode,
								Status:             &enum.HubShippingOrderStatus.WAIT_TO_DELIVERY,
								DriverID:           hubOrder.DriverID,
								ProductivityAction: &enum.ProductivityAction.ASSIGN_DELIVERY,
								ActionTime:         hubOrder.ActionTime,
								Type:               hubOrder.Type,
								SubType:            hubOrder.SubType,
							})
							hubOrder.Logs = append(hubOrder.Logs, model.HubShippingOrder{
								ReferenceCode:      hubOrder.ReferenceCode,
								Status:             &enum.HubShippingOrderStatus.DELIVERED,
								DriverID:           hubOrder.DriverID,
								ProductivityAction: &enum.ProductivityAction.DELIVERED,
								ActionTime:         hubOrder.ActionTime,
								Type:               hubOrder.Type,
								SubType:            hubOrder.SubType,
							})
							needUpdate = true
						} else if ContainsStatus(deliveringEnums, *hubOrder.Status) {
							hubOrder.Logs = append(hubOrder.Logs, model.HubShippingOrder{
								Status:             &enum.HubShippingOrderStatus.WAIT_TO_DELIVERY,
								DriverID:           hubOrder.DriverID,
								ProductivityAction: &enum.ProductivityAction.ASSIGN_DELIVERY,
								ActionTime:         hubOrder.ActionTime,
								Type:               hubOrder.Type,
								SubType:            hubOrder.SubType,
							})
							needUpdate = true
						}
					} else {
						if ContainsStatus(successPickupEnums, *hubOrder.Status) {
							hubOrder.Logs = append(hubOrder.Logs, model.HubShippingOrder{
								Status:             &enum.HubShippingOrderStatus.READY_TO_PICK,
								DriverID:           hubOrder.DriverID,
								ProductivityAction: &enum.ProductivityAction.ASSIGN_PICK,
								ActionTime:         hubOrder.ActionTime,
								Type:               hubOrder.Type,
								SubType:            hubOrder.SubType,
							})

							hubOrder.Logs = append(hubOrder.Logs, model.HubShippingOrder{
								Status:             &enum.HubShippingOrderStatus.WAIT_TO_STORING,
								DriverID:           hubOrder.DriverID,
								ProductivityAction: &enum.ProductivityAction.PICKED,
								ActionTime:         hubOrder.ActionTime,
								Type:               hubOrder.Type,
								SubType:            hubOrder.SubType,
							})
							needUpdate = true
						} else if ContainsStatus(pickupEnums, *hubOrder.Status) {
							hubOrder.Logs = append(hubOrder.Logs, model.HubShippingOrder{
								Status:             &enum.HubShippingOrderStatus.READY_TO_PICK,
								DriverID:           hubOrder.DriverID,
								ProductivityAction: &enum.ProductivityAction.ASSIGN_PICK,
								ActionTime:         hubOrder.ActionTime,
								Type:               hubOrder.Type,
								SubType:            hubOrder.SubType,
							})
							needUpdate = true
						}
					}
				}
				if needUpdate {
					err := model.HUBShippingOrderDB.UpdateOne(bson.M{
						"_id": hubOrder.ID,
					}, bson.M{
						"logs": hubOrder.Logs,
					})
					if err.Status != common.APIStatus.Ok {
						fmt.Println("Update logs error: ", err.Message)
					}
				}

				if MappingDriverProductivity(listData, hubOrder, bound) {
					continue
				}
			}
			for _, hub := range listData {
				for driverId, _ := range hub {
					if driverId != account.AccountID {
						delete(hub, driverId)
					}
				}
			}

			UpdateCrawlData(listData, listHubOrderTypes)

		}
	}
}

func InitMonthReport(data *model.CarrierProductivity, listHubOrderTypes []request.HubOrderType) (result *model.CarrierProductivity, isNew bool) {
	isExist := model.CarrierProductivityDB.QueryOne(data)

	if isExist.Status == common.APIStatus.Ok {
		isNew = false
		result = isExist.Data.([]*model.CarrierProductivity)[0]

	} else {
		isNew = true
		result = data
		day := time.Date(data.Year, time.Month(data.Month)+1, 0, 0, 0, 0, 0, time.Now().Location()).Day()
		result.Reports = []map[enum.HubOrderTypeValue][]model.HubOrderTypeReport{}
		for i := 0; i < day; i++ {
			result.Reports = append(result.Reports, map[enum.HubOrderTypeValue][]model.HubOrderTypeReport{})
			result.Reports[i] = InitDayReport()
		}
	}
	return result, isNew
}

func InitDayReport() (result map[enum.HubOrderTypeValue][]model.HubOrderTypeReport) {
	result = make(map[enum.HubOrderTypeValue][]model.HubOrderTypeReport) // make map tại đây
	listHubOrderTypes := listHubShippingOrderTypes()

	for _, hubOrderType := range listHubOrderTypes {
		typeReport := model.HubOrderTypeReport{
			SuccessDelivery: 0,
			TotalDelivery:   0,
			SuccessPickup:   0,
			TotalPickup:     0,
		}

		if hubOrderType.SubType != "" {
			typeReport.SubType = new(enum.SubTypeValue)
			*typeReport.SubType = hubOrderType.SubType
		}

		result[hubOrderType.Type] = append(result[hubOrderType.Type], typeReport)
	}
	return result
}

func ContainsStatus(s []enum.HubShippingOrderStatusValue, e enum.HubShippingOrderStatusValue) bool {
	for _, a := range s {
		if a == e {
			return true
		}
	}
	return false
}

func ContainsHubOrderTypes(list []request.HubOrderType, hubOrderType *enum.HubOrderTypeValue, hubOrderSubType *enum.SubTypeValue) bool {
	for _, v := range list {
		if hubOrderType != nil && v.Type == *hubOrderType {
			if (v.SubType == "" && hubOrderSubType == nil) ||
				(v.SubType != "" && hubOrderSubType != nil &&
					v.SubType == *hubOrderSubType) {
				return true
			}
		}
	}
	return false
}

func listHubShippingOrderTypes() (result []request.HubOrderType) {
	result = []request.HubOrderType{
		{
			Type: enum.HubOrderType.DELIVERY,
		},
		{
			Type: enum.HubOrderType.TRANSPORTING,
		},
		{
			Type:    enum.HubOrderType.PICKUP,
			SubType: enum.SubType.PICKUP,
		},
		{
			Type:    enum.HubOrderType.PICKUP,
			SubType: enum.SubType.RETURN,
		},
		{
			Type:    enum.HubOrderType.PICKUP,
			SubType: enum.SubType.FMPO,
		},
		/*
			request.HubOrderType{
				Type: enum.HubOrderType.DELIVERY,
				SubType: enum.SubType.EO,
			},
			request.HubOrderType{
				Type: enum.HubOrderType.PICKUP,
				SubType: enum.SubType.EO,
			},
		*/
	}
	return result
}

func listStatusProductivity() (successPickup, pickup, successDelivery, delivery []enum.HubShippingOrderStatusValue) {
	successPickup = []enum.HubShippingOrderStatusValue{
		enum.HubShippingOrderStatus.WAIT_TO_STORING,
		enum.HubShippingOrderStatus.STORING,
		enum.HubShippingOrderStatus.COD_COLLECTED,
		enum.HubShippingOrderStatus.DONE_TRANSPORTING,
		enum.HubShippingOrderStatus.COMPLETED,
	}

	pickup = []enum.HubShippingOrderStatusValue{
		enum.HubShippingOrderStatus.READY_TO_PICK,
		enum.HubShippingOrderStatus.PICK_FAIL,
		enum.HubShippingOrderStatus.PICKING,
		enum.HubShippingOrderStatus.RETURN,
		enum.HubShippingOrderStatus.RETURNING,
		enum.HubShippingOrderStatus.RETURNED,
		enum.HubShippingOrderStatus.DAMAGE,
		enum.HubShippingOrderStatus.LOST,
	}

	successDelivery = []enum.HubShippingOrderStatusValue{
		enum.HubShippingOrderStatus.DELIVERED,
		enum.HubShippingOrderStatus.COD_COLLECTED,
		enum.HubShippingOrderStatus.COMPLETED,
	}

	delivery = []enum.HubShippingOrderStatusValue{
		enum.HubShippingOrderStatus.WAIT_TO_DELIVERY,
		enum.HubShippingOrderStatus.DELIVERING,
		enum.HubShippingOrderStatus.DELIVERY_FAIL,
		enum.HubShippingOrderStatus.RETURN,
		enum.HubShippingOrderStatus.RETURNING,
		enum.HubShippingOrderStatus.RETURNED,
		enum.HubShippingOrderStatus.DAMAGE,
		enum.HubShippingOrderStatus.LOST,
	}
	return
}

func MappingData(listData map[string]map[int64]map[int]map[time.Month]map[int][]*model.HubShippingOrder, hubOrder *model.HubShippingOrder) {
	hubCode := hubOrder.HUBCode
	driverId := hubOrder.DriverID

	if listData[hubCode] == nil {
		listData[hubCode] = make(map[int64]map[int]map[time.Month]map[int][]*model.HubShippingOrder)
	}

	if listData[hubCode][driverId] == nil {
		listData[hubCode][driverId] = make(map[int]map[time.Month]map[int][]*model.HubShippingOrder)
	}

	//_, offset := time.Now().Zone()
	year, month, day := time.Unix(hubOrder.ActionTime, 0).Date()

	if listData[hubCode][driverId][year] == nil {
		listData[hubCode][driverId][year] = make(map[time.Month]map[int][]*model.HubShippingOrder)
	}

	if listData[hubCode][driverId][year][month] == nil {
		listData[hubCode][driverId][year][month] = make(map[int][]*model.HubShippingOrder)
	}

	listData[hubCode][driverId][year][month][day] = append(listData[hubCode][driverId][year][month][day], hubOrder)
}

func UpdateCrawlData(listData map[string]map[int64]map[int]map[time.Month]map[int][]*model.HubShippingOrder, listHubOrderTypes []request.HubOrderType) {
	successPickupEnums, pickupEnums, successDeliveryEnums, deliveringEnums := listStatusProductivity()

	for hub, listDriver := range listData {
		for driver, listTime := range listDriver {
			for year, months := range listTime {
				for month, days := range months {

					data := new(model.CarrierProductivity)
					data.HubCode = hub
					data.DriverId = driver
					data.Year = year
					data.Month = int(month)

					updateData, isNew := InitMonthReport(data, listHubOrderTypes)
					updateData.UpdatedTime = time.Now().Unix()

					for day, hubOrders := range days {
						updateData.Reports[day-1] = InitDayReport()
						for _, hubOrder := range hubOrders {
							for index, hubOrderReport := range updateData.Reports[day-1][*hubOrder.Type] {
								if (hubOrderReport.SubType == nil && hubOrder.SubType == nil) ||
									(hubOrderReport.SubType != nil && hubOrder.SubType != nil &&
										*hubOrderReport.SubType == *hubOrder.SubType) {

									if *hubOrder.Type != enum.HubOrderType.PICKUP {
										if ContainsStatus(successDeliveryEnums, *hubOrder.Status) {
											updateData.Reports[day-1][*hubOrder.Type][index].SuccessDelivery += 1
											//updateData.Reports[day-1][*hubOrder.Type][index].TotalDelivery += 1
										} else if ContainsStatus(deliveringEnums, *hubOrder.Status) {
											updateData.Reports[day-1][*hubOrder.Type][index].TotalDelivery += 1
										}
									} else {
										if ContainsStatus(successPickupEnums, *hubOrder.Status) {
											updateData.Reports[day-1][*hubOrder.Type][index].SuccessPickup += 1
											//updateData.Reports[day-1][*hubOrder.Type][index].TotalPickup += 1
											fmt.Println("Success: ")
											fmt.Println(hubOrder.ReferenceCode)

										} else if ContainsStatus(pickupEnums, *hubOrder.Status) {
											fmt.Println("Total: ")
											fmt.Println(hubOrder.ReferenceCode)
											updateData.Reports[day-1][*hubOrder.Type][index].TotalPickup += 1
										}
									}
								}
							}
						}
					}

					if isNew {
						update := model.CarrierProductivityDB.Create(updateData)
						if update.Status != common.APIStatus.Ok {
							fmt.Println(update)
						}
					} else {
						updateFilter := bson.M{}
						updateFilter["hub_code"] = hub
						updateFilter["driver_id"] = driver
						updateFilter["year"] = year
						updateFilter["month"] = int(month)
						update := model.CarrierProductivityDB.UpdateOne(updateFilter, updateData)
						if update.Status != common.APIStatus.Ok {
							fmt.Println(update)
						}
					}
				}
			}
		}
	}
}

func MappingPreviousDrivers(listData map[string]map[int64]map[int]map[time.Month]map[int][]*model.HubShippingOrder, hubOrder *model.HubShippingOrder, bound time.Time) {
	if len(hubOrder.PreviousDriverIds) != 0 {
		for _, oldDriver := range hubOrder.PreviousDriverIds {
			if hubOrder.DriverID == oldDriver.DriverID || oldDriver.DriverID == 0 {
				continue
			}

			if bound.Unix() > oldDriver.ActionTime {
				continue
			}
			oldDriver.HUBCode = hubOrder.HUBCode
			oldDriver.Type = hubOrder.Type
			oldDriver.SubType = hubOrder.SubType
			MappingData(listData, oldDriver)
		}
	}
}

func MappingDriverProductivity(
	listData map[string]map[int64]map[int]map[time.Month]map[int][]*model.HubShippingOrder,
	hubOrder *model.HubShippingOrder,
	bound time.Time) bool {
	isFoundValidLog := false
	reassignDriverMap := map[int64]bool{}
	successOrderDriverMap := map[int64]bool{}

	if len(hubOrder.Logs) > 0 {
		for _, log := range hubOrder.Logs {
			if bound.Unix() > log.ActionTime {
				continue
			}
			if log.ProductivityAction == nil {
				continue
			}

			if reassignDriverMap[log.DriverID] &&
				(*log.ProductivityAction == enum.ProductivityAction.ASSIGN_DELIVERY ||
					*log.ProductivityAction == enum.ProductivityAction.ASSIGN_PICK) {
				continue
			}

			if successOrderDriverMap[log.DriverID] &&
				(*log.ProductivityAction == enum.ProductivityAction.DELIVERED ||
					*log.ProductivityAction == enum.ProductivityAction.PICKED) {
				continue
			}

			if *log.ProductivityAction == enum.ProductivityAction.ASSIGN_DELIVERY ||
				*log.ProductivityAction == enum.ProductivityAction.ASSIGN_PICK {
				reassignDriverMap[log.DriverID] = true
			}

			if *log.ProductivityAction == enum.ProductivityAction.DELIVERED ||
				*log.ProductivityAction == enum.ProductivityAction.PICKED {
				successOrderDriverMap[log.DriverID] = true
			}

			isFoundValidLog = true
			tempHubOrder := &model.HubShippingOrder{}
			tempHubOrder.HUBCode = hubOrder.HUBCode
			tempHubOrder.Type = log.Type
			tempHubOrder.SubType = log.SubType
			tempHubOrder.DriverID = log.DriverID
			tempHubOrder.ActionTime = log.ActionTime
			tempHubOrder.Status = log.Status
			tempHubOrder.ReferenceCode = hubOrder.ReferenceCode

			MappingData(listData, tempHubOrder)
		}
	}

	return isFoundValidLog
}

func AddProductivityLog(driverProductivityRequest productivity.DriverProductivity, shippingOrder model.ShippingOrder) {
	driverActivity := model.DriverActivity{
		DriverId:           driverProductivityRequest.DriverID,
		ActionTime:         driverProductivityRequest.ActionTime,
		ProductivityAction: &driverProductivityRequest.ProductivityAction,
		HubCode:            driverProductivityRequest.HubCode,
		HubOrderType:       driverProductivityRequest.Type,
		HubOrderSubType:    driverProductivityRequest.SubType,
	}

	// TODO: đơn EO có thể vừa có đơn giao vừa có đơn lấy
	// Trường hợp gán tài xế,
	if driverProductivityRequest.ProductivityAction == enum.ProductivityAction.ASSIGN_DELIVERY ||
		driverProductivityRequest.ProductivityAction == enum.ProductivityAction.ASSIGN_PICK {
		shippingOrder.DriverActivities = append(shippingOrder.DriverActivities, &driverActivity)
	}

	if (driverProductivityRequest.ProductivityAction == enum.ProductivityAction.DELIVERY_FAIL ||
		driverProductivityRequest.ProductivityAction == enum.ProductivityAction.DELIVERED) &&
		len(shippingOrder.DriverActivities) > 0 {
		closestAssignAtIndex := -1
		for i, da := range shippingOrder.DriverActivities {
			if da.ProductivityAction != nil &&
				*da.ProductivityAction == enum.ProductivityAction.ASSIGN_DELIVERY &&
				da.DriverId == driverProductivityRequest.DriverID &&
				da.HubCode == driverProductivityRequest.HubCode {
				closestAssignAtIndex = i
			}
		}
		if closestAssignAtIndex != -1 {
			shippingOrder.DriverActivities[closestAssignAtIndex].CompleteAssignAt =
				driverProductivityRequest.ActionTime
		}

		shippingOrder.DriverActivities = append(shippingOrder.DriverActivities, &driverActivity)
	}

	if (driverProductivityRequest.ProductivityAction == enum.ProductivityAction.PICK_FAIL ||
		driverProductivityRequest.ProductivityAction == enum.ProductivityAction.PICKED) &&
		len(shippingOrder.DriverActivities) > 0 {
		closestAssignAtIndex := -1
		for i, da := range shippingOrder.DriverActivities {
			if da.ProductivityAction != nil &&
				*da.ProductivityAction == enum.ProductivityAction.ASSIGN_PICK &&
				da.DriverId == driverProductivityRequest.DriverID &&
				da.HubCode == driverProductivityRequest.HubCode {
				closestAssignAtIndex = i
			}
		}
		if closestAssignAtIndex != -1 {
			shippingOrder.DriverActivities[closestAssignAtIndex].CompleteAssignAt =
				driverProductivityRequest.ActionTime
		}

		shippingOrder.DriverActivities =
			append(shippingOrder.DriverActivities, &driverActivity)
	}

	model.ShippingOrderDB.UpdateOne(bson.M{
		"reference_code": driverProductivityRequest.ReferenceCode,
	}, bson.M{
		"driver_activities": shippingOrder.DriverActivities,
	})
}
