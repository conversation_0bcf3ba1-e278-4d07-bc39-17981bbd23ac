package retry_queue

import (
	"log"
	"sync"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"go.mongodb.org/mongo-driver/mongo"
)

type ExecutorJob struct {
	Job *job.Executor
}

var (
	instance     map[string]*ExecutorJob
	onceInit     map[string]*sync.Once
	defaultTopic = "retry_reconcile_client"
)

func init() {
	onceInit = map[string]*sync.Once{}
	instance = map[string]*ExecutorJob{}
	defaultTopic = conf.Config.Topics["retry_reconcile_client"]
}

// InitBackupExecutor func
func InitRetryClientExecutor(dbSession *mongo.Database, Database string, Collection string) {
	instanceName := defaultTopic
	if onceInit[instanceName] == nil {
		onceInit[instanceName] = &sync.Once{}
	}

	onceInit[instanceName].Do(func() {
		instance[instanceName] = &ExecutorJob{
			&job.Executor{ColName: defaultTopic},
		}

		instance[instanceName].Job.InitWithConfig(dbSession,
			Database,
			&job.ExecutorConfiguration{
				ParallelTopicProcessing: false,
				FailThreshold:           50,
				ChannelCount:            1,
				SortedItem:              false,
				LogSize:                 5,
				SelectorDelayMS:         100,
				WaitForReadyTime:        false,
				UniqueItem:              false,
				ConsumedExipredTime:     time.Duration(7) * time.Hour,
				CurVersionTimeoutS:      30,
				OldVersionTimeoutS:      30,
				MaximumWaitToRetryS:     15,
			},
		)

		log.Println("StartConsume", instanceName)
		instance[instanceName].retryReconcile()
		instance[instanceName].Job.StartConsume()
	})
}

func PushRetryReconcile(data interface{}, sortedKey string) (err error) {
	return instance[defaultTopic].Job.Push(
		data, &job.JobItemMetadata{
			Topic:     defaultTopic,
			SortedKey: sortedKey,
		})
}

type RetryReconcile struct {
	HubOrder      *model.HubShippingOrder `json:"hubOrder"`
	ReconcileType string                  `json:"reconcileType"`
}
