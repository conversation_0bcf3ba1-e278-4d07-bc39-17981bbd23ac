package api

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
)

func GetLeadTimeConfig(req sdk.APIRequest, resp sdk.APIResponder) error {

	var (
		q        = req.GetParam("q")
		offset   = int64(sdk.ParseInt(req.GetParam("offset"), 0))
		limit    = int64(sdk.ParseInt(req.GetParam("limit"), 100))
		getTotal = req.GetParam("getTotal") == "true"
	)

	if q == "" {
		q = "{}"
	}

	var input *request.LeadTimeQuery
	err := json.Unmarshal([]byte(q), &input)

	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data. " + err.Error(),
		})
	}

	return resp.Respond(action.GetListLeadTimeConfig(input, offset, limit, getTotal))
}
func ImportLeadTimeConfig(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input []model.ConfigLeadTime
	err := req.GetContent(&input)

	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.ImportLeadTimeConfig(input))
}

func CreateLeadTimeConfig(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.ConfigLeadTime
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.CreateLeadTimeConfig(&input))
}

func CalculateLeadTime(req sdk.APIRequest, resp sdk.APIResponder) error {
	var q = req.GetParam("q")
	var input *request.CalculateLeadTimeRequest
	err := json.Unmarshal([]byte(q), &input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data. " + err.Error(),
		})
	}

	return resp.Respond(action.CalculateLeadTime(input))
}

func GetAvailableConfigLeadTimeCarriers(req sdk.APIRequest, resp sdk.APIResponder) error {
	var q = req.GetParam("q")

	var input *request.GetAvailableLeadTimeRequest
	err := json.Unmarshal([]byte(q), &input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data. " + err.Error(),
		})
	}

	return resp.Respond(action.GetAvailableConfigLeadTimeCarriers(*input))
}

func GetConfigProvinceOfCarrier(req sdk.APIRequest, resp sdk.APIResponder) error {
	var q = req.GetParam("q")

	var input *request.GetAvailableLeadTimeRequest
	err := json.Unmarshal([]byte(q), &input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data. " + err.Error(),
		})
	}

	return resp.Respond(action.GetConfigProvinceOfCarrier(*input))
}

func GetProvinceLeadTime(req sdk.APIRequest, resp sdk.APIResponder) error {
	var q = req.GetParam("q")

	var input *request.GetAvailableLeadTimeRequest
	err := json.Unmarshal([]byte(q), &input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data. " + err.Error(),
		})
	}

	return resp.Respond(action.GetProvinceLeadTime(*input))
}

func GetProvincesLeadTime(req sdk.APIRequest, resp sdk.APIResponder) error {
	var q = req.GetParam("q")

	var input *request.GetAvailableLeadTimeRequest
	err := json.Unmarshal([]byte(q), &input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data. " + err.Error(),
		})
	}

	return resp.Respond(action.GetProvincesLeadTime(*input))
}

func DeleteConfigLeadTime(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input request.DeleteConfigLeadTimeRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.DeleteConfigLeadTime(input))
}

func UpdateConfigLeadTime(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input request.UpdateConfigLeadTimeRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.UpdateConfigLeadTime(input))
}
