package response

// BaseResponse model
type BaseResponse struct {
	Status    string            `json:"status"`
	Message   string            `json:"message"`
	ErrorCode string            `json:"errorCode,omitempty"`
	Headers   map[string]string `json:"headers,omitempty"`
}

type KeyValue struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type ImportExcelError struct {
	LineId  int    `json:"lineId"`
	Message string `json:"error"`
}
