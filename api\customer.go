package api

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
)

func GetCustomer(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q        = req.GetParam("q")
		offset   = int64(sdk.ParseInt(req.GetParam("offset"), 0))
		limit    = int64(sdk.ParseInt(req.GetParam("limit"), 100))
		getTotal = req.GetParam("getTotal") == "true"
	)
	if q == "" {
		q = "{}"
	}

	var input request.GetCustomerRequest
	err := json.Unmarshal([]byte(q), &input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.GetCustomer(input, offset, limit, getTotal))
}

func UpdateCustomer(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Customer
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.UpdateCustomer(&input))
}

func MigrateCustomer(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.MigrateCustomer())
}

func MigrateCustomerDetail(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.MigrateCustomerDetail())
}

func UpdateCustomersPayment(req sdk.APIRequest, resp sdk.APIResponder) error {
	type customersUpdate struct {
		CustomerIds   []int64                        `json:"listCustomer,omitempty"`
		PaymentMethod *enum.PreferPaymentMethodValue `json:"method,omitempty"`
	}

	var input customersUpdate
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.UpdateCustomersPayment(input.CustomerIds, input.PaymentMethod))
}

func CreateCustomers(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input []*model.Customer
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}

	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	return resp.Respond(action.CreateCustomers(input, UserInfo.Account.AccountID))
}

func UpsertCustomer(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input []*model.Customer
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}

	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	return resp.Respond(action.UpsertCustomers(input, UserInfo.Account.AccountID))
}

func ImportCustomerTags(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input []request.ImportCustomerRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.ImportCustomerTags(input))
}
