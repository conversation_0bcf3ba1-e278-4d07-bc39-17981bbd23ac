package hand_over_transfer

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	hub_shipping_order "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action/consumer/hub-shipping-order"
	sync_data "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action/consumer/tpl-callback"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func (j *ExecutorJob) sync() {
	j.Job.SetTopicConsumer(defaultTopic, func(item *job.JobItem) (err error) {
		var dataByte []byte
		dataByte, err = bson.Marshal(item.Data)
		if err != nil {
			return
		}

		data := request.CompleteHandOverRequest{}
		err = bson.Unmarshal(dataByte, &data)
		if err != nil {
			return
		}

		_ = client.Services.PickingClient.CompleteHandOverTransfer(&data)
		return nil
	})
}

func (j *ExecutorJob) doneCreateHandover() {
	j.Job.SetTopicConsumer(doneCreateHandover, func(item *job.JobItem) error {
		var dataByte []byte
		dataByte, err := bson.Marshal(item.Data)
		if err != nil {
			return nil
		}

		data := DoneHandoverTicketRequest{}
		err = bson.Unmarshal(dataByte, &data)
		if err != nil {
			return nil
		}

		referenceCodes := []string{}
		handoverItemMap := map[string]model.HandoverSOItem{}
		for _, handoverItem := range data.Items {
			referenceCodes = append(referenceCodes, handoverItem.ReferenceCode)
			handoverItemMap[handoverItem.ReferenceCode] = handoverItem
		}

		shippingOrdersRaw := model.ShippingOrderDB.Query(bson.M{
			"reference_code": bson.M{
				"$in": referenceCodes,
			},
		}, 0, int64(len(referenceCodes)), nil)

		ticketRaw := model.HandoverTicketDB.QueryOne(bson.M{
			"ticket_id": data.TicketId,
		})

		if shippingOrdersRaw.Status != common.APIStatus.Ok || ticketRaw.Status != common.APIStatus.Ok {
			return nil
		}
		current := time.Now()
		shippingOrders := shippingOrdersRaw.Data.([]*model.ShippingOrder)
		ticket := ticketRaw.Data.([]*model.HandoverTicket)[0]
		for _, shippingOrder := range shippingOrders {
			if shippingOrder.CallbackUrl != "" {
				callbackPartner := request.Callback{
					Status:        &enum.TPLCallbackStatus.TRANSPORTING,
					ReferenceCode: shippingOrder.ReferenceCode,
					PoCode:        shippingOrder.ParentReferenceCode,
					TrackingCode:  shippingOrder.TrackingCode,
					ActionTime:    &current,
					PartnerCode:   "INTERNAL",
					HubCode:       shippingOrder.CurrentHub,
					WarehouseCode: shippingOrder.CustomerCode,
					CallbackUrl:   shippingOrder.CallbackUrl,
					NumPackage:    handoverItemMap[shippingOrder.ReferenceCode].ScannedQuantity,
				}
				if shippingOrder.CheckinNumPack != 0 {
					callbackPartner.NumPackage = shippingOrder.CheckinNumPack
				}

				partner := client.GetWebhookClient("INTERNAL")
				_ = partner.SendCallbackToPartner(callbackPartner)
			}

			// Nếu không đủ số lượng sẽ tách đơn và cập nhật lại số kiện của đơn cũ
			if shippingOrder.NumPackage > handoverItemMap[shippingOrder.ReferenceCode].ScannedQuantity {
				// Tạo đơn mới
				remainQuantity := shippingOrder.NumPackage - handoverItemMap[shippingOrder.ReferenceCode].ScannedQuantity
				req := request.BookShippingOrder{
					ShippingType:     &enum.ShippingOrderType.FMPO,
					OldReferenceCode: shippingOrder.ReferenceCode,
					FeeCollectedOn:   shippingOrder.FeeCollectMethod,
					From: &model.Address{
						Address:      shippingOrder.FromCustomerAddress,
						Code:         shippingOrder.FromCustomerCode,
						Name:         shippingOrder.FromCustomerName,
						Phone:        shippingOrder.FromCustomerPhone,
						ProvinceCode: shippingOrder.FromProvinceCode,
						ProvinceName: shippingOrder.FromProvinceName,
						DistrictCode: shippingOrder.FromDistrictCode,
						DistrictName: shippingOrder.FromDistrictName,
						WardCode:     shippingOrder.FromWardCode,
						WardName:     shippingOrder.FromWardName,
					},
					To: &model.Address{
						Address:      shippingOrder.CustomerShippingAddress,
						Code:         shippingOrder.CustomerCode,
						Name:         shippingOrder.CustomerName,
						Phone:        shippingOrder.CustomerPhone,
						ProvinceCode: shippingOrder.CustomerProvinceCode,
						ProvinceName: shippingOrder.CustomerProvinceName,
						DistrictCode: shippingOrder.CustomerDistrictCode,
						DistrictName: shippingOrder.CustomerDistrictName,
						WardName:     shippingOrder.CustomerWardName,
						WardCode:     shippingOrder.CustomerWardCode,
					},
					Products:                 shippingOrder.Products,
					Height:                   shippingOrder.Height,
					Weight:                   shippingOrder.Weight,
					Width:                    shippingOrder.Width,
					Length:                   shippingOrder.Length,
					ParentReferenceCode:      shippingOrder.ParentReferenceCode,
					NumPackage:               remainQuantity,
					ReceiveSessionCode:       shippingOrder.ReceiveSessionCode,
					ParentReceiveSessionCode: shippingOrder.ParentReceiveSessionCode,
					DropOffAtHubCode:         shippingOrder.CurrentHub,
					IsSplitOrder:             true,
					IsReconciled:             shippingOrder.IsReconciled,
					References:               referenceCodes,
				}
				err = client.Services.TransportingClient.BookShippingService(req)
				if err != nil {
					syncDataByte, _ := json.Marshal(req)
					_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
						CmdType: "FAIL_TO_SPLIT_PO",
						Title:   string(syncDataByte),
						Message: err.Error(),
					})
				}
				// Giảm số kiện của shipping order cũ
				model.ShippingOrderDB.UpdateOne(bson.M{
					"reference_code": shippingOrder.ReferenceCode,
				}, bson.M{
					"num_package": handoverItemMap[shippingOrder.ReferenceCode].ScannedQuantity,
				})

			}
			// Tạo hub order mới
			createHubShippingOrderRequest := request.HandoverTicketCreateHubOrder{
				UpdatedBy:                ticket.UpdatedBy,
				FromDepartmentCode:       ticket.FromDepartmentCode,
				FromDepartmentName:       ticket.FromDepartmentName,
				ToDepartmentCode:         ticket.ToDepartmentCode,
				ToDepartmentName:         ticket.ToDepartmentName,
				TicketID:                 ticket.TicketID,
				TPLCode:                  ticket.TPLCode,
				Action:                   "TRANSPORTING",
				ActionName:               "Đang Luân chuyển hàng",
				TPLName:                  ticket.TPLName,
				HandoverType:             ticket.HandoverType,
				HandoverNote:             ticket.HandoverNote,
				Code:                     ticket.Code,
				SO:                       handoverItemMap[shippingOrder.ReferenceCode].SO,
				ReceivePackage:           handoverItemMap[shippingOrder.ReferenceCode].ScannedQuantity,
				HubStatus:                &enum.HubShippingOrderStatus.WAIT_TO_STORING,
				Products:                 shippingOrder.Products,
				HubOrderType:             &enum.HubOrderType.TRANSPORTING,
				ReferenceCode:            shippingOrder.ReferenceCode,
				ParentReceiveSessionCode: shippingOrder.ParentReceiveSessionCode,
			}
			_ = hub_shipping_order.PushCreateHubShippingOrderQueue(createHubShippingOrderRequest, handoverItemMap[shippingOrder.ReferenceCode].SO)

			// Update hub order cũ
			updateRequest := &request.UpdateHubOrder{
				HubCode:       ticket.FromDepartmentCode,
				ReferenceCode: shippingOrder.ReferenceCode,
				Action:        "OUT",
				ActionName:    "Đã bàn giao",
				Status:        &enum.HubShippingOrderStatus.DONE_TRANSPORTING,
			}

			_ = hub_shipping_order.PushUpdateHubShippingOrderQueue(updateRequest, shippingOrder.ReferenceCode)

			shippingOrderUpdater := bson.M{
				"status":      enum.TPLCallbackStatus.TRANSPORTING,
				"action_time": current.Unix(),
			}

			if shippingOrder.LeavedFirstHubTime == 0 {
				shippingOrderUpdater["leaved_first_hub_time"] = current.Unix()
			}

			model.ShippingOrderDB.UpdateOne(bson.M{
				"reference_code": shippingOrder.ReferenceCode,
			}, shippingOrderUpdater)

			CreateCallbackData(shippingOrder.ReferenceCode, ticket.FromDepartmentName, ticket.ToDepartmentName, ticket.FromDepartmentCode, &enum.TPLCallbackStatus.PICKED, shippingOrder.ShippingType)
			time.Sleep(1 * time.Second)
			CreateCallbackData(shippingOrder.ReferenceCode, ticket.FromDepartmentName, ticket.ToDepartmentName, ticket.FromDepartmentCode, &enum.TPLCallbackStatus.TRANSPORTING, shippingOrder.ShippingType)
		}

		return nil
	})
}

func CreateCallbackData(so, from, to, hubCode string, status *enum.TPLCallbackStatusValue, shippingOrderType *enum.ShippingOrderTypeValue) {
	actionTime := time.Now()
	callback := request.Callback{
		SO:         so,
		HubCode:    hubCode,
		ActionTime: &actionTime,
		Status:     status,
		TPLStatus:  string(*status),
	}

	if shippingOrderType == nil {
		callback.Type = &enum.ShippingOrderType.DELIVERY
	} else {
		callback.Type = shippingOrderType
	}

	switch *status {
	case enum.TPLCallbackStatus.PICKED:
		callback.StatusName = "Đã lấy hàng."
		if from != "" && to != "" {
			callback.TPLStatusName = to + " Đã lấy hàng từ " + from
		} else {
			callback.TPLStatusName = "Đã lấy hàng."
		}
		break
	case enum.TPLCallbackStatus.TRANSPORTING:
		callback.StatusName = "Đang vận chuyển hàng."
		callback.TPLStatusName = "Đang luân chuyển hàng từ " + from + " đến " + to
		break
	case enum.TPLCallbackStatus.RETURNING:
		callback.StatusName = "Đang Trả hàng."
		callback.TPLStatusName = "Đang trả hàng."
		callback.Action = string(enum.TPLCallbackStatus.TRANSPORTING)
		callback.ActionName = "Đang luân chuyển hàng từ " + from + " đến " + to
		break
	}

	_ = sync_data.PushCreateTPLCallbackQueue(callback, callback.SO)
}

func (j *ExecutorJob) doneReceiveHandover() {
	j.Job.SetTopicConsumer(doneReceiveHandover, func(item *job.JobItem) error {
		var dataByte []byte
		dataByte, err := bson.Marshal(item.Data)
		if err != nil {
			return nil
		}

		data := DoneHandoverTicketRequest{}
		err = bson.Unmarshal(dataByte, &data)
		if err != nil {
			return nil
		}

		referenceCodes := []string{}
		handoverItemMap := map[string]model.HandoverSOItem{}
		for _, handoverItem := range data.Items {
			referenceCodes = append(referenceCodes, handoverItem.ReferenceCode)
			handoverItemMap[handoverItem.ReferenceCode] = handoverItem
		}

		shippingOrdersRaw := model.ShippingOrderDB.Query(bson.M{
			"reference_code": bson.M{
				"$in": referenceCodes,
			},
		}, 0, int64(len(referenceCodes)), nil)

		ticketRaw := model.HandoverTicketDB.QueryOne(bson.M{
			"ticket_id": data.TicketId,
		})

		if shippingOrdersRaw.Status != common.APIStatus.Ok || ticketRaw.Status != common.APIStatus.Ok {
			return nil
		}

		current := time.Now()
		returnAfter := options.After
		shippingOrders := shippingOrdersRaw.Data.([]*model.ShippingOrder)
		ticket := ticketRaw.Data.([]*model.HandoverTicket)[0]
		hubRaw := model.HubDB.QueryOne(bson.M{"code": ticket.ToDepartmentCode})

		if hubRaw.Status != common.APIStatus.Ok {
			return nil
		}
		hub := hubRaw.Data.([]*model.Hub)[0]
		for _, shippingOrder := range shippingOrders {
			callbackPartner := request.Callback{
				Status:        &enum.TPLCallbackStatus.STORING,
				ReferenceCode: shippingOrder.ReferenceCode,
				PoCode:        shippingOrder.ParentReferenceCode,
				TrackingCode:  shippingOrder.TrackingCode,
				ActionTime:    &current,
				PartnerCode:   "INTERNAL",
				HubCode:       ticket.ToDepartmentCode,
				CallbackUrl:   shippingOrder.CallbackUrl,
				NumPackage:    shippingOrder.NumPackage,
				WarehouseCode: shippingOrder.CustomerCode,
			}
			if shippingOrder.CheckinNumPack != 0 {
				callbackPartner.NumPackage = shippingOrder.CheckinNumPack
			}

			callbackData := request.Callback{
				ActionTime:    &current,
				SO:            shippingOrder.ReferenceCode,
				ReferenceCode: shippingOrder.ReferenceCode,
				Status:        &enum.TPLCallbackStatus.STORING,
				StatusName:    "Đã nhập kho " + hub.Name,
				TPLStatus:     string(enum.TPLCallbackStatus.STORING),
				TPLStatusName: "Đã nhập kho " + hub.Name,
				Type:          &enum.ShippingOrderType.FMPO,
				WarehouseCode: shippingOrder.CustomerCode,
			}

			// Nếu nhận lc về kho đích thì tạo phiên đối soát cho đơn luôn không cần merge
			if hub.WarehouseReferenceCode == shippingOrder.CustomerCode {
				callbackData.Status = &enum.TPLCallbackStatus.DELIVERED
				callbackData.StatusName = "Giao hàng thành công cho kho: " + hub.WarehouseReferenceCode
				callbackData.TPLStatusName = "Giao hàng thành công cho kho: " + hub.WarehouseReferenceCode
				callbackPartner.Status = &enum.TPLCallbackStatus.DELIVERED
				needReconcile := true
				shippingOrderUpdater := bson.M{
					"current_hub":           ticket.ToDepartmentCode,
					"action_time":           current.Unix(),
					"handover_time":         current.Unix(),
					"stored_at_lm_hub_time": current.Unix(),
					"status":                &enum.TPLCallbackStatus.DELIVERED,
				}

				hubOrderUpdater := bson.M{
					"delivered_time": current.Unix(),
					"status":         &enum.HubShippingOrderStatus.COD_COLLECTED,
					"action_time":    current.Unix(),
				}

				// Nếu người gửi trả phí thì check xem đã đối soát chưa, nếu chưa thì chỉ chuyển sang delivered, nếu rồi thì chuyển thẳng sang Completed
				if shippingOrder.FeeCollectMethod != nil &&
					(*shippingOrder.FeeCollectMethod == enum.FeeCollectMethod.SENDER_PAY ||
						*shippingOrder.FeeCollectMethod == enum.FeeCollectMethod.DEBT) {
					if shippingOrder.IsReconciled != nil && *shippingOrder.IsReconciled {
						shippingOrderUpdater["status"] = &enum.TPLCallbackStatus.COMPLETED
						hubOrderUpdater["status"] = &enum.HubShippingOrderStatus.COMPLETED
						callbackData.Status = &enum.TPLCallbackStatus.COMPLETED
						callbackData.StatusName = "Hoàn thành đơn"
						callbackPartner.Status = &enum.TPLCallbackStatus.COMPLETED
					} else {
						hubOrderUpdater["status"] = &enum.HubShippingOrderStatus.DELIVERED
					}
					needReconcile = false
				}

				// Cập nhật shipping order sang đã giao, cập nhật current hub
				updateShippingOrderResult := model.ShippingOrderDB.UpdateOne(bson.M{
					"reference_code": shippingOrder.ReferenceCode,
				}, shippingOrderUpdater)

				// Update hub order sang COD_COLLECTED
				updateHubOrderResult := model.HUBShippingOrderDB.UpdateOne(bson.M{
					"reference_code": shippingOrder.ReferenceCode,
					"hub_code":       hub.Code,
				}, hubOrderUpdater, &options.FindOneAndUpdateOptions{
					ReturnDocument: &returnAfter,
				})

				if updateHubOrderResult.Status != common.APIStatus.Ok ||
					updateShippingOrderResult.Status != common.APIStatus.Ok {
					syncDataByte, _ := json.Marshal(shippingOrder)
					_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
						CmdType: "UPDATE_HUB_SHIPPING_ORDER_FAILED",
						Title:   fmt.Sprintf("UPDATE_FAIL_HUB_SHIPPING_ORDER %v - %d", shippingOrder.ReferenceCode),
						Message: string(syncDataByte) + "\n" +
							"Update shipping order message: " + updateShippingOrderResult.Message +
							"Update hub order message: " + updateHubOrderResult.Message,
					})
					continue
				}
				reconcileOrder := updateHubOrderResult.Data.([]*model.HubShippingOrder)[0]

				_ = sync_data.PushCreateTPLCallbackQueue(callbackData, shippingOrder.ReferenceCode)

				if shippingOrder.FeeCollectMethod != nil &&
					*shippingOrder.FeeCollectMethod == enum.FeeCollectMethod.DEBT {
					needReconcile = false
				}

				if needReconcile {
					_, _ = CreateReconcile(reconcileOrder, string(enum.ReconcileTypeRequest.HUB_COMP))
				}
				if shippingOrder.CallbackUrl != "" {
					partner := client.GetWebhookClient("INTERNAL")
					_ = partner.SendCallbackToPartner(callbackPartner)
				}

				continue
			}

			// Check xem có đơn nào ở hub này không để merge
			checkMergeOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
				"current_hub":          ticket.ToDepartmentCode,
				"receive_session_code": shippingOrder.ReceiveSessionCode,
				"status":               enum.TPLCallbackStatus.STORING,
			})

			// Nếu có đơn để merge, chuyển trạng thái đơn mới sang MERGED, cộng lại số kiện của đơn đang lưu kho
			if checkMergeOrderRaw.Status == common.APIStatus.Ok {
				existedOrder := checkMergeOrderRaw.Data.([]*model.ShippingOrder)[0]
				newNumPack := existedOrder.NumPackage + shippingOrder.NumPackage
				updateNumPackShippingOrder := model.HUBShippingOrderDB.UpdateOne(bson.M{
					"reference_code": existedOrder.ReferenceCode,
					"hub_code":       existedOrder.CurrentHub,
				}, bson.M{
					"num_package":      newNumPack,
					"receive_ package": newNumPack,
				})
				updateNumPackHubOrder := model.ShippingOrderDB.UpdateOne(bson.M{
					"reference_code": existedOrder.ReferenceCode,
				}, bson.M{
					"num_package": newNumPack,
				})

				if updateNumPackShippingOrder.Status != common.APIStatus.Ok ||
					updateNumPackHubOrder.Status != common.APIStatus.Ok {
					syncDataByte, _ := json.Marshal(existedOrder)
					_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
						CmdType: "MERGE_NUMPACK_ORDER_FAILED",
						Title:   fmt.Sprintf("MERGE_NUMPACK_ORDER_FAILED %v - %d", existedOrder.ReferenceCode),
						Message: string(syncDataByte) + "\n" +
							"Update shipping order message: " + updateNumPackShippingOrder.Message +
							"Update hub order message: " + updateNumPackHubOrder.Message,
					})
					continue
				}

				//	Chuyển status của shipping order và hub order sang MERGED
				updateMergedShippingOrderResult := model.ShippingOrderDB.UpdateOne(bson.M{
					"reference_code": shippingOrder.ReferenceCode,
				}, bson.M{
					"current_hub":           ticket.ToDepartmentCode,
					"action_time":           current.Unix(),
					"handover_time":         current.Unix(),
					"stored_at_lm_hub_time": current.Unix(),
					"status":                &enum.TPLCallbackStatus.MERGED,
				})

				updateMergedHubOrderResult := model.HUBShippingOrderDB.UpdateOne(bson.M{
					"reference_code": shippingOrder.ReferenceCode,
					"hub_code":       ticket.ToDepartmentCode,
				}, bson.M{
					"action_time": current.Unix(),
					"status":      &enum.HubShippingOrderStatus.MERGED,
					"action":      "IN",
				})

				if updateMergedShippingOrderResult.Status != common.APIStatus.Ok ||
					updateMergedHubOrderResult.Status != common.APIStatus.Ok {
					syncDataByte, _ := json.Marshal(shippingOrder)
					_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
						CmdType: "MERGE_NUMPACK_ORDER_FAILED",
						Title:   fmt.Sprintf("MERGE_NUMPACK_ORDER_FAILED %v - %d", existedOrder.ReferenceCode),
						Message: string(syncDataByte) + "\n" +
							"Update shipping order message: " + updateMergedShippingOrderResult.Message +
							"Update hub order message: " + updateMergedHubOrderResult.Message,
					})
				}

				_ = sync_data.PushCreateTPLCallbackQueue(callbackData, shippingOrder.ReferenceCode)
				continue
			}

			// Nếu không có đơn để merge thì chuyển trạng thái như thường
			updateResult := model.ShippingOrderDB.UpdateOne(bson.M{
				"reference_code": shippingOrder.ReferenceCode,
			}, bson.M{
				"current_hub":   ticket.ToDepartmentCode,
				"action_time":   current.Unix(),
				"handover_time": current.Unix(),
				"status":        &enum.TPLCallbackStatus.STORING,
			})

			updateHubOrderRequest := &request.UpdateHubOrder{
				HubCode:       ticket.ToDepartmentCode,
				ReferenceCode: shippingOrder.ReferenceCode,
				Action:        "IN",
				ActionName:    "Đã nhập kho " + ticket.ToDepartmentName,
				Status:        &enum.HubShippingOrderStatus.STORING,
			}

			_ = hub_shipping_order.PushUpdateHubShippingOrderQueue(updateHubOrderRequest, shippingOrder.ReferenceCode)

			if shippingOrder.CallbackUrl != "" {
				partner := client.GetWebhookClient("INTERNAL")
				_ = partner.SendCallbackToPartner(callbackPartner)
			}

			if updateResult.Status != common.APIStatus.Ok {
				itemByte, _ := json.Marshal(shippingOrder)
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "UPDATE_SHIPPING_ORDER_FAILED",
					Title:   "UPDATE_SHIPPING_ORDER_FAILED",
					Message: string(itemByte) + "\n" + updateResult.Message,
				})
			}
		}

		return nil
	})
}

func CreateReconcile(hubShippingOrder *model.HubShippingOrder, reconcileType string) (*common.APIResponse, bool) {

	carrierCode := string(*hubShippingOrder.TplCode)
	// Lấy carrier cha nếu có
	response := model.CarrierDB.QueryOne(&bson.M{"carrier_code": carrierCode})
	if response.Status == common.APIStatus.Ok {
		carrier := response.Data.([]*model.Carrier)[0]
		if carrier.ParentCode != nil && *carrier.ParentCode != "" {
			carrierCode = string(*carrier.ParentCode)
		}
	}

	createReconcileSession := &request.CreateReconcileSessionRequest{
		CarrierCode:   carrierCode,
		ReconcileType: reconcileType,
		HubCode:       hubShippingOrder.HUBCode,
		UserId:        int(hubShippingOrder.DriverID),
		Fullname:      hubShippingOrder.DriverName,
	}

	response = client.Services.AccountingClient.CreateReconcileSession(createReconcileSession)
	if response.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Tạo đối soát thất bại.",
		}, true
	} else {
		deliveredTime := time.Unix(hubShippingOrder.DeliveredTime, 0)

		paymentMethod := "COD"
		if hubShippingOrder.PaymentMethod != nil {
			paymentMethod = string(*hubShippingOrder.PaymentMethod)
		}
		createReconcileSessionOrder := &request.CreateReconcileSessionOrderRequest{
			HubCode:        hubShippingOrder.HUBCode,
			UserId:         int(hubShippingOrder.DriverID),
			ReferenceCode:  hubShippingOrder.ReferenceCode,
			TrackingCode:   hubShippingOrder.TrackingCode,
			ReconcileType:  reconcileType,
			CODAmount:      hubShippingOrder.CODAmount,
			TplCode:        hubShippingOrder.TplCode,
			PaymentMethod:  paymentMethod,
			CarrierCode:    string(*hubShippingOrder.TplCode),
			DeliveredTime:  &deliveredTime,
			DeliveryAmount: hubShippingOrder.DeliveryAmount,
		}
		if (strings.HasPrefix(hubShippingOrder.ReferenceCode, "SO") ||
			strings.HasPrefix(hubShippingOrder.ReferenceCode, "LO") ||
			strings.HasPrefix(hubShippingOrder.ReferenceCode, "RO") ||
			strings.HasPrefix(hubShippingOrder.ReferenceCode, "TO") ||
			strings.HasPrefix(hubShippingOrder.ReferenceCode, "CO")) &&
			(strings.Contains(hubShippingOrder.ReferenceCode, "-P") ||
				strings.Contains(hubShippingOrder.ReferenceCode, "-F")) {
			createReconcileSessionOrder.ParentReferenceCode = hubShippingOrder.ParentReferenceCode
		}

		response = client.Services.AccountingClient.CreateReconcileSessionOrder(createReconcileSessionOrder)
		if response.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Ok,
				Message: "Tạo đối soát thất bại.",
			}, true
		}
	}

	return nil, false
}
