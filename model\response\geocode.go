package response

type GeoCodeResponse struct {
	BaseResponse
	Results []GeocodingResult `json:"results"`
}

// LatLng specifies a point with latitude and longitude
type LatLng struct {
	Lat float64 `json:"lat"`
	Lng float64 `json:"lng"`
}

// Location is specified by its address and coordinates
type Location struct {
	Street      string `json:"street"`
	City        string `json:"adminArea5"`
	State       string `json:"adminArea3"`
	PostalCode  string `json:"postalCode"`
	County      string `json:"adminArea4"`
	CountryCode string `json:"adminArea1"`
	LatLng      LatLng `json:"latLng"`
	Type        string `json:"type"`
	DragPoint   bool   `json:"dragPoint"`
}

// Complete geocoding result
type GeocodingResult struct {
	Geometry struct {
		Location LatLng `json:"location"`
	} `json:"geometry"`
}

// Ahamove Geocode
type AhamoveGeocodeResponse struct {
	Lat          float64  `json:"latitude"`
	Lng          float64  `json:"longitude"`
	ShortAddress string   `json:"short_address"`
	Name         string   `json:"name"`
	Types        []string `json:"types"`
	Status       int      `json:"status"`
	Title        string   `json:"title"`
}
