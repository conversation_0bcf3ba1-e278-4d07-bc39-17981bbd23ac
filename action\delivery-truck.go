package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// CreateDeliveryTruck action
func CreateDeliveryTruck(input *model.DeliveryTruck) *common.APIResponse {
	if input.OwnerName == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Tên chủ xe không được để trống",
			ErrorCode: "OWNER_NAME_REQUIRED",
		}
	}

	if input.Phone == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Số điện thoại không được để trống",
			ErrorCode: "PHONE_REQUIRED",
		}
	}

	if input.IdCard == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Chứng minh thư không được để trống",
			ErrorCode: "ID_CARD_REQUIRED",
		}
	}

	if input.LicensePlate == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Biển số xe không được để trống",
			ErrorCode: "LICENSE_PLATE_REQUIRED",
		}
	}

	createResult := model.DeliveryTruckDB.Create(input)

	if createResult.Status != common.APIStatus.Ok {
		return createResult
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Tạo thành công",
	}
}

// GetDeliveryTruck action
func GetDeliveryTruck(input *request.DeliveryTruckQuery, offset int64, limit int64, getTotal bool) *common.APIResponse {
	filter := bson.M{}

	if input.IdCard != "" {
		filter["id_card"] = bson.M{
			"$regex": input.IdCard,
		}
	}

	if input.LicensePlate != "" {
		filter["license_plate"] = bson.M{
			"$regex": input.LicensePlate,
		}
	}

	if input.OwnerName != "" {
		filter["owner_name"] = bson.M{
			"$regex": input.OwnerName,
		}
	}

	if input.Phone != "" {
		filter["phone"] = bson.M{
			"$regex": input.Phone,
		}
	}

	result := model.DeliveryTruckDB.Query(filter, offset, limit, &primitive.M{"_id": -1})
	if getTotal {
		countResult := model.CarrierDB.Count(filter)
		result.Total = countResult.Total
	}
	return result
}

// UpdateDeliveryTruck action
func UpdateDeliveryTruck(input *model.DeliveryTruck) *common.APIResponse {
	if input.LicensePlate == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Biển số xe không được để trống",
			ErrorCode: "LICENSE_PLATE_REQUIRED",
		}
	}

	afterOption := options.After
	updateResult := model.DeliveryTruckDB.UpdateOne(
		bson.M{
			"license_plate": input.LicensePlate,
		},
		input,
		&options.FindOneAndUpdateOptions{
			Upsert:         &enum.False,
			ReturnDocument: &afterOption,
		})

	if updateResult.Status != common.APIStatus.Ok {
		return updateResult
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật thành công",
	}
}

// DeleteDeliveryTruck action
func DeleteDeliveryTruck(licensePlate string) *common.APIResponse {
	if licensePlate == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Biển số xe không được để trống",
			ErrorCode: "LICENSE_PLATE_REQUIRED",
		}
	}

	return model.DeliveryTruckDB.Delete(
		bson.M{
			"license_plate": licensePlate,
		})
}
