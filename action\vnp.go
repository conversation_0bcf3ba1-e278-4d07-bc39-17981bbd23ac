package action

import (
	"fmt"
	"strconv"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/shipping"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"go.mongodb.org/mongo-driver/bson"
)

type vnp struct{}

var VNP vnp

func (vnp) BookShipping(input *request.BookShipping, saleOrder *model.SaleOrder, carrierModel *model.Carrier, pickupAddress *model.Address) (result []*model.ShippingInfo, err error) {
	if shipping.TplShippingClient.VTPClient == nil {
		err = fmt.Errorf("Dịch vụ book VNPOST chưa được khởi tạo")
		return
	}

	if len(carrierModel.Service) == 0 {
		err = fmt.Errorf("Không tìm thấy dịch vụ vận chuyển phù hợp")
		return
	}

	requestBooking := request.BookVNPost{
		SenderTel:           pickupAddress.Phone,
		SenderFullName:      pickupAddress.Name,
		SenderAddress:       pickupAddress.Address,
		ReceiverTel:         saleOrder.CustomerInfos.Delivery.Phone,
		ReceiverFullName:    saleOrder.CustomerInfos.Delivery.Name,
		ReceiverAddress:     saleOrder.CustomerInfos.Delivery.Address,
		PackageContent:      "Thuốc",
		PickupPoscode:       carrierModel.PickupCode,
		WeightEvaluation:    input.Weight * 1000,
		IsPackageViewable:   true,                         // Cho xem hàng
		PickupType:          int(carrierModel.PickupType), // Thu gôm tận nơi
		CodAmountEvaluation: saleOrder.CODAmount,
	}

	if *saleOrder.PaymentMethod == enum.PaymentMethod.BANK {
		requestBooking.CodAmountEvaluation = 0
	}

	province := getProvinceInfo(pickupAddress.ProvinceName)
	if province == nil {
		err = fmt.Errorf("Không tìm thấy địa chỉ tỉnh lấy hàng %v", pickupAddress.ProvinceName)
		return
	}
	district := getDistrictInfo(province.Code, pickupAddress.DistrictName)
	if district == nil {
		err = fmt.Errorf("Không tìm thấy địa chỉ huyện lấy hàng %v", pickupAddress.DistrictName)
		return
	}
	ward := getWardInfo(district.Code, pickupAddress.WardName)
	if ward == nil {
		err = fmt.Errorf("Không tìm thấy địa chỉ xã lấy hàng %v", pickupAddress.WardName)
		return
	}

	requestBooking.SenderProvinceId = province.VNPId
	requestBooking.SenderDistrictId = district.VNPId
	requestBooking.SenderWardId = ward.VNPId

	province = getProvinceInfo(saleOrder.CustomerInfos.Delivery.Province)
	if province == nil {
		err = fmt.Errorf("Không tìm thấy địa chỉ tỉnh nhận hàng %v", saleOrder.CustomerInfos.Delivery.Province)
		return
	}
	district = getDistrictInfo(province.Code, saleOrder.CustomerInfos.Delivery.District)
	if district == nil {
		err = fmt.Errorf("Không tìm thấy địa chỉ huyện nhận hàng %v", saleOrder.CustomerInfos.Delivery.District)
		return
	}
	ward = getWardInfo(district.Code, saleOrder.CustomerInfos.Delivery.Ward)
	if ward == nil {
		err = fmt.Errorf("Không tìm thấy địa chỉ xã nhận hàng %v", saleOrder.CustomerInfos.Delivery.Ward)
		return
	}

	requestBooking.ReceiverProvinceId = province.VNPId
	requestBooking.ReceiverDistrictId = district.VNPId
	requestBooking.ReceiverWardId = ward.VNPId

	if input.NbOfPackages > 1 {
		requestBooking.OrderCodeInBatch = saleOrder.SaleOrderCode
		for i := 0; i < int(input.NbOfPackages); i++ {
			requestBooking.LstOrderDetailRequest = append(requestBooking.LstOrderDetailRequest, &request.VNPItem{
				CodAmount: 0,
				Height:    0,
				Length:    0,
				Weight:    (input.Weight / float64(input.NbOfPackages)) * 1000,
				Width:     0,
			})
		}

		service, errConv := strconv.Atoi(carrierModel.Service)

		if errConv != nil {
			return result, fmt.Errorf("Dịch vụ vận chuyển không hợp lệ")
		}

		requestBooking.ServiceId = service
		requestBooking.CodAmount = saleOrder.CODAmount
		if saleOrder.PaymentMethod != nil && *saleOrder.PaymentMethod == enum.PaymentMethod.BANK {
			requestBooking.CodAmount = 0
		}
		requestBooking.AdditionServices = []int{}
		result, err = shipping.TplShippingClient.VNPClient.CreateTrackingBash(requestBooking)
		if err != nil {
			return nil, err
		}
	} else {
		requestBooking.OrderCode = saleOrder.SaleOrderCode
		requestBooking.ServiceName = carrierModel.Service
		_, errConv := strconv.Atoi(carrierModel.Service)
		if errConv == nil {
			return result, fmt.Errorf("Dịch vụ vận chuyển không hợp lệ")
		}
		trackingInfo, err := shipping.TplShippingClient.VNPClient.CreateTracking(requestBooking)
		if err != nil {
			return nil, err
		}
		result = append(result, trackingInfo)
	}

	return
}

func getProvinceInfo(province string) *model.ProvinceMapping {
	provinceMapping := model.ProvinceMappingDB.QueryOne(bson.M{
		"dictionary": province,
	})

	if provinceMapping.Status != common.APIStatus.Ok {
		return nil
	}

	return provinceMapping.Data.([]*model.ProvinceMapping)[0]
}

func getDistrictInfo(provinceCode, district string) *model.DistrictMapping {
	districtMapping := model.DistrictMappingDB.QueryOne(bson.M{
		"dictionary":    district,
		"province_code": provinceCode,
	})

	if districtMapping.Status != common.APIStatus.Ok {
		return nil
	}

	return districtMapping.Data.([]*model.DistrictMapping)[0]
}

func getWardInfo(districtCode, ward string) *model.WardMapping {
	wardMapping := model.WardMappingDB.QueryOne(bson.M{
		"dictionary":    ward,
		"district_code": districtCode,
	})

	if wardMapping.Status != common.APIStatus.Ok {
		return nil
	}
	return wardMapping.Data.([]*model.WardMapping)[0]
}
