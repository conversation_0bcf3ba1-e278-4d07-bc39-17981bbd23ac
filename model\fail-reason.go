package model

import (
	"time"

	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
)

type FailReason struct {
	ReasonType      enum.FailReasonTypeValue `json:"reasonType,omitempty" bson:"reason_type,omitempty"`
	ReasonCode      string                   `json:"reasonCode,omitempty" bson:"reason_code,omitempty"`
	FailTime        int                      `json:"failTime,omitempty" bson:"fail_time,omitempty"`
	FormattedReason string                   `json:"formattedReason,omitempty" bson:"formatted_reason,omitempty"`
	CreatedAt       time.Time                `json:"createdAt,omitempty" bson:"created_at,omitempty"`
	ReasonName      string                   `json:"reasonName,omitempty" bson:"reason_name,omitempty"`
	Images          []string                 `json:"images,omitempty" bson:"images,omitempty"`
}
