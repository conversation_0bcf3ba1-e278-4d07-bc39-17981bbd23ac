package hrm

import (
	"encoding/json"
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
)

func (c *HrmClient) GetAccountInfo(accountID int64, departmentCode string, offset, limit int) (result []*model.Account, err error) {
	params := map[string]interface{}{
		"accountId":      accountID,
		"departmentCode": departmentCode,
	}
	paramByte, _ := json.Marshal(params)
	requestParams := map[string]string{
		"q":      string(paramByte),
		"offset": fmt.Sprintf("%d", offset),
		"limit":  fmt.Sprintf("%d", limit),
	}
	var res *client.RestResult
	res, err = c.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, c.headers, requestParams, nil, pathGetAccount, nil)
	if err != nil {
		return nil, err
	}

	type myResponse struct {
		Status string           `json:"status"`
		Code   string           `json:"errorCode"`
		Data   []*model.Account `json:"data"`
	}

	resp := new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &resp)
	if err != nil {
		return nil, err
	}

	if resp.Status != common.APIStatus.Ok || resp.Data == nil {
		return nil, fmt.Errorf("%v", resp.Code)
	}
	if len(resp.Data) > 0 {
		result = resp.Data
	}

	return
}

func (c *HrmClient) QueryEmployeeRoles(query model.HrmAccount) (result *model.ActionSource, err error) {
	var res *client.RestResult
	params := map[string]string{
		"username":    query.Username,
		"type":        query.Type,
		"getUserRole": "true",
	}

	res, err = c.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, c.headers, params, nil, pathGetAccountRole, nil)
	if err != nil {
		return nil, err
	}

	type myResponse struct {
		Status string                `json:"status"`
		Code   string                `json:"errorCode"`
		Data   []*model.ActionSource `json:"data"`
	}

	resp := new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &resp)
	if err != nil {
		return nil, err
	}

	if resp.Status != common.APIStatus.Ok || resp.Data == nil {
		return nil, fmt.Errorf("%v", resp.Code)
	}
	if len(resp.Data) > 0 {
		result = resp.Data[0]
	}

	return
}

func (c *HrmClient) GetHrmEmployeeRoles(username string, departmentCode string) (result []*model.UserRole, err error) {
	var res *client.RestResult
	params := map[string]string{
		"username":       username,
		"departmentCode": departmentCode,
	}
	paramByte, _ := json.Marshal(params)
	requestParams := map[string]string{
		"q": string(paramByte),
	}
	res, err = c.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, c.headers, requestParams, nil, pathGetHrmEmployeeRoles, nil)
	if err != nil {
		return nil, err
	}

	type myResponse struct {
		Status string            `json:"status"`
		Code   string            `json:"errorCode"`
		Data   []*model.UserRole `json:"data"`
	}

	resp := new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &resp)
	if err != nil {
		return nil, err
	}

	if resp.Status != common.APIStatus.Ok || resp.Data == nil {
		return nil, fmt.Errorf("%v", resp.Code)
	}
	if len(resp.Data) > 0 {
		result = resp.Data
	}

	return
}

func (c *HrmClient) GetAccountsByRole(roleCode string, offset, limit int) (result []*model.Account, err error) {
	params := map[string]interface{}{
		"type":     "EMPLOYEE",
		"roleCode": roleCode,
	}
	paramByte, _ := json.Marshal(params)
	requestParams := map[string]string{
		"q":      string(paramByte),
		"offset": fmt.Sprintf("%d", offset),
		"limit":  fmt.Sprintf("%d", limit),
	}
	var res *client.RestResult
	res, err = c.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, c.headers, requestParams, nil, pathGetAccount, nil)
	if err != nil {
		return nil, err
	}

	type myResponse struct {
		Status string           `json:"status"`
		Code   string           `json:"errorCode"`
		Data   []*model.Account `json:"data"`
	}

	resp := new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &resp)
	if err != nil {
		return nil, err
	}

	if resp.Status != common.APIStatus.Ok || resp.Data == nil {
		return nil, fmt.Errorf("%v", resp.Code)
	}
	if len(resp.Data) > 0 {
		result = resp.Data
	}

	return
}
