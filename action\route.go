package action

import (
	"sort"
	"strconv"

	"github.com/google/uuid"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// GetShippingOrder func
func GetRoute(query *model.RouteQuery, offset, limit int64, getTotal bool) *common.APIResponse {
	var filter = bson.M{}
	if query.RouteCode != "" {
		filter["route_code"] = query.RouteCode
	}

	if query.Status != "" {
		filter["status"] = query.Status
	}

	if query.Keyword != "" {
		filter["keyword"] = bson.M{
			"$regex": utils.ToRawText(query.Keyword),
		}
	}

	if query.FromEstimatedHandoverTime > 0 && query.ToEstimatedHandoverTime > 0 {
		filter["estimated_start_handover_time"] = bson.M{
			"$gte": query.FromEstimatedHandoverTime,
			"$lte": query.ToEstimatedHandoverTime,
		}
	} else if query.FromEstimatedHandoverTime > 0 && query.ToEstimatedHandoverTime == 0 {
		filter["estimated_start_handover_time"] = bson.M{
			"$gte": query.FromEstimatedHandoverTime,
		}
	} else if query.FromEstimatedHandoverTime == 0 && query.ToEstimatedHandoverTime > 0 {
		filter["estimated_start_handover_time"] = bson.M{
			"$lte": query.ToEstimatedHandoverTime,
		}
	}

	if len(query.DropOffCodes) > 0 {
		filter["drop_off_points.code"] = bson.M{
			"$in": query.DropOffCodes,
		}
	}

	result := model.RouteDB.Query(
		filter,
		offset,
		limit,
		&bson.M{"_id": -1})
	if getTotal {
		countResult := model.RouteDB.Count(filter)
		result.Total = countResult.Total
	}

	return result
}

// InitRoute action
func InitRoute(input *model.Route) *common.APIResponse {
	if input.RouteCode == "" {
		input.RouteCode = model.GenCode("TRANSPORTING_CODE")
	}

	if input.RouteName == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "INVALID_ROUTE_NAME",
			Message:   "Tên tuyến không được để trống",
		}
	}

	if input.EstimatedHandoverTime < 0 && input.EstimatedHandoverTime > 86399 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "INVALID_ESTIMATED_HANDOVER_TIME",
			Message:   "Thời gian bắt đầu bàn giao không đúng (phải nằm trong khoảng từ 00:00 - 23:59)",
		}
	}

	if input.TotalTransportTime < 36000 && input.TotalTransportTime > 259200 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "INVALID_TOTAL_TRANSPORT_TIME",
			Message:   "Thời gian luân chuyển phải nằm trong khoảng từ 10p -> 3d",
		}
	}

	getRoute := model.RouteDB.Count(bson.M{"route_code": input.RouteCode, "status": string(enum.RouteStatus.ACTIVE)})
	if getRoute.Total > 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Mã tuyến " + input.RouteCode + " đã tồn tại",
		}
	}

	getRoute = model.RouteDB.Count(bson.M{"route_name": input.RouteName, "status": string(enum.RouteStatus.ACTIVE)})
	if getRoute.Total > 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Tên Tuyến " + input.RouteName + " đã tồn tại",
		}
	}

	// Todo: Validate drop off point
	var codes = map[string]bool{}
	startPointCode, endPointCode := "", ""
	for i, p := range input.DropOffPoints {
		if p.Code == "" {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Mã điểm thả hàng không được để trống. Vị trí thứ " + strconv.Itoa(i),
			}
		}

		if codes[p.Code] {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Mã điểm thả hàng trùng lặp trong tuyến",
			}
		}

		if p.Index <= 0 {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Vị trí của điểm trong tuyến không được để trống.",
			}
		}

		if p.Type == nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Loại vị trí của điểm trong tuyến không được để trống.",
			}
		} else {
			if *p.Type == enum.DropOffPointType.START {
				if startPointCode == "" {
					startPointCode = p.Code
				} else {
					return &common.APIResponse{
						Status:  common.APIStatus.Invalid,
						Message: "Chỉ được cài đặt 1 vị trí bắt đầu của tuyến.",
					}
				}
			} else if *p.Type == enum.DropOffPointType.END {
				if endPointCode == "" {
					endPointCode = p.Code
				} else {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						ErrorCode: "INVALID_END_POINT",
						Message:   "Không thể cài đặt 2 vị trí của điểm cuối trong cùng 1 tuyến.",
					}
				}
			}
		}

		codes[p.Code] = true
	}

	if startPointCode == "" || endPointCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "INVALID_START_END_POINT",
			Message:   "Không thể để trống diểm bắt đầu hoặc điểm trong cùng tuyến.",
		}
	}

	// Sort theo index
	sort.Slice(input.DropOffPoints, func(i, j int) bool {
		return input.DropOffPoints[i].Index < input.DropOffPoints[j].Index
	})

	// Check valid start_end route
	//checkRoute, _ := utils.GenKeyword(startPointCode, endPointCode)
	//getRoute = model.RouteDB.QueryOne(bson.M{"keyword": bson.M{"$regex": checkRoute}, "status": string(enum.RouteStatus.ACTIVE)})
	//if getRoute.Status == common.APIStatus.Ok {
	//	existedRoute := getRoute.Data.([]*model.Route)[0]
	//	return &common.APIResponse{
	//		Status:    common.APIStatus.Invalid,
	//		ErrorCode: "DUPLICATE_START_END_POINT",
	//		Message:   "Trùng thông tin điểm bắt đầu và kết thúc với tuyến " + existedRoute.RouteName,
	//	}
	//}

	input.Keyword, _ = utils.GenKeyword(input.RouteCode, input.RouteName, startPointCode, endPointCode)
	input.VersionNo = uuid.New().String()
	input.Status = enum.RouteStatus.ACTIVE
	input.TotalDropOffPoint = int64(len(input.DropOffPoints))
	createResult := model.RouteDB.UpdateOne(bson.M{
		"route_code": input.RouteCode,
	}, input, &options.FindOneAndUpdateOptions{
		Upsert: &enum.True,
	})

	if createResult.Status == common.APIStatus.Ok || createResult.Status == common.APIStatus.NotFound {
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Tạo tuyến thành công.",
		}
	}

	return createResult
}

// UpdateRoute action
func UpdateRoute(updater *model.Route) *common.APIResponse {
	if updater.RouteCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "INVALID_ROUTE_CODE",
			Message:   "ID hub không hợp lệ",
		}
	}

	if updater.RouteName == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "INVALID_ROUTE_NAME",
			Message:   "Tên tuyến không được để trống",
		}
	} else {
		getRoute := model.RouteDB.QueryOne(bson.M{"route_name": updater.RouteName, "status": string(enum.RouteStatus.ACTIVE)})
		if getRoute.Status == common.APIStatus.Ok {
			route := getRoute.Data.([]*model.Route)[0]
			if route.RouteCode != updater.RouteCode {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					ErrorCode: "INVALID_ROUTE_NAME",
					Message:   "Tên tuyến đã tồn tại cho tuyến " + route.RouteCode,
				}
			}
		}
	}

	if updater.EstimatedHandoverTime < 0 && updater.EstimatedHandoverTime > 86399 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "INVALID_ESTIMATED_HANDOVER_TIME",
			Message:   "Thời gian bắt đầu bàn giao không đúng (phải nằm trong khoảng từ 00:00 - 23:59)",
		}
	}

	if updater.TotalTransportTime < 36000 && updater.TotalTransportTime > 259200 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "INVALID_TOTAL_TRANSPORT_TIME",
			Message:   "Thời gian luân chuyển phải nằm trong khoảng từ 10p -> 3d",
		}
	}

	if len(updater.DropOffPoints) <= 1 {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Tuyến cần có điểm bắt đầu và kết thúc. Vui lòng điền thông tin đầy đủ",
		}
	}

	getRoute := model.RouteDB.QueryOne(bson.M{"route_code": updater.RouteCode, "status": string(enum.RouteStatus.ACTIVE)})
	if getRoute.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã tuyến " + updater.RouteCode + " không tồn tại",
		}
	}
	oldRoute := getRoute.Data.([]*model.Route)[0]

	if updater.Status == enum.RouteStatus.DELETED {
		filterTrip := bson.M{}
		filterTrip["route.route_code"] = oldRoute.RouteCode
		filterTrip["status"] = bson.M{
			"$nin": []string{string(enum.TripStatus.COMPLETE), string(enum.TripStatus.CANCELED)},
		}

		tripRaw := model.TripDB.QueryOne(filterTrip)
		if tripRaw.Status == common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không thể xóa tuyến có Chuyến đi (Trip) chưa Hoàn thành/Hủy",
			}
		}
	}

	// Todo: Validate drop off point
	var codes = map[string]bool{}
	startPointCode, endPointCode := "", ""
	for i, p := range updater.DropOffPoints {
		if p.Code == "" {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Mã điểm thả hàng không được để trống. Vị trí thứ " + strconv.Itoa(i),
			}
		}

		if codes[p.Code] {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Mã điểm thả hàng trùng lặp trong tuyến",
			}
		}

		if p.Index <= 0 {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Vị trí của điểm trong tuyến không được để trống.",
			}
		}

		if p.Type == nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Loại vị trí của điểm trong tuyến không được để trống.",
			}
		} else {
			if *p.Type == enum.DropOffPointType.START {
				if startPointCode == "" {
					startPointCode = p.Code
				} else {
					return &common.APIResponse{
						Status:  common.APIStatus.Invalid,
						Message: "Vị trí của điểm trong tuyến không được để trống.",
					}
				}
			} else if *p.Type == enum.DropOffPointType.END {
				if endPointCode == "" {
					endPointCode = p.Code
				} else {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						ErrorCode: "INVALID_END_POINT",
						Message:   "Không thể cài đặt 2 vị trí của điểm cuối trong cùng 1 tuyến.",
					}
				}
			}
		}

		codes[p.Code] = true
	}

	if startPointCode == "" || endPointCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "INVALID_START_END_POINT",
			Message:   "Không thể để trống diểm bắt đầu hoặc điểm trong cùng tuyến.",
		}
	}

	sort.Slice(updater.DropOffPoints, func(i, j int) bool {
		return updater.DropOffPoints[i].Index < updater.DropOffPoints[j].Index
	})

	//// Check valid start_end route
	//checkRoute, _ := utils.GenKeyword(startPointCode, endPointCode)
	//getRoute = model.RouteDB.QueryOne(bson.M{"keyword": bson.M{"$regex": checkRoute}, "status": string(enum.RouteStatus.ACTIVE)})
	//if getRoute.Status == common.APIStatus.Ok {
	//	existedRoute := getRoute.Data.([]*model.Route)[0]
	//	if existedRoute.RouteCode != updater.RouteCode {
	//		return &common.APIResponse{
	//			Status:    common.APIStatus.Invalid,
	//			ErrorCode: "DUPLICATE_START_END_POINT",
	//			Message:   "Trùng thông tin điểm bắt đầu và kết thúc với tuyến " + existedRoute.RouteName,
	//		}
	//	}
	//}

	updater.Keyword, _ = utils.GenKeyword(updater.RouteCode, updater.RouteName, startPointCode, endPointCode)
	currentVersionNo := oldRoute.VersionNo
	updater.VersionNo = uuid.New().String()
	updater.TotalDropOffPoint = int64(len(updater.DropOffPoints))
	return model.RouteDB.UpdateOne(bson.M{
		"route_code": updater.RouteCode,
		"version_no": currentVersionNo,
	},
		updater,
		nil)
}
