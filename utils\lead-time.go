package utils

import (
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"go.mongodb.org/mongo-driver/bson"
)

func ParseLeadTimeConfig(carrierCode string, fromAddress *model.Address, toAddress *model.Address, typeLeadTime enum.LeadTimeConfigValue) (*model.ConfigLeadTime, error) {
	var err error

	if carrierCode == "" {
		err = fmt.Errorf("Không tìm thấy thông tin nhà vận chuyển")
	}

	if fromAddress == nil {
		err = fmt.Errorf("Không tìm thấy thông tin địa chỉ đi")
	}

	if toAddress == nil {
		err = fmt.Errorf("Không tìm thấy thông tin địa chỉ kết thúc")
	}

	if err != nil {
		return nil, err
	}

	queryLeadTimeResult := model.ConfigLeadTimeDB.QueryOne(bson.M{
		"type":         typeLeadTime,
		"active":       true,
		"carrier_code": carrierCode,
		"level":        3,
		"from_code":    fromAddress.WardCode,
		"to_code":      toAddress.WardCode,
	})

	if queryLeadTimeResult.Status == common.APIStatus.Ok {
		return queryLeadTimeResult.Data.([]*model.ConfigLeadTime)[0], err
	} else {
		queryLeadTimeResult = model.ConfigLeadTimeDB.QueryOne(bson.M{
			"type":         typeLeadTime,
			"active":       true,
			"carrier_code": carrierCode,
			"level":        2,
			"from_code":    fromAddress.DistrictCode,
			"to_code":      toAddress.DistrictCode,
		})
		if queryLeadTimeResult.Status == common.APIStatus.Ok {
			return queryLeadTimeResult.Data.([]*model.ConfigLeadTime)[0], err
		} else {
			queryLeadTimeResult = model.ConfigLeadTimeDB.QueryOne(bson.M{
				"type":         typeLeadTime,
				"active":       true,
				"carrier_code": carrierCode,
				"level":        1,
				"from_code":    fromAddress.ProvinceCode,
				"to_code":      toAddress.ProvinceCode,
			})

			if queryLeadTimeResult.Status == common.APIStatus.Ok {
				return queryLeadTimeResult.Data.([]*model.ConfigLeadTime)[0], err
			}
		}
	}

	return nil, nil
}
