package model

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Province struct {
	Dictionary     []string `json:"dictionary"`
	Code           string   `json:"code"`
	Name           string   `json:"name"`
	Level          string   `json:"level"`
	IsSupport      bool     `json:"isSupport"`
	FeeValue       float64  `json:"feeValue,omitempty"`
	FeeRegionValue float64  `json:"feeRegionValue,omitempty"`
	RegionCode     string   `json:"regionCode,omitempty"`
	RegionName     string   `json:"regionName,omitempty"`
	ErpCode        string   `json:"erpCode,omitempty"`
}

type ProvinceMapping struct {
	Code       string   `json:"code" bson:"code,omitempty"`
	Name       string   `json:"name" bson:"name,omitempty"`
	VTPId      int64    `json:"vtpId" bson:"vtp_id,omitempty"`
	NhatTinId  int64    `json:"nhattinId" bson:"nhattin_id,omitempty"`
	VNPId      string   `json:"vnpId" bson:"vnp_id,omitempty"`
	GHNId      int64    `json:"ghnId" bson:"ghn_id,omitempty"`
	Dictionary []string `json:"dictionary" bson:"dictionary,omitempty"`
}

type ProvinceGHN struct {
	Code          string   `json:"Code"`
	ProvinceId    int64    `json:"ProvinceId"`
	ProvinceName  string   `json:"ProvinceName"`
	NameExtension []string `json:"NameExtension"`
}

type ProvinceNhatTin struct {
	Id           int64  `json:"id"`
	ProvinceName string `json:"province_name"`
}

type ProvinceVTP struct {
	ProvinceId   int64  `json:"PROVINCE_ID"`
	ProvinceCode string `json:"PROVINCE_CODE"`
	ProvinceName string `json:"PROVINCE_NAME"`
}

type ProvinceVNP struct {
	Name string `json:"TenTinhThanh"`
	Code string `json:"MaTinhThanh"`
}

// ProvinceMappingDB is an instance db
var ProvinceMappingDB = &db.Instance{
	ColName:        "province_mapping",
	TemplateObject: &ProvinceMapping{},
}

// InitProvinceMappingModel is func init model province
func InitProvinceMappingModel(s *mongo.Database) {
	ProvinceMappingDB.ApplyDatabase(s)

	t := true
	_ = ProvinceMappingDB.CreateIndex(bson.D{
		primitive.E{Key: "name", Value: 1},
		primitive.E{Key: "code", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
		Unique:     &t,
	})

	ProvinceMappingDB.CreateIndex(bson.D{
		{"code", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	ProvinceMappingDB.CreateIndex(bson.D{
		{"dictionary", 1},
	}, &options.IndexOptions{
		Background: &t,
	})
}
