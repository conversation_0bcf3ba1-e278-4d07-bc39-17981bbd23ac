package action

import (
	"strings"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func GetProvinceMapping(provinceCode, name string) *common.APIResponse {
	conditions := bson.M{}
	if provinceCode != "" {
		conditions["code"] = provinceCode
	}

	if name != "" {
		conditions["dictionary"] = name
	}

	result := model.ProvinceMappingDB.Query(conditions, 0, 100, &primitive.M{"_id": -1})
	return result
}

func GetDistrictMapping(districtCode, name string) *common.APIResponse {
	conditions := bson.M{}
	if districtCode != "" {
		conditions["code"] = districtCode
	}

	if name != "" {
		conditions["dictionary"] = name
	}

	result := model.DistrictMappingDB.Query(conditions, 0, 100, &primitive.M{"_id": -1})
	return result

}

func GetWardMapping(wardCode, name string) *common.APIResponse {
	conditions := bson.M{}
	if wardCode != "" {
		conditions["code"] = wardCode
	}

	if name != "" {
		conditions["dictionary"] = name
	}

	result := model.WardMappingDB.Query(conditions, 0, 100, &primitive.M{"_id": -1})
	return result
}

func UpdateProvinceMapping(input *model.ProvinceMapping) *common.APIResponse {
	if input.Code == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Mã Tỉnh/TP không được để trống.",
		}
	}

	afterOption := options.After

	updateCallbackResult := model.ProvinceMappingDB.UpdateOne(
		&model.ProvinceMapping{
			Code: input.Code,
		},
		input,
		&options.FindOneAndUpdateOptions{
			Upsert:         &enum.True,
			ReturnDocument: &afterOption,
		})

	return updateCallbackResult
}

func UpdateDistrictMapping(input *model.DistrictMapping) *common.APIResponse {
	if input.Code == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Mã Quận/Huyện không được để trống.",
		}
	}

	afterOption := options.After

	updateCallbackResult := model.DistrictMappingDB.UpdateOne(
		&model.ProvinceMapping{
			Code: input.Code,
		},
		input,
		&options.FindOneAndUpdateOptions{
			Upsert:         &enum.True,
			ReturnDocument: &afterOption,
		})

	return updateCallbackResult
}

func UpdateWardMapping(input *model.WardMapping) *common.APIResponse {
	if input.Code == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Mã Phường/Xã không được để trống.",
		}
	}

	afterOption := options.After

	updateCallbackResult := model.WardMappingDB.UpdateOne(
		&model.ProvinceMapping{
			Code: input.Code,
		},
		input,
		&options.FindOneAndUpdateOptions{
			Upsert:         &enum.True,
			ReturnDocument: &afterOption,
		})

	return updateCallbackResult
}

func AddProvinceDictionary(provinceCode, dictionaryName string) *common.APIResponse {
	result := model.ProvinceMappingDB.UpdateOneWithOption(
		&model.Province{
			Code: provinceCode,
		},
		bson.M{
			"$push": bson.M{
				"dictionary": dictionaryName,
			},
		})
	return result
}

func AddWardDictionary(wardCode, dictionaryName string) *common.APIResponse {
	result := model.WardMappingDB.UpdateOneWithOption(
		&model.Ward{
			Code: wardCode,
		},
		bson.M{
			"$push": bson.M{
				"dictionary": dictionaryName,
			},
		})
	return result
}

func AddDistrictDictionary(districtCode, dictionaryName string) *common.APIResponse {
	result := model.DistrictMappingDB.UpdateOneWithOption(
		&model.District{
			Code: districtCode,
		},
		bson.M{
			"$push": bson.M{
				"dictionary": dictionaryName,
			},
		})
	return result
}

func DeleteProvinceMapping(provinceCode string) *common.APIResponse {
	return model.ProvinceMappingDB.Delete(
		bson.M{
			"code": provinceCode,
		})
}

func DeleteDistrictMapping(districtCode string) *common.APIResponse {
	return model.DistrictMappingDB.Delete(
		bson.M{
			"code": districtCode,
		})
}

func DeleteWardMapping(wardCode string) *common.APIResponse {
	return model.WardMappingDB.Delete(
		bson.M{
			"code": wardCode,
		})
}

func GetProvinceList(name, provinceCode string, offset, limit int64, getTotal bool) *common.APIResponse {
	conditions := bson.M{}
	if provinceCode != "" {
		conditions["code"] = provinceCode
	}

	if name != "" {
		search := []string{name}
		search = append(search, strings.ToLower(name))
		conditions["dictionary"] = bson.M{
			"$in": search,
		}
	}

	result := model.ProvinceMappingDB.Query(conditions, offset, limit, &primitive.M{"_id": -1})
	if getTotal {
		countResult := model.ProvinceMappingDB.Count(conditions)
		result.Total = countResult.Total
	}

	return result
}

func GetWardList(name, wardCode, districtCode, provinceCode string, offset, limit int64, getTotal bool) *common.APIResponse {
	conditions := bson.M{}
	if wardCode != "" {
		conditions["code"] = wardCode
	}

	if districtCode != "" {
		conditions["district_code"] = districtCode
	}

	if provinceCode != "" {
		conditions["province_code"] = provinceCode
	}

	if name != "" {
		search := []string{name}
		search = append(search, strings.ToLower(name))
		conditions["dictionary"] = bson.M{
			"$in": search,
		}
	}

	result := model.WardMappingDB.Query(conditions, offset, limit, &primitive.M{"_id": -1})
	if getTotal {
		countResult := model.WardMappingDB.Count(conditions)
		result.Total = countResult.Total
	}

	return result
}

func GetDistrictList(districtCode, provinceCode string, name string, offset, limit int64, getTotal bool) *common.APIResponse {
	conditions := bson.M{}
	if provinceCode != "" {
		conditions["province_code"] = provinceCode
	}

	if districtCode != "" {
		conditions["code"] = districtCode
	}

	if name != "" {
		search := []string{name}
		search = append(search, strings.ToLower(name))
		conditions["dictionary"] = bson.M{
			"$in": search,
		}
	}

	result := model.DistrictMappingDB.Query(conditions, offset, limit, &primitive.M{"_id": -1})
	if getTotal {
		countResult := model.DistrictMappingDB.Count(conditions)
		result.Total = countResult.Total
	}

	return result
}

func SyncMasterData() *common.APIResponse {
	provinces, err := client.Services.MasterDataClient.GetAllProvinces()
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "bad request when get Province",
		}
	}

	for _, province := range provinces {
		districts, err := client.Services.MasterDataClient.GetDistrictByProvinceCode(province.Code)
		if err != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Ok,
				Message: "bad request When get District",
			}
		}
		for _, district := range districts {
			var dictionaryDistrict []string
			dictionaryDistrict = append(dictionaryDistrict, district.Name)
			dictionaryDistrict = append(dictionaryDistrict, strings.ToLower(district.Name))
			if strings.HasPrefix(district.Name, "Thành phố") {
				dictionaryDistrict = append(dictionaryDistrict, strings.Replace(district.Name, "Thành phố", "TP", 1))
				dictionaryDistrict = append(dictionaryDistrict, strings.Replace(district.Name, "Thành phố", "Tp", 1))
				dictionaryDistrict = append(dictionaryDistrict, strings.Replace(district.Name, "Thành phố ", "", 1))
			} else if strings.HasPrefix(district.Name, "Quận") {
				dictionaryDistrict = append(dictionaryDistrict, strings.Replace(district.Name, "Quận", "Q", 1))
				dictionaryDistrict = append(dictionaryDistrict, strings.Replace(district.Name, "Quận ", "", 1))
			} else if strings.HasPrefix(district.Name, "Huyện") {
				dictionaryDistrict = append(dictionaryDistrict, strings.Replace(district.Name, "Huyện", "H", 1))
				dictionaryDistrict = append(dictionaryDistrict, strings.Replace(district.Name, "Huyện ", "", 1))
			} else if strings.HasPrefix(district.Name, "Thị xã") {
				dictionaryDistrict = append(dictionaryDistrict, strings.Replace(district.Name, "Thị xã", "Tx", 1))
				dictionaryDistrict = append(dictionaryDistrict, strings.Replace(district.Name, "Thị xã ", "", 1))
			}

			if strings.Contains(district.Name, "'") {
				dictionaryDistrict = append(dictionaryDistrict, strings.Replace(district.Name, "'", "", 1))
				dictionaryDistrict = append(dictionaryDistrict, strings.Replace(district.Name, "'", " ", 1))
			}

			district.Dictionary = dictionaryDistrict
			t := true
			districtMapping := model.DistrictMapping{
				Code:         district.Code,
				Name:         district.Name,
				ProvinceCode: district.ProvinceCode,
				Dictionary:   dictionaryDistrict,
			}
			_ = model.DistrictMappingDB.UpdateOne(
				bson.M{
					"code": district.Code,
				},
				districtMapping,
				&options.FindOneAndUpdateOptions{
					Upsert: &t,
				})
		}

		var dictionaryProvinces []string
		dictionaryProvinces = append(dictionaryProvinces, province.Name)
		dictionaryProvinces = append(dictionaryProvinces, strings.ToLower(province.Name))
		if province.Level == "Tỉnh" {
			dictionaryProvinces = append(dictionaryProvinces, strings.TrimSpace(strings.Replace(province.Name, "Tỉnh", "", 1)))
		} else {
			dictionaryProvinces = append(dictionaryProvinces, strings.Replace(province.Name, "Thành phố", "TP", 1))
			dictionaryProvinces = append(dictionaryProvinces, strings.Replace(province.Name, "Thành phố", "Tp", 1))
			dictionaryProvinces = append(dictionaryProvinces, strings.TrimSpace(strings.Replace(province.Name, "Thành phố", "", 1)))
		}
		province.Dictionary = dictionaryProvinces

		t := true
		_ = model.ProvinceMappingDB.UpdateOne(
			bson.M{
				"code": province.Code,
			},
			province,
			&options.FindOneAndUpdateOptions{
				Upsert: &t,
			})
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật thành công.",
	}
}

func SyncWard() *common.APIResponse {
	provinces, err := client.Services.MasterDataClient.GetAllProvinces()
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "bad request when get Province",
		}
	}

	for _, province := range provinces {
		districts, err := client.Services.MasterDataClient.GetDistrictByProvinceCode(province.Code)
		if err != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Ok,
				Message: "bad request When get District",
			}
		}
		for _, district := range districts {
			wards, _ := client.Services.MasterDataClient.GetWardByDistrictCode("", district.Code, province.Code)
			for _, ward := range wards {
				var dictionaryWards []string
				dictionaryWards = append(dictionaryWards, ward.Name)

				ward.Dictionary = dictionaryWards
				wardMapping := model.WardMapping{
					Code:         ward.Code,
					Name:         ward.Name,
					DistrictCode: ward.DistrictCode,
					Dictionary:   dictionaryWards,
				}
				t := true
				_ = model.WardMappingDB.UpdateOne(
					bson.M{
						"code": ward.Code,
					},
					wardMapping,
					&options.FindOneAndUpdateOptions{
						Upsert: &t,
					})
			}
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật thành công.",
	}
}

func DeleteWardById(id string) *common.APIResponse {
	_id, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Id không hợp lệ.",
		}
	}
	return model.WardMappingDB.Delete(
		bson.M{
			"_id": _id,
		})
}
