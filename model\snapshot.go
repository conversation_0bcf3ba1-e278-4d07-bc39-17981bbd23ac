package model

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

type Snapshot struct {
	ID         primitive.ObjectID     `json:"id,omitempty" bson:"_id,omitempty"`
	Type       enum.SnapshotTypeValue `json:"type,omitempty" bson:"type,omitempty"`
	Quantity   int64                  `json:"quantity,omitempty" bson:"quantity,omitempty"`
	Key        string                 `json:"key,omitempty" bson:"key,omitempty"`
	SnapshotAt time.Time              `json:"snapshotAt,omitempty" bson:"snapshot_at,omitempty"`
}

var SnapshotDB = &db.Instance{
	ColName:        "snapshot",
	TemplateObject: &Snapshot{},
}

func InitSnapshotModel(s *mongo.Database) {
	SnapshotDB.ApplyDatabase(s)
}
