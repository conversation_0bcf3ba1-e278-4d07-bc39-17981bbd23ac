package model

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type CarrierProductivity struct {
	HubCode     string                                            `json:"HubCode,omitempty" bson:"hub_code,omitempty"`
	DriverId    int64                                             `json:"driverId" bson:"driver_id,omitempty"`
	UpdatedTime int64                                             `json:"updatedTime,omitempty" bson:"updated_time,omitempty"`
	Month       int                                               `json:"month,omitempty" bson:"month,omitempty"`
	Year        int                                               `json:"year,omitempty" bson:"year,omitempty"`
	Reports     []map[enum.HubOrderTypeValue][]HubOrderTypeReport `json:"reports,omitempty" bson:"reports,omitempty"`
}

type HubOrderTypeReport struct {
	SubType         *enum.SubTypeValue `json:"subType,omitempty" bson:"sub_type,omitempty"`
	SuccessDelivery int64              `json:"successDelivery,omitempty" bson:"success_delivery"`
	TotalDelivery   int64              `json:"totalDelivery,omitempty" bson:"total_delivery"`
	SuccessPickup   int64              `json:"successPickup,omitempty" bson:"success_pickup"`
	TotalPickup     int64              `json:"totalPickup,omitempty" bson:"total_pickup"`
}

var CarrierProductivityDB = &db.Instance{
	ColName:        "carrier_productivity_cache",
	TemplateObject: &CarrierProductivity{},
}

func InitCarrierProductivityModel(s *mongo.Database) {
	CarrierProductivityDB.ApplyDatabase(s)

	t := true
	CarrierProductivityDB.CreateIndex(bson.D{
		{"hub_code", 1},
		{"driver_id", 1},
	}, &options.IndexOptions{
		Background: &t,
	})
}
