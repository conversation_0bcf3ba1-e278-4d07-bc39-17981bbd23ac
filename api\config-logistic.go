package api

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
)

// Transport config
func GetConfigLogistic(req sdk.APIRequest, resp sdk.APIResponder) error {
	str := req.GetParam("key")
	if str == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Key is not empty",
			ErrorCode: "INVALID_DATA",
		})
	}
	return resp.Respond(action.GetConfigLogistic(str))
}

func UpsertConfigLogistic(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.ConfigLogistic

	err := req.GetContent(&input)

	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.UpsertConfigLogistic(&input))
}

func MigrateDefaultHubConfig(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.ConfigLogistic

	err := req.GetContent(&input)

	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.MigrateDefaultHubConfig(&input))
}
