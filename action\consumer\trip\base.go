package trip

import (
	"log"
	"sync"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
	"go.mongodb.org/mongo-driver/mongo"
)

type ExecutorJob struct {
	Job *job.Executor
}

var (
	instance              map[string]*ExecutorJob
	onceInit              map[string]*sync.Once
	defaultTopic          = "complete_handover_trip"
	autoCompleteTripTopic = "auto_complete_trip"
)

func init() {
	onceInit = map[string]*sync.Once{}
	instance = map[string]*ExecutorJob{}
}

func init() {
	defaultTopic = conf.Config.Topics["complete_handover_trip"]
	autoCompleteTripTopic = conf.Config.Topics["auto_complete_trip"]
}

func InitTripJob(dbSession *mongo.Database, Database, Collection string) {
	instanceName := defaultTopic
	if onceInit[instanceName] == nil {
		onceInit[instanceName] = &sync.Once{}
	}
	onceInit[instanceName].Do(func() {
		instance[instanceName] = &ExecutorJob{
			&job.Executor{ColName: Collection},
		}

		instance[instanceName].Job.InitWithConfig(dbSession,
			Database,
			&job.ExecutorConfiguration{
				ParallelTopicProcessing: false,
				FailThreshold:           50,
				ChannelCount:            5,
				SortedItem:              false,
				LogSize:                 5,
				SelectorDelayMS:         100,
				WaitForReadyTime:        false,
				UniqueItem:              false,
				ConsumedExipredTime:     time.Duration(720) * time.Hour,
				CurVersionTimeoutS:      30,
				OldVersionTimeoutS:      30,
				MaximumWaitToRetryS:     15,
			},
		)

		log.Println("StartConsume", instanceName)
		instance[instanceName].completeHandoverTrip()
		instance[instanceName].checkAutoCompleteTrip()
		instance[instanceName].Job.StartConsume()
	})
}

func PushCompleteHandoverTrip(data interface{}, sortedKey string) (err error) {
	return instance[defaultTopic].Job.Push(
		data, &job.JobItemMetadata{
			Topic:     defaultTopic,
			SortedKey: sortedKey,
		})
}

func PushAutoCompleteTrip(data interface{}, sortedKey string) (err error) {
	return instance[defaultTopic].Job.Push(
		data, &job.JobItemMetadata{
			Topic:     autoCompleteTripTopic,
			SortedKey: sortedKey,
		})
}

type CompleteHandoverTripRequest struct {
	TripId  int64 `json:"tripId,omitempty" bson:"trip_id,omitempty"`
	Tickets []int `json:"tickets,omitempty" bson:"tickets,omitempty"`
}
