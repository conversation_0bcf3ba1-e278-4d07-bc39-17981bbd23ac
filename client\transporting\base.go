package transporting

import (
	"encoding/json"
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/golang/configuration"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathBookShipping = "/delivery/transporting/v1/shipping-order"
)

// TransportingClient
type Client struct {
	svc     *client.RestClient
	headers map[string]string
}

func NewServiceClient(serviceConfig configuration.Service, logName string, session *mongo.Database) *Client {
	if serviceConfig.Host == "" {
		return nil
	}

	newClient := &Client{
		svc: client.NewRESTClient(
			serviceConfig.Host,
			logName,
			10*time.Second,
			1,
			10*time.Second,
		),
		headers: map[string]string{
			"Authorization": serviceConfig.Authorization,
		},
	}

	newClient.svc.SetDBLog(session)
	return newClient
}

// BookShipping request...
func (cli *Client) BookShippingService(request interface{}) (err error) {
	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, request, pathBookShipping, nil)
	if err != nil {
		return nil
	}

	type myResponse struct {
		Status  string `json:"status"`
		Code    string `json:"code"`
		Message string `json:"message"`
	}

	resp := new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &resp)
	if err != nil {
		return err
	}

	if resp.Status != common.APIStatus.Ok {
		return fmt.Errorf("%v-%v", resp.Code, resp.Message)
	}

	return
}
