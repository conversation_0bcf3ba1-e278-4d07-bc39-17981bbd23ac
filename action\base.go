package action

import (
	"fmt"
	"reflect"
	"strings"
)

func validate(field interface{}) (err error) {
	switch field.(type) {
	case string:
		if field.(string) == "" {
			err = fmt.<PERSON><PERSON><PERSON>("BAD_DATA")
		}
		break
	case int:
		if field.(int) <= 0 {
			err = fmt.<PERSON><PERSON><PERSON>("BAD_DATA")
		}
		break
	case int32:
		if field.(int32) <= 0 {
			err = fmt.<PERSON><PERSON>("BAD_DATA")
		}
		break
	case int64:
		if field.(int64) <= 0 {
			err = fmt.<PERSON><PERSON><PERSON>("BAD_DATA")
		}
		break
	case float32:
		if field.(float32) <= 0 {
			err = fmt.<PERSON><PERSON><PERSON>("BAD_DATA")
		}
		break
	case float64:
		if field.(float64) <= 0 {
			err = fmt.<PERSON><PERSON><PERSON>("BAD_DATA")
		}
		break
	}
	return
}

func CheckItemInArray(item interface{}, array interface{}) bool {
	// Check if array or item is nil
	if array == nil || item == nil {
		return false
	}

	itemType := reflect.TypeOf(item)
	arrayType := reflect.TypeOf(array)

	if reflect.TypeOf(array).Kind() != reflect.Slice {
		return false
	}

	switch arrayType.String() {
	case "[]int64":
		newArray := array.([]int64)

		// Type khác nhau, array empty thì lấy gì pằng
		if len(newArray) == 0 {
			return false
		}

		arrayType = reflect.TypeOf(newArray[0])

		if itemType != arrayType {
			return false
		}

		for _, ele := range newArray {
			if reflect.ValueOf(ele).Int() == reflect.ValueOf(item).Int() {
				return true
			}
		}
		break
	case "[]string":
		newArray := array.([]string)
		// Type khác nhau, array empty thì lấy gì pằng
		if len(newArray) == 0 {
			return false
		}

		arrayType = reflect.TypeOf(newArray[0])
		if itemType != arrayType {
			return false
		}
		for _, ele := range newArray {
			if reflect.ValueOf(ele).String() == reflect.ValueOf(item).String() {
				return true
			}
		}
		break
	}
	return false
}

func NormalizeString(s string) string {
	return strings.ToLower(strings.TrimSpace(s))
}
