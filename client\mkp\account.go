package mkp

import (
	"encoding/json"
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/response"
)

func (c *MkpClient) GetListMkpAccount(req request.MkpAccountRequest) ([]*response.MkpAccountResponse, error) {
	res, err := c.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, c.headers, nil, req, pathGetListMkpAccount, nil)
	if err != nil {
		return nil, err
	}

	type responseData struct {
		response.BaseResponse
		Data []*response.MkpAccountResponse `json:"data"`
	}

	resBody := new(responseData)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return nil, err
	}

	if resBody.Status != common.APIStatus.Ok {
		return nil, fmt.Errorf("get list mkp account %v", resBody.ErrorCode)
	}

	return resBody.Data, nil
}
