package api

import (
	"encoding/json"
	"strconv"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
)

func GetConfigFee(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.ConfigFee
	str := req.GetParam("q")
	if str == "" {
		str = "{}"
	}
	var offset = int64(sdk.ParseInt(req.GetParam("offset"), 0))
	var limit = int64(sdk.ParseInt(req.GetParam("limit"), 100))
	var getTotal = req.GetParam("getTotal") == "true"
	err := json.Unmarshal([]byte(str), &input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.GetConfigFee(input, offset, limit, getTotal))
}

func CreateConfigFee(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.ConfigFee
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}

	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	input.LastUpdatedBy = strconv.FormatInt(UserInfo.Account.AccountID, 10)

	return resp.Respond(action.CreateConfigFee(&input))
}

func UpdateConfigFee(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.ConfigFee
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}

	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	input.LastUpdatedBy = strconv.FormatInt(UserInfo.Account.AccountID, 10)

	return resp.Respond(action.UpdateConfigFee(&input))
}

func MigrateConfigFeeKeyWord(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.MigrateConfigFeeKeyWord())
}

func GetDefaultConfigFee(req sdk.APIRequest, resp sdk.APIResponder) error {
	type GetDefaultConfigFee struct {
		ShippingOrderType enum.ShippingOrderTypeValue `json:"shippingOrderType"`
	}
	var input GetDefaultConfigFee
	str := req.GetParam("q")
	if str == "" {
		str = "{}"
	}
	err := json.Unmarshal([]byte(str), &input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.GetDefaultConfigFee(input.ShippingOrderType))
}
