package api

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
)

// GetMasterData api
func GetMasterData(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		provinceCode = req.GetParam("provinceCode")
		districtCode = req.GetParam("districtCode")
		wardCode     = req.GetParam("wardCode")
		name         = req.GetParam("name")
	)

	if provinceCode != "" {
		return resp.Respond(action.GetProvinceMapping(provinceCode, name))

	} else if districtCode != "" {
		return resp.Respond(action.GetDistrictMapping(districtCode, name))

	} else if wardCode != "" {
		return resp.Respond(action.GetWardMapping(wardCode, name))

	}

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Invalid,
		Message: "Invalid master data",
	})
}

// UpdateProvinceMapping api
func UpdateProvinceMapping(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.ProvinceMapping
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.UpdateProvinceMapping(&input))
}

// UpdateDistrictMapping api
func UpdateDistrictMapping(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.DistrictMapping
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.UpdateDistrictMapping(&input))
}

// UpdateWardMapping api
func UpdateWardMapping(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.WardMapping
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.UpdateWardMapping(&input))
}

// UpdateShippingOrder api
func UpdateDictionary(req sdk.APIRequest, resp sdk.APIResponder) error {
	type myRequest struct {
		DistrictCode string `json:"districtCode"`
		ProvinceCode string `json:"provinceCode"`
		WardCode     string `json:"wardCode"`
		Dictionary   string `json:"dictionary"`
	}

	var input myRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status: common.APIStatus.Invalid,
		})
	}

	if input.ProvinceCode != "" {
		return resp.Respond(action.AddProvinceDictionary(input.ProvinceCode, input.Dictionary))

	} else if input.DistrictCode != "" {
		return resp.Respond(action.AddDistrictDictionary(input.DistrictCode, input.Dictionary))

	} else if input.WardCode != "" {
		return resp.Respond(action.AddWardDictionary(input.WardCode, input.Dictionary))

	}

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Invalid,
		Message: "Invalid master data",
	})
}

// DeleteMasterData api
func DeleteMasterData(req sdk.APIRequest, resp sdk.APIResponder) error {
	type myRequest struct {
		DistrictCode string `json:"districtCode"`
		ProvinceCode string `json:"provinceCode"`
		WardCode     string `json:"wardCode"`
	}

	var input myRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status: common.APIStatus.Invalid,
		})
	}

	if input.ProvinceCode != "" {
		return resp.Respond(action.DeleteProvinceMapping(input.ProvinceCode))

	} else if input.DistrictCode != "" {
		return resp.Respond(action.DeleteDistrictMapping(input.DistrictCode))

	} else if input.WardCode != "" {
		return resp.Respond(action.DeleteWardMapping(input.WardCode))

	}

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Invalid,
		Message: "Invalid master data",
	})
}

// GetWard api
func GetWard(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset       = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit        = sdk.ParseInt64(req.GetParam("limit"), 100)
		districtCode = req.GetParam("districtCode")
		provinceCode = req.GetParam("provinceCode")
		code         = req.GetParam("code")
		getTotal     = req.GetParam("getTotal") == "true"
		name         = req.GetParam("name")
	)

	return resp.Respond(action.GetWardList(name, code, districtCode, provinceCode, offset, limit, getTotal))
}

// GetDistrict api
func GetDistrict(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset       = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit        = sdk.ParseInt64(req.GetParam("limit"), 100)
		provinceCode = req.GetParam("provinceCode")
		districtCode = req.GetParam("districtCode")
		getTotal     = req.GetParam("getTotal") == "true"
		name         = req.GetParam("name")
	)

	return resp.Respond(action.GetDistrictList(districtCode, provinceCode, name, offset, limit, getTotal))
}

// GetProvince api
func GetProvince(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset       = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit        = sdk.ParseInt64(req.GetParam("limit"), 100)
		getTotal     = req.GetParam("getTotal") == "true"
		provinceCode = req.GetParam("provinceCode")
		name         = req.GetParam("name")
	)

	return resp.Respond(action.GetProvinceList(name, provinceCode, offset, limit, getTotal))
}

func SyncMasterData(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.SyncMasterData())
}

func SyncWard(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.SyncWard())
}

func DeleteWardById(req sdk.APIRequest, resp sdk.APIResponder) error {
	type myRequest struct {
		Id string `json:"id"`
	}

	var input myRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status: common.APIStatus.Invalid,
		})
	}
	return resp.Respond(action.DeleteWardById(input.Id))
}
