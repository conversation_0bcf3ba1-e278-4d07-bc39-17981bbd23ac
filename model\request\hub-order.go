package request

import (
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
)

type HubShippingOrderQuery struct {
	Status                   *enum.HubShippingOrderStatusValue   `json:"status,omitempty"`
	Statuses                 []*enum.HubShippingOrderStatusValue `json:"statuses,omitempty"`
	ReferenceCode            string                              `json:"referenceCode,omitempty"`
	ListReferenceCode        []string                            `json:"listReferenceCode,omitempty"`
	TrackingCode             string                              `json:"trackingCode,omitempty"`
	HubCode                  string                              `json:"hubCode"`
	ListTrackingCode         []string                            `json:"listTrackingCode,omitempty"`
	TplCode                  *enum.PartnerValue                  `json:"tplCode,omitempty"`
	ListTplCode              []*enum.PartnerValue                `json:"listTplCode,omitempty"`
	DriverId                 int64                               `json:"driverId,omitempty"`
	FromTime                 int64                               `json:"fromDate"`
	ToTime                   int64                               `json:"toDate"`
	ProvinceCode             string                              `json:"provinceCode"`
	DistrictCode             string                              `json:"districtCode"`
	WardCode                 string                              `json:"wardCode"`
	ProvinceCodes            []string                            `json:"provinceCodes"`
	DistrictCodes            []string                            `json:"districtCodes"`
	WardCodes                []string                            `json:"wardCodes"`
	EstimateTime             int64                               `json:"estimateTime"`
	FromCompletedTime        int64                               `json:"fromCompletedTime"`
	ToCompletedTime          int64                               `json:"toCompletedTime"`
	FromDeliveredTime        int64                               `json:"fromDeliveredTime"`
	ToDeliveredTime          int64                               `json:"toDeliveredTime"`
	TransportCode            string                              `json:"transportCode"`
	Types                    []string                            `json:"types"`
	Skus                     []string                            `json:"skus"`
	Depots                   []*model.Address                    `json:"depots"`
	FromCreatedTime          int64                               `json:"fromCreatedTime"`
	ToCreatedTime            int64                               `json:"toCreatedTime"`
	FromWarningLevel         int64                               `json:"fromWarningLevel"`
	ToWarningLevel           int64                               `json:"toWarningLevel"`
	FromProvinceCodes        []string                            `json:"fromProvinceCodes"`
	FromDistrictCodes        []string                            `json:"fromDistrictCodes"`
	FromWardCodes            []string                            `json:"fromWardCodes"`
	CheckInReferenceCode     string                              `json:"checkInReferenceCode"`
	HubOrderTypes            []HubOrderType                      `json:"hubOrderTypes"`
	Tags                     []string                            `json:"tags"`
	ParentReferenceCode      string                              `json:"parentReferenceCode"`
	ParentReceiveSessionCode string                              `json:"parentReceiveSessionCode"`
	ReadPreference           *enum.ReadPreferenceValue           `json:"readPreference"`
	MergeStatus              *enum.MergeStatusValue              `json:"mergeStatus,omitempty" bson:"merge_status,omitempty"`
	MergeStatuses            []*enum.MergeStatusValue            `json:"mergeStatuses,omitempty"`
	ProductivityQuery        *enum.ProductivityQueryValue        `json:"productivityQuery,omitempty"`
	LeadTimeQuery            *LeadTimeQuery                      `json:"leadTimeQuery,omitempty"`
	TripType                 *enum.TripTypeValue                 `json:"tripType,omitempty"`
	CustomerCode             string                              `json:"customerCode,omitempty"`
	References               []string                            `json:"references,omitempty"`
}

type HubOrderType struct {
	Type    enum.HubOrderTypeValue `json:"type"`
	SubType enum.SubTypeValue      `json:"subType"`
}

type UpdateHubOrder struct {
	Id                 string                            `json:"id"`
	Action             string                            `json:"action"`
	ActionName         string                            `json:"actionName"`
	ReferenceCode      string                            `json:"referenceCode"`
	TplCode            *enum.PartnerValue                `json:"tplCode"`
	TplName            string                            `json:"tplName"`
	TrackingNumber     string                            `json:"trackingNumber"`
	HubCode            string                            `json:"hubCode"`
	Status             *enum.HubShippingOrderStatusValue `json:"status,omitempty"`
	ReceiveQuantity    int64                             `json:"receiveQuantity"`
	HandoverTicketId   int                               `json:"handoverTicketId"`
	HandoverTicketCode string                            `json:"handoverTicketCode"`
	HandoverTicketNote string                            `json:"handoverTicketNote"`

	// DriverID
	DriverID   int64            `json:"driverId"`
	DriverName string           `json:"driverName"`
	Products   []*model.Product `json:"products"`

	CheckInAt enum.CheckInAtValue `json:"checkInAt"`
}

type FilterSort struct {
	ActionTime    *int64                  `json:"actionTime"`
	ProvinceCode  *int64                  `json:"toProvinceCode"`
	DistrictCode  *int64                  `json:"toDistrictCode"`
	WardCode      *int64                  `json:"toWardCode"`
	DeliveredTime *int64                  `json:"deliveredTime"`
	CompletedTime *int64                  `json:"completedTime"`
	Distance      *bool                   `json:"distance"`
	Zone          *enum.HubOrderTypeValue `json:"zone"`
}

type CheckInPickup struct {
	HubCode   string         `json:"hubCode"`
	HubOrders []*CheckInItem `json:"hubOrders"`
}

// This struct can also be used for PO
type CheckInItem struct {
	ReferenceCode    string `json:"referenceCode"`
	NumOfPackages    int64  `json:"numOfPackages"`
	ReceivedPackages int64  `json:"receivedPackages"`
	Reason           string `json:"reason"`
}

// Structure for sorting by distance
type DistanceOption struct {
	VehicleNumber    int                   `json:"vehicleNumber"`
	FromLocation     *LatLng               `json:"fromLocation"`
	MinParcels       int                   `json:"minParcels"`
	MaxParcels       int                   `json:"maxParcels"`
	MaxDistance      int                   `json:"maxDistance"`
	PointMaxDistance int                   `json:"pointMaxDistance"`
	MinVehicles      *bool                 `json:"minVehicles,omitempty"`
	Dimensions       []*model.VRPDimension `json:"dimensions"`
}
