package action

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/response"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/utils"
	"go.mongodb.org/mongo-driver/bson"
)

func GetTrip(query request.GetTripRequest, offset, limit int64, getTotal bool) *common.APIResponse {
	var filter = bson.M{}
	if query.TruckId != 0 {
		filter["truck.truck_id"] = query.TruckId
	}

	if query.DriverId != 0 {
		filter["driver_id"] = query.DriverId
	}

	if query.TripCode != "" {
		filter["trip_code"] = query.TripCode
	}

	if query.TripId != 0 {
		filter["trip_id"] = query.TripId
	}

	if query.RouteCode != "" {
		filter["route.route_code"] = query.RouteCode
	}

	if query.Status != nil {
		filter["status"] = query.Status
	}

	if query.Type != "" {
		filter["type"] = query.Type
	}

	if query.CreatedTimeFrom > 0 && query.CreatedTimeTo > 0 {
		from := time.Unix(query.CreatedTimeFrom, 0)
		to := time.Unix(query.CreatedTimeTo, 0)

		filter["created_time"] = bson.M{
			"$gte":    from,
			"$lte":    to,
			"$exists": true,
		}
	} else if query.CreatedTimeFrom > 0 && query.CreatedTimeTo == 0 {
		from := time.Unix(query.CreatedTimeFrom, 0)
		filter["created_time"] = bson.M{
			"$gte":    from,
			"$exists": true,
		}
	} else if query.CreatedTimeFrom == 0 && query.CreatedTimeTo > 0 {
		to := time.Unix(query.CreatedTimeTo, 0)
		filter["created_time"] = bson.M{
			"$lte":    to,
			"$exists": true,
		}
	}

	if query.CompletedTimeFrom > 0 && query.CompletedTimeTo > 0 {
		from := time.Unix(query.CompletedTimeFrom, 0)
		to := time.Unix(query.CompletedTimeTo, 0)

		filter["completed_time"] = bson.M{
			"$gte":    from,
			"$lte":    to,
			"$exists": true,
		}
	} else if query.CompletedTimeFrom > 0 && query.CompletedTimeTo == 0 {
		from := time.Unix(query.CompletedTimeFrom, 0)
		filter["completed_time"] = bson.M{
			"$gte":    from,
			"$exists": true,
		}
	} else if query.CompletedTimeFrom == 0 && query.CompletedTimeTo > 0 {
		to := time.Unix(query.CompletedTimeTo, 0)
		filter["completed_time"] = bson.M{
			"$lte":    to,
			"$exists": true,
		}
	}

	result := model.TripDB.Query(
		filter,
		offset,
		limit,
		&bson.M{"_id": -1})
	if getTotal {
		countResult := model.TripDB.Count(filter)
		result.Total = countResult.Total
	}

	return result
}

func CountTripByStatus(query request.GetTripRequest) *common.APIResponse {
	type CountTripModel struct {
		Status   string `json:"status,omitempty" bson:"_id,omitempty"`
		Quantity int64  `json:"quantity" bson:"total"`
	}
	filter := bson.D{}
	if query.Type != "" {
		filter = append(filter, bson.E{
			Key:   "type",
			Value: query.Type,
		})
	}

	if query.TripCode != "" {
		filter = append(filter, bson.E{
			Key:   "trip_code",
			Value: query.TripCode,
		})
	}

	if query.TripId != 0 {
		filter = append(filter, bson.E{
			Key:   "trip_id",
			Value: query.TripId,
		})
	}

	if query.RouteCode != "" {
		filter = append(filter, bson.E{
			Key:   "route.route_code",
			Value: query.RouteCode,
		})
	}

	if query.DriverId != 0 {
		filter = append(filter, bson.E{
			Key:   "driver_id",
			Value: query.DriverId,
		})
	}

	if query.TruckId != 0 {
		filter = append(filter, bson.E{
			Key:   "truck.truck_id",
			Value: query.TruckId,
		})
	}

	if query.CreatedTimeFrom > 0 && query.CreatedTimeTo > 0 {
		from := time.Unix(query.CreatedTimeFrom, 0)
		to := time.Unix(query.CreatedTimeTo, 0)
		filter = append(filter, bson.E{
			Key: "created_time",
			Value: bson.M{
				"$gte":    from,
				"$lte":    to,
				"$exists": true,
			}})
	} else if query.CreatedTimeFrom > 0 && query.CreatedTimeTo == 0 {
		from := time.Unix(query.CreatedTimeFrom, 0)
		filter = append(filter, bson.E{
			Key: "created_time",
			Value: bson.M{
				"$gte":    from,
				"$exists": true,
			}})
	} else if query.CreatedTimeFrom == 0 && query.CreatedTimeTo > 0 {
		to := time.Unix(query.CreatedTimeTo, 0)
		filter = append(filter, bson.E{
			Key: "created_time",
			Value: bson.M{
				"$lte":    to,
				"$exists": true,
			}})
	}
	if query.CompletedTimeFrom > 0 && query.CompletedTimeTo > 0 {
		from := time.Unix(query.CompletedTimeFrom, 0)
		to := time.Unix(query.CompletedTimeTo, 0)

		filter = append(filter, bson.E{
			Key: "completed_time",
			Value: bson.M{
				"$gte":    from,
				"$lte":    to,
				"$exists": true,
			}})
	} else if query.CompletedTimeFrom > 0 && query.CompletedTimeTo == 0 {
		from := time.Unix(query.CompletedTimeFrom, 0)
		filter = append(filter, bson.E{
			Key: "completed_time",
			Value: bson.M{
				"$gte":    from,
				"$exists": true,
			}})
	} else if query.CompletedTimeFrom == 0 && query.CompletedTimeTo > 0 {
		to := time.Unix(query.CompletedTimeTo, 0)
		filter = append(filter, bson.E{
			Key: "completed_time",
			Value: bson.M{
				"$lte":    to,
				"$exists": true,
			}})
	}
	statuses := utils.EnumToStringSlice(*enum.TripStatus)
	result := make([]*CountTripModel, len(statuses))
	waitGroup := new(sync.WaitGroup)
	waitGroup.Add(len(statuses))

	for index, status := range statuses {
		go func(i int, s string, f bson.D, r []*CountTripModel, wg *sync.WaitGroup) {
			defer wg.Done()
			// Don't need to deep clone filter because pass variable to function by value means it is already clone the filter
			copyFilter := append(f, bson.E{
				Key:   "status",
				Value: s,
			})

			countResult := model.TripDB.Count(copyFilter)
			r[i] = &CountTripModel{
				Status:   s,
				Quantity: countResult.Total,
			}
		}(index, status, filter, result, waitGroup)
	}

	waitGroup.Wait()

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Thông tin số lượng trip theo trạng thái.",
		Data:    result,
	}
}

func CreateTrip(input *model.Trip) *common.APIResponse {
	if input.FromCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Hub tạo luân chuyển không được để trống",
		}
	}

	if input.DriverID == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Thông tin tài xế không được để trống",
		}
	}

	if input.Truck == nil || input.Truck.TruckId == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Thông tin xe luân chuyển không được để trống",
		}
	}

	if input.Route == nil || input.Route.RouteCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã tuyến xe không được để trống",
		}
	}

	routeRaw := model.RouteDB.QueryOne(bson.M{
		"route_code": input.Route.RouteCode,
	})

	if routeRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Không tìm thấy mã tuyến",
		}
	}

	truckRaw := model.TruckDB.QueryOne(bson.M{
		"truck_id": input.Truck.TruckId,
	})

	if truckRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Không tìm thấy xe luân chuyển",
		}
	}

	accountRaw := model.AccountDB.QueryOne(bson.M{
		"account_id": input.DriverID,
		"role_code":  conf.Config.TransportingDriverRole,
	})

	if accountRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Không tìm thấy tài xế luân chuyển",
		}
	}

	input.Truck = truckRaw.Data.([]*model.Truck)[0]
	input.Driver = accountRaw.Data.([]*model.Account)[0]
	input.Route = routeRaw.Data.([]*model.Route)[0]

	if input.Truck.Status == nil || *input.Truck.Status != enum.TruckStatus.AVAILABLE {

		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Xe đang dùng trong tuyến khác",
		}
	}

	input.TripId = model.GenId("TRIP_ID")
	input.TripCode = "TRIP-" + strconv.FormatInt(input.TripId, 10)
	input.Status = &enum.TripStatus.DRAFT
	createResult := model.TripDB.Create(input)
	if createResult.Status == common.APIStatus.Ok {
		model.TruckDB.UpdateOne(bson.M{
			"truck_id": input.Truck.TruckId,
		}, bson.M{
			"status": enum.TruckStatus.USING,
		})

		model.AccountDB.UpdateOne(bson.M{
			"account_id": input.Driver.AccountID,
			"role_code":  conf.Config.TransportingDriverRole,
		}, bson.M{
			"is_on_trip": true,
		})

	}
	return createResult
}

func AddItemToTrip(input *request.AddItemToTripRequest) *common.APIResponse {
	tripRaw := model.TripDB.QueryOne(bson.M{
		"trip_id": input.TripId,
	})

	if tripRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Không tìm thấy trip",
		}
	}
	trip := tripRaw.Data.([]*model.Trip)[0]

	var refCode []string
	var referenceCode string
	var itemType *enum.HandoverItemTypeValue

	if len(input.ReferenceCodes) > 0 {
		refCode = input.ReferenceCodes
		itemType = &enum.HandoverItemType.BIN
	} else {
		itemType, referenceCode = GetHandoverItemType(input.ReferenceCode)
		input.ReferenceCode = referenceCode
		refCode = []string{input.ReferenceCode}
	}

	if itemType == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã không hợp lệ",
		}
	}

	var addToTicketId int64 = 0
	// Nếu đã có ticket thì add item vào ticket đó
	for _, ticket := range trip.HandoverTickets {
		if input.HanoverTicketId == 0 &&
			ticket.ToDepartmentCode == input.ToCode &&
			ticket.FromDepartmentCode == input.FromCode &&
			ticket.ItemType != nil &&
			*ticket.ItemType == *itemType {
			addToTicketId = int64(ticket.TicketID)
		}
		if input.HanoverTicketId == int64(ticket.TicketID) {
			if ticket.ItemType == nil || *ticket.ItemType != *itemType {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Loại hàng không hợp lệ",
				}
			}
			addToTicketId = input.HanoverTicketId
		}
	}
	if addToTicketId == 0 && input.HanoverTicketId != 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không tìm thấy phiếu luân chuyển trong trip",
		}
	}

	if addToTicketId != 0 {
		return AddHandoverItem(&request.HandoverItemRequest{
			TicketID:           addToTicketId,
			ItemType:           itemType,
			Skus:               refCode,
			ReferenceCodes:     refCode,
			ReceiveSessionCode: input.ReferenceCode,
			ScannedQuantity:    input.ScannedQuantity,
			NumPackage:         input.NumPackage,
			CheckInCode:        input.ReferenceCode,
			SO:                 input.SO,
		}, input.AccountID)
	}

	isExistFrom := false
	ifExistTo := false
	for _, point := range trip.Route.DropOffPoints {
		if point.Code == input.FromCode {
			isExistFrom = true
			continue
		}

		if point.Code == input.ToCode {
			ifExistTo = true
			continue
		}
	}

	hubsRaw := model.HubDB.Query(bson.M{
		"code": bson.M{
			"$in": []string{input.FromCode, input.ToCode},
		},
	}, 0, 2, nil)

	if hubsRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Điểm giao hoặc nhận hàng không hợp lệ",
		}
	}

	hubs := hubsRaw.Data.([]*model.Hub)
	if len(hubs) != 2 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Điểm giao hoặc nhận hàng không hợp lệ",
		}
	}

	for _, hub := range hubs {
		if hub.Code == input.FromCode {
			input.FromName = hub.Name
			input.FromAddress = hub.Address
		}
		if hub.Code == input.ToCode {
			input.ToName = hub.Name
			input.ToAddress = hub.Address
		}
	}

	if !isExistFrom || !ifExistTo {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Điểm giao hoặc nhận hàng không hợp lệ",
		}
	}

	// Nếu chưa có phiếu transport thì tạo mới
	newTransportTicketRaw := CreateHandoverTicket(&request.CreateHandoverRequest{
		DeliveryTruck: &model.DeliveryTruck{
			OwnerName:    trip.Driver.Username,
			Phone:        "Không xác định",
			IdCard:       "Không xác định",
			LicensePlate: trip.Truck.LicensePlate,
			DriverID:     trip.DriverID,
		},
		HandoverTicket: model.HandoverTicket{
			HandoverType:       &enum.HandoverType.TRANSPORTING,
			FromDepartmentCode: input.FromCode,
			FromDepartmentName: input.FromName,
			ToDepartmentCode:   input.ToCode,
			ToDepartmentName:   input.ToName,
			ItemType:           itemType,
			TripId:             trip.TripId,
			TripCode:           trip.TripCode,
			RouteCode:          trip.Route.RouteCode,
			RouteName:          trip.Route.RouteName,
			TruckID:            trip.Truck.TruckId,
			LicensePlate:       trip.Truck.LicensePlate,
			DriverID:           trip.DriverID,
			DriverName:         trip.Driver.Fullname,
			FromAddress:        input.FromAddress,
			ToAddress:          input.ToAddress,
		},
	}, input.AccountID)
	if newTransportTicketRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: newTransportTicketRaw.Message,
		}
	}
	newTransportTicket := newTransportTicketRaw.Data.([]*model.HandoverTicket)[0]
	addItemResp := AddHandoverItem(&request.HandoverItemRequest{
		TicketID:           int64(newTransportTicket.TicketID),
		ItemType:           itemType,
		Skus:               refCode,
		ReferenceCodes:     refCode,
		ReceiveSessionCode: input.ReferenceCode,
		ScannedQuantity:    input.ScannedQuantity,
		NumPackage:         input.NumPackage,
		CheckInCode:        input.ReferenceCode,
		SO:                 input.SO,
	}, input.AccountID)
	if addItemResp.Status != common.APIStatus.Ok {
		model.HandoverTicketDB.Delete(bson.M{"ticket_id": newTransportTicket.TicketID})
		return addItemResp
	}

	// Chỉ cần  giữ bản copy một phần của ticket ở trip chứ không lưu toàn bộ data của ticket vào trip
	trip.HandoverTickets = append(trip.HandoverTickets, &model.HandoverTicket{
		TicketID:           newTransportTicket.TicketID,
		ToDepartmentCode:   input.ToCode,
		FromDepartmentCode: input.FromCode,
		ItemType:           itemType,
		Status:             &enum.HandoverStatus.DRAFT,
	})

	updateResult := model.TripDB.UpdateOne(bson.M{
		"trip_id": input.TripId,
	}, bson.M{
		"handover_tickets": trip.HandoverTickets,
	})

	if updateResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không thể cập nhật trip",
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Thêm item thành công",
		Data:    addItemResp.Data,
	}
}

func DoneCreateTrip(tripId int64, accountID int64) *common.APIResponse {
	tripRaw := model.TripDB.QueryOne(bson.M{
		"trip_id": tripId,
	})
	if tripRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Không tìm thấy trip",
		}
	}
	trip := tripRaw.Data.([]*model.Trip)[0]
	if trip.Status == nil ||
		(*trip.Status == enum.TripStatus.CANCELED || *trip.Status == enum.TripStatus.COMPLETE || len(trip.HandoverTickets) == 0) {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Trạng thái trip không hợp lệ",
		}
	}

	isAllTicketDone := true
	for _, ticket := range trip.HandoverTickets {
		if *ticket.Status == enum.HandoverStatus.DRAFT {
			isAllTicketDone = false
			break
		}
	}
	if isAllTicketDone {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Tất cả phiếu luân chuyển trong tuyến đã hoàn thành bàn giao",
		}
	}

	var tripTickets []*model.HandoverTicket
	for _, ticket := range trip.HandoverTickets {
		if *ticket.Status != enum.HandoverStatus.DRAFT {
			tripTickets = append(tripTickets, ticket)
			continue
		}

		ticketRaw := model.HandoverTicketDB.QueryOne(bson.M{
			"ticket_id": ticket.TicketID,
		})

		if ticketRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.NotFound,
				Message: "Không tìm thấy phiếu luân chuyển trong trip",
			}
		}

		ticketResult := ticketRaw.Data.([]*model.HandoverTicket)[0]
		if ticketResult.Status == nil ||
			*ticketResult.Status != enum.HandoverStatus.DRAFT {
			tripTickets = append(tripTickets,
				&model.HandoverTicket{
					TicketID:           ticketResult.TicketID,
					ToDepartmentCode:   ticketResult.ToDepartmentCode,
					FromDepartmentCode: ticketResult.FromDepartmentCode,
					ItemType:           ticketResult.ItemType,
					Status:             ticketResult.Status,
				})
			continue
		}
		isExistsPackage := false
		for _, SO := range ticketResult.SOList {
			if *SO.Status == enum.HandoverItemStatus.CANCEL {
				continue
			}
			if SO.NumPackage > SO.ScannedQuantity && *ticket.ItemType != enum.HandoverItemType.PO && *ticket.ItemType != enum.HandoverItemType.FMPO {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: fmt.Sprintf("Chưa quét hết kiện trong phiếu %v", ticket.TicketID),
				}
			}
			isExistsPackage = true
		}

		if isExistsPackage {
			ticket.Status = &enum.HandoverStatus.TRANSPORTING
			var resDoneTicket *common.APIResponse
			if *ticket.ItemType == enum.HandoverItemType.PO || *ticket.ItemType == enum.HandoverItemType.FMPO {
				resDoneTicket = DoneCreateHanoverTicket(&model.HandoverTicket{
					TicketID:     ticket.TicketID,
					Status:       &enum.HandoverStatus.TRANSPORTING,
					HandoverType: &enum.HandoverType.TRANSPORTING,
				}, accountID)
			} else {
				resDoneTicket = DoneHandoverTicket(&model.HandoverTicket{
					TicketID:     ticket.TicketID,
					Status:       &enum.HandoverStatus.TRANSPORTING,
					HandoverType: &enum.HandoverType.TRANSPORTING,
				}, accountID)
			}
			if resDoneTicket.Status != common.APIStatus.Ok {
				return resDoneTicket
			}

			tripTickets = append(tripTickets, ticket)

		} else {
			ticketUpdated := model.HandoverTicketDB.UpdateOne(bson.M{
				"ticket_id": ticket.TicketID,
			}, bson.M{
				"status": &enum.HandoverStatus.CANCEL,
			})
			if ticketUpdated.Status != common.APIStatus.Ok {
				return ticketUpdated
			}
		}
	}
	if len(tripTickets) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không có phiếu luân chuyển nào có đơn.",
		}
	}
	trip.HandoverTickets = tripTickets

	updatedTrip := model.TripDB.UpdateOne(bson.M{"trip_id": tripId}, bson.M{
		"status":           &enum.TripStatus.TRANSPORTING,
		"handover_tickets": trip.HandoverTickets,
	})

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Hoàn thành trip thành công",
		Data:    updatedTrip.Data,
	}
}

func UpdateCurrentAddress(tripId int64, address model.Address) *common.APIResponse {
	tripRaw := model.TripDB.QueryOne(bson.M{
		"trip_id": tripId,
	})
	if tripRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Không tìm thấy trip",
		}
	}
	result := model.TripDB.UpdateOne(bson.M{
		"trip_id": tripId,
	}, bson.M{
		"current_address": address,
	})

	return result
}

func ResetCreateTrip(tripId int64) *common.APIResponse {
	tripRaw := model.TripDB.QueryOne(bson.M{
		"trip_id": tripId,
	})
	if tripRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Không tìm thấy trip",
		}
	}

	trip := tripRaw.Data.([]*model.Trip)[0]
	if trip.Status == nil || *trip.Status != enum.TripStatus.DRAFT {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Trip không ở trạng thái nháp",
		}
	}

	var ticketSlice []int
	for _, ticket := range trip.HandoverTickets {
		ticketSlice = append(ticketSlice, ticket.TicketID)
	}

	if len(ticketSlice) > 0 {
		result := model.HandoverTicketDB.UpdateMany(bson.M{
			"ticket_id": bson.M{
				"$in": ticketSlice,
			},
		}, bson.M{
			"status": &enum.HandoverStatus.CANCEL,
		})

		if result.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không thể hủy phiếu luân chuyển",
			}
		}
	}

	result := model.TruckDB.UpdateOne(bson.M{
		"truck_id": trip.Truck.TruckId,
	}, bson.M{
		"status": &enum.TruckStatus.AVAILABLE,
	})

	updateAccountResp := model.AccountDB.UpdateOne(bson.M{
		"account_id": trip.DriverID,
		"role_code":  conf.Config.TransportingDriverRole,
	}, bson.M{
		"is_on_trip": false,
	})
	if updateAccountResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Không thể cập nhật tình trạng của tài xế",
		}
	}

	if result.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không thể cập nhật trạng thái xe",
		}
	}

	updateTripResult := model.TripDB.UpdateOne(bson.M{"trip_id": tripId}, bson.M{
		"status": &enum.TripStatus.CANCELED,
	})

	if updateTripResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không thể cập nhật trạng thái trip",
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Hủy trip thành công",
	}

}

func GetHandoverItemTypeByRefCode(code string) *enum.HandoverItemTypeValue {
	if strings.HasPrefix(code, "SO") ||
		strings.HasPrefix(code, "LO") ||
		strings.HasPrefix(code, "CO") ||
		strings.HasPrefix(code, "RO") {
		shippingOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
			"reference_code": code,
		})

		if shippingOrderRaw.Status != common.APIStatus.Ok {
			return nil
		}

		shippingOrder := shippingOrderRaw.Data.([]*model.ShippingOrder)[0]
		if (shippingOrder.ShippingType != nil &&
			*shippingOrder.ShippingType == enum.ShippingOrderType.RETURN) ||
			(shippingOrder.Status != nil &&
				(*shippingOrder.Status == enum.TPLCallbackStatus.RETURN ||
					*shippingOrder.Status == enum.TPLCallbackStatus.RETURNING ||
					*shippingOrder.Status == enum.TPLCallbackStatus.RETURN_FAIL ||
					*shippingOrder.Status == enum.TPLCallbackStatus.RETURNED)) {
			return &enum.HandoverItemType.RO
		}
		return &enum.HandoverItemType.SO
	}

	if strings.HasPrefix(code, "BIN") {
		return &enum.HandoverItemType.BIN
	}

	if (strings.HasPrefix(code, "PO") || strings.HasPrefix(code, "PGH")) &&
		strings.Contains(code, "-") {
		return &enum.HandoverItemType.PO
	}

	if strings.HasPrefix(code, "TO") {
		return &enum.HandoverItemType.TO
	}

	if strings.HasPrefix(code, "BMX") || strings.HasPrefix(code, "DH") {
		return &enum.HandoverItemType.EO
	}

	return nil
}

func GetHandoverItemType(code string) (*enum.HandoverItemTypeValue, string) {

	// assuming that input is a reference code
	itemType := GetHandoverItemTypeByRefCode(code)

	if itemType != nil {
		return itemType, code
	}

	// assuming that input is a tracking code
	if isValid, _ := regexp.Match("^([^50IOZ\\W]+)$", []byte(code)); !isValid {
		return nil, ""
	}

	shippingOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
		"tracking_code": code,
	})

	if shippingOrderRaw.Status != common.APIStatus.Ok {
		return nil, ""
	}

	shippingOrder := shippingOrderRaw.Data.([]*model.ShippingOrder)[0]
	itemType = GetHandoverItemTypeByRefCode(shippingOrder.ReferenceCode)
	return itemType, shippingOrder.ReferenceCode
}

func CountTripsPerDriverID(driverIDs []int64) *common.APIResponse {
	pipeline := []bson.M{
		{"$match": bson.M{
			"driver_id": bson.M{"$in": driverIDs},
			"type":      enum.TripType.TRANSPORT_TRIP,
		}},
		{"$group": bson.M{"_id": "$driver_id", "trip_amount": bson.M{"$sum": 1}}},
	}
	type TripsPerDriver struct {
		DriverID    int64 `bson:"_id,omitempty" json:"driverID,omitempty"`
		TripsAmount int64 `bson:"trip_amount,omitempty" json:"tripsAmount"`
	}
	var result []TripsPerDriver
	tripsPerDriverIDMap := model.TripDB.Aggregate(pipeline, &result)
	if tripsPerDriverIDMap.Status != common.APIStatus.Ok || len(result) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: fmt.Sprintf("không tìm thấy trips với driver_ids: %v", driverIDs),
		}
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Successfully",
		Data:    result,
	}
}

func CountPackages(tripIDs []int64) *common.APIResponse {
	var pipeline = []bson.M{
		{"$match": bson.M{"trip_id": bson.M{"$in": tripIDs}}},
		{"$unwind": bson.M{"path": "$so_list"}},
		{"$match": bson.M{"so_list.status": bson.M{"$ne": "CANCEL"}}},
		{"$group": bson.M{"_id": "$trip_id",
			"num_package":    bson.M{"$sum": "$so_list.num_package"},
			"scaned_package": bson.M{"$sum": "$so_list.scanned_quantity"},
			"weight":         bson.M{"$sum": "$so_list.weight"},
			"so_list":        bson.M{"$push": "$so_list"},
		}},
		{"$project": bson.M{
			"_id":            1,
			"num_package":    1,
			"scaned_package": 1,
			"weight":         1,
			"num_item":       bson.M{"$size": "$so_list"},
		}},
	}
	type Output struct {
		TripID        int64   `bson:"_id,omitempty" json:"tripID"`
		NumPackage    int64   `bson:"num_package,omitempty" json:"numPackage"`
		ScanedPackage int64   `bson:"scaned_package,omitempty" json:"scanedPackage"`
		Weight        float64 `bson:"weight,omitempty" json:"weight"`
		NumItem       int64   `bson:"num_item,omitempty" json:"numItem"`
	}
	var result []Output
	output := model.HandoverTicketDB.Aggregate(pipeline, &result)
	if output.Status != common.APIStatus.Ok || len(result) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: fmt.Sprintf("không tìm thấy trip với trip_ids: %v", tripIDs),
		}
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Successfully",
		Data:    result,
	}
}

func CompleteTrip(tripId int64) *common.APIResponse {
	tripRaw := model.TripDB.QueryOne(bson.M{
		"trip_id": tripId,
	})
	if tripRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Không tìm thấy thông tin chuyến đi",
		}
	}
	trip := tripRaw.Data.([]*model.Trip)[0]
	listTicketID := []int{}
	for _, item := range trip.HandoverTickets {
		listTicketID = append(listTicketID, item.TicketID)
	}

	handoverTicketsIds := make([]int, len(trip.HandoverTickets))
	for i, ticket := range trip.HandoverTickets {
		handoverTicketsIds[i] = ticket.TicketID
	}

	existsNotCheckinTicketRaw := model.HandoverTicketDB.QueryOne(bson.M{
		"ticket_id": bson.M{
			"$in": handoverTicketsIds,
		},
		"status": bson.M{
			"$nin": []enum.HandoverStatusValue{
				enum.HandoverStatus.COMPLETED,
				enum.HandoverStatus.CANCEL,
				enum.HandoverStatus.WAIT_TO_CHECK,
				enum.HandoverStatus.CHECKING,
			},
		},
	})

	if existsNotCheckinTicketRaw.Status == common.APIStatus.Ok {
		existedTicket := existsNotCheckinTicketRaw.Data.([]*model.HandoverTicket)[0]
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "INVALID_HANDOVER_TICKET_STATUS",
			Message: fmt.Sprintf("Không thể hoàn thành trip do phiếu luân chuyển: %s đang ở trạng thái %s",
				existedTicket.Code, *existedTicket.Status),
		}
	}

	updateTripResp := model.TripDB.UpdateOne(bson.M{
		"trip_id": tripId,
	}, bson.M{
		"handover_tickets": trip.HandoverTickets,
		"status":           &enum.TripStatus.COMPLETE,
		"completed_time":   utils.Now(),
	})
	if updateTripResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: fmt.Sprintf("Không thể cập nhật trip:%d - Error: %s -", tripId, updateTripResp.Message),
		}
	}

	updateTruckResp := model.TruckDB.UpdateOne(bson.M{
		"truck_id": trip.Truck.TruckId,
	}, bson.M{
		"status": &enum.TruckStatus.AVAILABLE,
	})
	if updateTruckResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: fmt.Sprintf(" Không tìm thấy trip với id: %d -", tripId),
		}
	}

	updateAccountResp := model.AccountDB.UpdateOne(bson.M{
		"account_id": trip.DriverID,
		"role_code":  conf.Config.TransportingDriverRole,
	}, bson.M{
		"is_on_trip": false,
	})
	if updateAccountResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Không thể cập nhật tình trạng của tài xế",
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Hoản thành trip thành công",
	}
}

func CreateDraftTicketInTrip(req request.CreateDraftTicketInTripRequest) *common.APIResponse {
	if req.TripId == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Id tuyến không được để trống",
		}
	}

	if req.FromCode == "" || req.ToCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã điểm giao hoặc nhận hàng không được để trống",
		}
	}

	if req.CarrierId == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã nhà vận chuyển không được để trống",
		}
	}

	if req.ItemType == nil || !utils.CheckExistInEnum(*req.ItemType, *enum.HandoverItemType) {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Loại hàng không hợp lệ",
		}
	}

	tripResp := model.TripDB.QueryOne(bson.M{
		"trip_id": req.TripId,
	})

	if tripResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Không tìm thấy tuyến",
		}
	}

	trip := tripResp.Data.([]*model.Trip)[0]
	isExistFrom := false
	ifExistTo := false
	for _, point := range trip.Route.DropOffPoints {
		if point.Code == req.FromCode {
			isExistFrom = true
			continue
		}

		if point.Code == req.ToCode {
			ifExistTo = true
			continue
		}
	}
	if !isExistFrom || !ifExistTo {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Điểm giao hoặc nhận hàng không hợp lệ",
		}
	}

	hubsRaw := model.HubDB.Query(bson.M{
		"code": bson.M{
			"$in": []string{req.FromCode, req.ToCode},
		},
	}, 0, 2, nil)

	if hubsRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Điểm giao hoặc nhận hàng không hợp lệ",
		}
	}

	hubs := hubsRaw.Data.([]*model.Hub)
	if len(hubs) != 2 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Điểm giao hoặc nhận hàng không hợp lệ",
		}
	}

	for _, hub := range hubs {
		if hub.Code == req.FromCode {
			req.FromName = hub.Name
			req.FromAddress = hub.Address
		}
		if hub.Code == req.ToCode {
			req.ToName = hub.Name
			req.ToAddress = hub.Address
		}
	}

	carrierResp := model.CarrierDB.QueryOne(bson.M{
		"carrier_id": req.CarrierId,
	})
	if carrierResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã nhà vận chuyển không hợp lệ",
		}
	}

	carrier := carrierResp.Data.([]*model.Carrier)[0]

	newTransportTicketRaw := CreateHandoverTicket(&request.CreateHandoverRequest{
		DeliveryTruck: &model.DeliveryTruck{
			OwnerName:    trip.Driver.Username,
			Phone:        "Không xác định",
			IdCard:       "Không xác định",
			LicensePlate: trip.Truck.LicensePlate,
			DriverID:     trip.DriverID,
		},
		HandoverTicket: model.HandoverTicket{
			HandoverType:       &enum.HandoverType.TRANSPORTING,
			FromDepartmentCode: req.FromCode,
			FromDepartmentName: req.FromName,
			ToDepartmentCode:   req.ToCode,
			ToDepartmentName:   req.ToName,
			ItemType:           req.ItemType,
			TripId:             trip.TripId,
			TripCode:           trip.TripCode,
			RouteCode:          trip.Route.RouteCode,
			RouteName:          trip.Route.RouteName,
			TruckID:            trip.Truck.TruckId,
			LicensePlate:       trip.Truck.LicensePlate,
			DriverID:           trip.DriverID,
			DriverName:         trip.Driver.Fullname,
			FromAddress:        req.FromAddress,
			ToAddress:          req.ToAddress,
			CarrierId:          carrier.CarrierId,
			CarrierName:        carrier.CarrierName,
		},
	}, req.CreatedBy)
	if newTransportTicketRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: newTransportTicketRaw.Message,
		}
	}
	newTransportTicket := newTransportTicketRaw.Data.([]*model.HandoverTicket)[0]

	// Chỉ cần  giữ bản copy một phần của ticket ở trip chứ không lưu toàn bộ data của ticket vào trip
	trip.HandoverTickets = append(trip.HandoverTickets, &model.HandoverTicket{
		TicketID:           newTransportTicket.TicketID,
		ToDepartmentCode:   req.ToCode,
		FromDepartmentCode: req.FromCode,
		ItemType:           req.ItemType,
		Status:             &enum.HandoverStatus.DRAFT,
	})
	afterOption := options.After

	updateResult := model.TripDB.UpdateOne(bson.M{
		"trip_id": req.TripId,
	}, bson.M{
		"handover_tickets": trip.HandoverTickets,
	}, &options.FindOneAndUpdateOptions{
		ReturnDocument: &afterOption,
	})

	if updateResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không thể cập nhật trip",
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Tạo phiếu thành công",
		Data: []response.CreateDraftTicketInTripResponse{
			{
				Trip:            updateResult.Data.([]*model.Trip)[0],
				TransportTicket: newTransportTicket,
			},
		},
	}

}

func RemoveTicketInTrip(req request.RemoveTicketFromTripRequest) *common.APIResponse {
	if req.TripId == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Id tuyến không được để trống",
		}
	}
	if req.TicketId == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Id phiếu không được để trống",
		}
	}

	tripResp := model.TripDB.QueryOne(bson.M{
		"trip_id": req.TripId,
	})
	if tripResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Không tìm thấy tuyến",
		}
	}
	trip := tripResp.Data.([]*model.Trip)[0]
	if trip.Status != nil &&
		(*trip.Status == enum.TripStatus.COMPLETE ||
			*trip.Status == enum.TripStatus.CANCELED) {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Trạng thái trip không hợp lệ.",
		}
	}
	ticketResp := model.HandoverTicketDB.QueryOne(bson.M{
		"ticket_id": req.TicketId,
	})
	if ticketResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Không tìm thấy phiếu",
		}
	}
	ticket := ticketResp.Data.([]*model.HandoverTicket)[0]
	if ticket.Status == nil ||
		*ticket.Status != enum.HandoverStatus.DRAFT {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Trạng thái phiếu không hợp lệ.",
		}
	}

	found := false
	for i, item := range trip.HandoverTickets {
		if item.TicketID == int(req.TicketId) {
			found = true
			trip.HandoverTickets = append(trip.HandoverTickets[:i], trip.HandoverTickets[i+1:]...)
			break
		}
	}
	if !found {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Không tìm thấy phiếu trong tuyến",
		}
	}
	afterOption := options.After
	updateTripResp := model.TripDB.UpdateOne(bson.M{
		"trip_id": req.TripId,
	}, bson.M{
		"handover_tickets": trip.HandoverTickets,
	}, &options.FindOneAndUpdateOptions{
		ReturnDocument: &afterOption,
	})
	if updateTripResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Không thể cập nhật tuyến",
		}
	}

	transportTicketResp := model.HandoverTicketDB.UpdateOne(bson.M{
		"ticket_id": req.TicketId,
	}, bson.M{
		"status": &enum.HandoverStatus.CANCEL,
	})

	if transportTicketResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Không thể cập nhật phiếu",
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Xóa phiếu thành công",
		Data:    updateTripResp.Data,
	}
}

func MigrateTruckIdInTrip() *common.APIResponse {
	var objectId primitive.ObjectID
	filter := bson.M{
		"type": enum.TripType.TRANSPORT_TRIP,
	}
	limit := 100

	truckLicenseToTruckId := make(map[string]int64)

	getTruckResp := model.TruckDB.Query(nil, 0, 200, nil)
	if getTruckResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không thể lấy thông tin xe",
		}
	}

	trucks := getTruckResp.Data.([]*model.Truck)
	for _, truck := range trucks {
		truckLicenseToTruckId[truck.LicensePlate] = truck.TruckId
	}

	failedMigrate := []string{}

	for {
		if !objectId.IsZero() {
			filter["_id"] = bson.M{
				"$gt": objectId,
			}
		}

		getTripResp := model.TripDB.Query(filter, 0, int64(limit), &bson.M{"_id": 1})
		if getTripResp.Status != common.APIStatus.Ok {
			break
		}
		trips := getTripResp.Data.([]*model.Trip)
		if len(trips) == 0 {
			break
		}
		objectId = trips[len(trips)-1].ID

		for _, trip := range trips {
			truckId, ok := truckLicenseToTruckId[trip.Truck.LicensePlate]
			if !ok {
				failedMigrate = append(failedMigrate, trip.TripCode)
				continue
			}

			model.TripDB.UpdateOne(bson.M{
				"trip_id": trip.TripId,
			}, bson.M{
				"truck.truck_id": truckId,
			})
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Successfully",
		Data:    failedMigrate,
	}
}
