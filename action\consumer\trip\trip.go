package trip

import (
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/utils"
	"go.mongodb.org/mongo-driver/bson"
)

func (j *ExecutorJob) completeHandoverTrip() {
	j.Job.SetTopicConsumer(defaultTopic, func(item *job.JobItem) (err error) {
		var dataByte []byte
		dataByte, err = bson.Marshal(item.Data)
		if err != nil {
			return
		}

		completeRequest := CompleteHandoverTripRequest{}
		err = bson.Unmarshal(dataByte, &completeRequest)
		if err != nil {
			return
		}

		tripRaw := model.TripDB.QueryOne(bson.M{
			"trip_id": completeRequest.TripId,
		})

		if tripRaw.Status != common.APIStatus.Ok {
			err = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
				CmdType: "NOT_FOUND_TRIP",
				Title:   "NOT FOUND TRIP TO COMPLETE HANDOVER",
				Message: fmt.Sprintf(" Không tìm thấy trip với id: %d -", completeRequest.TripId),
			})
			return nil
		}

		trip := tripRaw.Data.([]*model.Trip)[0]
		totalCompletedTicket := 0
		for _, ticket := range trip.HandoverTickets {
			for _, completeTicket := range completeRequest.Tickets {
				if completeTicket == ticket.TicketID {
					ticket.Status = &enum.HandoverStatus.COMPLETED
				}
			}
			if *ticket.Status == enum.HandoverStatus.CANCEL || *ticket.Status == enum.HandoverStatus.WAIT_TO_CHECK || *ticket.Status == enum.HandoverStatus.CHECKING || *ticket.Status == enum.HandoverStatus.COMPLETED {
				totalCompletedTicket++
			}
		}

		updater := bson.M{
			"handover_tickets": trip.HandoverTickets,
		}

		if trip.Status != nil && *trip.Status != enum.TripStatus.COMPLETE && len(trip.HandoverTickets) == totalCompletedTicket {
			updater["completed_time"] = utils.Now()
			updater["status"] = &enum.TripStatus.COMPLETE
			updateTruckResp := model.TruckDB.UpdateOne(bson.M{
				"truck_id": trip.Truck.TruckId,
			}, bson.M{
				"status": &enum.TruckStatus.AVAILABLE,
			})
			if updateTruckResp.Status != common.APIStatus.Ok {
				err = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "FREE_TRUCK_ERROR",
					Title:   "CANT FREE TRUCK WHEN COMPLETE TRIP",
					Message: fmt.Sprintf(" Không tìm thấy trip với id: %d -", completeRequest.TripId),
				})
			}
			updateAccountResp := model.AccountDB.UpdateOne(bson.M{
				"account_id": trip.DriverID,
				"role_code":  conf.Config.TransportingDriverRole,
			}, bson.M{
				"is_on_trip": false,
			})
			if updateAccountResp.Status != common.APIStatus.Ok {
				err = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "FREE_DRIVER_ERROR",
					Title:   "CANT FREE DRIVER WHEN COMPLETE TRIP",
					Message: fmt.Sprintf(" Không tìm thấy trip với id: %d -", completeRequest.TripId),
				})
			}
		}

		updateTripResp := model.TripDB.UpdateOne(bson.M{"trip_id": completeRequest.TripId}, updater)
		if updateTripResp.Status != common.APIStatus.Ok {
			err = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
				CmdType: "UPDATE_TRIP_ERROR",
				Title:   "CANT UPDATE COMPLETE HANDOVER TRIP",
				Message: fmt.Sprintf("Khong the cap nhat trip:%d - Error: %s -", completeRequest.TripId, updateTripResp.Message),
			})
		}
		return nil
	})
}

func (j *ExecutorJob) checkAutoCompleteTrip() {
	j.Job.SetTopicConsumer(autoCompleteTripTopic, func(item *job.JobItem) (err error) {
		var dataByte []byte
		dataByte, err = bson.Marshal(item.Data)
		if err != nil {
			return
		}

		completeRequest := CompleteHandoverTripRequest{}
		err = bson.Unmarshal(dataByte, &completeRequest)
		if err != nil {
			return
		}

		tripRaw := model.TripDB.QueryOne(bson.M{
			"trip_id": completeRequest.TripId,
		})

		if tripRaw.Status != common.APIStatus.Ok {
			err = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
				CmdType: "NOT_FOUND_TRIP",
				Title:   "NOT FOUND TRIP TO COMPLETE HANDOVER",
				Message: fmt.Sprintf(" Không tìm thấy trip với id: %d -", completeRequest.TripId),
			})
			return nil
		}

		trip := tripRaw.Data.([]*model.Trip)[0]
		if trip.Status != nil && *trip.Status == enum.TripStatus.COMPLETE {
			return nil
		}

		handoverTicketsIds := make([]int, len(trip.HandoverTickets))
		for i, ticket := range trip.HandoverTickets {
			handoverTicketsIds[i] = ticket.TicketID
		}

		existsNotCheckinTicketRaw := model.HandoverTicketDB.QueryOne(bson.M{
			"ticket_id": bson.M{
				"$in": handoverTicketsIds,
			},
			"status": bson.M{
				"$nin": []enum.HandoverStatusValue{
					enum.HandoverStatus.COMPLETED,
					enum.HandoverStatus.CANCEL,
					enum.HandoverStatus.WAIT_TO_CHECK,
					enum.HandoverStatus.CHECKING,
				},
			},
		})

		if existsNotCheckinTicketRaw.Status == common.APIStatus.Ok {
			return nil
		}

		if trip.Truck == nil {
			return nil
		}

		if trip.DriverID == 0 {
			return nil
		}

		model.TripDB.UpdateOne(bson.M{
			"trip_id": trip.TripId,
		}, bson.M{
			"status":         &enum.TripStatus.COMPLETE,
			"completed_time": utils.Now(),
		})

		model.TruckDB.UpdateOne(bson.M{
			"truck_id": trip.Truck.TruckId,
		}, bson.M{
			"status": &enum.TruckStatus.AVAILABLE,
		})

		model.AccountDB.UpdateOne(bson.M{
			"account_id": trip.DriverID,
			"role_code":  conf.Config.TransportingDriverRole,
		}, bson.M{
			"is_on_trip": false,
		})

		return nil
	})
}
