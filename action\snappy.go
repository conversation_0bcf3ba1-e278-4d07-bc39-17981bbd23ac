package action

import (
	"fmt"

	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/shipping"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
)

type snappy struct{}

var Snappy snappy

func (snappy) BookShipping(input *request.BookShipping, saleOrder *model.SaleOrder, carrierModel *model.Carrier) (result *model.ShippingInfo, err error) {
	if shipping.TplShippingClient.SnappyClient == nil {
		err = fmt.Errorf("Dịch vụ book SNAPPY chưa được khởi tạo")
		return
	}

	// booking snappy
	requestBookingSnappy := request.BookSnappy{
		PickupNote:          input.PickupNote,
		DeliveryNote:        input.DeliveryNote,
		ReceiverName:        saleOrder.CustomerInfos.Delivery.BusinessName,
		ReceiverPhoneNumber: saleOrder.CustomerInfos.Delivery.Phone,
		Items:               []*request.SnappyItem{},
		COD:                 int64(saleOrder.CODAmount),
		ServiceName:         string(enum.SnappyService.EXPRESS),
		CustomerTrackingId:  input.SO,
		BusinessId:          carrierModel.ExtraData.PartnerId,
		BusinessAddressId:   carrierModel.ExtraData.BusinessAddressId,
	}

	if saleOrder.PaymentMethod != nil && *saleOrder.PaymentMethod == enum.PaymentMethod.BANK {
		requestBookingSnappy.COD = 0
	}

	for i := 0; i < int(input.NbOfPackages); i++ {
		requestBookingSnappy.Items = append(requestBookingSnappy.Items, &request.SnappyItem{
			Name:     "Thuốc",
			Quantity: 1,
		})
	}

	if input.NbOfPackages > 1 {
		requestBookingSnappy.IsSplitPkg = true
	}

	if input.DeliveryNote == "" {
		requestBookingSnappy.DeliveryNote = "Thuốc"
	}

	requestBookingSnappy.ReceiverFullAddress = saleOrder.CustomerInfos.Delivery.Address + ", " + saleOrder.CustomerInfos.Delivery.Ward + ", " + saleOrder.CustomerInfos.Delivery.District + ", " + saleOrder.CustomerInfos.Delivery.Province
	if conf.Config.Env != "prd" {
		requestBookingSnappy.ReceiverFullAddress = "[Test] " + requestBookingSnappy.ReceiverFullAddress
	}

	if *saleOrder.PaymentMethod == enum.PaymentMethod.COD {
		requestBookingSnappy.COD = int64(saleOrder.CODAmount)
	}

	// Giá trị hàng hóa mặc định không quá 3 củ
	requestBookingSnappy.Value = int64(saleOrder.DeliveryAmount)
	if requestBookingSnappy.Value > int64(carrierModel.InsuranceValue) {
		requestBookingSnappy.Value = int64(carrierModel.InsuranceValue)
	}

	result, err = shipping.TplShippingClient.SnappyClient.CreateTrackingSnappy(requestBookingSnappy, carrierModel)
	if err != nil {
		return
	}

	return
}
