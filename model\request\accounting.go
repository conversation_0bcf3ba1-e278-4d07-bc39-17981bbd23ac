package request

import (
	"time"

	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type CreateReconcileSessionRequest struct {
	CarrierCode   string `json:"carrierCode"`
	ReconcileType string `json:"reconcileType"`
	HubCode       string `json:"hubCode"`
	UserName      string `json:"userName"`
	Fullname      string `json:"fullname"`
	UserId        int    `json:"userId"`
}

type CreateReconcileSessionOrderRequest struct {
	HubCode             string                 `json:"hubCode"`
	UserId              int                    `json:"userId"`
	ReferenceCode       string                 `json:"referenceCode"`
	TrackingCode        string                 `json:"trackingCode"`
	ReconcileType       string                 `json:"reconcileType"`
	CODAmount           float64                `json:"codAmount"`
	FeeAmount           float64                `json:"feeAmount"`
	TplCode             *enum.PartnerValue     `json:"tplCode"`
	PaymentMethod       string                 `json:"paymentMethod"`
	CarrierCode         string                 `json:"carrierCode"`
	DeliveredTime       *time.Time             `json:"deliveredTime"`
	DeliveryAmount      float64                `json:"deliveryAmount"`
	PickedTime          *time.Time             `json:"pickedTime"`
	CustomerCode        string                 `json:"customerCode"`
	CustomerType        enum.CustomerTypeValue `json:"customerType"`
	IsFeeBeenCalculated *bool                  `json:"isFeeBeenCalculated,omitempty"`
	IsBookDropOff       *bool                  `json:"isBookDropOff"`
	OrderValue          *float64               `json:"orderValue"`
	ParentReferenceCode string                 `json:"parentReferenceCode"`
	NumPackage          int64                  `json:"numPackage"`
	OrderType           *enum.SubTypeValue     `json:"orderType,omitempty" bson:"order_type,omitempty"`
	DeliveryFeeAmount   *int64                 `json:"deliveryFeeAmount"`
	IsMerged            *bool                  `json:"isMerged,omitempty" bson:"is_merged,omitempty"`
	CollectAmount       *int64                 `json:"collectionAmount,omitempty" bson:"collection_amount,omitempty"`
}

type ReconcileCodCollectedRequest struct {
	ReferenceCode string `json:"referenceCode"`
	TrackingCode  string `json:"trackingCode"`
	HubCode       string `json:"hubCode"`
	UserId        int64  `json:"userId"`
}

type GetReconcileSessionsRequest struct {
	UserId   int64                       `json:"userId"`
	Statuses []enum.ReconcileStatusValue `json:"statuses"`
}

type GetReconcileSessionOrderRequest struct {
	ReferenceCode string                            `json:"referenceCode"`
	ReconcileCode string                            `json:"reconcileCode"`
	CarrierCode   string                            `json:"carrierCode"`
	OrderID       int64                             `json:"orderID"`
	Status        *enum.ReconcileOrderStatusValue   `json:"status"`
	Statuses      []*enum.ReconcileOrderStatusValue `json:"statuses"`
}

type CreateBillRequest struct {
	OrderId           int64               `json:"orderId"`
	CustomerName      string              `json:"customerName"`
	ExternalOrderCode string              `json:"externalOrderCode"`
	FeeLogistic       *FeeLogisticRequest `json:"feeLogistics"`
	IsAuto            bool                `json:"isAuto"`
}

type FeeLogisticRequest struct {
	LogisticFeeService int64 `json:"logisticsFeeService"`
	CollectionFee      int64 `json:"collectionFee"`
}

type ReconcileSessionOrder struct {
	ID                primitive.ObjectID              `json:"-" bson:"_id,omitempty"`
	VersionNo         string                          `json:"versionNo,omitempty" bson:"version_no,omitempty"`
	CreatedTime       *time.Time                      `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime   *time.Time                      `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	CreatedBy         int64                           `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	UpdatedBy         int64                           `json:"updatedBy,omitempty" bson:"updated_by,omitempty"`
	LineID            int64                           `json:"lineID,omitempty" bson:"line_id,omitempty"`
	ReconcileCode     string                          `json:"reconcileCode,omitempty" bson:"reconcile_code,omitempty"`
	Status            *enum.ReconcileOrderStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	OrderID           int64                           `json:"orderID,omitempty" bson:"order_id,omitempty"`
	ReferenceCode     string                          `json:"referenceCode,omitempty" bson:"reference_code,omitempty"`
	TrackingCode      string                          `json:"trackingCode,omitempty" bson:"tracking_code,omitempty"`
	HubCode           string                          `json:"hubCode,omitempty" bson:"hub_code,omitempty"`
	CODAmount         *int64                          `json:"codAmount,omitempty" bson:"cod_amount,omitempty"`
	CarrierCODAmount  *int64                          `json:"carrierCodAmount,omitempty" bson:"carrier_cod_amount,omitempty"`
	ReceivedCODAmount *int64                          `json:"receivedCODAmount,omitempty" bson:"received_cod_amount,omitempty"`
	PaymentMethod     string                          `json:"paymentMethod,omitempty" bson:"payment_method,omitempty"`
	CarrierCode       string                          `json:"carrierCode,omitempty" bson:"carrier_code,omitempty"`
	Note              string                          `json:"note,omitempty" bson:"note,omitempty"`
	ReconciledTime    *time.Time                      `json:"reconciledTime,omitempty" bson:"reconciled_time,omitempty"`
	DeliveredTime     *time.Time                      `json:"deliveredTime,omitempty" bson:"delivered_time,omitempty"`
	DeliveryAmount    float64                         `json:"deliveryAmount,omitempty" bson:"delivery_amount,omitempty"`
	IsConflictAmount  bool                            `json:"isConflictAmount,omitempty" bson:"is_conflict_amount,omitempty"`
	NumPackage        int64                           `json:"numPackage,omitempty" bson:"num_package,omitempty"`
	// for firstmile order
	DiscountAmount      *int64     `json:"discountAmount,omitempty" bson:"discount_amount,omitempty"`
	DateCalculateDebts  []string   `json:"-" bson:"date_calculate_debts,omitempty"`
	PenaltyFee          *int64     `json:"penaltyFee,omitempty" bson:"penalty_fee,omitempty"`
	PickedTime          *time.Time `json:"pickedTime,omitempty" bson:"picked_time,omitempty"`
	CustomerCode        string     `json:"customerCode,omitempty" bson:"customer_code,omitempty"`
	IsFeeBeenCalculated *bool      `json:"isFeeBeenCalculated,omitempty" bson:"is_fee_been_calculated,omitempty"`
	ReceivedFeeAmount   *int64     `json:"receivedFeeAmount,omitempty" bson:"received_fee_amount,omitempty"`
	ComplexQuery        []*bson.M  `json:"-" bson:"$and,omitempty"`
	IsBookDropOff       *bool      `json:"isBookDropOff,omitempty" bson:"is_book_drop_off,omitempty"`
	OrderValue          *float64   `json:"orderValue,omitempty" bson:"order_value,omitempty"`
	ParentReferenceCode string     `json:"parentReferenceCode,omitempty" bson:"parent_reference_code,omitempty"`
	DeliveryFeeAmount   *int64     `json:"deliveryFeeAmount,omitempty" bson:"delivery_fee_amount,omitempty"`
	TotalReceivedAmount *int64     `json:"totalReceivedAmount,omitempty" bson:"total_received_amount,omitempty"`
	IsMerged            *bool      `json:"isMerged,omitempty" bson:"is_merged,omitempty"`
}
