package model

import "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"

type Signature struct {
	SignatureImage string                  `json:"signatureImage,omitempty" bson:"signature_image,omitempty"`
	SignerName     string                  `json:"signerName,omitempty" bson:"signer_name,omitempty"`
	Type           enum.SignatureTypeValue `json:"type,omitempty" bson:"type,omitempty"`
}
