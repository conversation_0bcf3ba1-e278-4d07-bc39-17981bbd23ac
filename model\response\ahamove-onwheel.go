package response

// Distance optimization Ahamove
type AhamoveRouteOptimizationResponse struct {
	Cluster         [][]int     `json:"cluster,omitempty" bson:"cluster,omitempty"`
	Metrics         [][]float64 `json:"metrics,omitempty" bson:"metrics,omitempty"`
	Performance     Performance `json:"performance,omitempty" bson:"performance,omitempty"`
	ViolatedPoints  []int       `json:"violated_points,omitempty" bson:"violated_points,omitempty"`
	ViolatedCluster []int       `json:"violated_cluster,omitempty" bson:"violated_cluster,omitempty"`
}

// Old struct
type Metric struct {
	Count    int     `json:"count,omitempty" bson:"count,omitempty"`
	Distance float64 `json:"distance" bson:"distance,omitempty"`
}
type Performance struct {
	TotalDistance            float64 `json:"total_distance,omitempty" bson:"total_distance,omitempty"`
	TotalLocations           int     `json:"total_locations,omitempty" bson:"total_locations,omitempty"`
	ToTotalRunningTimeSecond float64 `json:"total_running_time_second,omitempty" bson:"total_running_time_second,omitempty"`
	TotalVehicle             int     `json:"total_vehicle,omitempty" bson:"total_vehicle,omitempty"`
}
