package model

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/mongo"
)

type AhamoveOnWheelCache struct {
	Key         string                    `json:"key,omitempty" bson:"key,omitempty"`
	MappingType string                    `json:"mappingType,omitempty" bson:"mapping_type,omitempty"`
	Clusters    [][]int                   `json:"clusters,omitempty" bson:"clusters,omitempty"`
	Metrics     [][]float64               `json:"metrics,omitempty" bson:"metrics,omitempty"`
	Query       []*MappingSortOptionModel `json:"query,omitempty" bson:"query,omitempty"`
	DriverId    int64                     `json:"driverId,omitempty" bson:"driver_id,omitempty"`
	HubCode     string                    `json:"hubCode,omitempty" bson:"hub_code,omitempty"`
}

// Build model for sort option by distance, zone and caching
type MappingSortOptionModel struct {
	ReferenceCode  interface{} `json:"referenceCode,omitempty" bson:"reference_code,omitempty"` // Unique
	LatitudePoint  float64     `json:"latitudePoint,omitempty" bson:"latitude_point,omitempty"`
	LongitudePoint float64     `json:"LongitudePoint,omitempty" bson:"longitude_point,omitempty"`
	Type           string      `json:"type,omitempty" bson:"type,omitempty"` // [DELIVERY,PICKUP]
	ProvinceName   string      `json:"provinceName,omitempty" bson:"province_name,omitempty"`
	DistrictName   string      `json:"districtName,omitempty" bson:"district_name,omitempty"`
	WardName       string      `json:"wardName,omitempty" bson:"ward_name,omitempty"`
	ProvinceCode   string      `json:"provinceCode,omitempty" bson:"province_code,omitempty"`
	DistrictCode   string      `json:"districtCode,omitempty" bson:"district_code,omitempty"`
	WardCode       string      `json:"wardCode,omitempty" bson:"ward_code,omitempty"`
	CustomerName   string      `json:"customerName,omitempty" bson:"customer_name,omitempty"`
	CustomerCode   string      `json:"customerCode,omitempty" bson:"customer_code,omitempty"`
	Address        string      `json:"address,omitempty" bson:"address,omitempty"`
}

type VRPConstrain struct {
	VehicleNumber    int             `json:"vehicleNumber,omitempty"`
	MaxParcels       int             `json:"maxParcels,omitempty"`
	MinParcels       int             `json:"minParcels,omitempty"`
	MaxDistance      int             `json:"maxDistance,omitempty"`
	PointMaxDistance int             `json:"pointMaxDistance,omitempty"`
	MinVehicles      bool            `json:"minVehicles,omitempty"`
	ResultMode       string          `json:"resultMode,omitempty"` // COORDINATES OR INDEX
	Dimensions       []*VRPDimension `json:"dimensions,omitempty"`
	TransportMode    string          `json:"transportMode,omitempty"`
}

type VRPDimension struct {
	Values      []int  `json:"values,omitempty"`
	Name        string `json:"name,omitempty"`
	MaxCapacity int    `json:"max_capacity,omitempty"`
}

var AhamoveOnWheelCacheDB = &db.Instance{
	ColName:        "ahamove_onwheel_cache",
	TemplateObject: &AhamoveOnWheelCache{},
}

func InitAhamoveOnWheelCacheModel(s *mongo.Database) {
	AhamoveOnWheelCacheDB.ApplyDatabase(s)
	//t := true
	//AhamoveOnWheelCacheDB.CreateIndex(bson.D{
	//	{"key", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})
	//
	//AhamoveOnWheelCacheDB.CreateIndex(bson.D{
	//	{"hub_code", 1},
	//	{"driver_id", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})
}
