package model

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type CustomerCarrier struct {
	ID primitive.ObjectID `json:"-" bson:"_id,omitempty" `

	CustomerId     int64              `json:"customerId" bson:"customer_id,omitempty"`
	CustomerName   string             `json:"customerName" bson:"customer_name,omitempty"`
	IsActive       *bool              `json:"isActive" bson:"is_active,omitempty"`
	Carriers       []*Available       `json:"carriers" bson:"carriers,omitempty"`
	PickUpCarriers []*PickUpAvailable `json:"pickUpCarriers" bson:"pick_up_carriers,omitempty"`
}

type PickUpAvailable struct {
	Available
	HubCode string `json:"hubCode" bson:"hub_code,omitempty"`
}

var CustomerCarriersDB = &db.Instance{
	ColName:        "customer_carriers",
	TemplateObject: &CustomerCarrier{},
}

func InitCustomerCarriersModel(s *mongo.Database) {
	CustomerCarriersDB.ApplyDatabase(s)
	//t := true

	//CustomerCarriersDB.CreateIndex(bson.D{
	//	{"customer_id", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})
}
