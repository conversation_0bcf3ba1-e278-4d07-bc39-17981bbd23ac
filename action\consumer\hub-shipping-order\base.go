package hub_shipping_order

import (
	"fmt"
	"sync"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
	"go.mongodb.org/mongo-driver/mongo"
)

type ExecutorJob struct {
	Job *job.Executor
}

var (
	instance map[string]*ExecutorJob
	onceInit map[string]*sync.Once
	// Remove here one done change queue
	oldOnceInit                       map[string]*sync.Once
	oldInstance                       map[string]*ExecutorJob
	hubShippingOrderTopic             = "hub_shipping_order"
	updateHubShippingOrderTopic       = "update_hub_shipping_order"
	setCurrentHubShippingOrder        = "set_current_hub_shipping_order"
	pickedExternalHubShippingOrder    = "picked_hub_shipping_order"
	deliveredExternalHubShippingOrder = "delivered_hub_shipping_order"
)

func init() {
	onceInit = map[string]*sync.Once{}
	instance = map[string]*ExecutorJob{}
	// Remove here one done change queue
	oldOnceInit = map[string]*sync.Once{}
	oldInstance = map[string]*ExecutorJob{}
}

func init() {
	hubShippingOrderTopic = conf.Config.Topics["hub_shipping_order"]
	updateHubShippingOrderTopic = conf.Config.Topics["update_hub_shipping_order"]
	setCurrentHubShippingOrder = conf.Config.Topics["set_current_hub_shipping_order"]
}

func InitHubShippingOrderExecutor(dbSession *mongo.Database, Database, Collection string) {
	instanceName := hubShippingOrderTopic
	if oldOnceInit[instanceName] == nil {
		oldOnceInit[instanceName] = &sync.Once{}
	}
	oldOnceInit[instanceName].Do(func() {
		oldInstance[instanceName] = &ExecutorJob{
			&job.Executor{ColName: hubShippingOrderTopic},
		}

		oldInstance[instanceName].Job.InitWithConfig(dbSession,
			Database,
			&job.ExecutorConfiguration{
				ParallelTopicProcessing: false,
				FailThreshold:           50,
				ChannelCount:            5,
				SortedItem:              false,
				LogSize:                 5,
				SelectorDelayMS:         100,
				WaitForReadyTime:        false,
				UniqueItem:              false,
				ConsumedExipredTime:     time.Duration(720) * time.Hour,
				CurVersionTimeoutS:      30,
				OldVersionTimeoutS:      30,
				MaximumWaitToRetryS:     15,
			},
		)

		fmt.Println("StartConsumeDevHub", instanceName)
		oldInstance[instanceName].createHubShippingOrder()
		oldInstance[instanceName].updateHubShippingOrder()
		oldInstance[instanceName].setCurrentHub()
		oldInstance[instanceName].Job.StartConsume()
	})
}

func InitHubShippingOrderJob(dbSession *mongo.Database, Database, Collection string) {
	instanceName := hubShippingOrderTopic
	if onceInit[instanceName] == nil {
		onceInit[instanceName] = &sync.Once{}
	}
	onceInit[instanceName].Do(func() {
		instance[instanceName] = &ExecutorJob{
			&job.Executor{ColName: hubShippingOrderTopic},
		}

		instance[instanceName].Job.InitWithConfig(dbSession,
			Database,
			&job.ExecutorConfiguration{
				ParallelTopicProcessing: false,
				FailThreshold:           50,
				ChannelCount:            5,
				SortedItem:              false,
				LogSize:                 5,
				SelectorDelayMS:         100,
				WaitForReadyTime:        false,
				UniqueItem:              false,
				ConsumedExipredTime:     time.Duration(720) * time.Hour,
				CurVersionTimeoutS:      30,
				OldVersionTimeoutS:      30,
				MaximumWaitToRetryS:     15,
			},
		)

		fmt.Println("StartConsumeDevHub", instanceName)
		instance[instanceName].createHubShippingOrder()
		instance[instanceName].updateHubShippingOrder()
		instance[instanceName].setCurrentHub()
		instance[instanceName].Job.StartConsume()
	})
}

func PushCreateHubShippingOrderQueue(data interface{}, sortedKey string) (err error) {
	return instance[hubShippingOrderTopic].Job.Push(
		data, &job.JobItemMetadata{
			Topic:     hubShippingOrderTopic,
			SortedKey: sortedKey,
		})
}

func PushUpdateHubShippingOrderQueue(data interface{}, sortedKey string) (err error) {
	return instance[hubShippingOrderTopic].Job.Push(
		data, &job.JobItemMetadata{
			Topic:     updateHubShippingOrderTopic,
			SortedKey: sortedKey,
		})
}

func PushUpdateShippingOrder(data interface{}, sortedKey string) (err error) {
	return instance[hubShippingOrderTopic].Job.Push(
		data, &job.JobItemMetadata{
			Topic:     setCurrentHubShippingOrder,
			SortedKey: sortedKey,
		})
}

func PushPickedExternalOrder(data interface{}, sortedKey string) (err error) {
	return instance[hubShippingOrderTopic].Job.Push(
		data, &job.JobItemMetadata{
			Topic:     pickedExternalHubShippingOrder,
			SortedKey: sortedKey,
		})
}

func PushDeliveredExternalOrder(data interface{}, sortedKey string) (err error) {
	return instance[hubShippingOrderTopic].Job.Push(
		data, &job.JobItemMetadata{
			Topic:     deliveredExternalHubShippingOrder,
			SortedKey: sortedKey,
		})
}
