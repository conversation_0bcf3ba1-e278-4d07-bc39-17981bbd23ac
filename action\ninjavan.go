package action

import (
	"encoding/json"
	"fmt"
	"log"

	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/shipping"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
)

type ninjavan struct{}

var NINJAVAN ninjavan

func (ninjavan) BookShipping(input *request.BookShipping, saleOrder *model.SaleOrder, carrierModel *model.Carrier) (result *model.ShippingInfo, err error) {
	if shipping.TplShippingClient.NinjavanClient == nil {
		err = fmt.Errorf("Dịch vụ book NINJAVAN chưa được khởi tạo")
		return
	}

	warehouse, err := client.Services.WarehouseCoreClient.GetWarehouse(input.WarehouseCode)
	if err != nil {
		return
	}

	requestBooking := request.BookNinjaVanRequest{
		ServiceType:             "parcel",
		ServiceLevel:            carrierModel.Service,
		RequestedTrackingNumber: input.SO,
		Reference: &request.NinjaVanReference{
			MerchantOrderNumber: input.SO,
		},
		From: &request.NinjaVanShippingInfo{
			Name:        warehouse.Name,
			PhoneNumber: conf.Config.Supporter["delivery"].Phone,
			Email:       conf.Config.Supporter["delivery"].Email,
			Address: &request.NinjaVanAddress{
				City:        warehouse.ProvinceName,
				District:    warehouse.DistrictName,
				Ward:        warehouse.WardName,
				Address1:    warehouse.Address,
				Country:     "VN",
				AddressType: "",
			},
		},
		To: &request.NinjaVanShippingInfo{
			Name:        saleOrder.CustomerInfos.Delivery.Name,
			PhoneNumber: saleOrder.CustomerInfos.Delivery.Phone,
			Email:       saleOrder.CustomerInfos.Delivery.Email,
			Address: &request.NinjaVanAddress{
				City:        saleOrder.CustomerInfos.Delivery.Province,
				Country:     "VN",
				District:    saleOrder.CustomerInfos.Delivery.District,
				Ward:        saleOrder.CustomerInfos.Delivery.Ward,
				Address1:    saleOrder.CustomerInfos.Delivery.Address,
				AddressType: "",
			},
		},
		ParcelJob: &request.NinjaVanParcel{
			CashOnDelivery:       saleOrder.InternalCODAmount,
			DeliveryInstructions: input.DeliveryNote,
			PickupAddress: &request.NinjaVanShippingInfo{
				Name:        warehouse.Name,
				PhoneNumber: conf.Config.Supporter["delivery"].Phone,
				Email:       conf.Config.Supporter["delivery"].Email,
				Address: &request.NinjaVanAddress{
					Country:     "VN",
					City:        warehouse.ProvinceName,
					District:    warehouse.DistrictName,
					Ward:        warehouse.WardName,
					Address1:    warehouse.Address,
					AddressType: "",
				},
			},
			DeliveryTimeSlot: &request.NinjaVanTimeSlot{
				StartTime: "09:00",
				EndTime:   "12:00",
				TimeZone:  "Asia/Ho_Chi_Minh",
			},
			DeliveryStartDate: "09:00",
			Dimensions: &request.NinjaVanPackageInfo{
				Weight: input.Weight,
				Width:  input.Width,
				Height: input.Height,
				Length: input.Length,
			},
		},
	}

	for i := 0; i < int(input.NbOfPackages); i++ {
		requestBooking.ParcelJob.Items = append(requestBooking.ParcelJob.Items, &request.NinjaVanItem{
			ItemDescription: "Thuốc",
			Quantity:        1,
		})
	}

	if input.NbOfPackages > 1 {
		requestBooking.ParcelJob.DeliveryInstructions = input.DeliveryNote + "Giao cùng lúc mặc định"
	}

	a, _ := json.Marshal(requestBooking)

	log.Println(string(a))

	result, err = shipping.TplShippingClient.NinjavanClient.CreateTrackingNinjaVan(requestBooking, warehouse.Code)
	if err != nil {
		return
	}

	result.CODAmount = saleOrder.InternalCODAmount

	return
}
