package request

import (
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
)

type GetTripRequest struct {
	DriverId          int64                 `json:"driverId,omitempty"`
	TripCode          string                `json:"tripCode,omitempty"`
	TripId            int64                 `json:"tripId,omitempty"`
	RouteCode         string                `json:"routeCode,omitempty"`
	TruckId           int64                 `json:"truckId,omitempty"`
	Type              enum.TripTypeValue    `json:"type,omitempty"`
	Status            *enum.TripStatusValue `json:"status,omitempty"`
	CreatedTimeFrom   int64                 `json:"createdTimeFrom,omitempty"`
	CreatedTimeTo     int64                 `json:"createdTimeTo,omitempty"`
	CompletedTimeFrom int64                 `json:"completedTimeFrom,omitempty"`
	CompletedTimeTo   int64                 `json:"completedTimeTo,omitempty"`
}

type AddItemToTripRequest struct {
	ReferenceCode   string         `json:"referenceCode,omitempty"`
	ReferenceCodes  []string       `json:"referenceCodes,omitempty"`
	FromCode        string         `json:"fromCode,omitempty"`
	FromName        string         `json:"fromName,omitempty"`
	ToCode          string         `json:"toCode,omitempty"`
	ToName          string         `json:"toName,omitempty"`
	FromAddress     *model.Address `json:"fromAddress,omitempty"`
	ToAddress       *model.Address `json:"toAddress,omitempty"`
	AccountID       int64          `json:"accountId,omitempty"`
	TripId          int64          `json:"tripId,omitempty"`
	ScannedQuantity int64          `json:"scannedQuantity"`
	NumPackage      int64          `json:"numPackage,omitempty"`
	SO              string         `json:"so,omitempty"`
	HanoverTicketId int64          `json:"hanoverTicketId,omitempty"`
}

type CreateDraftTicketInTripRequest struct {
	// Required
	TripId    int64                       `json:"tripId,omitempty"`
	FromCode  string                      `json:"fromCode,omitempty"`
	ToCode    string                      `json:"toCode,omitempty"`
	CreatedBy int64                       `json:"createdBy,omitempty"`
	ItemType  *enum.HandoverItemTypeValue `json:"itemType,omitempty" bson:"item_type,omitempty"`
	CarrierId int64                       `json:"carrierId,omitempty"`
	// Auto fill
	FromName    string         `json:"fromName,omitempty"`
	ToName      string         `json:"toName,omitempty"`
	FromAddress *model.Address `json:"fromAddress,omitempty"`
	ToAddress   *model.Address `json:"toAddress,omitempty"`
	CarrierName string         `json:"carrierName,omitempty"`
}

type RemoveTicketFromTripRequest struct {
	TripId   int64 `json:"tripId,omitempty"`
	TicketId int64 `json:"ticketId,omitempty"`
}
