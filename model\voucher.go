package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/mongo"
)

type Voucher struct {
	Code                     string     `json:"code,omitempty" bson:"code,omitempty"`
	MaxNumOfReusePerCustomer int        `json:"maxNumOfReusePerCustomer,omitempty" bson:"max_num_of_reuse_per_customer,omitempty"`
	CreatedBy                int64      `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	LastModifiedBy           int64      `json:"lastModifiedBy,omitempty" bson:"last_modified_by,omitempty"`
	IsActive                 *bool      `json:"isActive,omitempty" bson:"is_active,omitempty"`
	ValidFromDate            *time.Time `json:"validFromDate,omitempty" bson:"valid_from_date,omitempty"`
	ValidToDate              *time.Time `json:"validToDate,omitempty" bson:"valid_to_date,omitempty"`
}

var VoucherDB = &db.Instance{
	ColName:        "voucher",
	TemplateObject: &Voucher{},
}

func InitVoucherModel(s *mongo.Database) {
	VoucherDB.ApplyDatabase(s)
}
