package request

import (
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
)

type LeadTimeQuery struct {
	model.ConfigLeadTime
	FromCodes    []string `json:"fromCodes"`
	ToCodes      []string `json:"toCodes"`
	CarrierCodes []string `json:"carrierCodes"`

	// Query shipping order and hub order lead time
	FromMultiplier int64 `json:"fromMultiplier"`
	FromAddition   int64 `json:"fromAddition"`
	ToMultiplier   int64 `json:"toMultiplier"`
	ToAddition     int64 `json:"toAddition"`
}

type CalculateLeadTimeRequest struct {
	From        *model.Address            `json:"from"`
	To          *model.Address            `json:"to"`
	NumOfPack   int64                     `json:"numOfPack"`
	ServiceType *enum.LeadTimeConfigValue `json:"serviceType"`
	Partner     string                    `json:"partner"`
	Weight      float64                   `json:"weight"`
	Height      float64                   `json:"height"`
	Width       float64                   `json:"width"`
	Length      float64                   `json:"length"`
}

type GetAvailableLeadTimeRequest struct {
	FromHub           string                       `json:"fromHub"`
	ShippingOrderType *enum.ShippingOrderTypeValue `json:"shippingOrderType"`
	TimeFrame         *enum.TimeFrameValue         `json:"timeFrame"`
	CarrierCode       string                       `json:"carrierCode"`
	ProvinceCode      string                       `json:"provinceCode"`
	DistrictCode      string                       `json:"districtCode"`
	WardCode          string                       `json:"wardCode"`
}
