package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func GetMultipleCheckinBinList(query *request.GetMultilpleCheckinBinListRequest, offset int64, limit int64, getTotal bool) *common.APIResponse {
    if query.WarehouseCode == "" {
        return &common.APIResponse{
            Status:  common.APIStatus.Invalid,
            Message: "Warehouse code is required",
        }
    }
	filter := bson.M{
		"warehouse_code": query.WarehouseCode,
	}
	if query.Code != "" {
		filter["code"] = query.Code
	}
	if len(query.Bins) > 0 {
		filter["bins"] = bson.M{
			"$in": query.Bins,
		}
	}
	var total int64
	if getTotal {
		countResult := model.CheckinMultipleBinDB.Count(filter)
		total = countResult.Total
	}
	sortField := primitive.M{"_id": -1}
	result := model.CheckinMultipleBinDB.Query(filter, offset, limit, &sortField)
	if result.Status == common.APIStatus.Ok {
		if getTotal {
			result.Total = total
		}
	}
	return result
}
