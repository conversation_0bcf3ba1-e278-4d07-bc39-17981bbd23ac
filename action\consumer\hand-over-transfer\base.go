package hand_over_transfer

import (
	"log"
	"sync"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"go.mongodb.org/mongo-driver/mongo"
)

type ExecutorJob struct {
	Job *job.Executor
}

var (
	instance            map[string]*ExecutorJob
	onceInit            map[string]*sync.Once
	defaultTopic        = "hand_over_transfer"
	doneCreateHandover  = "done_create_handover"
	doneReceiveHandover = "done_receive_handover"
	// Remove here one done change queue
	oldOnceInit map[string]*sync.Once
	oldInstance map[string]*ExecutorJob
)

func init() {
	onceInit = map[string]*sync.Once{}
	instance = map[string]*ExecutorJob{}
	defaultTopic = conf.Config.Topics["hand_over_transfer"]
	doneCreateHandover = conf.Config.Topics["done_create_handover"]
	doneReceiveHandover = conf.Config.Topics["done_receive_handover"]
	// Remove here one done change queue
	oldOnceInit = map[string]*sync.Once{}
	oldInstance = map[string]*ExecutorJob{}
}

// InitJobHandOverTransferExecutor func
func InitJobHandOverTransferExecutor(dbSession *mongo.Database, Database, Collection string) {
	instanceName := defaultTopic
	if oldOnceInit[instanceName] == nil {
		oldOnceInit[instanceName] = &sync.Once{}
	}

	oldOnceInit[instanceName].Do(func() {
		oldInstance[instanceName] = &ExecutorJob{
			&job.Executor{ColName: Collection},
		}

		oldInstance[instanceName].Job.InitWithConfig(dbSession,
			Database,
			&job.ExecutorConfiguration{
				ParallelTopicProcessing: false,
				FailThreshold:           50,
				ChannelCount:            5,
				SortedItem:              false,
				LogSize:                 5,
				SelectorDelayMS:         100,
				WaitForReadyTime:        false,
				UniqueItem:              false,
				ConsumedExipredTime:     time.Duration(7) * time.Hour,
				CurVersionTimeoutS:      30,
				OldVersionTimeoutS:      30,
				MaximumWaitToRetryS:     15,
			},
		)

		log.Println("StartConsume", instanceName)
		oldInstance[instanceName].sync()
		oldInstance[instanceName].doneCreateHandover()
		oldInstance[instanceName].doneReceiveHandover()
		oldInstance[instanceName].Job.StartConsume()
	})
}

// InitJobHandOverTransferExecutor func
func InitJobHandOverTransferJob(dbSession *mongo.Database, Database, Collection string) {
	instanceName := defaultTopic
	if onceInit[instanceName] == nil {
		onceInit[instanceName] = &sync.Once{}
	}

	onceInit[instanceName].Do(func() {
		instance[instanceName] = &ExecutorJob{
			&job.Executor{ColName: Collection},
		}

		instance[instanceName].Job.InitWithConfig(dbSession,
			Database,
			&job.ExecutorConfiguration{
				ParallelTopicProcessing: false,
				FailThreshold:           50,
				ChannelCount:            1,
				SortedItem:              false,
				LogSize:                 5,
				SelectorDelayMS:         100,
				WaitForReadyTime:        false,
				UniqueItem:              false,
				ConsumedExipredTime:     time.Duration(7) * time.Hour,
				CurVersionTimeoutS:      30,
				OldVersionTimeoutS:      30,
				MaximumWaitToRetryS:     15,
			},
		)

		log.Println("StartConsume", instanceName)
		instance[instanceName].sync()
		instance[instanceName].doneCreateHandover()
		instance[instanceName].doneReceiveHandover()
		instance[instanceName].Job.StartConsume()
	})
}

func Push(data interface{}, sortedKey string) (err error) {
	return instance[defaultTopic].Job.Push(
		data, &job.JobItemMetadata{
			Topic:     defaultTopic,
			SortedKey: sortedKey,
		})
}

func PushDoneCreateHandover(data interface{}, sortedKey string) (err error) {
	return instance[defaultTopic].Job.Push(
		data, &job.JobItemMetadata{
			Topic:     doneCreateHandover,
			SortedKey: sortedKey,
		})
}

func PushDoneReceiveHandover(data interface{}, sortedKey string) (err error) {
	return instance[defaultTopic].Job.Push(
		data, &job.JobItemMetadata{
			Topic:     doneReceiveHandover,
			SortedKey: sortedKey,
		})
}

type DoneHandoverTicketRequest struct {
	TicketId  int
	Items     []model.HandoverSOItem
	CheckInAt enum.CheckInAtValue
}
