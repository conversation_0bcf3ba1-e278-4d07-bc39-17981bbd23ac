package response

type BookVTP struct {
	OrderNumber        string `json:"ORDER_NUMBER"`
	MoneyCollection    int64  `json:"MONEY_COLLECTION"`
	ExchangeWeight     int64  `json:"EXCHANGE_WEIGHT"`
	MoneyTotal         int64  `json:"MONEY_TOTAL"`
	MoneyTotalFee      int64  `json:"MONEY_TOTAL_FEE"`
	MoneyFee           int64  `json:"MONEY_FEE"`
	MoneyCollectionFee int64  `json:"MONEY_COLLECTION_FEE"`
	MoneyOtherFee      int64  `json:"MONEY_OTHER_FEE"`
	MoneyVat           int64  `json:"MONEY_VAT"`
}

type GetVTPShippingService struct {
	PrimaryServiceCode string `json:"MA_DV_CHINH"`
	DeliveryFee        int64  `json:"GIA_CUOC"`
}
