package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Trip struct {
	ID primitive.ObjectID `json:"-" bson:"_id,omitempty" `

	TripId          int64                 `json:"tripId,omitempty" bson:"trip_id,omitempty"`
	TripCode        string                `json:"tripCode,omitempty" bson:"trip_code,omitempty"`
	CreatedBy       int64                 `json:"createdBy,omitempty" bson:"created_by,omitempty" `
	UpdatedBy       int64                 `json:"updatedBy,omitempty" bson:"updated_by,omitempty" `
	CreatedTime     *time.Time            `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	CompletedTime   *time.Time            `json:"completedTime,omitempty" bson:"completed_time,omitempty"`
	LastUpdatedTime *time.Time            `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	Keyword         int64                 `json:"keyword,omitempty" bson:"keyword,omitempty"`
	Addresses       []*Address            `json:"addresses,omitempty" bson:"addresses,omitempty"`
	Driver          *Account              `json:"driver,omitempty" bson:"driver,omitempty"`
	DriverID        int                   `json:"driverId,omitempty" bson:"driver_id,omitempty"`
	DriverName      string                `json:"driverName,omitempty" bson:"driver_name,omitempty"`
	TotalDistance   float64               `json:"totalDistance,omitempty" bson:"total_distance,omitempty"`
	Status          *enum.TripStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	IsOptimized     *bool                 `json:"isOptimized,omitempty" bson:"is_optimized,omitempty"`
	Type            enum.TripTypeValue    `json:"type,omitempty" bson:"type,omitempty"`
	Route           *Route                `json:"route,omitempty" bson:"route,omitempty"`
	HandoverTickets []*HandoverTicket     `json:"handoverTickets,omitempty" bson:"handover_tickets,omitempty"`
	Truck           *Truck                `json:"truck,omitempty" bson:"truck,omitempty"`
	CurrentAddress  *Address              `json:"currentAddress,omitempty" bson:"current_address,omitempty"`
	FromCode        string                `json:"fromCode,omitempty"`
}

var TripDB = &db.Instance{
	ColName:        "trip",
	TemplateObject: &Trip{},
}

func InitTripModel(s *mongo.Database) {
	TripDB.ApplyDatabase(s)
	t := true

	TripDB.CreateIndex(bson.D{
		{"driver_id", 1},
		{"type", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	TripDB.CreateIndex(bson.D{
		{"type", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	TripDB.CreateIndex(bson.D{
		{"type", 1},
		{"status", 1},
	}, &options.IndexOptions{
		Background: &t,
	})
}
