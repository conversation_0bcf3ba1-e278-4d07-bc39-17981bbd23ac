# This file is autogenerated, do not edit; changes may be undone by the next 'dep ensure'.


[[projects]]
  name = "github.com/modern-go/concurrent"
  packages = ["."]
  revision = "e0a39a4cb4216ea8db28e22a69f4ec25610d513a"
  version = "1.0.0"

[[projects]]
  name = "github.com/modern-go/reflect2"
  packages = ["."]
  revision = "4b7aa43c6742a2c18fdef89dd197aaae7dac7ccd"
  version = "1.0.1"

[solve-meta]
  analyzer-name = "dep"
  analyzer-version = 1
  inputs-digest = "ea54a775e5a354cb015502d2e7aa4b74230fc77e894f34a838b268c25ec8eeb8"
  solver-name = "gps-cdcl"
  solver-version = 1
