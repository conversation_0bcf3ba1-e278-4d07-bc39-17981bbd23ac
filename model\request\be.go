package request

type BeOrderInfo struct {
	ReferenceId     string `json:"reference_id"`
	Remarks         string `json:"remarks"`
	PaymentMode     int    `json:"payment_mode"`
	PaidByRecipient bool   `json:"paid_by_recipient"`
	Note            string `json:"note"`
	GoodsValue      string `json:"goods_value"`
	CashCollectFee  string `json:"cash_collect_fee"`
}

type BePickup struct {
	Longitude   float64 `json:"longitude"`
	Latitude    float64 `json:"latitude"`
	AddressName string  `json:"address_name"`
	FullAddress string  `json:"full_address"`
	PhoneNumber string  `json:"phone_number"`
	Name        string  `json:"name"`
}

type BeDropoff struct {
	Latitude        float64 `json:"latitude"`
	Longitude       float64 `json:"longitude"`
	AddressName     string  `json:"address_name"`
	FullAddress     string  `json:"full_address"`
	PhoneNumber     string  `json:"phone_number"`
	AddressAddnInfo string  `json:"address_addn_info"`
	Name            string  `json:"name"`
	Cod             float64 `json:"cod"`
	Priority        float64 `json:"priority"`
	AddonService    []struct {
		ServiceKey string `json:"service_key"`
		Selected   bool   `json:"selected"`
	} `json:"addon_service"`
}

type BeInvoice struct {
	CompanyName    string `json:"company_name"`
	CompanyAddress string `json:"company_address"`
	CompanyTaxCode string `json:"company_tax_code"`
	Email          string `json:"email"`
}

type BookBeRequest struct {
	PartnerUserId string      `json:"partner_user_id"`
	ServiceType   string      `json:"service_type"`
	OrderInfo     BeOrderInfo `json:"order_info"`
	Pickup        BePickup    `json:"pickup"`
	Dropoff       []BeDropoff `json:"dropoff"`
	Invoice       BeInvoice   `json:"invoice"`
}
