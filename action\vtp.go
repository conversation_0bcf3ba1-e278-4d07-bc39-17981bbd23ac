package action

import (
	"fmt"
	"strconv"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/shipping"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"go.mongodb.org/mongo-driver/bson"
)

type vtp struct{}

var VTP vtp

func (vtp) BookShipping(input *request.BookShipping, saleOrder *model.SaleOrder, carrierModel *model.Carrier, pickupAddress *model.Address, feeCollectOn *enum.FeeCollectMethodValue) (result *model.ShippingInfo, err error) {
	if shipping.TplShippingClient.VTPClient == nil {
		err = fmt.Errorf("Dịch vụ book VIETTEL_POST chưa được khởi tạo")
		return
	}

	if len(carrierModel.Service) == 0 {
		err = fmt.Errorf("Không tìm thấy dịch vụ vận chuyển phù hợp")
		return
	}
	ReceiverFullName := saleOrder.CustomerInfos.Delivery.Name
	if saleOrder.Type == enum.TPLCallbackStatus.RETURN {
		ReceiverFullName = "Công ty TNHH Buymed Thuốc Sỉ" + saleOrder.CustomerInfos.Delivery.Name
	}

	requestBooking := request.BookVTP{
		CusId:            carrierModel.ExtraData.PartnerId,
		OrderReference:   input.SO,
		GroupAddressId:   carrierModel.ExtraData.BusinessAddressId,
		OrderService:     carrierModel.Service,
		SenderFullName:   pickupAddress.Name,
		ProductType:      "HH",
		ProductName:      "Thuốc",
		ProductQuantity:  input.NbOfPackages,
		ProductWeight:    input.Weight * 1000,
		SenderAddress:    pickupAddress.Address,
		MoneyCollection:  saleOrder.CODAmount,
		SenderPhone:      pickupAddress.Phone,
		ReceiverAddress:  saleOrder.CustomerInfos.Delivery.Address,
		ReceiverFullName: ReceiverFullName,
		ReceiverEmail:    saleOrder.CustomerInfos.Delivery.Email,
		ReceiverPhone:    saleOrder.CustomerInfos.Delivery.Phone,
	}

	// Nếu config là gửi hàng tại bưu cục thì truyền thêm thông tin
	if carrierModel.PickupType == 2 {
		requestBooking.OrderServiceAdd = "GNG"
	}

	if *saleOrder.PaymentMethod == enum.PaymentMethod.BANK {
		requestBooking.MoneyCollection = 0
	}

	paymentMethod, err := strconv.Atoi(carrierModel.PaymentMethod)
	if err != nil {
		err = fmt.Errorf("Phương thức thanh toán với nhà vận chuyển không hợp lệ")
		return
	}

	if feeCollectOn != nil && *feeCollectOn == enum.FeeCollectMethod.RECEIVER_PAY {
		paymentMethod = 4 // Người nhận trả phí
	}

	requestBooking.OrderPayment = paymentMethod

	requestBooking.SenderProvince, requestBooking.SenderDistrict, requestBooking.SenderWard, err = CompareMasterDataVTP(pickupAddress.ProvinceName, pickupAddress.DistrictName, pickupAddress.WardName)
	if err != nil {
		return
	}

	requestBooking.ReceiverProvince, requestBooking.ReceiverDistrict, requestBooking.ReceiverWard, err = CompareMasterDataVTP(saleOrder.CustomerInfos.Delivery.Province, saleOrder.CustomerInfos.Delivery.District, saleOrder.CustomerInfos.Delivery.Ward)
	if err != nil {
		return
	}

	prices, err := shipping.TplShippingClient.VTPClient.GetShippingAllPrice(request.GetVTPShippingFee{
		Type:             1,
		SenderProvince:   requestBooking.SenderProvince,
		SenderDistrict:   requestBooking.SenderDistrict,
		ReceiverProvince: requestBooking.ReceiverProvince,
		ReceiverDistrict: requestBooking.ReceiverDistrict,
		ProductWeight:    int64(requestBooking.ProductWeight),
		ProductType:      requestBooking.ProductType,
		ProductPrice:     requestBooking.ProductPrice,
		MoneyCollection:  int64(requestBooking.MoneyCollection),
	})

	if err != nil || len(prices) == 0 {
		return
	}

	lowestPrice := prices[0]
	for _, price := range prices {
		// Only prioritize carrierModel.Service if value is VCBO
		if carrierModel.Service == "VCBO" && price.PrimaryServiceCode == "VCBO" {
			lowestPrice = price
			break
		}
		// Prioritize VSL7
		// TODO: When VTP clear about how they charge additional fee, remove this
		if price.PrimaryServiceCode == "VSL7" {
			lowestPrice = price
			break
		}
		if price.DeliveryFee < lowestPrice.DeliveryFee {
			lowestPrice = price
		}
	}
	requestBooking.OrderService = lowestPrice.PrimaryServiceCode

	result, err = shipping.TplShippingClient.VTPClient.CreateTrackingVTP(requestBooking)
	if err != nil {
		return
	}

	return
}

func (vtp) GetShippingFee(input *request.GetShippingService, carrierModel *model.Carrier) (feeAmount float64) {
	feeAmount = -1
	var err error
	if shipping.TplShippingClient.VTPClient == nil {
		err = fmt.Errorf("Dịch vụ book VIETTEL_POST chưa được khởi tạo")
		return
	}

	if len(carrierModel.Service) == 0 {
		err = fmt.Errorf("Không tìm thấy dịch vụ vận chuyển phù hợp")
		return
	}

	requestData := request.GetVTPShippingFee{
		ProductWeight: int64(input.Weight) * 1000,
		OrderService:  carrierModel.Service,
		ProductType:   "HH",
		NationalType:  1,
	}

	requestData.SenderProvince, requestData.SenderDistrict, _, err = CompareMasterDataVTP(
		input.FromProvince, input.FromDistrict, "")
	if err != nil {
		return
	}

	requestData.ReceiverProvince, requestData.ReceiverDistrict, _, err = CompareMasterDataVTP(
		input.Province, input.District, "")
	if err != nil {
		return
	}

	feeAmount, err = shipping.TplShippingClient.VTPClient.GetShippingFee(requestData)
	if err != nil {
		feeAmount = -1
	}
	return
}

func CompareMasterDataVTP(provinceName, districtName, wardName string) (provinceId, districtId, wardId int64, err error) {
	provinceMappingVTP := model.ProvinceMappingDB.QueryOne(bson.M{
		"dictionary": provinceName,
	})

	if provinceMappingVTP.Status != common.APIStatus.Ok || provinceMappingVTP.Data == nil {
		err = fmt.Errorf("Không tìm thấy thông tin %v", provinceName)
		return
	}
	provinceMappingVTPModel := provinceMappingVTP.Data.([]*model.ProvinceMapping)[0]

	districtMappingVTP := model.DistrictMappingDB.QueryOne(bson.M{
		"dictionary":    districtName,
		"province_code": provinceMappingVTPModel.Code,
	})

	if districtMappingVTP.Status != common.APIStatus.Ok || districtMappingVTP.Data == nil {
		err = fmt.Errorf("Không tìm thấy thông tin %v, %v", districtName, provinceName)
		return
	}

	districtMappingVTPModel := districtMappingVTP.Data.([]*model.DistrictMapping)[0]

	// TODO: Remove hardcode
	if districtMappingVTPModel.VTPId == 602 {
		return provinceMappingVTPModel.VTPId, districtMappingVTPModel.VTPId, 10287, nil
	}

	wardMappingVTPModel := new(model.WardMapping)

	wardMappingVTP := model.WardMappingDB.QueryOne(bson.M{
		"dictionary":    wardName,
		"district_code": districtMappingVTPModel.Code,
	})

	if wardMappingVTP.Status == common.APIStatus.Ok {
		wardMappingVTPModel = wardMappingVTP.Data.([]*model.WardMapping)[0]
	}

	return provinceMappingVTPModel.VTPId, districtMappingVTPModel.VTPId, wardMappingVTPModel.VTPId, err
}

func (vtp) BookShippingOrder(
	input *request.BookShippingOrder,
	carrierModel *model.Carrier) (
	result *model.ShippingInfo, err error) {
	if shipping.TplShippingClient.VTPClient == nil {
		err = fmt.Errorf("Dịch vụ book VIETTEL_POST chưa được khởi tạo")
		return
	}

	if len(carrierModel.Service) == 0 {
		err = fmt.Errorf("Không tìm thấy dịch vụ vận chuyển phù hợp")
		return
	}

	// OrderPayment:
	// 1 Không thu hộ phí
	// 2 Thu COD + phí
	// 3 Chỉ thu COD
	// 4 Chỉ thu phí

	requestBooking := request.BookVTP{
		CusId:            carrierModel.ExtraData.PartnerId,
		OrderReference:   input.ReferenceCode,
		GroupAddressId:   carrierModel.ExtraData.BusinessAddressId,
		OrderService:     carrierModel.Service,
		SenderFullName:   input.From.Name,
		ProductType:      "HH",
		ProductName:      "Thuốc",
		ProductQuantity:  input.NumPackage,
		ProductWeight:    input.Weight * 1000,
		SenderAddress:    input.From.Address,
		MoneyCollection:  input.CODAmount,
		SenderPhone:      input.From.Phone,
		ReceiverAddress:  input.To.Address,
		ReceiverFullName: input.To.Name,
		ReceiverEmail:    input.To.Email,
		ReceiverPhone:    input.To.Phone,
		OrderPayment:     3, // Người nhận trả COD
	}

	if input.FeeCollectedOn != nil && *input.FeeCollectedOn == enum.FeeCollectMethod.RECEIVER_PAY {
		requestBooking.OrderPayment = 4 // Người nhận trả phí
	}

	requestBooking.SenderProvince, requestBooking.SenderDistrict, requestBooking.SenderWard, err = CompareMasterDataVTP(input.From.ProvinceName, input.From.DistrictName, input.From.WardName)
	if err != nil {
		return
	}

	requestBooking.ReceiverProvince, requestBooking.ReceiverDistrict, requestBooking.ReceiverWard, err = CompareMasterDataVTP(input.To.ProvinceName, input.To.DistrictName, input.To.WardName)
	if err != nil {
		return
	}

	if input.FeeCollectedOn != nil && *input.FeeCollectedOn == enum.FeeCollectMethod.WALLET_PAY {
		requestBooking.OrderPayment = 1 // Không thu phí người nhận
	}

	prices, err := shipping.TplShippingClient.VTPClient.GetShippingAllPrice(request.GetVTPShippingFee{
		Type:             1,
		SenderProvince:   requestBooking.SenderProvince,
		SenderDistrict:   requestBooking.SenderDistrict,
		ReceiverProvince: requestBooking.ReceiverProvince,
		ReceiverDistrict: requestBooking.ReceiverDistrict,
		ProductWeight:    int64(requestBooking.ProductWeight),
		ProductType:      requestBooking.ProductType,
		ProductPrice:     requestBooking.ProductPrice,
		MoneyCollection:  int64(requestBooking.MoneyCollection),
	})

	if err != nil || len(prices) == 0 {
		return
	}

	lowestPrice := prices[0]
	for _, price := range prices {
		if price.DeliveryFee < lowestPrice.DeliveryFee {
			lowestPrice = price
		}
	}
	requestBooking.OrderService = lowestPrice.PrimaryServiceCode

	result, err = shipping.TplShippingClient.VTPClient.CreateTrackingVTP(requestBooking)
	if err != nil {
		return
	}

	return
}
