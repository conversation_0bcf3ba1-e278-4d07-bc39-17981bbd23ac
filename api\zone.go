package api

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
)

func CreateZone(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Zone
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}

	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	return resp.Respond(action.CreateZone(&input, UserInfo.Account.AccountID))
}

func GetZones(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q        = req.GetParam("q")
		offset   = int64(sdk.ParseInt(req.GetParam("offset"), 0))
		limit    = int64(sdk.ParseInt(req.GetParam("limit"), 100))
		getTotal = req.GetParam("getTotal") == "true"
	)

	if q == "" {
		q = "{}"
	}

	var query request.ZoneQuery
	err := json.Unmarshal([]byte(q), &query)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data." + err.Error(),
			ErrorCode: "JSON_INVALID",
		})
	}

	return resp.Respond(action.GetZones(query, offset, limit, getTotal))
}

func GetNearZones(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = int64(sdk.ParseInt(req.GetParam("offset"), 0))
		limit    = int64(sdk.ParseInt(req.GetParam("limit"), 100))
		getTotal = req.GetParam("getTotal") == "true"
	)

	return resp.Respond(action.GetNearZones(offset, limit, getTotal))
}

func UpdateZoneArea(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Zone
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")

	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}

	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	if input.Action == "update" {
		return resp.Respond(action.UpdateZoneArea(&input, UserInfo.Account.AccountID))
		// return resp.Respond(action.UpdateZoneArea(&input,123))
	}

	if input.Action == "delete" {
		return resp.Respond(action.DeleteZone(input.Code, UserInfo.Account.AccountID))
		// return resp.Respond(action.DeleteZone(input.Code,123))
	}

	return resp.Respond(
		&common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Chọn hoạt động không hợp lệ",
		})
}

func PrepareUpdate(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Zone
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.PrepareUpdate(&input))
}

func MigrateZone(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.MigrateLeafZone())
}

func MigrateZoneLevel(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.MigrateZoneLevel())
}

func MigrateZoneKeyword(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.MigrateZoneKeyword())
}

func MigrateWardIndexes(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input []*model.Zone
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data: " + err.Error(),
			ErrorCode: "JSON_INVALID",
		})
	}
	return resp.Respond(action.MigrateWardIndexes(input))
}
