package warehouse_core

import (
	"encoding/json"
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
)

func (cli *Client) GetWarehouse(warehouseCode string) (result *model.Warehouse, err error) {
	type getWarehouse struct {
		WarehouseCode string `json:"code"`
	}
	reqGetWarehouse := &getWarehouse{
		WarehouseCode: warehouseCode,
	}

	queryParam, err := json.Marshal(reqGetWarehouse)

	if err != nil {
		return
	}

	params := map[string]string{
		"q": string(queryParam),
	}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetWarehouse, nil)
	if err != nil {
		return nil, err
	}

	type myResponse struct {
		Status string             `json:"status"`
		Code   string             `json:"code"`
		Data   []*model.Warehouse `json:"data"`
	}

	response := new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return nil, err
	}

	if response.Status != common.APIStatus.Ok || response.Data == nil {
		return nil, fmt.Errorf("%v", response.Code)
	}

	if len(response.Data) > 0 {
		result = response.Data[0]
	}

	return
}

func (cli *Client) GetAllWarehouse() (result []*model.Warehouse, err error) {
	type getWarehouse struct {
		Status string `json:"status"`
	}
	reqGetWarehouse := &getWarehouse{
		Status: "ACTIVE",
	}

	queryParam, err := json.Marshal(reqGetWarehouse)

	if err != nil {
		return
	}

	params := map[string]string{
		"q": string(queryParam),
	}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetWarehouse, nil)
	if err != nil {
		return nil, err
	}

	type myResponse struct {
		Status string             `json:"status"`
		Code   string             `json:"code"`
		Data   []*model.Warehouse `json:"data"`
	}

	response := new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return nil, err
	}

	if response.Status != common.APIStatus.Ok || response.Data == nil {
		return nil, fmt.Errorf("%v", response.Code)
	}

	if len(response.Data) > 0 {
		result = response.Data
	}

	return
}

func (cli *Client) ResetLocationWarehouse(warehouseCode string, binCode string) (result *model.Location, err error) {
	body := map[string]string{
		"warehouseCode": warehouseCode,
		"code":          binCode,
	}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, nil, body, pathResetLocationWarehouse, nil)
	if err != nil {
		return nil, err
	}

	type myResponse struct {
		Status  string            `json:"status"`
		Code    string            `json:"code"`
		Data    []*model.Location `json:"data"`
		Message string            `json:"message"`
	}

	response := new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return nil, err
	}

	if response.Status != common.APIStatus.Ok || response.Data == nil {
		return nil, fmt.Errorf("%v", response.Code)
	}

	if len(response.Data) > 0 {
		result = response.Data[0]
	}

	return
}

func (cli *Client) GetLocationDetail(warehouseCode string, locationCode string) (result *model.Location, err error) {
	params := map[string]string{
		"code":          locationCode,
		"warehouseCode": warehouseCode,
	}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params,nil, padthGetLocationDetailWarehouse, nil)
		if err != nil {
		return nil, err
	}

	type myResponse struct {
		Status  string            `json:"status"`
		Code    string            `json:"code"`
		Data    []*model.Location `json:"data"`
		Message string            `json:"message"`
	}

	response := new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return nil, err
	}

	if response.Status != common.APIStatus.Ok || response.Data == nil {
		return nil, fmt.Errorf("%v", response.Code)
	}

	if len(response.Data) > 0 {
		result = response.Data[0]
	}

	return result, nil
}
