package model

type Warehouse struct {
	WarehouseID  int64    `json:"warehouseId,omitempty"`
	Code         string   `json:"code,omitempty"`
	Name         string   `json:"name,omitempty"`
	Address      string   `json:"address,omitempty" `
	ProvinceCode string   `json:"provinceCode,omitempty"`
	ProvinceName string   `json:"provinceName"`
	DistrictName string   `json:"districtName"`
	DistrictCode string   `json:"districtCode,omitempty"`
	WardCode     string   `json:"wardCode,omitempty"`
	WardName     string   `json:"wardName"`
	Areas        []string `json:"areas,omitempty"`
	ManagerID    int64    `json:"managerId,omitempty"`
	NumberGate   int64    `json:"number_gate,omitempty" `
	Width        float64  `json:"width,omitempty"`
	Length       float64  `json:"length,omitempty"`
	Area         float64  `json:"area,omitempty"`
	Phone        string   `json:"phone"`
	IsMergeDO    bool     `json:"isMergeDO"`
	MainHubCode  string   `json:"mainHubCode"`
}

type Location struct {
	LocationID        int         `json:"locationId,omitempty" bson:"location_id,omitempty"`
	Code              string      `json:"code,omitempty" bson:"code,omitempty"`
	WarehouseCode     string      `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`
	Name              string      `json:"name,omitempty" bson:"name,omitempty"`
	Keyword           string      `json:"keyword,omitempty" bson:"keyword,omitempty"`
	Level             int         `json:"level,omitempty" bson:"level,omitempty"`
	Parent            string      `json:"parent" bson:"parent"`
	IsLastChild       *bool       `json:"isLastChild,omitempty" bson:"is_last_child,omitempty"`
	IsUsed            *bool       `json:"isUsed" bson:"is_used"`
	IsPrinted         *bool       `json:"isPrinted" bson:"is_printed,omitempty"`
	IsPick            *bool       `json:"isPick,omitempty" bson:"is_pick,omitempty"`
	IsPut             *bool       `json:"isPut,omitempty" bson:"is_put,omitempty"`
	IsZone            *bool       `json:"isZone,omitempty" bson:"is_zone,omitempty"`
	IsTempStorage     *bool       `json:"isTempStorage,omitempty" bson:"is_temp_storage,omitempty"`
	ParentOrder       *[]string   `json:"parentOrder,omitempty" bson:"parent_order,omitempty"`
	Width             float64     `json:"width,omitempty" bson:"width,omitempty"`
	Height            float64     `json:"height,omitempty" bson:"height,omitempty"`
	Length            float64     `json:"length,omitempty" bson:"length,omitempty"`
	ParentDetailOrder *[]Location `json:"parentDetailOrder,omitempty" bson:"-"`
}
