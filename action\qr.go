package action

import (
	"fmt"
	"strconv"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/utils"
)

const (
	// List ID of params
	PAYLOAD_FORMAT_INDICATOR_ID      = "00"
	POINT_OF_INITIATION_METHOD_ID    = "01"
	BANK_ID                          = "00"
	BEN_ACCOUNT_ID                   = "01"
	CUSTOMER_ACCOUNT_INFORMATION_ID  = "38"
	AID_NAPAS_ID                     = "00"
	NAPAS_SERVICE_CODE               = "02"
	BENEFICIAL_ACCOUNT_INFO_ID       = "01"
	TRANSACTION_CURRENCY             = "53"
	TRANSACTION_AMOUNT               = "54"
	COUNTRY_CODE                     = "58"
	ADDITONAL_DATA_FIELD_TEMPLATE_ID = "62"
	CRC16_ID                         = "63"
	PURPOSE_OF_TRANSACTION_ID        = "08"

	// List const
	PAYLOAD_FORMAT_INDICATOR_VALUE = "01"       // version 1
	DYNAMIC_POINT_OF_INITIATION    = "12"       // QR động - áp dụng khi mã QR chỉ cho phép thực hiện một lần giao dịch.
	FAST_TRANSACTION_NAPAS         = "QRIBFTTA" //
	AID_NAPAS                      = "A000000727"
	CRC_LENGTH                     = "04"
	VND_CURRENCY                   = "704"
)

var crcUint16 uint16 = 0xFFFF

func CalcCRC16(strInput string) string {
	crc := crcUint16
	data := []byte(strInput)
	for _, b := range data {
		crc ^= uint16(b) << 8
		for i := 0; i < 8; i++ {
			if crc&0x8000 > 0 {
				crc = (crc << 1) ^ 0x1021
			} else {
				crc <<= 1
			}
		}
	}
	return fmt.Sprintf("%04X", crc)
}

func GenerateQRCode(genReq request.GenerateQRCodeRequest) string {
	// Định danh của ngân hàng
	ACQID := genReq.ACQID
	// Tài khoản nhận giao dịch
	benAccount := genReq.BenAccount
	qrVersion := PAYLOAD_FORMAT_INDICATOR_ID + TwoDigitString(len(PAYLOAD_FORMAT_INDICATOR_VALUE)) + PAYLOAD_FORMAT_INDICATOR_VALUE
	initiationMethod := POINT_OF_INITIATION_METHOD_ID + TwoDigitString(len(DYNAMIC_POINT_OF_INITIATION)) + DYNAMIC_POINT_OF_INITIATION
	naspasIdentifier := AID_NAPAS_ID + TwoDigitString(len(AID_NAPAS)) + AID_NAPAS
	bankTransferInfo :=
		BANK_ID +
			TwoDigitString(len(ACQID)) +
			ACQID +
			BEN_ACCOUNT_ID +
			TwoDigitString(len(benAccount)) +
			benAccount

	beneficialAccountInfo :=
		BENEFICIAL_ACCOUNT_INFO_ID +
			TwoDigitString(len(bankTransferInfo)) +
			bankTransferInfo

	napasServiceCode := NAPAS_SERVICE_CODE + TwoDigitString(len(FAST_TRANSACTION_NAPAS)) + FAST_TRANSACTION_NAPAS
	customerAccountInfo := CUSTOMER_ACCOUNT_INFORMATION_ID +
		TwoDigitString(len(naspasIdentifier+beneficialAccountInfo+napasServiceCode)) +
		naspasIdentifier + beneficialAccountInfo + napasServiceCode
	stringAmount := strconv.FormatInt(genReq.TxAmount, 10)
	transactionAmountInfo := TRANSACTION_AMOUNT + TwoDigitString(len(stringAmount)) + stringAmount
	remarkInfo := PURPOSE_OF_TRANSACTION_ID + TwoDigitString(len(genReq.Remark)) + genReq.Remark
	additionalInfo := ADDITONAL_DATA_FIELD_TEMPLATE_ID +
		TwoDigitString(len(remarkInfo)) +
		remarkInfo
	qrInfo :=
		qrVersion +
			initiationMethod +
			customerAccountInfo +
			TRANSACTION_CURRENCY + TwoDigitString(len(VND_CURRENCY)) + VND_CURRENCY +
			transactionAmountInfo +
			COUNTRY_CODE + TwoDigitString(len(genReq.CountryCode)) + genReq.CountryCode +
			additionalInfo + CRC16_ID + CRC_LENGTH

	crc := CalcCRC16(qrInfo)

	return fmt.Sprintf("%s%s", qrInfo, crc)
}

func GenerateQRCodeHandler(genReq request.GenerateQRCodeRequest) *common.APIResponse {
	if genReq.CountryCode == "" {
		genReq.TxCurrency = "VN"
	}

	if (genReq.TxAmount) <= 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid amount",
		}
	}

	if genReq.BenAccount == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid benAccount",
		}
	}

	if genReq.BankCode == "" ||
		!utils.CheckExistInEnum(genReq.BankCode, *enum.BankCode) {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid BankCode",
		}
	}

	if genReq.Remark == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid remark",
		}
	}

	key, ok := enum.BankCodeToACQID[genReq.BankCode]
	if !ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid BankCode",
		}
	}
	genReq.ACQID = key

	qrCode := GenerateQRCode(genReq)
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
		Data:    []string{qrCode},
	}
}

func TwoDigitString(input int) string {
	if input < 10 {
		return fmt.Sprintf("0%d", input)
	}
	return fmt.Sprintf("%d", input)
}
