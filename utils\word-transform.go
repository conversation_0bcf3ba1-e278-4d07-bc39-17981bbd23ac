package utils

import (
	"fmt"
	"log"
	"regexp"
	"strconv"
	"strings"
)

func VietnamesToEng(originalString string) string {
	var Regexp_A = `à|á|ạ|ã|ả|ă|ắ|ằ|ẳ|ẵ|ặ|â|ấ|ầ|ẩ|ẫ|ậ`
	var Regexp_E = `è|ẻ|ẽ|é|ẹ|ê|ề|ể|ễ|ế|ệ`
	var Regexp_I = `ì|ỉ|ĩ|í|ị`
	var Regexp_U = `ù|ủ|ũ|ú|ụ|ư|ừ|ử|ữ|ứ|ự`
	var Regexp_Y = `ỳ|ỷ|ỹ|ý|ỵ`
	var Regexp_O = `ò|ỏ|õ|ó|ọ|ô|ồ|ổ|ỗ|ố|ộ|ơ|ờ|ở|ỡ|ớ|ợ`
	var Regexp_D = `Đ|đ`
	reg_a := regexp.MustCompile(Regexp_A)
	reg_e := regexp.MustCompile(Regexp_E)
	reg_i := regexp.MustCompile(Regexp_I)
	reg_o := regexp.MustCompile(Regexp_O)
	reg_u := regexp.MustCompile(Regexp_U)
	reg_y := regexp.MustCompile(Regexp_Y)
	reg_d := regexp.MustCompile(Regexp_D)
	originalString = reg_a.ReplaceAllLiteralString(originalString, "a")
	originalString = reg_e.ReplaceAllLiteralString(originalString, "e")
	originalString = reg_i.ReplaceAllLiteralString(originalString, "i")
	originalString = reg_o.ReplaceAllLiteralString(originalString, "o")
	originalString = reg_u.ReplaceAllLiteralString(originalString, "u")
	originalString = reg_y.ReplaceAllLiteralString(originalString, "y")
	originalString = reg_d.ReplaceAllLiteralString(originalString, "d")
	// regexp remove charaters in ()
	var RegexpPara = `\(.*\)`
	reg_para := regexp.MustCompile(RegexpPara)
	originalString = reg_para.ReplaceAllLiteralString(originalString, "")

	return strings.ToLower(originalString)
}

func RemoveSpecialChar(originalString string) string {
	reg, err := regexp.Compile("[^a-zA-Z0-9 ]+")
	if err != nil {
		log.Fatal(err)
	}
	return reg.ReplaceAllString(originalString, "")
}

func RemoveDupSpace(originalString string) string {
	return strings.Join(strings.Fields(originalString), " ")
}

func ToRawText(originalString string) string {
	return RemoveDupSpace(RemoveSpecialChar(VietnamesToEng(strings.TrimSpace(strings.ToLower(originalString)))))
}

func GenKeyword(keys ...interface{}) (string, error) {
	if keys == nil || len(keys) == 0 {
		return "", fmt.Errorf("Không được để trống từ khóa")
	}
	result := ""
	for _, key := range keys {
		switch key.(type) {
		case int64:
			int64Var, ok := key.(int64)
			if !ok {
				return "", fmt.Errorf("Không thể chuyển kiểu dữ liệu")
			}
			result += "_" + strconv.FormatInt(int64Var, 10)
		case int:
			intVar, ok := key.(int)
			if !ok {
				return "", fmt.Errorf("Không thể chuyển kiểu dữ liệu")
			}
			result += "_" + strconv.Itoa(intVar)
		case string:
			stringVar := fmt.Sprintf("%v", key)
			result += "_" + ToRawText(stringVar)
		case float32:
			float32Var, ok := key.(float32)
			if !ok {
				return "", fmt.Errorf("Không thể chuyển kiểu dữ liệu")
			}
			result += "_" + fmt.Sprintf("%f", float32Var)
		case float64:
			float64Var, ok := key.(float64)
			if !ok {
				return "", fmt.Errorf("Không thể chuyển kiểu dữ liệu")
			}
			result += "_" + fmt.Sprintf("%f", float64Var)
		default:
			return "", fmt.Errorf("Không thể chuyển kiểu dữ liệu")
		}
	}
	return result, nil
}

// So sánh độ tương đồng của 2 string nhỏ nhất = 0 lớn nhất = 1
func CompareStringDistance(s1, s2 string) float64 {

	s1Matches := make([]bool, len(s1)) // |s1|
	s2Matches := make([]bool, len(s2)) // |s2|

	var matchingCharacters = 0.0
	var transpositions = 0.0

	// return 0 if either one is empty string
	if len(s1) == 0 || len(s2) == 0 {
		return 0 // no similarity
	}

	// return 1 if both strings are empty
	if len(s1) == 0 && len(s2) == 0 {
		return 1 // exact match
	}

	if strings.EqualFold(s1, s2) { // case insensitive
		return 1 // exact match
	}

	// Two characters from s1 and s2 respectively,
	// are considered matching only if they are the same and not farther than
	// [ max(|s1|,|s2|) / 2 ] - 1
	matchDistance := len(s1)
	if len(s2) > matchDistance {
		matchDistance = len(s2)
	}
	matchDistance = matchDistance/2 - 1

	// Each character of s1 is compared with all its matching characters in s2
	for i := range s1 {
		low := i - matchDistance
		if low < 0 {
			low = 0
		}
		high := i + matchDistance + 1
		if high > len(s2) {
			high = len(s2)
		}
		for j := low; j < high; j++ {
			if s2Matches[j] {
				continue
			}
			if s1[i] != s2[j] {
				continue
			}
			s1Matches[i] = true
			s2Matches[j] = true
			matchingCharacters++
			break
		}
	}

	if matchingCharacters == 0 {
		return 0 // no similarity, exit early
	}

	// Count the transpositions.
	// The number of matching (but different sequence order) characters divided by 2 defines the number of transpositions
	k := 0
	for i := range s1 {
		if !s1Matches[i] {
			continue
		}
		for !s2Matches[k] {
			k++
		}
		if s1[i] != s2[k] {
			transpositions++ // increase transpositions
		}
		k++
	}
	transpositions /= 2

	weight := (matchingCharacters/float64(len(s1)) + matchingCharacters/float64(len(s2)) + (matchingCharacters-transpositions)/matchingCharacters) / 3

	//  the length of common prefix at the start of the string up to a maximum of four characters
	l := 0

	// is a constant scaling factor for how much the score is adjusted upwards for having common prefixes.
	//The standard value for this constant in Winkler's work is {\displaystyle p=0.1}p=0.1
	p := 0.1

	// make it easier for s1[l] == s2[l] comparison
	s1 = strings.ToLower(s1)
	s2 = strings.ToLower(s2)

	if weight > 0.7 {
		for (l < 4) && s1[l] == s2[l] {
			l++
		}

		weight = weight + float64(l)*p*(1-weight)
	}

	return weight
}
