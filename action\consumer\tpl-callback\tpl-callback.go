package sync_data

import (
	"crypto/hmac"
	"crypto/sha1"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action/consumer/backup"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// TPLCallbackConsume topic
func (j *ExecutorJob) tplCallbackConsume() {
	j.Job.SetTopicConsumer(tplCallbackTopic, func(item *job.JobItem) (err error) {
		var streamByte []byte
		streamByte, err = bson.Marshal(item.Data)
		if err != nil {
			return
		}

		callbackInfo := model.Callback{}
		err = bson.Unmarshal(streamByte, &callbackInfo)
		if err != nil {
			log.Println("Callback Main Unmarshal", err)
			return
		}

		err = InitShippingOrder(callbackInfo)
		if err != nil {
			if item.FailCount%10 == 0 {
				syncDataByte, _ := json.Marshal(callbackInfo)
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "INIT_SHIPPING_ORDER_ERROR",
					Title:   string(syncDataByte),
					Message: "================================ " + err.Error(),
				})
			}

			if item.FailCount > 10 {
				backup.Push(callbackInfo, "SYNC_SHIPPING_ORDER_ERROR")
				return nil
			}

			time.Sleep(1000 * time.Millisecond)
			return
		}

		return nil
	})
}

func InitShippingOrder(callbackInfo model.Callback) (err error) {
	if callbackInfo.Status == nil || callbackInfo.CreatedSource == nil {
		backup.Push(callbackInfo, "ERROR_DATA_CALLBACK")
		return
	}

	switch *callbackInfo.Status {
	case enum.TPLCallbackStatus.CREATE_FAIL:
		return nil
	case enum.TPLCallbackStatus.CREATE_ORDER:
		return nil
	}

	isOrderReturn := false
	if strings.Contains(callbackInfo.SO, "thu hồi") {
		isOrderReturn = true
	}

	if strings.Contains(callbackInfo.SO, "R") && !strings.HasPrefix(callbackInfo.SO, "RO") {
		isOrderReturn = true
	}

	saleOrderCode := callbackInfo.SO
	shippingOrderQuery := model.ShippingOrderDB.QueryOne(bson.M{
		"reference_code": saleOrderCode,
	})

	if shippingOrderQuery.Status == common.APIStatus.Ok {
		shippingOrder := shippingOrderQuery.Data.([]*model.ShippingOrder)[0]
		if shippingOrder.ShippingType != nil && *shippingOrder.ShippingType == enum.ShippingOrderType.RETURN {
			return handleReturnCallback(&callbackInfo, shippingOrder)
		}

		if shippingOrder.ShippingType != nil &&
			*shippingOrder.ShippingType == enum.ShippingOrderType.CS {
			return nil
		}

		if shippingOrder.ShippingType != nil &&
			*shippingOrder.ShippingType == enum.ShippingOrderType.EO {
			return handleEOCallback(&callbackInfo, shippingOrder)
		}

		if shippingOrder.ShippingType != nil &&
			*shippingOrder.ShippingType == enum.ShippingOrderType.OPM {
			return handleOPMCallback(&callbackInfo, shippingOrder)
		}
	}

	//parseSO := strings.Split(saleOrderCode, " ")
	//for _, v := range parseSO {
	//	v = strings.TrimSpace(strings.ToUpper(v))
	//	if strings.HasPrefix(v, "SO") {
	//		splitV := strings.Split(v, "-")
	//		for _, l := range splitV {
	//			if strings.HasPrefix(l, "SO") {
	//				saleOrderCode = l
	//			}
	//		}
	//	}
	//}

	if strings.HasPrefix(saleOrderCode, "0") || strings.HasPrefix(saleOrderCode, "O") {
		saleOrderCode = "SO" + saleOrderCode[1:]
	}

	if saleOrderCode == "" {
		return
	}

	if isOrderReturn {
		switch *callbackInfo.Status {
		case enum.TPLCallbackStatus.READY_TO_PICK:
			callbackInfo.Status = &enum.TPLCallbackStatus.RETURN
			break
		case enum.TPLCallbackStatus.PICKED:
			callbackInfo.Status = &enum.TPLCallbackStatus.RETURNING
			break
		case enum.TPLCallbackStatus.DELIVERED:
			callbackInfo.Status = &enum.TPLCallbackStatus.RETURNED
			break
		default:
		}
	}

	var saleOrder *model.SaleOrder
	saleOrder, err = client.Services.WarehouseCoreClient.GetSaleOrder(saleOrderCode, "")
	if err != nil {
		_ = backup.Push(callbackInfo, "GET_SALE_ORDER_ERROR")
		return nil
	}

	var shippingOrderInfo *model.ShippingOrder
	if shippingOrderQuery.Status != common.APIStatus.Ok || shippingOrderQuery.Data == nil {
		shippingOrderInfo = &model.ShippingOrder{
			ReferenceCode: saleOrderCode,
			Height:        30,
			Width:         40,
			Length:        50,
		}

		if saleOrder.CustomerInfos.Delivery != nil {
			shippingOrderInfo.CustomerDistrictName = saleOrder.CustomerInfos.Delivery.District
			shippingOrderInfo.CustomerShippingAddress = saleOrder.CustomerInfos.Delivery.Address
			shippingOrderInfo.CustomerProvinceName = saleOrder.CustomerInfos.Delivery.Province
			shippingOrderInfo.CustomerWardName = saleOrder.CustomerInfos.Delivery.Ward
		} else {
			shippingOrderInfo.CustomerDistrictName = saleOrder.CustomerInfos.District
			shippingOrderInfo.CustomerShippingAddress = saleOrder.CustomerInfos.Address
			shippingOrderInfo.CustomerProvinceName = saleOrder.CustomerInfos.Province
			shippingOrderInfo.CustomerWardName = saleOrder.CustomerInfos.Ward
		}

		shippingOrderInfo.CustomerName = saleOrder.CustomerInfos.Name
		shippingOrderInfo.CustomerEmail = saleOrder.CustomerInfos.Email
		shippingOrderInfo.CustomerPhone = saleOrder.CustomerInfos.Phone
		shippingOrderInfo.CustomerCode = strconv.Itoa(saleOrder.CustomerInfos.Code)

		if saleOrder.Type == "RETURN" && saleOrder.CustomerInfos.Delivery != nil && !saleOrder.CustomerInfos.Delivery.IsDropOffAtWarehouse {
			shippingOrderInfo.FeeCollectMethod = &enum.FeeCollectMethod.RECEIVER_PAY
		}
	} else {
		shippingOrderInfo = shippingOrderQuery.Data.([]*model.ShippingOrder)[0]
	}

	if saleOrder.CustomerInfos != nil && saleOrder.CustomerInfos.Delivery != nil {
		shippingOrderInfo.CustomerWardCode = saleOrder.CustomerInfos.Delivery.WardCode
		shippingOrderInfo.CustomerProvinceCode = saleOrder.CustomerInfos.Delivery.ProvinceCode
		shippingOrderInfo.CustomerDistrictCode = saleOrder.CustomerInfos.Delivery.DistrictCode
	}

	if shippingOrderInfo.CustomerProvinceCode == "" && shippingOrderInfo.CustomerProvinceName != "" {
		provinces, err := client.Services.WarehouseCoreClient.GetProvince(shippingOrderInfo.CustomerProvinceName)
		if err != nil {
			log.Println("Get province err ", shippingOrderInfo.CustomerProvinceName, err)
		}

		if len(provinces) > 0 {
			shippingOrderInfo.CustomerProvinceCode = provinces[0].Code
		}
	}

	if shippingOrderInfo.CustomerDistrictCode == "" && shippingOrderInfo.CustomerDistrictName != "" {
		var districts []*model.District
		districts, err = client.Services.WarehouseCoreClient.GetDistrictByProvinceCode(shippingOrderInfo.CustomerProvinceCode, shippingOrderInfo.CustomerDistrictName)
		if err != nil {
			log.Println("Get district err ", shippingOrderInfo.CustomerDistrictName, err)
		}

		if len(districts) > 0 {
			shippingOrderInfo.CustomerDistrictCode = districts[0].Code
		}
	}

	if shippingOrderInfo.CustomerWardCode == "" && shippingOrderInfo.CustomerWardName != "" {
		wards, err := client.Services.WarehouseCoreClient.GetWard(shippingOrderInfo.CustomerWardName, "", shippingOrderInfo.CustomerDistrictCode, "")
		if err != nil {
			log.Println("Get ward err ", shippingOrderInfo.CustomerWardName, err)
		}

		if len(wards) > 0 {
			shippingOrderInfo.CustomerWardCode = wards[0].Code
		}
	}

	if callbackInfo.ActionTime != nil {
		shippingOrderInfo.ActionTime = callbackInfo.ActionTime.Unix()
	} else {
		shippingOrderInfo.ActionTime = time.Now().Unix()
	}

	if len(callbackInfo.ExtraCallback) > 0 {
		if shippingOrderInfo.ExtraInfo == nil {
			shippingOrderInfo.ExtraInfo = map[string]interface{}{}
		}

		shippingOrderInfo.ExtraInfo = utils.MergeMaps(shippingOrderInfo.ExtraInfo, callbackInfo.ExtraCallback)
	}

	// Xử lí trường hợp book tay
	if shippingOrderInfo.TplCode != nil && callbackInfo.CreatedSource != nil && *shippingOrderInfo.TplCode != *callbackInfo.CreatedSource {
		carrier := model.CarrierDB.QueryOne(bson.M{
			"carrier_code": *callbackInfo.CreatedSource,
		})

		if carrier.Status != common.APIStatus.Ok || carrier.Data == nil {
			return
		}

		carrierInfo := carrier.Data.([]*model.Carrier)[0]

		shippingOrderInfo.TplCode = callbackInfo.CreatedSource
		shippingOrderInfo.TplName = carrierInfo.CarrierName
	}

	is3PLReason := false
	isGHTKFail :=
		enum.TplCodeFailGHTK[callbackInfo.TPLStatus] == enum.TPLCallbackStatus.DELIVERY_FAIL ||
			enum.TplCodeFailGHTK[callbackInfo.TPLStatus] == enum.TPLCallbackStatus.PICK_FAIL
	if shippingOrderInfo.TplCode != nil && *shippingOrderInfo.TplCode == enum.Partner.GHTK && isGHTKFail {
		ghtkFailReason := &model.FailReason{}
		is3PLReason = true
		ghtkFailReason.ReasonCode = callbackInfo.ReasonCode
		ghtkFailReason.CreatedAt = time.Now()
		ghtkFailReason.FailTime = len(shippingOrderInfo.FailReasons) + 1
		ghtkFailReason.FormattedReason = FormatFailReasonGHTK(callbackInfo.ReasonCode, len(shippingOrderInfo.FailReasons)+1, "")

		if enum.TplCodeFailGHTK[callbackInfo.TPLStatus] == enum.TPLCallbackStatus.DELIVERY_FAIL {
			ghtkFailReason.ReasonType = enum.FailReasonType.DELIVERY_FAIL
		}
		if enum.TplCodeFailGHTK[callbackInfo.TPLStatus] == enum.TPLCallbackStatus.PICK_FAIL {
			ghtkFailReason.ReasonType = enum.FailReasonType.PICK_FAIL
		}

		// Check consume dư event:
		// - Nếu status của shipping order khác với status callback thì cập nhật lí do thất bại
		if shippingOrderInfo.Status != nil && callbackInfo.Status != nil &&
			*shippingOrderInfo.Status != *callbackInfo.Status {
			shippingOrderInfo.FailReasons = append(shippingOrderInfo.FailReasons, *ghtkFailReason)
		}
	}

	isVTPFail := enum.TplCodeFailVTP[callbackInfo.TPLStatus] == enum.TPLCallbackStatus.DELIVERY_FAIL
	if shippingOrderInfo.TplCode != nil && *shippingOrderInfo.TplCode == enum.Partner.VIETTEL_POST && isVTPFail {
		vtpFailReason := &model.FailReason{}
		is3PLReason = true
		vtpFailReason.ReasonType = enum.FailReasonType.DELIVERY_FAIL
		vtpFailReason.ReasonCode = callbackInfo.TPLStatus
		vtpFailReason.CreatedAt = time.Now()
		vtpFailReason.FailTime = len(shippingOrderInfo.FailReasons) + 1
		vtpFailReason.FormattedReason = FormatFailReasonVTP(callbackInfo.TPLStatusName, len(shippingOrderInfo.FailReasons)+1, "")

		// Check consume dư event:
		// - Nếu status của shipping order khác với status callback thì cập nhật lí do thất bại
		if shippingOrderInfo.Status != nil && callbackInfo.Status != nil &&
			*shippingOrderInfo.Status != *callbackInfo.Status {
			shippingOrderInfo.FailReasons = append(shippingOrderInfo.FailReasons, *vtpFailReason)
		}
	}

	if !is3PLReason {
		// Không dùng luồng sync để update fail reason nội bộ
		shippingOrderInfo.FailReasons = nil
	}

	shippingOrderInfo.DeliveryAmount = saleOrder.DeliveryAmount
	shippingOrderInfo.Weight = callbackInfo.Weight
	shippingOrderInfo.NumPackage = callbackInfo.NumPackage
	shippingOrderInfo.TrackingCode = callbackInfo.TPLCode
	shippingOrderInfo.Status = callbackInfo.Status

	if shippingOrderInfo.PaymentMethod == nil {
		shippingOrderInfo.CODAmount = callbackInfo.COD
		shippingOrderInfo.FeeAmount = callbackInfo.TotalFee
		shippingOrderInfo.PaymentMethod = saleOrder.PaymentMethod
		shippingOrderInfo.TotalAmount = saleOrder.TotalAmount
	}

	shippingOrderInfo.Reason = callbackInfo.Reason

	if callbackInfo.RescheduledDeliveryTime != 0 {
		shippingOrderInfo.RescheduledDeliveryTime = callbackInfo.RescheduledDeliveryTime
	}

	if callbackInfo.RescheduledPickupTime != 0 {
		shippingOrderInfo.RescheduledPickupTime = callbackInfo.RescheduledPickupTime
	}

	// check data update by status
	switch *callbackInfo.Status {
	case enum.TPLCallbackStatus.DELIVERED:
		if callbackInfo.ActionTime != nil {
			shippingOrderInfo.DeliveredTime = callbackInfo.ActionTime.Unix()
		} else {
			shippingOrderInfo.DeliveredTime = time.Now().Unix()
		}
		break
	case enum.TPLCallbackStatus.PICKED:
		if shippingOrderInfo.PickedTime == 0 {
			if callbackInfo.ActionTime != nil {
				shippingOrderInfo.PickedTime = callbackInfo.ActionTime.Unix()
			} else {
				shippingOrderInfo.PickedTime = time.Now().Unix()
			}
		}
		break
	case enum.TPLCallbackStatus.STORING:
		if shippingOrderInfo.PickedTime == 0 {
			if callbackInfo.ActionTime != nil {
				shippingOrderInfo.PickedTime = callbackInfo.ActionTime.Unix()
			} else {
				shippingOrderInfo.PickedTime = time.Now().Unix()
			}
		}

		// Update stored time
		if callbackInfo.ActionTime != nil {
			shippingOrderInfo.StoredAtLMHubTime = callbackInfo.ActionTime.Unix()
		} else {
			shippingOrderInfo.StoredAtLMHubTime = time.Now().Unix()
		}
		break
	case enum.TPLCallbackStatus.RETURN:
		configLeadTime, _ := utils.ParseLeadTimeConfig(string(*callbackInfo.CreatedSource), &model.Address{
			Name:         shippingOrderInfo.CustomerName,
			Address:      shippingOrderInfo.CustomerShippingAddress,
			Phone:        shippingOrderInfo.CustomerPhone,
			Email:        shippingOrderInfo.CustomerEmail,
			WardName:     shippingOrderInfo.CustomerWardName,
			DistrictName: shippingOrderInfo.CustomerDistrictName,
			ProvinceName: shippingOrderInfo.CustomerProvinceName,
			WardCode:     shippingOrderInfo.CustomerWardCode,
			DistrictCode: shippingOrderInfo.CustomerDistrictCode,
			ProvinceCode: shippingOrderInfo.CustomerProvinceCode,
		}, &model.Address{
			Name:         shippingOrderInfo.FromCustomerName,
			Address:      shippingOrderInfo.FromCustomerAddress,
			Phone:        shippingOrderInfo.FromCustomerPhone,
			Email:        shippingOrderInfo.FromCustomerName,
			WardName:     shippingOrderInfo.FromWardName,
			DistrictName: shippingOrderInfo.FromDistrictName,
			ProvinceName: shippingOrderInfo.FromProvinceName,
			WardCode:     shippingOrderInfo.FromWardCode,
			DistrictCode: shippingOrderInfo.FromDistrictCode,
			ProvinceCode: shippingOrderInfo.FromProvinceCode,
		}, enum.LeadTimeConfigType.RETURNING)

		if configLeadTime != nil {
			leadTimePicking := callbackInfo.ActionTime.Add(time.Second * time.Duration(configLeadTime.CommitmentTime))
			shippingOrderInfo.EstimatePickingTime = leadTimePicking.Unix()
		}
		break
	case enum.TPLCallbackStatus.RETURNED:
		if callbackInfo.ActionTime != nil {
			shippingOrderInfo.ReceiveReturnTime = callbackInfo.ActionTime.Unix()
		} else {
			shippingOrderInfo.ReceiveReturnTime = time.Now().Unix()
		}
		break
	}
	// This flow not controlling driver activities
	shippingOrderInfo.DriverActivities = nil
	shippingOrderInfo.DeliveryLeadTime = nil
	// Update sale order
	result := model.ShippingOrderDB.UpdateOne(
		&bson.M{
			"reference_code": saleOrderCode,
		},
		shippingOrderInfo,
		&options.FindOneAndUpdateOptions{
			Upsert: &enum.True,
		},
	)

	if result.Status != common.APIStatus.Ok {
		return fmt.Errorf(result.Message)
	}

	if !isOrderReturn {
		// update hub status delivering
		err = updateHubOrderStatus(shippingOrderInfo, callbackInfo.CreatedSource)

		if err != nil {
			return
		}
	}

	return
}

func (j *ExecutorJob) updateStatusTPLCallback() {
	j.Job.SetTopicConsumer(syncStatusTPLCallbackTopic, func(item *job.JobItem) (err error) {
		var dataByte []byte
		dataByte, err = bson.Marshal(item.Data)
		if err != nil {
			return
		}

		syncData := request.Callback{}
		err = bson.Unmarshal(dataByte, &syncData)
		if err != nil {
			return
		}

		err = client.Services.TplCallbackClient.UpdateCallback(syncData)
		if err != nil {
			if item.FailCount%100 == 0 {
				syncDataByte, _ := json.Marshal(syncData)
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "UPDATE_CALLBACK_STATUS_ERROR",
					Title:   string(syncDataByte),
					Message: "================================ " + err.Error(),
				})
			}

			if item.FailCount > 200 {
				return nil
			}

			time.Sleep(1000 * time.Millisecond)
			return
		}

		return nil
	})
}

func retryCreateHubOrder(maxAttempt, sleepTime int, input *model.ShippingOrder) (err []string) {
	for i := 0; i < maxAttempt; i++ {
		if i > 0 {
			time.Sleep(time.Duration(sleepTime) * time.Second)
			sleepTime *= 2
		}
		hubOrder := utils.BuildHubOrderByShippingOrder(input)
		createdHubOrderRaw := model.HUBShippingOrderDB.Create(hubOrder)
		if createdHubOrderRaw.Status == common.APIStatus.Ok {
			return nil
		}
		err = append(err, createdHubOrderRaw.Message)
	}
	return
}

func retryQueryCurrentHub(maxAttempt, sleepTime int, referenceCode string) *model.ShippingOrder {
	for i := 0; i < maxAttempt; i++ {
		if i > 0 {
			time.Sleep(time.Duration(sleepTime) * time.Second)
			sleepTime *= 2
		}

		shippingOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
			"reference_code": referenceCode,
		})

		if shippingOrderRaw.Status != common.APIStatus.Ok {
			return nil
		}

		shippingOrder := shippingOrderRaw.Data.([]*model.ShippingOrder)[0]
		if shippingOrder.CurrentHub != "" {
			return shippingOrder
		}
	}

	return nil
}

func updateHubOrderStatus(input *model.ShippingOrder, callbackInfo *enum.PartnerValue) error {
	if input.CurrentHub == "" {
		// Wait for BookShippingService api to update current hub of shipping order
		maxAttempt := 3
		sleepTime := 5
		shippingOrder := retryQueryCurrentHub(maxAttempt, sleepTime, input.ReferenceCode)
		if shippingOrder == nil {
			defer client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
				CmdType: "ERROR_EMPTY_CURRENT_HUB",
				Title:   fmt.Sprintf("HSO_CODE[%v]", input.ReferenceCode),
				Message: "Current hub rỗng hoặc shipping order chưa tồn tại",
			})

			// Nếu book shipping KHÔNG thành công sẽ tạo shipping order với trạng thái CREATE_FAIL và sẽ không tạo hub order
			// và nếu book tay lại lần nữa thì khi 3pl gọi callback sẽ không có current hub trong shipping order cũng như không có hub order để cập nhật
			// lúc này tạo hub order mới cũng như gán lại current hub cho shipping order để có thể bàn giao cho 3pl

			input.CurrentHub = chooseHubCodeFromRefCode(input.ReferenceCode)
			if input.CurrentHub == "" {
				client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "INVALID_REFERENCE_CODE",
					Title:   fmt.Sprintf("HSO_CODE[%v]", input.ReferenceCode),
					Message: "Mã đơn không hợp lệ",
				})
				return nil
			}

			if input.TplCode == nil || *input.TplCode == "" {
				client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "EMPTY_CARRIER_CODE",
					Title:   fmt.Sprintf("HSO_CODE[%v]", input.ReferenceCode),
					Message: "Không tìm thấy nhà vận chuyển cho đơn",
				})
				return nil
			}

			if _, ok := conf.Config.DefaultPartnerInfo[*input.TplCode]; !ok {
				client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "NOT_FOUND_DEFAULT_CARRIER",
					Title:   fmt.Sprintf("HSO_CODE[%v]", input.ReferenceCode),
					Message: "Không tìm thấy nhà vận chuyển mặc định cho đơn với mã nvc: " + string(*input.TplCode),
				})
				return nil
			}

			input.TplName = conf.Config.DefaultPartnerInfo[*input.TplCode].TplName
			input.TplServiceId = conf.Config.DefaultPartnerInfo[*input.TplCode].TplServiceId
			input.TplCode = conf.Config.DefaultPartnerInfo[*input.TplCode].TplCode

			err := retryCreateHubOrder(maxAttempt, sleepTime, input)
			if err != nil {
				errString := strings.Join(err, " | ")
				return fmt.Errorf("Không thể tạo mới hub order, danh sách lỗi: %v", errString)
			}

			_ = model.ShippingOrderDB.UpdateOne(&model.ShippingOrder{
				ReferenceCode: input.ReferenceCode,
			}, &bson.M{
				"current_hub":    input.CurrentHub,
				"scope":          []string{input.CurrentHub},
				"tpl_service_id": input.TplServiceId,
				"tpl_code":       input.TplCode,
				"tpl_name":       input.TplName,
			})

			return nil
		}
		input.CurrentHub = shippingOrder.CurrentHub
	}

	hubOrdersRaw := model.HUBShippingOrderDB.Query(bson.M{
		"reference_code": input.ReferenceCode,
	}, 0, 100, nil)

	// if there is no hub order in db related to reference_code -> create one
	if hubOrdersRaw.Status != common.APIStatus.Ok || hubOrdersRaw.Data == nil {
		maxAttempt := 3
		sleepTime := 5
		err := retryCreateHubOrder(maxAttempt, sleepTime, input)
		if err == nil {
			return nil
		}
		errString := strings.Join(err, " | ")
		return fmt.Errorf("Không thể tạo mới hub order, danh sách lỗi: %v", errString)
	}

	hubOrders := hubOrdersRaw.Data.([]*model.HubShippingOrder)
	var hubOrderInfo *model.HubShippingOrder
	isContainActionIn := false
	for _, hubOrder := range hubOrders {
		if hubOrder.HUBCode == input.CurrentHub {
			hubOrderInfo = hubOrder
		}

		if hubOrder.Action == "IN" {
			isContainActionIn = true
		}
	}

	if !isContainActionIn {
		carrier := model.CarrierDB.QueryOne(bson.M{
			"carrier_code": callbackInfo,
		})

		if carrier.Status != common.APIStatus.Ok || carrier.Data == nil {
			return fmt.Errorf("Sai mã nhà vận chuyển")
		}
		carrierInfo := carrier.Data.([]*model.Carrier)[0]

		if !*carrierInfo.IsInternal {
			return nil
		}
	}

	// Check nil return
	if hubOrderInfo == nil || hubOrderInfo.Status == nil {
		defer client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
			CmdType: "ERROR_EMPTY_CURRENT_HUB",
			Title:   fmt.Sprintf("HSO_CODE[%v]", input.ReferenceCode),
			Message: "Không lấy được thông tin hubOrderInfo",
		})
		return nil
	}

	// Nếu loại đơn là luân chuyển nội bộ thì skip update hub order nếu chuyển sang trạng thái DELIVERED
	if input.ShippingType != nil &&
		(*input.ShippingType == enum.ShippingOrderType.INTERNAL_TRANS || *input.ShippingType == enum.ShippingOrderType.FMPO) &&
		input.Status != nil &&
		*input.Status == enum.TPLCallbackStatus.DELIVERED {
		return nil
	}

	updateData := &model.HubShippingOrder{}
	updateData.TrackingCode = input.TrackingCode
	updateData.ActionTime = input.ActionTime
	updateData.TplCode = input.TplCode
	updateData.Reason = input.Reason

	logs := model.HubShippingOrder{
		ActionTime:    input.ActionTime,
		Status:        hubOrderInfo.Status,
		ExtraInfo:     hubOrderInfo.ExtraInfo,
		LastUpdatedBy: hubOrderInfo.LastUpdatedBy,
	}
	updateData.Logs = append(hubOrderInfo.Logs, logs)

	switch *input.Status {
	case enum.TPLCallbackStatus.DELIVERING:
		updateData.Status = &enum.HubShippingOrderStatus.DELIVERING
		break
	case enum.TPLCallbackStatus.DELIVERY_FAIL:
		updateData.Status = &enum.HubShippingOrderStatus.DELIVERY_FAIL
		break
	case enum.TPLCallbackStatus.CANCEL:
		if hubOrderInfo.Type != nil && (*hubOrderInfo.Type == enum.HubOrderType.TRANSPORTING ||
			*hubOrderInfo.Type == enum.HubOrderType.DELIVERY) {
			updateData.Status = &enum.HubShippingOrderStatus.RETURN
		}

		warehouseHub := chooseHubCodeFromRefCode(hubOrderInfo.ReferenceCode)
		if warehouseHub == hubOrderInfo.HUBCode {
			updateData.Status = &enum.HubShippingOrderStatus.RETURNED
		}

		if strings.Contains(hubOrderInfo.ReferenceCode, "LO") {
			updateData.Status = &enum.HubShippingOrderStatus.CANCEL
		}
		break
	case enum.TPLCallbackStatus.CANCEL_DELIVERY:
		updateData.Status = &enum.HubShippingOrderStatus.DELIVERY_FAIL
		break
	case enum.TPLCallbackStatus.DELIVERED:
		updateData.Status = &enum.HubShippingOrderStatus.DELIVERED
		break
	case enum.TPLCallbackStatus.RETURN:
		updateData.Status = &enum.HubShippingOrderStatus.RETURN
		break
	case enum.TPLCallbackStatus.COMPLETED:
		updateData.Status = &enum.HubShippingOrderStatus.COMPLETED
		now := time.Now()
		updateData.CompletedTime = now.Unix()
		break
	case enum.TPLCallbackStatus.RETURNED:
		getHubResult := model.HubDB.QueryOne(bson.M{
			"code": input.CurrentHub,
		})
		if getHubResult.Status != common.APIStatus.Ok {
			break
		}
		hub := getHubResult.Data.([]*model.Hub)[0]
		if hub.WarehouseReferenceCode != "" {
			updateData.Status = &enum.HubShippingOrderStatus.RETURNED
		}
		break
	default:
		return nil
	}

	var resultUpdateHubOrder *common.APIResponse
	afterOption := options.After
	resultUpdateHubOrder = model.HUBShippingOrderDB.UpdateOne(&model.HubShippingOrder{
		ReferenceCode: input.ReferenceCode,
		Action:        "IN",
	}, updateData,
		&options.FindOneAndUpdateOptions{
			ReturnDocument: &afterOption,
		})

	if resultUpdateHubOrder.Status != common.APIStatus.Ok {
		return fmt.Errorf("%v -%v", resultUpdateHubOrder.Status, resultUpdateHubOrder.Message)
	}
	hubOrder := resultUpdateHubOrder.Data.([]*model.HubShippingOrder)[0]
	// Remove tiền nhận khi completed đơn tại hub
	if *hubOrder.Status == enum.HubShippingOrderStatus.COMPLETED &&
		*hubOrderInfo.Type != enum.HubOrderType.PICKUP && hubOrderInfo.CODAmount > 0 {
		hubRaw := model.HubDB.QueryOne(bson.M{
			"code": hubOrderInfo.HUBCode,
		})
		if hubRaw.Status != common.APIStatus.Ok {
			_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
				CmdType: "NOT_FOUND_HUB",
				Title:   fmt.Sprintf("Not found hub: [%v]", hubOrderInfo.HUBCode),
				Message: "Không tìm thấy hub để sync trạng thái",
			})
			return nil
		}

		hub := hubRaw.Data.([]*model.Hub)[0]
		if hub.LimitStatus != "ACTIVE" {
			return nil
		}

		filter := bson.M{}
		filter["status"] = bson.M{"$in": []string{string(enum.HubShippingOrderStatus.DELIVERED), string(enum.HubShippingOrderStatus.COD_COLLECTED)}}
		filter["hub_code"] = hubOrderInfo.HUBCode
		totalReceivedAmount := 0.0
		hubShippingOrderQuery := model.HUBShippingOrderDB.Query(filter, 0, 1000, nil)
		if hubShippingOrderQuery.Status == common.APIStatus.Ok {
			hubShippingOrders := hubShippingOrderQuery.Data.([]*model.HubShippingOrder)
			for _, val := range hubShippingOrders {
				totalReceivedAmount += val.CODAmount
			}
		}
		updateResult := model.HubDB.UpdateOne(bson.M{
			"code":         hubOrderInfo.HUBCode,
			"limit_status": "ACTIVE",
		}, bson.M{"total_received_amount": float64(totalReceivedAmount)})

		if updateResult.Status == common.APIStatus.Error {
			_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
				CmdType: "ERROR_CANT_UPDATE_HUB_TOTAL_RECEIVED_AMOUNT",
				Title:   fmt.Sprintf(" HUB-[%v] REFERENCE_CODE-[%v] AMOUNT-[%0.f]", hubOrderInfo.HUBCode, hubOrderInfo.ReferenceCode, hubOrderInfo.CODAmount),
				Message: "Cập nhật số tiền nhận của Hub thất bại",
			})
		}
	}

	if hubOrder.HUBCode != input.CurrentHub {
		defer client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
			CmdType: "ERROR_CURRENT_HUB_FAIL",
			Title:   fmt.Sprintf("[%v] - SO_HUB [%v] - HSO_CODE[%v]", input.ReferenceCode, hubOrder.HUBCode, input.CurrentHub),
			Message: "Sai hub code",
		})

		_ = model.ShippingOrderDB.UpdateOne(&model.ShippingOrder{
			ReferenceCode: input.ReferenceCode,
		}, &bson.M{
			"current_hub": hubOrder.HUBCode,
		})
	}
	return nil
}

func (j *ExecutorJob) postStatusTPLCallback() {
	j.Job.SetTopicConsumer(postStatusTPLCallbackTopic, func(item *job.JobItem) (err error) {
		var dataByte []byte
		dataByte, err = bson.Marshal(item.Data)
		if err != nil {
			return
		}
		syncData := request.Callback{}
		err = bson.Unmarshal(dataByte, &syncData)
		if err != nil {
			return
		}
		err = client.Services.TplCallbackClient.CreateCallback(syncData)
		if err != nil {
			if item.FailCount%100 == 0 {
				syncDataByte, _ := json.Marshal(syncData)
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "CREATE_CALLBACK_STATUS_ERROR",
					Title:   string(syncDataByte),
					Message: "================================ " + err.Error(),
				})
			}

			if item.FailCount > 200 {
				return nil
			}

			time.Sleep(1000 * time.Millisecond)
			return
		}
		return nil
	})
}

func (j *ExecutorJob) assignCallback() {
	j.Job.SetTopicConsumer(assignTPLCallbackTopic, func(item *job.JobItem) (err error) {
		var dataByte []byte
		dataByte, err = bson.Marshal(item.Data)
		if err != nil {
			return
		}
		syncData := request.AssignCallback{}
		err = bson.Unmarshal(dataByte, &syncData)
		if err != nil {
			return
		}
		err = client.Services.TplCallbackClient.AssignCallback(syncData.ListReferenceCode, syncData.AccountId, syncData.AccountName)
		if err != nil {
			if item.FailCount%100 == 0 {
				syncDataByte, _ := json.Marshal(syncData)
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "CREATE_CALLBACK_STATUS_ERROR",
					Title:   string(syncDataByte),
					Message: "================================ " + err.Error(),
				})
			}

			if item.FailCount > 200 {
				return nil
			}

			time.Sleep(1000 * time.Millisecond)
			return
		}
		return nil
	})
}

func (j *ExecutorJob) sendCallback() {
	j.Job.SetTopicConsumer(sendCallbackTopic, func(item *job.JobItem) (err error) {
		defer utils.RecoverFromPanic(item)
		var dataByte []byte
		dataByte, err = bson.Marshal(item.Data)
		if err != nil {
			return
		}
		callback := request.Callback{}
		err = bson.Unmarshal(dataByte, &callback)

		if callback.Status == nil {
			return &common.Error{
				Message: "Status không được để trống",
			}
		}

		partner := client.GetWebhookClient(callback.PartnerCode)
		if partner == nil {
			return &common.Error{
				Message: "Mã đối tác không hợp lệ",
			}
		}

		isUrl := utils.IsUrl(partner.Config.Url)
		if !isUrl {
			return &common.Error{
				Message: "Url không hợp lệ",
			}
		}

		current := time.Now()
		callback.ActionTime = &current
		err = BuildCallbackByStatus(&callback)
		if err != nil {
			return err
		}

		err = partner.SendCallbackToPartner(callback)
		if err != nil {
			return err
		}

		return nil
	})
}

func (j *ExecutorJob) assignShipperCallback() {
	j.Job.SetTopicConsumer(assignShipperCallbackTopic, func(item *job.JobItem) (err error) {
		defer utils.RecoverFromPanic(item)

		var dataByte []byte
		dataByte, err = bson.Marshal(item.Data)
		if err != nil {
			return
		}
		assignCallback := request.AssignCallback{}
		err = bson.Unmarshal(dataByte, &assignCallback)
		if err != nil {
			return
		}

		err = client.Services.TplCallbackClient.AssignCallback(assignCallback.ListReferenceCode, assignCallback.AccountId, assignCallback.AccountName)
		if err != nil {
			return err
		}

		// Verify carefully before send callback
		// Because if it fails in the middle of the array, retry will lead to duplicate callback
		shippingOrders := []*model.ShippingOrder{}
		for _, ref := range assignCallback.ListReferenceCode {
			shippingOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
				"reference_code": ref,
			})

			if shippingOrderRaw.Status != common.APIStatus.Ok {
				syncDataByte, _ := json.Marshal(assignCallback)
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "NOT_FOUND_SHIPPING_ORDER",
					Title:   string(syncDataByte),
					Message: "Không tìm thấy shipping order cho đơn: " + ref,
				})
				return
			}
			shippingOrder := shippingOrderRaw.Data.([]*model.ShippingOrder)[0]
			if shippingOrder.PartnerCode == "" {
				continue
			}

			partner := client.GetWebhookClient(shippingOrder.PartnerCode)
			if partner == nil {
				syncDataByte, _ := json.Marshal(shippingOrder)
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "NOT_FOUND_PARTNER",
					Title:   string(syncDataByte),
					Message: "Không tìm thấy đối tác",
				})
				continue
			}

			if partner.Config.Url == "" {
				continue
			}

			shippingOrders = append(shippingOrders, shippingOrder)
		}

		current := time.Now()
		for _, shippingOrder := range shippingOrders {
			partner := client.GetWebhookClient(shippingOrder.PartnerCode)

			callback := request.Callback{
				Status:        &enum.TPLCallbackStatus.READY_TO_PICK,
				ReferenceCode: shippingOrder.ReferenceCode,
				TrackingCode:  shippingOrder.TrackingCode,
				ActionTime:    &current,
				PartnerCode:   partner.Config.PartnerCode,
				HubCode:       shippingOrder.CurrentHub,
				DriverName:    assignCallback.AccountName,
				DriverId:      assignCallback.AccountId,
			}

			err = BuildCallbackByStatus(&callback)
			if err != nil {
				return err
			}

			err = partner.SendCallbackToPartner(callback)
			if err != nil {
				return err
			}
		}

		return nil
	})
}

func BuildCallbackByStatus(callback *request.Callback) error {
	if callback.Status == nil {
		return fmt.Errorf("Trạng thái callback không được để trống")
	}

	switch *callback.Status {
	case enum.TPLCallbackStatus.INIT:
		callback.StatusName = "Khởi tạo đơn hàng thành công"
		callback.Action = string(enum.TPLCallbackStatus.INIT)
		callback.ActionName = "Đã khởi tạo đơn hàng thành công"

	case enum.TPLCallbackStatus.CREATE_FAIL:
		callback.StatusName = "Gán vận chuyễn thất bại"
		callback.Action = string(enum.TPLCallbackStatus.CREATE_FAIL)
		callback.ActionName = "Gán vận chuyễn thất bại, lí do:" + callback.Reason

	case enum.TPLCallbackStatus.CANCEL:
		callback.StatusName = "Đơn giao đã bị hủy"
		callback.Action = string(enum.TPLCallbackStatus.CANCEL)
		callback.ActionName = "Đơn giao đã bị hủy, lí do:" + callback.Reason

	case enum.TPLCallbackStatus.READY_TO_PICK:
		callback.StatusName = "Chuẩn bị lấy hàng"
		callback.Action = string(enum.TPLCallbackStatus.READY_TO_PICK)
		callback.ActionName = callback.DriverName + " đang trên đường lấy hàng"

	case enum.TPLCallbackStatus.PICKING:
		callback.StatusName = "Đang lấy hàng"
		callback.Action = string(enum.TPLCallbackStatus.PICKING)
		callback.ActionName = "Đang lấy hàng từ " + callback.FromName + " tại địa chỉ " + callback.FromAddress

	case enum.TPLCallbackStatus.PICKED:
		callback.StatusName = "Đã lấy hàng"
		callback.Action = string(enum.TPLCallbackStatus.PICKED)
		callback.ActionName = "Đã lấy hàng từ " + callback.FromName + " tại địa chỉ " + callback.FromAddress

	case enum.TPLCallbackStatus.PICK_FAIL:
		callback.StatusName = "Lấy hàng thất bại"
		callback.Action = string(enum.TPLCallbackStatus.PICK_FAIL)
		callback.ActionName = "Không thể lấy hàng từ " + callback.FromName + " tại địa chỉ: " + callback.FromAddress

	case enum.TPLCallbackStatus.STORING:
		callback.StatusName = "Đang lư trữ hàng"
		callback.Action = string(enum.TPLCallbackStatus.STORING)
		callback.ActionName = "Đang lưu trữ hàng tại hub: " + callback.HubCode

	case enum.TPLCallbackStatus.TRANSPORTING:
		callback.StatusName = "Đang luân chuyển hàng từ " + callback.FromName + " đến " + callback.ToName
		callback.Action = string(enum.TPLCallbackStatus.TRANSPORTING)
		callback.ActionName = "Đang vận chuyển hàng từ " + callback.FromName + " đến " + callback.ToName

	case enum.TPLCallbackStatus.DELIVERING:
		callback.StatusName = "Đang giao hàng"
		callback.Action = string(enum.TPLCallbackStatus.DELIVERING)
		callback.ActionName = "Đang giao hàng đến " + callback.ToName + " tại địa chỉ " + callback.ToAddress

	case enum.TPLCallbackStatus.DELIVERED:
		callback.StatusName = "Giao hàng thành công"
		callback.Action = string(enum.TPLCallbackStatus.DELIVERED)
		callback.ActionName = "Đã giao hàng thành công đến" + callback.ToName + " tại địa chỉ " + callback.ToAddress

	case enum.TPLCallbackStatus.DELIVERY_FAIL:
		callback.StatusName = "Giao hàng thất bại"
		callback.Action = string(enum.TPLCallbackStatus.DELIVERY_FAIL)
		callback.ActionName = "Giao hàng thất bại đến " + callback.ToName + " tại địa chỉ " + callback.ToAddress

	case enum.TPLCallbackStatus.RETURNING:
		callback.StatusName = "Đang Trả hàng."
		callback.Action = string(enum.TPLCallbackStatus.RETURNING)
		callback.ActionName = "Đang trả hàng từ " + callback.FromName + " đến " + callback.ToName

	case enum.TPLCallbackStatus.RETURNED:
		callback.StatusName = "Đã trả hàng."
		callback.Action = string(enum.TPLCallbackStatus.RETURNED)
		callback.ActionName = "Đã trả hàng"

	case enum.TPLCallbackStatus.CANCEL_DELIVERY:
		callback.StatusName = "Hủy giao hàng."
		callback.Action = string(enum.TPLCallbackStatus.CANCEL_DELIVERY)
		callback.ActionName = "Hủy giao hàng."

	case enum.TPLCallbackStatus.COMPLETED:
		callback.StatusName = "Đã đối soát"
		callback.Action = string(enum.TPLCallbackStatus.COMPLETED)
		callback.ActionName = "Đã đối soát"

	}
	return nil
}

func handleReturnCallback(callbackInfo *model.Callback, shippingOrder *model.ShippingOrder) error {
	// We only need to update shipping order if carrier is external
	carrierRaw := model.CarrierDB.QueryOne(bson.M{
		"carrier_id": shippingOrder.TplServiceId,
	})

	if carrierRaw.Status != common.APIStatus.Ok {
		defer client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
			CmdType: "NOT_FOUND_CARRIER",
			Title:   "Không tìm thấy nhà vận chuyển với Id: " + strconv.FormatInt(shippingOrder.TplServiceId, 10),
			Message: "Không tìm thấy nhà vận chuyển",
		})
		return nil
	}

	carrier := carrierRaw.Data.([]*model.Carrier)[0]
	// Allow internal carrier update buy callback if status is LOST, STORING
	if carrier.IsInternal != nil &&
		*carrier.IsInternal && callbackInfo.Status != nil &&
		*callbackInfo.Status != enum.TPLCallbackStatus.LOST &&
		*callbackInfo.Status != enum.TPLCallbackStatus.STORING {
		return nil
	}

	// Nếu chuyển trạng thái về cancel thì check nhà vận chuyển của đơn có giống nhau không để tránh trường hợp cancel lẫn nhau
	if callbackInfo.CreatedSource != nil &&
		shippingOrder.TplCode != nil &&
		(string(*shippingOrder.TplCode) != string(*callbackInfo.CreatedSource)) &&
		callbackInfo.Status != nil &&
		*callbackInfo.Status == enum.TPLCallbackStatus.CANCEL {
		return nil
	}

	// update shipping order
	shippingOrder.Weight = callbackInfo.Weight
	shippingOrder.NumPackage = callbackInfo.NumPackage
	shippingOrder.TrackingCode = callbackInfo.TPLCode
	shippingOrder.Status = callbackInfo.Status
	shippingOrder.Reason = callbackInfo.Reason

	// Update sale order
	result := model.ShippingOrderDB.UpdateOne(
		&bson.M{
			"reference_code": callbackInfo.SO,
		},
		shippingOrder,
		&options.FindOneAndUpdateOptions{
			Upsert: &enum.True,
		},
	)

	if result.Status != common.APIStatus.Ok {
		shippingOrderJson, _ := json.Marshal(shippingOrder)
		defer client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
			CmdType: "UPDATE_SHIPPING_ORDER_ERROR",
			Title:   string(shippingOrderJson),
			Message: "Đã có lỗi xảy ra khi cập nhật shipping order",
		})
		return nil
	}

	return nil
}

func handleOPMCallback(callbackInfo *model.Callback, shippingOrder *model.ShippingOrder) error {
	if callbackInfo.Status == nil {
		return nil
	}
	now := time.Now()
	updateShippingOrderRaw := model.ShippingOrderDB.UpdateOne(bson.M{
		"reference_code": callbackInfo.SO,
	}, bson.M{
		"status": callbackInfo.Status,
	})
	if updateShippingOrderRaw.Status != common.APIStatus.Ok {
		return nil
	}

	if partner := client.GetWebhookClient(shippingOrder.PartnerCode); partner != nil {
		callbackPartner := request.Callback{
			Status:              callbackInfo.Status,
			ReferenceCode:       shippingOrder.ReferenceCode,
			ParentReferenceCode: shippingOrder.ParentReferenceCode,
			TrackingCode:        shippingOrder.TrackingCode,
			ActionTime:          &now,
			PartnerCode:         shippingOrder.PartnerCode,
			CallbackUrl:         shippingOrder.CallbackUrl,
		}
		// TODO: if partner does require hash for validation, we need to hash the callback data
		if partner.Config.PartnerCode == "CIRCA" {
			callbackPartner = CallbackToCircaAdapter(callbackPartner, *partner.Config)
		}
		partner.SendCallbackToPartner(callbackPartner)
	}

	if v, ok := enum.NeedAddReconcileOpmGHTK[callbackInfo.TPLStatus]; ok &&
		v == enum.TPLCallbackStatus.DELIVERED {
		if shippingOrder.Status != nil &&
			(*shippingOrder.Status == enum.TPLCallbackStatus.DELIVERED ||
				*shippingOrder.Status == enum.TPLCallbackStatus.COMPLETED) {
			return nil
		}

		collectAmount := int64(shippingOrder.CODAmount)
		client.Services.AccountingClient.AddCircaReconcileOrder(&request.CreateReconcileSessionOrderRequest{
			ReferenceCode: shippingOrder.ReferenceCode,
			TrackingCode:  shippingOrder.TrackingCode,
			CarrierCode:   string(*shippingOrder.TplCode),
			ReconcileType: "CIRCA",
			PickedTime:    &now,
			CollectAmount: &collectAmount,
		})
	}

	return nil
}

func chooseHubCodeFromRefCode(code string) string {
	if strings.HasPrefix(code, "SOBD") ||
		strings.HasPrefix(code, "ROBD") ||
		strings.HasPrefix(code, "TOBD") ||
		strings.HasPrefix(code, "DOBD") {
		return conf.Config.SaleOrderCodeToHubCode["SOBD"]
	} else if strings.HasPrefix(code, "SOHN") ||
		strings.HasPrefix(code, "ROHN") ||
		strings.HasPrefix(code, "TOHN") ||
		strings.HasPrefix(code, "DOHN") {
		return conf.Config.SaleOrderCodeToHubCode["SOHN"]
	} else {
		return ""
	}
}

func FormatFailReasonGHTK(reasonCode string, failTime int, lang enum.SupportedLangValue) string {
	if lang == "" {
		lang = enum.SupportedLang.VN
	}

	reason := "Lần " + strconv.Itoa(failTime) + ": " + enum.GHTKFailDetailReason[reasonCode]

	return reason
}

func FormatFailReasonVTP(reasonDetail string, failTime int, lang enum.SupportedLangValue) string {
	if lang == "" {
		lang = enum.SupportedLang.VN
	}

	reason := "Lần " + strconv.Itoa(failTime) + ": " + reasonDetail

	return reason
}

func CallbackToCircaAdapter(callback request.Callback, partner conf.WebHookConfig) request.Callback {
	dataBytes, err := json.Marshal(callback)
	if err != nil {
		return callback
	}
	dataJsonStr := string(dataBytes)

	h := hmac.New(sha1.New, []byte(partner.HashKey))
	h.Write(dataBytes)
	hashedData := hex.EncodeToString(h.Sum(nil))

	return request.Callback{
		JsonStrData: dataJsonStr,
		HashedData:  hashedData,
	}
}

func handleEOCallback(callbackInfo *model.Callback, shippingOrder *model.ShippingOrder) error {
	carrierRaw := model.CarrierDB.QueryOne(bson.M{
		"carrier_id": shippingOrder.TplServiceId,
	})
	if carrierRaw.Status != common.APIStatus.Ok {
		return nil
	}
	carrier := carrierRaw.Data.([]*model.Carrier)[0]
	if carrier.IsInternal == nil || *carrier.IsInternal {
		return nil
	}
	updateResp := model.ShippingOrderDB.UpdateOne(bson.M{
		"reference_code": shippingOrder.ReferenceCode,
	}, bson.M{
		"status": callbackInfo.Status,
	})
	if updateResp.Status != common.APIStatus.Ok {
		return errors.New("can not update shipping order")
	}
	now := time.Now()
	if partner := client.GetWebhookClient(shippingOrder.PartnerCode); partner != nil {
		callbackPartner := request.Callback{
			Status:              callbackInfo.Status,
			ReferenceCode:       shippingOrder.ReferenceCode,
			ParentReferenceCode: shippingOrder.ParentReferenceCode,
			TrackingCode:        shippingOrder.TrackingCode,
			ActionTime:          &now,
			PartnerCode:         shippingOrder.PartnerCode,
			CallbackUrl:         shippingOrder.CallbackUrl,
		}
		partner.SendCallbackToPartner(callbackPartner)
	}

	return nil
}
