package tpl_callback

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/golang/configuration"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/response"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathCallback        = "/integration/tpl-callback/v1/callback"
	pathAssignCallback  = "/integration/tpl-callback/v1/callback/assign"
	pathGetCallbackList = "/integration/tpl-callback/v1/callback/list"
	pathGHTKCallback    = "/integration/tpl-callback/v1/GHTK/callback"
	pathGHNCallback     = "/integration/tpl-callback/v1/GHN/callback"
	pathVTPCallback     = "/integration/tpl-callback/v1/VIETTEL_POST/callback"
	pathNTCallback      = "/integration/tpl-callback/v1/NHAT_TIN/callback"
	pathVNPCallback     = "/integration/tpl-callback/v1/VNP/callback"
	pathAhamoveCallback = "/integration/tpl-callback/v1/AHAMOVE/callback"
	pathSnappyCallback  = "/integration/tpl-callback/v1/SNAPPY/callback"
)

// TPLCallbackClient
type TPLCallbackClient struct {
	svc     *client.RestClient
	headers map[string]string
}

func NewServiceClient(serviceConfig configuration.Service, logName string, session *mongo.Database) *TPLCallbackClient {
	if serviceConfig.Host == "" {
		return nil
	}

	tplClient := &TPLCallbackClient{
		svc: client.NewRESTClient(
			serviceConfig.Host,
			logName,
			30*time.Second,
			0,
			30*time.Second,
		),
		headers: map[string]string{
			"Authorization": serviceConfig.Authorization,
		},
	}
	tplClient.svc.SetDBLog(session)
	return tplClient
}

// GetCallback func
func (cli *TPLCallbackClient) GetCallback(saleOrderCode string) (result *response.Callback, err error) {

	params := map[string]string{
		"so": saleOrderCode,
	}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathCallback, nil)
	if err != nil {
		return nil, err
	}

	type myResponse struct {
		Status string               `json:"status"`
		Code   string               `json:"errorCode"`
		Data   []*response.Callback `json:"data"`
	}

	resp := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &resp)
	if err != nil {
		return nil, err
	}

	if resp.Status != common.APIStatus.Ok || resp.Data == nil {
		return nil, fmt.Errorf("%v", resp.Code)
	}
	if len(resp.Data) > 0 {
		result = resp.Data[0]
	}
	return
}

// GetListCallback func
func (cli *TPLCallbackClient) GetListCallback(limit, offset int, query *model.CallbackQuery) (result []*request.Callback, err error) {
	queryParam, err := json.Marshal(query)

	if err != nil {
		return
	}

	params := map[string]string{
		"q":        string(queryParam),
		"limit":    strconv.Itoa(limit),
		"offset":   strconv.Itoa(offset),
		"getTotal": "true",
	}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetCallbackList, nil)
	if err != nil {
		return nil, err
	}

	type myResponse struct {
		Status string              `json:"status"`
		Code   string              `json:"code"`
		Data   []*request.Callback `json:"data"`
		Total  int64               `json:"total"`
	}

	response := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return nil, err
	}

	if response.Status != common.APIStatus.Ok || response.Data == nil {
		return nil, fmt.Errorf("%v", response.Code)
	}
	if len(response.Data) > 0 {
		result = response.Data
	}
	return
}

// CreateCallback func
func (cli *TPLCallbackClient) CreateCallback(callback request.Callback) (err error) {
	var res *client.RestResult

	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, callback, pathCallback, nil)
	if err != nil {
		return err
	}

	type myResponse struct {
		Status  string `json:"status"`
		Message string `json:"message"`
	}

	response := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return err
	}

	if response.Status != common.APIStatus.Ok {
		return fmt.Errorf("%v", response.Message)
	}

	return err
}

// UpdateCallback func
func (cli *TPLCallbackClient) UpdateCallback(callback request.Callback) (err error) {
	var res *client.RestResult

	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, nil, callback, pathCallback, nil)
	if err != nil {
		return err
	}

	type myResponse struct {
		Status string `json:"status"`
		Code   string `json:"code"`
	}

	response := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return err
	}

	if response.Status != common.APIStatus.Ok {
		return fmt.Errorf("%v", response.Code)
	}

	return err
}

// AssignCallback func
func (cli *TPLCallbackClient) AssignCallback(listSo []string, accountId int, accountName string) (err error) {
	type myRequest struct {
		ListSO     []string `json:"listSo"`
		Employee   string   `json:"employee"`
		EmployeeID int      `json:"employeeId"`
	}

	body := myRequest{
		ListSO:     listSo,
		Employee:   accountName,
		EmployeeID: accountId,
	}

	var res *client.RestResult

	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, nil, body, pathAssignCallback, nil)
	if err != nil {
		return err
	}

	type myResponse struct {
		Status  string `json:"status"`
		Code    string `json:"code"`
		Message string `json:"message"`
	}

	response := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return err
	}

	if response.Status != common.APIStatus.Ok {
		return fmt.Errorf("%v", response.Message)
	}

	return err
}

// GHTKCallback func
func (cli *TPLCallbackClient) GHTKCallback(callback request.GHTKCallback) (err error) {
	var res *client.RestResult

	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, callback, pathGHTKCallback, nil)
	if err != nil {
		return err
	}

	type myResponse struct {
		Status string `json:"status"`
		Code   string `json:"code"`
	}

	response := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return err
	}

	if response.Status != common.APIStatus.Ok {
		return fmt.Errorf("%v", response.Code)
	}

	return err
}

// GHNCallback func
func (cli *TPLCallbackClient) GHNCallback(callback request.GHNCallback) (err error) {
	var res *client.RestResult

	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, callback, pathGHNCallback, nil)
	if err != nil {
		return err
	}

	type myResponse struct {
		Status string `json:"status"`
		Code   string `json:"code"`
	}

	response := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return err
	}

	if response.Status != common.APIStatus.Ok {
		return fmt.Errorf("%v", response.Code)
	}

	return err
}

// VTPCallback func
func (cli *TPLCallbackClient) VTPCallback(callback request.VTPCallback) (err error) {
	var res *client.RestResult

	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, callback, pathVTPCallback, nil)
	if err != nil {
		return err
	}

	type myResponse struct {
		Status string `json:"status"`
		Code   string `json:"code"`
	}

	response := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return err
	}

	if response.Status != common.APIStatus.Ok {
		return fmt.Errorf("%v", response.Code)
	}

	return err
}

// NTCallback func
func (cli *TPLCallbackClient) NTCallback(callback request.NhatTinCallback) (err error) {
	var res *client.RestResult

	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, callback, pathNTCallback, nil)
	if err != nil {
		return err
	}

	type myResponse struct {
		Status string `json:"status"`
		Code   string `json:"code"`
	}

	response := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return err
	}

	if response.Status != common.APIStatus.Ok {
		return fmt.Errorf("%v", response.Code)
	}

	return err
}

// SNAPPYCallback func
func (cli *TPLCallbackClient) SNAPPYCallback(callback request.Snappy) (err error) {
	var res *client.RestResult

	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, callback, pathSnappyCallback, nil)
	if err != nil {
		return err
	}

	type myResponse struct {
		Status string `json:"status"`
		Code   string `json:"code"`
	}

	response := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return err
	}

	if response.Status != common.APIStatus.Ok {
		return fmt.Errorf("%v", response.Code)
	}

	return err
}

// VNPCallback func
func (cli *TPLCallbackClient) VNPCallback(callback string) (err error) {
	type myRequest struct {
		Data string `json:"data"`
	}
	body := myRequest{
		Data: callback,
	}
	var res *client.RestResult

	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, body, pathVNPCallback, nil)
	if err != nil {
		return err
	}

	type myResponse struct {
		Status string `json:"status"`
		Code   string `json:"code"`
	}

	response := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return err
	}

	if response.Status != common.APIStatus.Ok {
		return fmt.Errorf("%v", response.Code)
	}

	return err
}

// AhamoveCallback func
func (cli *TPLCallbackClient) AhamoveCallback(ahamoveCallback *request.AhamoveCallback) (err error) {
	var res *client.RestResult

	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, ahamoveCallback, pathAhamoveCallback, nil)
	if err != nil {
		return err
	}

	type myResponse struct {
		Status string `json:"status"`
		Code   string `json:"code"`
	}

	response := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return err
	}

	if response.Status != common.APIStatus.Ok {
		return fmt.Errorf("%v", response.Code)
	}

	return err
}
