package client

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/response"
	"go.mongodb.org/mongo-driver/mongo"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/golang/configuration"
)

const (
	pathReconcileSession            = "/accounting/core/v1/reconcile-session"
	pathReconcileSessionOrder       = "/accounting/core/v1/reconcile-session/order"
	pathUpdateReconcileCodCollected = "/accounting/core/v1/reconcile-session/cod-collected"
	pathUpdateOrdersCOD             = "/accounting/core/v1/reconcile-session/orders/update-cod"
	pathUpdateReconcileFee          = "/accounting/core/v1/reconcile-session/orders/fee"
	pathCreateBill                  = "/accounting/core/v1/bill-from-logistics"
	pathAddCircaReconcileOrder      = "/accounting/core/v1/reconcile-session/circa/order"
)

type AccountingClient struct {
	svc     *client.RestClient
	headers map[string]string
}

func NewServiceClient(serviceConfig configuration.Service, logName string, session *mongo.Database) *AccountingClient {
	if serviceConfig.Host == "" {
		return nil
	}
	accountingClient := &AccountingClient{
		svc:     client.NewRESTClient(serviceConfig.Host, logName, 10*time.Second, 0, 5*time.Second),
		headers: map[string]string{"Authorization": serviceConfig.Authorization},
	}
	accountingClient.svc.SetDBLog(session)
	return accountingClient
}

func (cli *AccountingClient) CreateReconcileSession(body *request.CreateReconcileSessionRequest) *common.APIResponse {
	params := map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, body, pathReconcileSession, nil)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_CREATE_RECONCILE_SESSION",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHALL_CREATE_RECONCILE_SESSION",
		}
	}

	return result
}

func (cli *AccountingClient) CreateReconcileSessionOrder(body *request.CreateReconcileSessionOrderRequest) *common.APIResponse {
	params := map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, body, pathReconcileSessionOrder, nil)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_CREATE_RECONCILE_SESSION",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHALL_CREATE_RECONCILE_SESSION",
		}
	}

	return result
}

func (cli *AccountingClient) UpdateReconcileCodCollected(body *request.ReconcileCodCollectedRequest) *common.APIResponse {
	params := map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, params, body, pathUpdateReconcileCodCollected, nil)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_UPDATE_RECONCILE_COD_COLLECTED",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHALL_RECONCILE_COD_COLLECTED",
		}
	}

	return result
}

func (cli *AccountingClient) UpdateOrdersCOD(body request.ImportFeeRequest) *common.APIResponse {
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, nil, body, pathUpdateOrdersCOD, nil)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "FAIL_TO_UPDATE_ORDERS_COD",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHALL_RECONCILE_COD_COLLECTED",
		}
	}

	return result
}

func (cli *AccountingClient) AddReconcileFeeSession(
	newOrders, updateOrders []*request.CreateReconcileSessionOrderRequest,
	tags []string) *common.APIResponse {
	body := struct {
		ReconcileType enum.ReconcileTypeRequestValue                `json:"reconcileType,omitempty"`
		Tags          []string                                      `json:"tags,omitempty"`
		NewOrders     []*request.CreateReconcileSessionOrderRequest `json:"newOrders"`
		UpdateOrders  []*request.CreateReconcileSessionOrderRequest `json:"updateOrders"`
	}{
		ReconcileType: enum.ReconcileTypeRequest.LOGISTICS_CUS_COMP,
		NewOrders:     newOrders,
		UpdateOrders:  updateOrders,
		Tags:          tags,
	}

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, nil, body, pathUpdateReconcileFee, nil)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_CREATE_RECONCILE_SESSION",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHALL_CREATE_RECONCILE_SESSION",
		}
	}

	return result
}

func (cli *AccountingClient) GetReconcileOrderOfCustomer(customerCode string, orderStatus enum.ReconcileOrderStatusValue) ([]*response.ReconcileSessionOrder, error) {
	params := map[string]string{
		"customerCode": customerCode,
		"status":       string(orderStatus),
	}
	paramByte, _ := json.Marshal(params)
	requestParams := map[string]string{
		"q":     string(paramByte),
		"limit": fmt.Sprintf("%d", 1000),
	}

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, requestParams, nil, pathReconcileSessionOrder, nil)
	if err != nil {
		return nil, err
	}

	type myResponse struct {
		Status string                            `json:"status"`
		Code   string                            `json:"code"`
		Data   []*response.ReconcileSessionOrder `json:"data"`
	}

	resp := new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &resp)
	if err != nil {
		return nil, err
	}

	if resp.Status != common.APIStatus.Ok || resp.Data == nil {
		return nil, fmt.Errorf("%v", resp.Code)
	}

	if len(resp.Data) > 0 {
		return resp.Data, nil
	}

	return nil, errors.New("not found")
}

func (cli *AccountingClient) CreateBill(billRequest request.CreateBillRequest) *common.APIResponse {
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, billRequest, pathCreateBill, nil)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "CANT_CREATE_BILL",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHALL_CANT_CREATE_BILL",
		}
	}

	return result
}

func (cli *AccountingClient) AddCircaReconcileOrder(body *request.CreateReconcileSessionOrderRequest) *common.APIResponse {
	params := map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, body, pathAddCircaReconcileOrder, nil)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_CREATE_RECONCILE_SESSION",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHALL_CREATE_RECONCILE_SESSION",
		}
	}

	return result
}
