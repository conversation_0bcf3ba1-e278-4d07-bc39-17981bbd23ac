package ahamove

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/response"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathRouteOptimization = "/ro/sdvrp"
	pathGeocode           = "/geocode/geocode"
)

type OnWheelClient struct {
	svc     *client.RestClient
	headers map[string]string
}

func NewOnWheelServiceClient(apiHost, key, logName string, session *mongo.Database) *OnWheelClient {
	if apiHost == "" {
		return nil
	}
	onWheelClient := &OnWheelClient{
		svc: client.NewRESTClient(
			apiHost,
			logName,
			10*time.Second,
			1,
			3*time.Second,
		),
		headers: map[string]string{
			"Api-key": key,
		},
	}
	onWheelClient.svc.SetDBLog(session)
	return onWheelClient
}

func (cli *OnWheelClient) GetRoutePlanning(req request.AhamoveRouteOptimizationRequest) *response.AhamoveRouteOptimizationResponse {
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, req, pathRouteOptimization, nil)
	if err != nil {
		log.Printf("[ERROR] Call API %s: %v\n", pathRouteOptimization, err.Error())
		return nil
	}

	resBody := new(response.AhamoveRouteOptimizationResponse)
	err = json.Unmarshal([]byte(res.Body), &resBody)

	if err != nil {
		log.Printf("[ERROR] Call API %s: %v\n", pathRouteOptimization, err.Error())
		return nil
	}

	// Check solution
	if resBody.Cluster != nil {
		return resBody
	}
	return nil
}

func (cli *OnWheelClient) GetLatLngFromAddress(address string) (latitude, longitude float64, err error) {
	body := map[string]string{
		"address": address,
	}

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, body, pathGeocode, nil)
	if err != nil {
		log.Printf("[ERROR] Call API %s: %v\n", pathRouteOptimization, err.Error())
		return
	}

	resBody := new(response.AhamoveGeocodeResponse)
	err = json.Unmarshal([]byte(res.Body), &resBody)

	if err != nil {
		log.Printf("[ERROR] Call API %s: %v\n", pathRouteOptimization, err)
		return
	}

	if resBody.Status == 500 {
		err = fmt.Errorf("[ERROR] Call API %s: %v\n", pathRouteOptimization, resBody.Title)
		return
	}

	latitude = resBody.Lat
	longitude = resBody.Lng
	return
}
