package request

import "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"

type BookNTRequest struct {
	PartnerId      int64   `json:"partner_id"`
	SName          string  `json:"s_name"`
	SPhone         string  `json:"s_phone"`
	SAddress       string  `json:"s_address"`
	SProvinceId    int64   `json:"s_province_id"`
	SDistrictId    int64   `json:"s_district_id"`
	SWardId        int64   `json:"s_ward_id"`
	SAddressValue  string  `json:"s_address_value"`
	RPhone         string  `json:"r_phone"`
	RAddress       string  `json:"r_address"`
	RProvinceId    int64   `json:"r_province_id"`
	RDistrictId    int64   `json:"r_district_id"`
	RName          string  `json:"r_name"`
	RWardId        int64   `json:"r_ward_id"`
	CODAmount      float64 `json:"cod_amount"`
	ServiceId      int64   `json:"service_id"`
	PaymentMethod  int64   `json:"payment_method"`
	Weight         float64 `json:"weight"`
	PackageNo      int64   `json:"package_no"`
	RefCode        string  `json:"ref_code"`
	CargoContent   string  `json:"cargo_content"`
	CargoValue     float64 `json:"cargo_value"`
	CargoContentId float64 `json:"cargo_content_id"`
	Note           string  `json:"note"`
	UTMSource      string  `json:"utm_source"`
}

type CancelNTRequest struct {
	ListDoCode []string `json:"listDocode"`
}

type NhatTinCallback struct {
	BillNo      string                   `json:"bill_no"`
	RefCode     string                   `json:"ref_code"`
	StatusId    *enum.NhatTinStatusValue `json:"status_id"`
	StatusName  string                   `json:"status_name"`
	StatusTime  int64                    `json:"status_time"`
	ShippingFee float64                  `json:"shipping_fee"`
	Reason      string                   `json:"reason"`
	Weight      float64                  `json:"weight"`
}
