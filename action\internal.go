package action

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/utils"
	"go.mongodb.org/mongo-driver/bson"
)

type internal struct {
}

var Internal internal

func (internal) BookShipping(shippingOrder *model.ShippingOrder, carrierModel *model.Carrier, fromWarehouseCode string, needCalculateShippingFee bool) (result *model.ShippingInfo, err error) {
	actionTime := time.Now()
	trackingNumber := model.GenCode("TRACKING_CODE")
	var feeAmount float64 = 0
	if needCalculateShippingFee {
		// TODO: change to calculate fee by config fee instead of default fee
		if shippingOrder.CustomerInfo == nil {
			return nil, errors.New("missing customer info")
		}

		bookReq := shippingOrderToBookShippingReq(shippingOrder)
		configFeeFilter := bson.M{
			"is_deleted": bson.M{
				"$ne": true,
			},
		}

		if *shippingOrder.CustomerInfo.CustomerType == enum.CustomerType.SELLER {
			if shippingOrder.IsReceiveAtLMHub {
				configFeeFilter["code"] = conf.Config.DefaultSellerROTransportCode
			} else {
				configFeeFilter["code"] = conf.Config.DefaultSellerROTransNDeliCode
			}
		}
		if *shippingOrder.CustomerInfo.CustomerType == enum.CustomerType.VENDOR {
			if shippingOrder.IsReceiveAtLMHub {
				configFeeFilter["code"] = conf.Config.DefaultVendorROTransportCode
			} else {
				configFeeFilter["code"] = conf.Config.DefaultVendorROTransNDeliCode
			}
		}

		configFeeRaw := model.ConfigFeeDB.QueryOne(configFeeFilter)
		if configFeeRaw.Status != common.APIStatus.Ok {
			return nil, errors.New("no config fee found")
		}
		configFee := configFeeRaw.Data.([]*model.ConfigFee)[0]
		feeAmount, err = CalculateOrderFee(bookReq, configFee)
		if err != nil {
			return nil, err
		}
		if shippingOrder.CODAmount == 0 {
			shippingOrder.CODAmount = feeAmount
			shippingOrder.TotalAmount = feeAmount
		} else {
			shippingOrder.TotalAmount = feeAmount + shippingOrder.CODAmount
		}
		shippingOrder.PaymentMethod = &enum.PaymentMethod.COD
		shippingOrder.FeeAmount = feeAmount

		customerRaw := model.CustomerDB.QueryOne(bson.M{
			"code": shippingOrder.CustomerInfo.Code,
		})
		if customerRaw.Status == common.APIStatus.Ok {
			customer := customerRaw.Data.([]*model.Customer)[0]
			if customer.PreferPaymentMethod != nil &&
				*customer.PreferPaymentMethod == enum.PreferPaymentMethod.DEBT {
				shippingOrder.FeeCollectMethod = &enum.FeeCollectMethod.DEBT
				shippingOrder.CODAmount = 0
				shippingOrder.FeeAmount = feeAmount
			}
		}

	}
	createCallbackRequest := request.Callback{
		SO:            shippingOrder.ReferenceCode,
		CreatedSource: carrierModel.ParentCode,
		WarehouseCode: fromWarehouseCode,
		Status:        &enum.TPLCallbackStatus.READY_TO_PICK,
		Weight:        shippingOrder.Weight,
		TPLCode:       trackingNumber,
		ActionTime:    &actionTime,
		StatusName:    "Tạo mới vận đơn",
		TotalFee:      feeAmount,
		ExtraCallback: map[string]interface{}{
			"shippingInfo": []*model.ShippingInfo{
				{
					CODAmount:      shippingOrder.CODAmount,
					TrackingNumber: trackingNumber,
					FeeAmount:      feeAmount,
					ExtraInfo:      nil,
				},
			},
		},
		ExternalTPLName: carrierModel.CarrierName,
		TPLStatus:       string(enum.TPLCallbackStatus.READY_TO_PICK),
		TPLStatusName:   "Tạo mới vận đơn",
		NumPackage:      shippingOrder.NumPackage,
	}

	if *shippingOrder.PaymentMethod == enum.PaymentMethod.COD {
		createCallbackRequest.COD = shippingOrder.CODAmount
	}

	err = client.Services.TplCallbackClient.CreateCallback(createCallbackRequest)

	if err != nil {
		return
	}

	result = &model.ShippingInfo{
		TrackingNumber: trackingNumber,
		CODAmount:      createCallbackRequest.COD,
	}

	if needCalculateShippingFee &&
		shippingOrder.FeeCollectMethod != nil &&
		*shippingOrder.FeeCollectMethod == enum.FeeCollectMethod.DEBT {
		result.FeeAmount = feeAmount
	}

	return
}

func CalculateOrderFee(bookReq request.BookShippingOrder, configFee *model.ConfigFee) (float64, error) {
	feeStructures := configFee.FeeStructures

	if configFee.IsApplyToArea != nil && *configFee.IsApplyToArea {
		isMatchArea := false
		errorDetail := "config code: " + configFee.Code + " "
		if configFee.ShippingOrderTypes != nil &&
			len(*configFee.ShippingOrderTypes) > 0 &&
			bookReq.ShippingType != nil &&
			!isContainsShippingOrderType(*configFee.ShippingOrderTypes, *bookReq.ShippingType) {
			return 0, errors.New("no fee structure match type")
		}

		if bookReq.ShippingType != nil &&
			*bookReq.ShippingType == enum.ShippingOrderType.EO {
			if bookReq.DropOffAtHubCode != "" && bookReq.ReceiveAtHubCode != "" {
				var err error
				feeStructures, err = findMatchHubToHub(bookReq.DropOffAtHubCode, bookReq.ReceiveAtHubCode, *configFee)
				if err == nil || len(feeStructures) > 0 {
					isMatchArea = true
				}
			}
			// Nếu trùng tỉnh => chỉ check phí internal province
			if !isMatchArea &&
				bookReq.From.ProvinceCode == bookReq.To.ProvinceCode {
				var err error
				feeStructures, err = findMatchInternalProvince(*bookReq.From, *bookReq.To, *configFee)
				if err != nil || len(feeStructures) == 0 {
					return 0, err
				}
				isMatchArea = true
			}

			if !isMatchArea {
				queryFromZoneResult := model.ZoneDB.QueryOne(bson.M{
					"provinces.districts.district_code": bookReq.From.DistrictCode,
					"parent_code": bson.M{
						"$exists": false,
					},
					"is_deleted": false,
				})
				queryToZoneResult := model.ZoneDB.QueryOne(bson.M{
					"provinces.districts.district_code": bookReq.To.DistrictCode,
					"parent_code": bson.M{
						"$exists": false,
					},
					"is_deleted": false,
				})
				if queryFromZoneResult.Status != common.APIStatus.Ok {
					return 0, errors.New("not found valid from zone")
				}
				if queryToZoneResult.Status != common.APIStatus.Ok {
					return 0, errors.New("not found valid to zone")
				}
				fromZone := queryFromZoneResult.Data.([]*model.Zone)[0]
				toZone := queryToZoneResult.Data.([]*model.Zone)[0]
				var fromZoneAreaLevel, toZoneAreaLevel *enum.AreaLevelValue
			outFrom:
				for _, province := range fromZone.Provinces {
					for _, district := range province.DistrictArea {
						if district.DistrictCode == bookReq.From.DistrictCode {
							if district.AreaLevel == nil {
								district.AreaLevel = &enum.AreaLevel.URBAN
							}
							fromZoneAreaLevel = district.AreaLevel
							break outFrom
						}
					}
				}
			outTo:
				for _, province := range toZone.Provinces {
					for _, district := range province.DistrictArea {
						if district.DistrictCode == bookReq.To.DistrictCode {
							if district.AreaLevel == nil {
								district.AreaLevel = &enum.AreaLevel.URBAN
							}
							toZoneAreaLevel = district.AreaLevel
							break outTo
						}
					}
				}
				checkAtAreaLevel :=
					SelectFeeBaseOnAreaLevel(*fromZoneAreaLevel, *toZoneAreaLevel)
				// Nếu trùng zone => chỉ check phí internal zone
				if fromZone.Code == toZone.Code {
					for _, feeArea := range configFee.FeeAreaList {
						if feeArea.FeeArea.Type == enum.FeeArea.INTERNAL_ZONE &&
							isContainsAreaLevel(feeArea.FeeArea.AreaLevels, checkAtAreaLevel) &&
							isContainInAreaIdentifier(feeArea.FeeArea.FromAreaIdentifiers, fromZone.Code) {
							isMatchArea = true
							feeStructures = feeArea.FeeStructures
						}
					}
				} else {
					// Nếu khác zone thì chỉ check NEAR_ZONE và DISTANT_ZONE
					for _, feeArea := range configFee.FeeAreaList {
						if (feeArea.FeeArea.Type == enum.FeeArea.NEAR_ZONE ||
							feeArea.FeeArea.Type == enum.FeeArea.DISTANT_ZONE) &&
							isContainsAreaLevel(feeArea.FeeArea.AreaLevels, checkAtAreaLevel) &&
							isMatchNearOrDistanceZone(fromZone.Code, toZone.Code, feeArea.FeeArea.ArrAreaIdentifiers) {
							isMatchArea = true
							feeStructures = feeArea.FeeStructures
						}
					}
				}
			}
		} else {
			for _, feeArea := range configFee.FeeAreaList {
				isMatchArea = isMatchFeeArea(bookReq, feeArea)
				errorDetail += "fromCode: " + bookReq.From.Code + " toCode: " + bookReq.To.Code + " "
				if isMatchArea {
					for _, freeStruct := range feeArea.FeeStructures {
						isMatchFeeStructure := false
						for _, condition := range freeStruct.FeeConditions {
							compareValue := ""
							switch condition.ConditionType {
							case enum.FeeConditionType.PACKAGE:
								compareValue = strconv.FormatInt(bookReq.NumPackage, 10)
								isMatchFeeStructure = isMatchNumPack(bookReq.NumPackage, condition)
							case enum.FeeConditionType.WEIGHT:
								compareValue = strconv.FormatFloat(bookReq.Weight, 'f', -1, 64)
								isMatchFeeStructure = isMatchWeight(bookReq.Weight, condition)
							case enum.FeeConditionType.ORDER_TYPE:
								compareValue = string(*bookReq.ShippingType)
								isMatchFeeStructure = isMatchOrderType(*bookReq.ShippingType, condition)
							}

							// TODO: Nếu có loại mới thêm vào cụm switch case này để mở rộng
							if !isMatchFeeStructure {
								errorDetail = fmt.Sprintf("not match condition: %v, with value %s", condition, compareValue)
								break
							}
						}
						if isMatchFeeStructure {
							var feePerPackage float64 = 0
							var feePercentOrderValue float64 = 0
							var additionalFee float64 = 0
							if fee, ok := freeStruct.Fees[enum.FeeType.FEE_PER_PACKAGE]; ok {
								feePerPackage = fee
							}

							if fee, ok := freeStruct.Fees[enum.FeeType.PERCENT_ORDER_VALUE]; ok {
								feePercentOrderValue = fee / 100
							}

							if fee, ok := freeStruct.Fees[enum.FeeType.ADDITIONAL_FEE]; ok {
								additionalFee = fee
							}

							// Công thức tính phí tổng quát:
							// Phí = Số lượng kiện * Phí kiện + Giá trị đơn hàng * phần trăm phí trên giá trị đơn hàng + Phí thêm
							calculatedFee := float64(bookReq.NumPackage)*feePerPackage + bookReq.OrderValue*feePercentOrderValue + additionalFee
							// TODO: Add voucher validation and discount here, for now if voucher is not empty, we will not calculate fee
							if bookReq.VoucherCode != "" {
								calculatedFee = 0
							}
							return calculatedFee, nil
						}
					}
				}
			}
		}
		if !isMatchArea {
			return 0, errors.New("no fee structure match area: " + errorDetail)
		}
	}

	for _, freeStruct := range feeStructures {
		isMatchFeeStructure := false
		for _, condition := range freeStruct.FeeConditions {
			switch condition.ConditionType {
			case enum.FeeConditionType.PACKAGE:
				isMatchFeeStructure = isMatchNumPack(bookReq.NumPackage, condition)
			case enum.FeeConditionType.WEIGHT:
				isMatchFeeStructure = isMatchWeight(bookReq.Weight, condition)
			case enum.FeeConditionType.ORDER_TYPE:
				isMatchFeeStructure = isMatchOrderType(*bookReq.ShippingType, condition)
			}

			// TODO: Nếu có loại mới thêm vào cụm switch case này để mở rộng
			if !isMatchFeeStructure {
				break
			}
		}

		if isMatchFeeStructure {
			var feePerPackage float64 = 0
			var feePercentOrderValue float64 = 0
			var additionalFee float64 = 0
			if fee, ok := freeStruct.Fees[enum.FeeType.FEE_PER_PACKAGE]; ok {
				feePerPackage = fee
			}

			if fee, ok := freeStruct.Fees[enum.FeeType.PERCENT_ORDER_VALUE]; ok {
				feePercentOrderValue = fee / 100
			}

			if fee, ok := freeStruct.Fees[enum.FeeType.ADDITIONAL_FEE]; ok {
				additionalFee = fee
			}

			// Công thức tính phí tổng quát:
			// Phí = Số lượng kiện * Phí kiện + Giá trị đơn hàng * phần trăm phí trên giá trị đơn hàng + Phí thêm
			calculatedFee :=
				float64(bookReq.NumPackage)*feePerPackage +
					bookReq.OrderValue*feePercentOrderValue +
					additionalFee

			if configFee.AdditionalFees != nil {
				for _, addfee := range *configFee.AdditionalFees {
					switch addfee.AdditionalFeeType {
					case enum.AdditionalFeeType.FRAGILE_PRODUCT:
						if isContainsAdditionalFee(bookReq.AdditionalFees, enum.AdditionalFeeType.FRAGILE_PRODUCT) {
							calculatedFee += addfee.Fee
						}
					case enum.AdditionalFeeType.STORAGE_FEE:
						if isContainsAdditionalFee(bookReq.AdditionalFees, enum.AdditionalFeeType.STORAGE_FEE) {
							calculatedFee += addfee.Fee
						}
					case enum.AdditionalFeeType.AUDIT_FEE:
						if isContainsAdditionalFee(bookReq.AdditionalFees, enum.AdditionalFeeType.AUDIT_FEE) {
							calculatedFee += addfee.Fee
						}
					case enum.AdditionalFeeType.COLLECT_COD_FEE:
						if bookReq.CODAmount > 0 {
							calculatedFee += bookReq.CODAmount * addfee.Fee / 100
						}
					case enum.AdditionalFeeType.HIGH_VALUE_PRODUCT:
						if bookReq.OrderValue > 3000000 {
							calculatedFee += addfee.Fee * bookReq.OrderValue / 100
						}
						//case enum.AdditionalFeeType.RETURN_FEE:
					}
				}
			}

			// TODO: Add voucher validation and discount here, for now if voucher is not empty, we will not calculate fee
			if bookReq.VoucherCode != "" {
				calculatedFee = 0
			}
			return calculatedFee, nil
		}
	}

	return 0, errors.New("no fee structure match condition")
}

func calculateShippingFee(packNumber int64, weight float64, fromProvinceCode, toProvinceCode string, isReceiveAtLMHub bool) (shippingFee float64) {
	// Nếu chưa có config LOGISTIC
	config := model.ConfigLogisticDB.QueryOne(bson.M{"key": "LOGISTIC"})
	if config.Status != common.APIStatus.Ok {
		return DefaultFeeStructure(packNumber, fromProvinceCode, toProvinceCode)
	}

	if isReceiveAtLMHub {
		feeConfigI, ok := config.Data.([]*model.ConfigLogistic)[0].Configs["DROP_OFF_FEE"]
		// Nếu chưa có trường FEE_TO_PROVINCE
		if !ok {
			return DefaultFeeStructure(packNumber, fromProvinceCode, toProvinceCode)
		}
		feeConfigS, ok := feeConfigI.(string)
		if !ok {
			return DefaultFeeStructure(packNumber, fromProvinceCode, toProvinceCode)
		}

		feeConfig, err := strconv.ParseFloat(feeConfigS, 64)
		if err != nil {
			return DefaultFeeStructure(packNumber, fromProvinceCode, toProvinceCode)
		}

		shippingFee = float64(packNumber) * feeConfig
		return shippingFee
	}

	feeConfigI, ok := config.Data.([]*model.ConfigLogistic)[0].Configs["FEE_TO_PROVINCE"]
	// Nếu chưa có trường FEE_TO_PROVINCE
	if !ok {
		return DefaultFeeStructure(packNumber, fromProvinceCode, toProvinceCode)
	}
	feeConfigS := feeConfigI.(string)
	decoded, err := base64.StdEncoding.DecodeString(feeConfigS)
	if err != nil {
		return DefaultFeeStructure(packNumber, fromProvinceCode, toProvinceCode)
	}
	configMap := map[string][]model.FeeStructure{}
	// Nếu không thể Unmarshal do config sai format
	err = json.Unmarshal(decoded, &configMap)
	if err != nil {
		return DefaultFeeStructure(packNumber, fromProvinceCode, toProvinceCode)
	}

	// Nếu có config fee cho tỉnh
	if value, ok := configMap[toProvinceCode]; ok {
		for _, feeStruct := range value {
			isMatchFeeStructure := false
			for _, condition := range feeStruct.FeeConditions {
				switch condition.ConditionType {
				case enum.FeeConditionType.PACKAGE:
					isMatchFeeStructure = isMatchNumPack(packNumber, condition)
				case enum.FeeConditionType.WEIGHT:
					isMatchFeeStructure = isMatchWeight(weight, condition)
				}
				// TODO: Nếu có loại mới thêm vào cụm switch case này để mở rộng
				if !isMatchFeeStructure {
					break
				}
			}

			if isMatchFeeStructure {
				shippingFee = float64(packNumber)*feeStruct.Fees[enum.FeeType.FEE_PER_PACKAGE] +
					feeStruct.Fees[enum.FeeType.ADDITIONAL_FEE]
				return shippingFee
			}
		}
	}

	// Nếu config fee không trường hợp nào thỏa
	return DefaultFeeStructure(packNumber, fromProvinceCode, toProvinceCode)
}

func DefaultFeeStructure(packNumber int64, fromProvinceCode, toProvinceCode string) (shippingFee float64) {
	shippingFee = float64(packNumber) * conf.Config.DefaultShippingFee.FeePerPackage
	for _, cfgFee := range conf.Config.DefaultShippingFee.FeePerOrder {
		if cfgFee.FromProvinceCode == fromProvinceCode {
			if cfgFee.FeePerPackage != 0 {
				shippingFee = float64(packNumber) * cfgFee.FeePerPackage
			}
			for _, provinceCode := range cfgFee.ToProvinceCodes {
				if provinceCode == toProvinceCode {
					shippingFee = shippingFee + cfgFee.Fee
					return
				}
			}
		}
	}
	return
}

func (internal) BookInternalCarrier(
	input *request.BookShippingOrder,
	carrier *model.Carrier,
	warehouseCode string,
	needCalculateShippingFee bool,
) (result *model.ShippingInfo, err error) {
	actionTime := time.Now()
	trackingNumber := model.GenCode("TRACKING_CODE")
	createCallbackRequest := request.Callback{
		SO:            input.ReferenceCode,
		CreatedSource: carrier.ParentCode,
		WarehouseCode: warehouseCode,
		Status:        &enum.TPLCallbackStatus.READY_TO_PICK,
		Weight:        input.Weight,
		TPLCode:       trackingNumber,
		ActionTime:    &actionTime,
		StatusName:    "Tạo mới vận đơn",
		ExtraCallback: map[string]interface{}{
			"shippingInfo": []*model.ShippingInfo{
				{
					CODAmount:      input.CODAmount,
					TrackingNumber: trackingNumber,
					FeeAmount:      0,
					ExtraInfo:      nil,
				},
			},
		},
		ExternalTPLName: carrier.CarrierName,
		TPLStatus:       string(enum.TPLCallbackStatus.READY_TO_PICK),
		TPLStatusName:   "Tạo mới vận đơn",
		NumPackage:      input.NumPackage,
	}

	if *input.PaymentMethod == enum.PaymentMethod.COD {
		createCallbackRequest.COD = input.CODAmount
	}

	err = client.Services.TplCallbackClient.CreateCallback(createCallbackRequest)

	if err != nil {
		return
	}

	result = &model.ShippingInfo{
		TrackingNumber: trackingNumber,
		CODAmount:      createCallbackRequest.COD,
	}

	if needCalculateShippingFee {
		result.FeeAmount = calculateShippingFee(input.NumPackage, input.Weight, input.From.ProvinceCode, input.To.ProvinceCode, false)
		if input.CODAmount == 0 {
			input.CODAmount = result.FeeAmount
			input.TotalAmount = result.FeeAmount
		} else {
			input.TotalAmount = result.FeeAmount + input.CODAmount
		}
		input.PaymentMethod = &enum.PaymentMethod.COD
	}
	return
}

func isMatchFeeArea(bookReq request.BookShippingOrder, configFee model.ConfigFee) bool {

	if bookReq.ShippingType != nil &&
		*bookReq.ShippingType == enum.ShippingOrderType.EO {

	}
	// Đơn EO hiện tại chưa hỗ trợ giao hàng Hub-Hub
	switch configFee.FeeArea.Type {
	case enum.FeeArea.HUB_TO_HUB,
		enum.FeeArea.HUB_TO_WH,
		enum.FeeArea.WH_TO_HUB:
		fromCode := bookReq.FromHubCode
		if fromCode == "" {
			fromCode = bookReq.From.Code
		}
		toCode := bookReq.ToHubCode
		if toCode == "" {
			toCode = bookReq.To.Code
		}
		return isContainInAreaIdentifier(configFee.FeeArea.FromAreaIdentifiers, fromCode) &&
			isContainInAreaIdentifier(configFee.FeeArea.ToAreaIdentifiers, toCode)
	case enum.FeeArea.PROVINCE_TO_WH:
		toCode := bookReq.To.Code
		if bookReq.ToHubCode != "" {
			toCode = bookReq.ToHubCode
		}

		return isContainInAreaIdentifier(configFee.FeeArea.FromAreaIdentifiers, bookReq.From.ProvinceCode) &&
			isContainInAreaIdentifier(configFee.FeeArea.ToAreaIdentifiers, toCode)
	case enum.FeeArea.ZONE_TO_WH:
		queryFromZoneResult := model.ZoneDB.QueryOne(bson.M{
			"provinces.districts.wards.ward_code": bookReq.From.WardCode,
			"parent_code": bson.M{
				"$exists": false,
			},
			"is_deleted": false,
		})
		if queryFromZoneResult.Status != common.APIStatus.Ok {
			return false
		}
		fromZone := queryFromZoneResult.Data.([]*model.Zone)[0]
		toCode := bookReq.ToHubCode
		if toCode == "" {
			toCode = bookReq.To.Code
		}
		return isContainInAreaIdentifier(configFee.FeeArea.FromAreaIdentifiers, fromZone.Code) &&
			isContainInAreaIdentifier(configFee.FeeArea.ToAreaIdentifiers, toCode)
	case enum.FeeArea.WH_TO_PROVINCE:
		fromCode := bookReq.FromHubCode
		if fromCode == "" {
			fromCode = bookReq.From.Code
		}
		return isContainInAreaIdentifier(configFee.FeeArea.ToAreaIdentifiers, bookReq.To.ProvinceCode) &&
			isContainInAreaIdentifier(configFee.FeeArea.FromAreaIdentifiers, fromCode)
	case enum.FeeArea.WH_TO_ZONE:
		queryToZoneResult := model.ZoneDB.QueryOne(bson.M{
			"provinces.districts.wards.ward_code": bookReq.To.WardCode,
			"parent_code": bson.M{
				"$exists": false,
			},
			"is_deleted": false,
		})
		if queryToZoneResult.Status != common.APIStatus.Ok {
			return false
		}
		toZone := queryToZoneResult.Data.([]*model.Zone)[0]

		fromCode := bookReq.FromHubCode
		if fromCode == "" {
			fromCode = bookReq.From.Code
		}

		return isContainInAreaIdentifier(configFee.FeeArea.ToAreaIdentifiers, toZone.Code) &&
			isContainInAreaIdentifier(configFee.FeeArea.FromAreaIdentifiers, fromCode)

	case enum.FeeArea.INTERNAL_PROVINCE:
		return isContainInAreaIdentifier(configFee.FeeArea.FromAreaIdentifiers, bookReq.From.ProvinceCode) &&
			isContainInAreaIdentifier(configFee.FeeArea.ToAreaIdentifiers, bookReq.To.ProvinceCode)
	case enum.FeeArea.INTERNAL_ZONE:
		queryFromZoneResult := model.ZoneDB.QueryOne(bson.M{
			"provinces.districts.wards.ward_code": bookReq.From.WardCode,
			"parent_code": bson.M{
				"$exists": false,
			},
			"is_deleted": false,
		})

		queryToZoneResult := model.ZoneDB.QueryOne(bson.M{
			"provinces.districts.wards.ward_code": bookReq.To.WardCode,
			"parent_code": bson.M{
				"$exists": false,
			},
			"is_deleted": false,
		})

		if queryFromZoneResult.Status != common.APIStatus.Ok {
			return false
		}
		if queryToZoneResult.Status != common.APIStatus.Ok {
			return false
		}

		fromZone := queryFromZoneResult.Data.([]*model.Zone)[0]
		toZone := queryToZoneResult.Data.([]*model.Zone)[0]

		return isContainInAreaIdentifier(configFee.FeeArea.FromAreaIdentifiers, fromZone.Code) && isContainInAreaIdentifier(configFee.FeeArea.ToAreaIdentifiers, toZone.Code)
	case enum.FeeArea.NEAR_ZONE:
		queryFromZoneResult := model.ZoneDB.QueryOne(bson.M{
			"provinces.districts.wards.ward_code": bookReq.From.WardCode,
			"parent_code": bson.M{
				"$exists": false,
			},
			"is_deleted": false,
		})
		queryToZoneResult := model.ZoneDB.QueryOne(bson.M{
			"provinces.districts.wards.ward_code": bookReq.To.WardCode,
			"parent_code": bson.M{
				"$exists": false,
			},
			"is_deleted": false,
		})
		if queryFromZoneResult.Status != common.APIStatus.Ok {
			return false
		}
		if queryToZoneResult.Status != common.APIStatus.Ok {
			return false
		}
		fromZone := queryFromZoneResult.Data.([]*model.Zone)[0]
		toZone := queryToZoneResult.Data.([]*model.Zone)[0]
		return isContainInAreaIdentifier(configFee.FeeArea.FromAreaIdentifiers, fromZone.Code) && isContainInAreaIdentifier(configFee.FeeArea.ToAreaIdentifiers, toZone.Code) ||
			isContainInAreaIdentifier(configFee.FeeArea.FromAreaIdentifiers, toZone.Code) && isContainInAreaIdentifier(configFee.FeeArea.ToAreaIdentifiers, fromZone.Code)
	case enum.FeeArea.DISTANT_ZONE:
		queryFromZoneResult := model.ZoneDB.QueryOne(bson.M{
			"provinces.districts.wards.ward_code": bookReq.From.WardCode,
			"parent_code": bson.M{
				"$exists": false,
			},
			"is_deleted": false,
		})
		queryToZoneResult := model.ZoneDB.QueryOne(bson.M{
			"provinces.districts.wards.ward_code": bookReq.To.WardCode,
			"parent_code": bson.M{
				"$exists": false,
			},
			"is_deleted": false,
		})

		if queryFromZoneResult.Status != common.APIStatus.Ok {
			return false
		}
		if queryToZoneResult.Status != common.APIStatus.Ok {
			return false
		}

		fromZone := queryFromZoneResult.Data.([]*model.Zone)[0]
		toZone := queryToZoneResult.Data.([]*model.Zone)[0]
		return isContainInAreaIdentifier(configFee.FeeArea.FromAreaIdentifiers, fromZone.Code) && isContainInAreaIdentifier(configFee.FeeArea.ToAreaIdentifiers, toZone.Code) ||
			isContainInAreaIdentifier(configFee.FeeArea.ToAreaIdentifiers, toZone.Code) && isContainInAreaIdentifier(configFee.FeeArea.ToAreaIdentifiers, fromZone.Code)
	}
	return false
}

func isMatchNumPack(value int64, condition model.FeeCondition) bool {
	compareValue := int64(condition.Value.(float64))
	switch condition.Method {
	case enum.ComparableMethod.EQ:
		return compareValue == value
	case enum.ComparableMethod.LT:
		return value < compareValue
	case enum.ComparableMethod.GT:
		return value > compareValue
	case enum.ComparableMethod.LTE:
		return value <= compareValue
	case enum.ComparableMethod.GTE:
		return value >= compareValue
	}
	return false
}

func isMatchWeight(value float64, condition model.FeeCondition) bool {
	compareValue := utils.CeilFloat(condition.Value.(float64))
	switch condition.Method {
	case enum.ComparableMethod.EQ:
		return compareValue == value
	case enum.ComparableMethod.LT:
		return value < compareValue
	case enum.ComparableMethod.GT:
		return value > compareValue
	case enum.ComparableMethod.LTE:
		return value <= compareValue
	case enum.ComparableMethod.GTE:
		return value >= compareValue
	}
	return false
}

func isMatchTotalOrder(numOfOrder int64, condition model.FeeCondition) bool {
	compareValue := int64(condition.Value.(float64))
	switch condition.Method {
	case enum.ComparableMethod.EQ:
		return compareValue == numOfOrder
	case enum.ComparableMethod.LT:
		return numOfOrder < compareValue
	case enum.ComparableMethod.GT:
		return numOfOrder > compareValue
	case enum.ComparableMethod.LTE:
		return numOfOrder <= compareValue
	case enum.ComparableMethod.GTE:
		return numOfOrder >= compareValue
	}
	return false
}

func isMatchOrderType(orderType enum.ShippingOrderTypeValue, condition model.FeeCondition) bool {
	compareValueA := condition.Value.(primitive.A)
	compareValue := make([]string, len(compareValueA))
	for i, v := range compareValueA {
		compareValue[i] = v.(string)
	}
	switch condition.Method {
	case enum.ComparableMethod.EQ:
		for _, v := range compareValue {
			if v == string(orderType) {
				return true
			}
		}
	}
	return false
}

func isMatchTotalNumPackages(numPack int64, condition model.FeeCondition) bool {
	compareValue := int64(condition.Value.(float64))
	switch condition.Method {
	case enum.ComparableMethod.EQ:
		return compareValue == numPack
	case enum.ComparableMethod.LT:
		return numPack < compareValue
	case enum.ComparableMethod.GT:
		return numPack > compareValue
	case enum.ComparableMethod.LTE:
		return numPack <= compareValue
	case enum.ComparableMethod.GTE:
		return numPack >= compareValue
	}
	return false
}

func CalculateOrdersFee(orders []*model.ShippingOrder, configFee *model.ConfigFee) ([]*model.ShippingOrder, error) {
	if len(orders) == 0 {
		return nil, errors.New("no order to calculate fee")
	}

	if configFee.IsApplyToArea != nil &&
		*configFee.IsApplyToArea {
		return CalculateAreaOrdersFee(orders, configFee)
	}

	var totalOrders int64 = 0
	var totalPackages int64 = 0
	for _, order := range orders {
		totalOrders += 1
		if order.CheckinNumPack != 0 {
			order.NumPackage = order.CheckinNumPack
		}
		totalPackages += order.NumPackage
	}

	var needUpdateOrderFee []*model.ShippingOrder

	for _, order := range orders {
		for _, freeStruct := range configFee.FeeStructures {
			isMatchFeeStructure := false
			for _, condition := range freeStruct.FeeConditions {
				switch condition.ConditionType {
				case enum.FeeConditionType.PACKAGE:
					isMatchFeeStructure = isMatchNumPack(order.NumPackage, condition)
				case enum.FeeConditionType.WEIGHT:
					isMatchFeeStructure = isMatchWeight(order.Weight, condition)
				case enum.FeeConditionType.TOTAL_ORDER:
					isMatchFeeStructure = isMatchTotalOrder(totalOrders, condition)
				case enum.FeeConditionType.TOTAL_PACKAGE:
					isMatchFeeStructure = isMatchTotalNumPackages(totalPackages, condition)
				}
				// TODO: Nếu có loại mới thêm vào cụm switch case này để mở rộng
				if !isMatchFeeStructure {
					break
				}
			}

			if isMatchFeeStructure {
				var feePerPackage float64 = 0
				var feePercentOrderValue float64 = 0
				var additionalFee float64 = 0
				if fee, ok := freeStruct.Fees[enum.FeeType.FEE_PER_PACKAGE]; ok {
					feePerPackage = fee
				}

				if fee, ok := freeStruct.Fees[enum.FeeType.PERCENT_ORDER_VALUE]; ok {
					feePercentOrderValue = fee
				}

				if fee, ok := freeStruct.Fees[enum.FeeType.ADDITIONAL_FEE]; ok {
					additionalFee = fee
				}
				var zero float64 = 0
				if order.OrderValue == nil {
					order.OrderValue = &zero
				}
				// Công thức tính phí tổng quát:
				// Phí = Số lượng kiện * Phí kiện + Giá trị đơn hàng * phần trăm phí trên giá trị đơn hàng + Phí thêm
				calculatedFee := float64(order.NumPackage)*feePerPackage + *order.OrderValue*feePercentOrderValue + additionalFee
				// TODO: Add voucher validation and discount here, for now if voucher is not empty, we will not calculate fee
				if order.VoucherCode != "" {
					calculatedFee = 0
				}
				if calculatedFee != order.FeeAmount {
					order.FeeAmount = calculatedFee
					needUpdateOrderFee = append(needUpdateOrderFee, order)
				}
			}
		}
	}

	return needUpdateOrderFee, nil
}

func isContainInAreaIdentifier(areaIdentifier []model.AreaIdentifier, code string) bool {
	for _, a := range areaIdentifier {
		if a.Code == code {
			return true
		}
	}
	return false
}

func isContainsShippingOrderType(types []enum.ShippingOrderTypeValue, orderType enum.ShippingOrderTypeValue) bool {
	for _, t := range types {
		// If orderType is PO/PGH will be the same FMPO
		if t == enum.ShippingOrderType.FMPO &&
			(orderType == enum.ShippingOrderType.PO || orderType == enum.ShippingOrderType.PGH) {
			return true
		}
		if t == orderType {
			return true
		}
	}
	return false
}

func CalculateAreaOrdersFee(orders []*model.ShippingOrder, configFee *model.ConfigFee) ([]*model.ShippingOrder, error) {
	applyFeeAtIndexConfigFee := map[int][]*model.ShippingOrder{}
	splitOrderFilter := []*model.ShippingOrder{}
	for _, order := range orders {
		// Chi tinh tien don dau tien chu khong tinh tien don tach
		if order.SplitFromCode != "" {
			continue
		}
		splitOrderFilter = append(splitOrderFilter, order)
	}
	for _, order := range splitOrderFilter {
		for index, feeArea := range configFee.FeeAreaList {
			bookReq := shippingOrderToBookShippingReq(order)
			if isMatchFeeArea(bookReq, feeArea) {
				applyFeeAtIndexConfigFee[index] = append(applyFeeAtIndexConfigFee[index], order)
			}
		}
	}

	var needUpdateOrderFee []*model.ShippingOrder
	for atIndexFee, groupOrders := range applyFeeAtIndexConfigFee {
		for _, order := range groupOrders {
			for _, freeStruct := range configFee.FeeAreaList[atIndexFee].FeeStructures {
				isMatchFeeStructure := false
				for _, condition := range freeStruct.FeeConditions {
					switch condition.ConditionType {
					case enum.FeeConditionType.PACKAGE:
						isMatchFeeStructure = isMatchNumPack(order.NumPackage, condition)
					case enum.FeeConditionType.WEIGHT:
						isMatchFeeStructure = isMatchWeight(order.Weight, condition)
					case enum.FeeConditionType.TOTAL_ORDER:
						isMatchFeeStructure = isMatchTotalOrder(int64(len(groupOrders)), condition)
					case enum.FeeConditionType.TOTAL_PACKAGE:
						isMatchFeeStructure = isMatchTotalNumPackages(int64(len(groupOrders)), condition)
					case enum.FeeConditionType.ORDER_VALUE:
						if order.OrderValue == nil {
							break
						}
						isMatchFeeStructure = isMatchOrderValue(*order.OrderValue, condition)
					}
					// TODO: Nếu có loại mới thêm vào cụm switch case này để mở rộng
					if !isMatchFeeStructure {
						break
					}
				}
				if isMatchFeeStructure {
					var feePerPackage float64 = 0
					var feePercentOrderValue float64 = 0
					var additionalFee float64 = 0
					if fee, ok := freeStruct.Fees[enum.FeeType.FEE_PER_PACKAGE]; ok {
						feePerPackage = fee
					}

					if fee, ok := freeStruct.Fees[enum.FeeType.PERCENT_ORDER_VALUE]; ok {
						feePercentOrderValue = fee / 100
					}

					if fee, ok := freeStruct.Fees[enum.FeeType.ADDITIONAL_FEE]; ok {
						additionalFee = fee
					}
					var zero float64 = 0
					if order.OrderValue == nil {
						order.OrderValue = &zero
					}
					// Công thức tính phí tổng quát:
					// Phí = Số lượng kiện * Phí kiện + Giá trị đơn hàng * phần trăm phí trên giá trị đơn hàng + Phí thêm
					calculatedFee := float64(order.NumPackage)*feePerPackage + *order.OrderValue*feePercentOrderValue + additionalFee
					// TODO: Add voucher validation and discount here, for now if voucher is not empty, we will not calculate fee
					if order.VoucherCode != "" {
						calculatedFee = 0
					}
					if calculatedFee != order.FeeAmount ||
						order.VoucherCode != "" ||
						calculatedFee == 0 {
						order.FeeAmount = calculatedFee
						needUpdateOrderFee = append(needUpdateOrderFee, order)
					}
				}
			}
		}
	}
	return needUpdateOrderFee, nil
}

func isMatchOrderValue(value float64, condition model.FeeCondition) bool {
	compareValue := utils.CeilFloat(condition.Value.(float64))
	switch condition.Method {
	case enum.ComparableMethod.EQ:
		return compareValue == value
	case enum.ComparableMethod.LT:
		return value < compareValue
	case enum.ComparableMethod.GT:
		return value > compareValue
	case enum.ComparableMethod.LTE:
		return value <= compareValue
	case enum.ComparableMethod.GTE:
		return value >= compareValue
	}
	return false
}

func findMatchInternalProvince(fromAddress model.Address, toAddress model.Address, configFee model.ConfigFee) ([]model.FeeStructure, error) {
	queryFromZoneResult := model.ZoneDB.QueryOne(bson.M{
		"provinces.districts.district_code": fromAddress.DistrictCode,
		"parent_code": bson.M{
			"$exists": false,
		},
		"is_deleted": false,
	})
	queryToZoneResult := model.ZoneDB.QueryOne(bson.M{
		"provinces.districts.district_code": toAddress.DistrictCode,
		"parent_code": bson.M{
			"$exists": false,
		},
		"is_deleted": false,
	})
	if queryFromZoneResult.Status != common.APIStatus.Ok {
		return nil, fmt.Errorf("cannot find zone of from address")
	}
	if queryToZoneResult.Status != common.APIStatus.Ok {
		return nil, fmt.Errorf("cannot find zone of to address")
	}
	fromZone := queryFromZoneResult.Data.([]*model.Zone)[0]
	toZone := queryToZoneResult.Data.([]*model.Zone)[0]

	var fromZoneAreaLevel *enum.AreaLevelValue
	var toZoneAreaLevel *enum.AreaLevelValue

outFrom:
	for _, province := range fromZone.Provinces {
		for _, district := range province.DistrictArea {
			if district.DistrictCode == fromAddress.DistrictCode {
				if district.AreaLevel == nil {
					district.AreaLevel = &enum.AreaLevel.URBAN
				}
				fromZoneAreaLevel = district.AreaLevel
				break outFrom
			}
		}
	}
outTo:
	for _, province := range toZone.Provinces {
		for _, district := range province.DistrictArea {
			if district.DistrictCode == toAddress.DistrictCode {
				if district.AreaLevel == nil {
					district.AreaLevel = &enum.AreaLevel.URBAN
				}
				toZoneAreaLevel = district.AreaLevel
				break outTo
			}
		}
	}

	if fromZoneAreaLevel == nil || toZoneAreaLevel == nil {
		return nil, fmt.Errorf("cannot find zone area level")
	}

	checkAtAreaLevel := SelectFeeBaseOnAreaLevel(*fromZoneAreaLevel, *toZoneAreaLevel)

	// Group fee structure by province code
	for _, feeArea := range configFee.FeeAreaList {
		if feeArea.FeeArea.Type == enum.FeeArea.INTERNAL_PROVINCE &&
			isContainInAreaIdentifier(
				feeArea.FeeArea.FromAreaIdentifiers,
				fromAddress.ProvinceCode,
			) &&
			isContainsAreaLevel(feeArea.FeeArea.AreaLevels, checkAtAreaLevel) {
			return feeArea.FeeStructures, nil
		}
	}

	return nil, fmt.Errorf("cannot find fee structure")
}

func calculateShippingFeeByWh(
	packNumber int64, weight float64,
	fromWhCode, fromProvinceCode, toProvinceCode string,
	shippingOrder *model.ShippingOrder,
	isReceiveAtLMHub bool) (shippingFee float64) {
	// Nếu chưa có config LOGISTIC
	config := model.ConfigLogisticDB.QueryOne(bson.M{"key": "LOGISTIC"})
	if config.Status != common.APIStatus.Ok {
		return DefaultFeeStructure(packNumber, fromProvinceCode, toProvinceCode)
	}
	if isReceiveAtLMHub &&
		shippingOrder.CustomerInfo != nil &&
		shippingOrder.CustomerInfo.CustomerType != nil {
		configs, err := getDropOffFeeByWh()
		if err != nil || len(configs) == 0 {
			return DefaultFeeStructure(packNumber, fromProvinceCode, toProvinceCode)
		}
		feeConfig := 0.0
		for _, c := range configs {
			if c.WarehouseCode == fromWhCode &&
				*shippingOrder.CustomerInfo.CustomerType == enum.CustomerType.SELLER {
				if strings.HasPrefix(shippingOrder.ReferenceCode, "RONS") {
					feeConfig = c.RONSSellerFeeAmount
					break
				}
				if strings.HasPrefix(shippingOrder.ReferenceCode, "RO") {
					feeConfig = c.ROSellerFeeAmount
					break
				}
			}
			if c.WarehouseCode == fromWhCode &&
				*shippingOrder.CustomerInfo.CustomerType == enum.CustomerType.VENDOR {
				if strings.HasPrefix(shippingOrder.ReferenceCode, "RONS") {
					feeConfig = c.RONSVendorFeeAmount
					break
				}
				if strings.HasPrefix(shippingOrder.ReferenceCode, "RO") {
					feeConfig = c.ROVendorFeeAmount
					break
				}
			}
		}
		shippingFee = float64(packNumber) * feeConfig
		return shippingFee
	}

	feeConfigI, ok := config.Data.([]*model.ConfigLogistic)[0].Configs["FEE_TO_PROVINCE"]
	// Nếu chưa có trường FEE_TO_PROVINCE
	if !ok {
		return DefaultFeeStructure(packNumber, fromProvinceCode, toProvinceCode)
	}
	feeConfigS := feeConfigI.(string)
	decoded, err := base64.StdEncoding.DecodeString(feeConfigS)
	if err != nil {
		return DefaultFeeStructure(packNumber, fromProvinceCode, toProvinceCode)
	}
	configMap := map[string][]model.FeeStructure{}
	// Nếu không thể Unmarshal do config sai format
	err = json.Unmarshal(decoded, &configMap)
	if err != nil {
		return DefaultFeeStructure(packNumber, fromProvinceCode, toProvinceCode)
	}

	// Nếu có config fee cho tỉnh
	if value, ok := configMap[toProvinceCode]; ok {
		for _, feeStruct := range value {
			isMatchFeeStructure := false
			for _, condition := range feeStruct.FeeConditions {
				switch condition.ConditionType {
				case enum.FeeConditionType.PACKAGE:
					isMatchFeeStructure = isMatchNumPack(packNumber, condition)
				case enum.FeeConditionType.WEIGHT:
					isMatchFeeStructure = isMatchWeight(weight, condition)
				}
				// TODO: Nếu có loại mới thêm vào cụm switch case này để mở rộng
				if !isMatchFeeStructure {
					break
				}
			}

			if isMatchFeeStructure {
				shippingFee = float64(packNumber)*feeStruct.Fees[enum.FeeType.FEE_PER_PACKAGE] +
					feeStruct.Fees[enum.FeeType.ADDITIONAL_FEE]
				return shippingFee
			}
		}
	}
	return DefaultFeeStructure(packNumber, fromProvinceCode, toProvinceCode)
}

func SelectFeeBaseOnAreaLevel(from enum.AreaLevelValue, to enum.AreaLevelValue) enum.AreaLevelValue {
	if from == to {
		return from
	}

	if from == enum.AreaLevel.EXURBAN ||
		to == enum.AreaLevel.EXURBAN {
		return enum.AreaLevel.EXURBAN
	}

	if from == enum.AreaLevel.SUBURBAN ||
		to == enum.AreaLevel.SUBURBAN {
		return enum.AreaLevel.SUBURBAN
	}

	return enum.AreaLevel.URBAN
}

func isContainsAreaLevel(values []enum.AreaLevelValue, value enum.AreaLevelValue) bool {
	for _, v := range values {
		if v == value {
			return true
		}
	}
	return false
}

func isMatchNearOrDistanceZone(
	fromZoneCode string,
	toZoneCode string,
	arrAreaIdentifiers [][]model.AreaIdentifier) bool {

	for _, areaIdentifiers := range arrAreaIdentifiers {
		if isContainInAreaIdentifier(areaIdentifiers, fromZoneCode) &&
			isContainInAreaIdentifier(areaIdentifiers, toZoneCode) {
			return true
		}
	}
	return false
}

func isContainsAdditionalFee(
	additionalFee []model.AdditionalFee,
	additionalFeeType enum.AdditionalFeeTypeValue) bool {
	if len(additionalFee) == 0 {
		return false
	}
	for _, fee := range additionalFee {
		if fee.AdditionalFeeType == additionalFeeType {
			return true
		}
	}
	return false

}

func isContainsTags(tags []string, tag string) bool {
	for _, t := range tags {
		if t == tag {
			return true
		}
	}
	return false
}

func isContainsProductType(productTypes []enum.ProductTypeValue, productType enum.ProductTypeValue) bool {
	for _, t := range productTypes {
		if t == productType {
			return true
		}
	}
	return false
}

func getDropOffFeeByWh() ([]model.DropOffFeeByWarehouse, error) {
	config := model.ConfigLogisticDB.QueryOne(bson.M{"key": "LOGISTIC"})
	if config.Status != common.APIStatus.Ok {
		return nil, &common.Error{
			Type:    "CONFIG_ERROR",
			Message: "Thiếu thông tin config logistic",
		}
	}
	warehouseHubConfigRaw, ok := config.Data.([]*model.ConfigLogistic)[0].Configs["DROP_OFF_FEE_BY_WH"]
	if !ok {
		return nil, &common.Error{
			Type:    "CONFIG_ERROR",
			Message: "Thiếu thông tin config mapping hub và kho",
		}
	}

	warehouseHubConfigArr, ok := warehouseHubConfigRaw.(bson.A)
	if !ok {
		return nil, &common.Error{
			Type:    "CONFIG_ERROR",
			Message: "Định dạng config mapping hub và kho không hợp lệ",
		}
	}

	feeConfig := []model.DropOffFeeByWarehouse{}
	var builder strings.Builder
	for i, v := range warehouseHubConfigArr {
		if str, ok := v.(string); ok {
			builder.WriteString(str)
			if i == len(warehouseHubConfigArr)-1 {
				continue
			}
			builder.WriteString(",")
		}
	}
	err := json.Unmarshal([]byte(builder.String()), &feeConfig)
	if err != nil {
		return nil, &common.Error{
			Type:    "CONFIG_ERROR",
			Message: "Định dạng config mapping hub và kho không hợp lệ",
		}
	}

	return feeConfig, nil
}

func findMatchHubToHub(fromHub, toHub string, configFee model.ConfigFee) ([]model.FeeStructure, error) {
	for _, feeArea := range configFee.FeeAreaList {
		if feeArea.FeeArea.Type == enum.FeeArea.HUB_TO_HUB {
			for _, areaIdentifiers := range feeArea.FeeArea.ArrAreaIdentifiers {
				if isContainInAreaIdentifier(areaIdentifiers, fromHub) &&
					isContainInAreaIdentifier(areaIdentifiers, toHub) {
					return feeArea.FeeStructures, nil
				}
			}
		}

	}
	return nil, fmt.Errorf("cannot find fee structure")
}
