package api

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action"
	tplCallbackConsumer "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action/consumer/tpl-callback"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func UpdateAccountTotalReceiveAmount(req sdk.APIRequest, resp sdk.APIResponder) error {
	type updateReq struct {
		DriverId int64   `json:"driverId"`
		HubCode  string  `json:"hubCode"`
		Amount   float64 `json:"amount"`
	}
	var input updateReq
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.UpdateAccountTotalReceiveAmount(input.DriverId, input.HubCode, input.Amount))
}

func FixOrderAmount(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input request.UpdateOrderFee
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.FixOrderAmount(input))
}

func MigrateParentReceiveSessionCode(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.MigrateParentReceiveSessionCode())
}

func SetWarehouseRefCode(req sdk.APIRequest, resp sdk.APIResponder) error {
	type updateReq struct {
		HubId                  int64  `json:"hubId"`
		WarehouseReferenceCode string `json:"warehouseReferenceCode"`
	}
	var input updateReq
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.SetWarehouseReferenceCode(input.HubId, input.WarehouseReferenceCode))

}

func UnSetWarehouseRefCode(req sdk.APIRequest, resp sdk.APIResponder) error {
	type updateReq struct {
		HubId int64 `json:"hubId"`
	}
	var input updateReq
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.UnSetWarehouseReferenceCode(input.HubId))
}

func DeleteCustomer(req sdk.APIRequest, resp sdk.APIResponder) error {
	type updateReq struct {
		CustomerId int64 `json:"customerId"`
	}

	var input updateReq
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.DeleteCustomer(input.CustomerId))
}

func TestShippingOrderFee(req sdk.APIRequest, resp sdk.APIResponder) error {
	type updateReq struct {
		BookShippingOrder request.BookShippingOrder `json:"bookRequest"`
		ConfigId          int64                     `json:"configId"`
	}

	var input updateReq
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.TestShippingOrderFee(input.BookShippingOrder, input.ConfigId))
}

func SetUpDefaultFee(req sdk.APIRequest, resp sdk.APIResponder) error {
	type updateReq struct {
		FromCode string `json:"fromCode"`
		ToCode   string `json:"toCode"`
	}

	var input updateReq
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	if input.FromCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "From code can not be empty",
		})
	}

	return resp.Respond(action.SetUpDefaultFee(input.FromCode, input.ToCode))
}

func MigratePickedNDeliveredLog(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.MigratePickedNDeliveredLog())
}

func ToolUpdateRoute(req sdk.APIRequest, resp sdk.APIResponder) error {
	type updateReq struct {
		RouteCode string `json:"routeCode"`
		Status    string `json:"status"`
	}

	var input updateReq
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.ToolUpdateRoute(input.RouteCode, input.Status))
}

func MigrateCustomerPhone(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.MigrateCustomerPhone())
}

func MigrateDonePackTime(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.MigrateDonePackTime())
}

func ChangeHandoverSOCode(req sdk.APIRequest, resp sdk.APIResponder) error {
	type updateReq struct {
		HandoverCode string `json:"handoverCode"`
		OldSoCode    string `json:"oldSoCode"`
		NewSoCode    string `json:"newSoCode"`
	}
	var input updateReq
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.ChangeHandoverSOCode(input.HandoverCode, input.OldSoCode, input.NewSoCode))
}

func UpdateReferenceCode(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input []request.UpdateReferenceCodeRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.UpdateReferenceCode(input))
}

func ToolUpdateAccountStatus(req sdk.APIRequest, resp sdk.APIResponder) error {
	type updateReq struct {
		AccountId int    `json:"accountId"`
		HubCode   string `json:"hubCode"`
		Status    string `json:"status"`
	}
	var input updateReq
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.ToolUpdateAccountStatus(input.AccountId, input.HubCode, input.Status))
}

func UpdateHubOrderHub(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input request.UpdateHubOrder
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.UpdateHubOrderHub(input))
}

func RevertPreviousBinStatus(req sdk.APIRequest, resp sdk.APIResponder) error {
	type updateReq struct {
		ReferenceCodes []string `json:"referenceCodes"`
		RequireUpdate  bool     `json:"requireUpdate"`
	}
	var input updateReq
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.RevertPreviousBinStatus(input.ReferenceCodes, input.RequireUpdate))
}

func MigrateReferences(req sdk.APIRequest, resp sdk.APIResponder) error {
	type reqStruct struct {
		ReferenceCodes []string `json:"referenceCodes"`
	}
	var input reqStruct
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.MigrateReferences(input.ReferenceCodes))

}

// MigrateShippingOrderSnapshotStatus Take a snapshot of the current status of shipping order to cache it
func MigrateShippingOrderSnapshotStatus(req sdk.APIRequest, resp sdk.APIResponder) error {
	type updateReq struct {
		ToTime time.Time `json:"toTime"`
	}
	var input updateReq
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.MigrateShippingOrderSnapshotStatus(input.ToTime))
}

// MigrateHubOrderSnapshotStatus Take a snapshot of the current status of hub shipping order to cache it
func MigrateHubOrderSnapshotStatus(req sdk.APIRequest, resp sdk.APIResponder) error {
	type updateReq struct {
		ToTime time.Time `json:"toTime"`
	}
	var input updateReq
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.MigrateHubOrderSnapshotStatus(input.ToTime))
}

// Sometimes operation config zone incorrectly and assign order to wrong handler, use this function will help to fix it
func ToolChangeOrderCarrier(req sdk.APIRequest, resp sdk.APIResponder) error {
	type updateReq struct {
		ReferenceCodes []string `json:"referenceCodes"`
		CarrierId      int      `json:"carrierId"`
		HubCode        string   `json:"hubCode"`
	}
	var input updateReq
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.ToolChangeOrderCarrier(input.ReferenceCodes, input.CarrierId, input.HubCode))
}

// DeleteExecuteQueueMessage
func DeleteExecuteQueueMessage(req sdk.APIRequest, resp sdk.APIResponder) error {
	type request struct {
		UniqueKey  string   `json:"uniqueKey,omitempty"`
		UniqueKeys []string `json:"uniqueKeys,omitempty"`
		FailCount  int      `json:"failCount,omitempty"`
		Topic      string   `json:"topic,omitempty"`
	}
	var input request
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	dbInstance := tplCallbackConsumer.GetExecuteQueueDatabase()
	if dbInstance == nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   "Không kết nối được database.",
			ErrorCode: "SERVER_ERROR",
		})
	}

	filter := bson.M{}

	if len(input.UniqueKeys) > 0 {
		filter["unique_key"] = bson.M{
			"$in": input.UniqueKeys,
		}
	}

	if input.UniqueKey != "" {
		filter["unique_key"] = input.UniqueKey
	}

	if input.FailCount > 0 {
		filter["fail_count"] = bson.M{
			"$gt": input.FailCount,
		}
	}

	if input.Topic != "" {
		filter["topic"] = input.Topic
	}

	if len(filter) == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Chưa truyền param",
			ErrorCode: "PARAM_INVALID",
		})
	}

	return resp.Respond(dbInstance.Delete(filter))
}

func UpdateHubOrderProductStatus(req sdk.APIRequest, resp sdk.APIResponder) error {
	type myRequest struct {
		HubCode       string                  `json:"hubCode"`
		ReferenceCode string                  `json:"referenceCode"`
		SKU           string                  `json:"sku"`
		Status        enum.ProductStatusValue `json:"status"`
	}

	var input myRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data: " + err.Error(),
			ErrorCode: "JSON_INVALID",
		})
	}

	return resp.Respond(action.UpdateHubOrderProductStatus(input.HubCode, input.ReferenceCode, input.SKU, input.Status))
}

func CreateWardMapping(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.WardMapping
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data: " + err.Error(),
			ErrorCode: "JSON_INVALID",
		})
	}

	if input.Code == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "WARD_CODE_REQUIRED",
			ErrorCode: "WARD_CODE_REQUIRED",
		})
	}

	afterOption := options.After
	return resp.Respond(model.WardMappingDB.UpdateOne(bson.M{
		"code": input.Code,
	}, input, &options.FindOneAndUpdateOptions{
		Upsert:         &enum.True,
		ReturnDocument: &afterOption,
	}))
}

func BulkUpdateHubBank(req sdk.APIRequest, resp sdk.APIResponder) error {
	type myRequest struct {
		HubCodes []string   `json:"hubCodes"`
		HubInfo  *model.Hub `json:"hubInfo"`
	}
	var input myRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data: " + err.Error(),
			ErrorCode: "JSON_INVALID",
		})
	}

	return resp.Respond(action.BulkUpdateHubBank(input.HubCodes, input.HubInfo))
}
