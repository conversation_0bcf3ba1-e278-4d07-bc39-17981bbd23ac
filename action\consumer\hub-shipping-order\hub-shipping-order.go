package hub_shipping_order

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action/consumer/backup"
	sync_data "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action/consumer/tpl-callback"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func (j *ExecutorJob) updateHubShippingOrder() {
	j.Job.SetTopicConsumer(updateHubShippingOrderTopic, func(item *job.JobItem) (err error) {
		var dataByte []byte
		dataByte, err = bson.Marshal(item.Data)
		if err != nil {
			return
		}

		updateHubOrderRequest := request.UpdateHubOrder{}
		err = bson.Unmarshal(dataByte, &updateHubOrderRequest)
		if err != nil {
			return
		}

		filter := bson.M{}
		filter["reference_code"] = updateHubOrderRequest.ReferenceCode
		filter["hub_code"] = updateHubOrderRequest.HubCode
		getHubShippingOrder := model.HUBShippingOrderDB.Query(filter, 0, 100, nil)

		if getHubShippingOrder.Status != common.APIStatus.Ok {
			return nil
		}
		hubShippingOrder := getHubShippingOrder.Data.([]*model.HubShippingOrder)[0]
		var oldStatus enum.HubShippingOrderStatusValue
		if hubShippingOrder.Status != nil {
			oldStatus = *hubShippingOrder.Status
		}
		now := time.Now()
		hubShippingOrder.VersionNo = uuid.New().String()
		hubShippingOrder.Status = updateHubOrderRequest.Status
		if updateHubOrderRequest.TplCode != nil {
			hubShippingOrder.TplCode = updateHubOrderRequest.TplCode
		}

		hubShippingOrder.TplName = updateHubOrderRequest.TplName
		hubShippingOrder.ActionName = updateHubOrderRequest.ActionName
		hubShippingOrder.Action = updateHubOrderRequest.Action
		hubShippingOrder.DriverID = updateHubOrderRequest.DriverID
		hubShippingOrder.DriverName = updateHubOrderRequest.DriverName
		hubShippingOrder.ActionTime = now.Unix()

		if hubShippingOrder.StoredTime == 0 {
			hubShippingOrder.StoredTime = now.Unix()
		}

		if updateHubOrderRequest.ReceiveQuantity > 0 {
			hubShippingOrder.ReceivePackage += updateHubOrderRequest.ReceiveQuantity
		}

		// Upadate new weight, new numpackage
		var newWeight float64
		var newNumPackage int64
		for _, p := range updateHubOrderRequest.Products {
			if p.Status != nil && *p.Status == enum.ProductStatus.STORING {
				newWeight += p.Weight
				newNumPackage += 1
			}
		}

		// Nếu số kiện > 0, những bin LOST -> REMOVED
		if newNumPackage > 0 {
			for _, p := range updateHubOrderRequest.Products {
				if p.Status != nil && *p.Status == enum.ProductStatus.LOST {
					p.Status = &enum.ProductStatus.REMOVED
				}
			}
		}
		hubShippingOrder.Weight = newWeight
		hubShippingOrder.NumPackage = newNumPackage

		if updateHubOrderRequest.HandoverTicketCode != "" ||
			updateHubOrderRequest.HandoverTicketNote != "" ||
			updateHubOrderRequest.HandoverTicketId > 0 {
			if hubShippingOrder.ExtraInfo == nil {
				hubShippingOrder.ExtraInfo = map[string]interface{}{}
			}
			handOverInfoLogs := HandoverLog{
				HandoverTicketId:   updateHubOrderRequest.HandoverTicketId,
				HandoverTickerCode: updateHubOrderRequest.HandoverTicketCode,
				HandoverTicketNote: updateHubOrderRequest.HandoverTicketNote,
			}

			if hubShippingOrder.ExtraInfo["logs"] == nil {
				hubShippingOrder.ExtraInfo["logs"] = []HandoverLog{handOverInfoLogs}
			} else {
				var handoverLog interface{}
				var oldLogs []interface{}
				handoverLogsByte, _ := json.Marshal(handOverInfoLogs)
				_ = json.Unmarshal(handoverLogsByte, &handoverLog)
				if logs, ok := hubShippingOrder.ExtraInfo["logs"].(primitive.A); ok {
					oldLogs = logs
				}

				oldLogs = append(oldLogs, handoverLog)
				hubShippingOrder.ExtraInfo["logs"] = oldLogs
			}

			if updateHubOrderRequest.HandoverTicketCode != "" {
				hubShippingOrder.ExtraInfo["handoverNote"] = updateHubOrderRequest.HandoverTicketNote
			}

			if updateHubOrderRequest.HandoverTicketCode != "" {
				hubShippingOrder.ExtraInfo["handoverCode"] = updateHubOrderRequest.HandoverTicketCode
			}

			if updateHubOrderRequest.HandoverTicketId > 0 {
				hubShippingOrder.ExtraInfo["handoverTicket"] = updateHubOrderRequest.HandoverTicketId
			}
		}

		if hubShippingOrder.Status != nil && updateHubOrderRequest.Status != nil {
			if *hubShippingOrder.Status != *updateHubOrderRequest.Status {
				logs := model.HubShippingOrder{
					ActionTime:    hubShippingOrder.ActionTime,
					Status:        hubShippingOrder.Status,
					Note:          hubShippingOrder.Note,
					PrivateNote:   hubShippingOrder.PrivateNote,
					ExtraInfo:     hubShippingOrder.ExtraInfo,
					LastUpdatedBy: hubShippingOrder.LastUpdatedBy,
				}
				hubShippingOrder.Logs = append(hubShippingOrder.Logs, logs)
			}
		}

		// Update product status
		if len(updateHubOrderRequest.Products) > 0 {
			for _, updateHubOrderProduct := range updateHubOrderRequest.Products {
				for _, hubOrderProduct := range hubShippingOrder.Products {
					if updateHubOrderProduct.SKU == hubOrderProduct.SKU {
						hubOrderProduct.Status = updateHubOrderProduct.Status
					}
				}
			}
		}

		// Nếu là internal transfer và check in ở inbound wms thì chuyển đơn sang đã thu COD và tạo phiên đối soát hub comp
		if hubShippingOrder.SubType != nil &&
			*hubShippingOrder.SubType == enum.SubType.INTERNAL_TRANS &&
			updateHubOrderRequest.CheckInAt == enum.CheckInAt.WAREHOUSE {
			hubShippingOrder.Status = &enum.HubShippingOrderStatus.COD_COLLECTED
			hubShippingOrder.DeliveredTime = now.Unix()
			_, _ = CreateReconcile(hubShippingOrder, string(enum.ReconcileTypeRequest.HUB_COMP))
		}

		afterOption := options.After
		updateResult := model.HUBShippingOrderDB.UpdateOne(
			bson.M{
				"reference_code": updateHubOrderRequest.ReferenceCode,
				"hub_code":       updateHubOrderRequest.HubCode,
			},
			hubShippingOrder,
			&options.FindOneAndUpdateOptions{
				ReturnDocument: &afterOption,
			},
		)

		if updateResult.Status != common.APIStatus.Ok {
			if item.FailCount%100 == 0 {
				syncDataByte, _ := json.Marshal(hubShippingOrder)
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "UPDATE_HUB_SHIPPING_ORDER_FAILED",
					Title:   fmt.Sprintf("UPDATE_FAIL_HUB_SHIPPING_ORDER %v - %d", hubShippingOrder.ReferenceCode, item.FailCount),
					Message: "================================ " + string(syncDataByte) + "\n" + updateResult.Message,
				})
			}
			if item.FailCount == 50 {
				return nil
			}

			time.Sleep(1000 * time.Millisecond)
			return fmt.Errorf(updateResult.Message)
		}

		if updateHubOrderRequest.Status != nil && (oldStatus != *updateHubOrderRequest.Status || *updateHubOrderRequest.Status == enum.HubShippingOrderStatus.LOST) {
			callback := request.Callback{
				ActionTime: &now,
				SO:         hubShippingOrder.ReferenceCode,
			}

			// Check shipping order type
			shippingOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
				"reference_code": updateHubOrderRequest.ReferenceCode,
			})

			if shippingOrderRaw.Status != common.APIStatus.Ok {
				syncDataByte, _ := json.Marshal(hubShippingOrder)
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "NOT_FOUND_SHIPPING_ORDER",
					Title:   fmt.Sprintf("Không tìm thấy shipping order cho đơn %v", updateHubOrderRequest.ReferenceCode),
					Message: string(syncDataByte),
				})
			}

			shippingOrder := shippingOrderRaw.Data.([]*model.ShippingOrder)[0]

			// Nếu loại đơn là trả hàng và checkin ở kho thì tự hoàn thành đơn trả hàng khi hoàn thành NHẬN luân chuyển
			if shippingOrder.ShippingType != nil &&
				*shippingOrder.ShippingType == enum.ShippingOrderType.RETURN &&
				updateHubOrderRequest.CheckInAt == enum.CheckInAt.WAREHOUSE {

				hubShippingOrder.Status = &enum.HubShippingOrderStatus.COMPLETED
				// Cập nhật shipping order sang completed
				updateShippingOrderResult := model.ShippingOrderDB.UpdateOne(bson.M{
					"reference_code": updateHubOrderRequest.ReferenceCode,
				}, bson.M{
					"status": &enum.TPLCallbackStatus.COMPLETED,
				})
				// Cập nhật hub order sang completed
				updateHubOrderResult := model.HUBShippingOrderDB.UpdateOne(bson.M{
					"reference_code": updateHubOrderRequest.ReferenceCode,
					"hub_code":       updateHubOrderRequest.HubCode,
				}, bson.M{
					"status": &enum.HubShippingOrderStatus.COMPLETED,
				})

				if updateShippingOrderResult.Status != common.APIStatus.Ok || updateHubOrderResult.Status != common.APIStatus.Ok {
					syncDataByte, _ := json.Marshal(updateHubOrderRequest)
					_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
						CmdType: "UPDATE_HUB_SHIPPING_ORDER_FAILED",
						Title:   fmt.Sprintf("UPDATE_FAIL_HUB_SHIPPING_ORDER %v - %d", hubShippingOrder.ReferenceCode),
						Message: string(syncDataByte) + "\n" +
							"Update shipping order message: " + updateShippingOrderResult.Message +
							"Update hub order message: " + updateHubOrderResult.Message,
					})
				}
			}

			if shippingOrder.ShippingType == nil {
				callback.Type = &enum.ShippingOrderType.DELIVERY
			} else {
				callback.Type = shippingOrder.ShippingType
			}

			// Nếu số kiện == 0 và loại đơn lấy bin thì reset received pack của hub order về 0 để quét bổ sung lost
			if newNumPackage == 0 && shippingOrder.ShippingType != nil && *shippingOrder.ShippingType == enum.ShippingOrderType.PICKUP {
				model.HUBShippingOrderDB.UpdateOne(
					bson.M{
						"reference_code": updateHubOrderRequest.ReferenceCode,
						"hub_code":       updateHubOrderRequest.HubCode,
					},
					bson.M{
						"receive_ package": 0,
					},
				)
			}

			hubName := updateHubOrderRequest.HubCode
			hubResp := model.HubDB.QueryOne(bson.M{"code": updateHubOrderRequest.HubCode})
			if hubResp.Status == common.APIStatus.Ok {
				hub := hubResp.Data.([]*model.Hub)[0]
				hubName = hub.Name
			}

			switch *hubShippingOrder.Status {
			case enum.HubShippingOrderStatus.STORING:
				callback.Status = &enum.TPLCallbackStatus.STORING
				callback.StatusName = "Đã nhập kho/Hub: " + hubName
				callback.TPLStatusName = "Đã nhập kho/Hub: " + hubName
				callback.TPLStatus = string(enum.TPLCallbackStatus.STORING)
				callback.HubCode = updateHubOrderRequest.HubCode
				_ = sync_data.PushCreateTPLCallbackQueue(callback, hubShippingOrder.ReferenceCode)
				break
			case enum.HubShippingOrderStatus.LOST:
				callback.Status = &enum.TPLCallbackStatus.LOST
				callback.StatusName = "Đã nhập kho/Hub: " + hubName
				callback.TPLStatusName = "Đã nhập kho/Hub: " + hubName
				callback.TPLStatus = string(enum.TPLCallbackStatus.LOST)
				callback.Reason = "Nhập kho thiếu kiện."
				callback.HubCode = updateHubOrderRequest.HubCode
				_ = sync_data.PushCreateTPLCallbackQueue(callback, hubShippingOrder.ReferenceCode)
				break
			case enum.HubShippingOrderStatus.COMPLETED:
				callback.Status = &enum.TPLCallbackStatus.COMPLETED
				callback.StatusName = "Đã hoàn thành"
				callback.TPLStatusName = "Đã hoàn thành"
				callback.TPLStatus = string(enum.TPLCallbackStatus.COMPLETED)
				callback.HubCode = updateHubOrderRequest.HubCode
				_ = sync_data.PushCreateTPLCallbackQueue(callback, hubShippingOrder.ReferenceCode)
			case enum.HubShippingOrderStatus.COD_COLLECTED:
				callback.Status = &enum.TPLCallbackStatus.DELIVERED
				callback.StatusName = "Giao hàng thành công"
				callback.TPLStatusName = "Giao hàng thành công"
				callback.TPLStatus = string(enum.TPLCallbackStatus.DELIVERED)
				callback.HubCode = updateHubOrderRequest.HubCode
				_ = sync_data.PushCreateTPLCallbackQueue(callback, hubShippingOrder.ReferenceCode)
			}

			// Kiểm tra Hub nguồn
			getHub := model.HubDB.QueryOne(bson.M{"code": updateHubOrderRequest.HubCode})
			hub := getHub.Data.([]*model.Hub)[0]
			var failProducts []string
			if hub.WarehouseReferenceCode != "" &&
				shippingOrder.ShippingType != nil &&
				*shippingOrder.ShippingType == enum.ShippingOrderType.PICKUP &&
				oldStatus == enum.HubShippingOrderStatus.WAIT_TO_STORING &&
				hubShippingOrder.Status != nil &&
				*hubShippingOrder.Status == enum.HubShippingOrderStatus.STORING {
				for _, product := range updateHubOrderRequest.Products {
					// Kiểm tra trạng thái sản phẩm
					if product.Status != nil && *product.Status == enum.ProductStatus.STORING {
						_, err := client.Services.WarehouseCoreClient.ResetLocationWarehouse(hub.WarehouseReferenceCode, product.SKU)
						// Kiểm tra sản phẩm có tồn tại bên WH
						if err != nil {
							failProducts = append(failProducts, product.SKU)
						}
					}
				}

				// Nếu có các đơn BIN bị lỗi => thông báo các đơn (BIN) bị lỗi qua telegram
				if len(failProducts) > 0 {
					err = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
						CmdType: "UNABLE_RESET_LOCATION",
						Title:   "CANT RESET LOCATION BIN",
						Message: fmt.Sprintf("%v", failProducts),
					})
				}

				// Cập nhật trạng thái đơn => COMPLETED
				referenceCodes := []string{updateHubOrderRequest.ReferenceCode}
				err := client.Services.TransportingClient.CompleteShippingOrder(referenceCodes)
				if err != nil {
					err = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
						CmdType: "UNABLE_COMPLETE_SHIPPING_ORDER",
						Title:   "CANT COMPLETE SHIPPING ORDER",
						Message: fmt.Sprintf("%v", failProducts),
					})
				}
			}
		}

		return nil
	})
}

func (j *ExecutorJob) createHubShippingOrder() {
	j.Job.SetTopicConsumer(hubShippingOrderTopic, func(item *job.JobItem) (err error) {
		var dataByte []byte
		dataByte, err = bson.Marshal(item.Data)
		if err != nil {
			return
		}

		dataHandover := request.HandoverTicketCreateHubOrder{}
		err = bson.Unmarshal(dataByte, &dataHandover)
		if err != nil {
			return
		}

		filter := bson.M{}
		filter["reference_code"] = dataHandover.SO
		if dataHandover.ReferenceCode != "" {
			filter["reference_code"] = dataHandover.ReferenceCode
		}

		resultQueryDataShippingOrder := model.ShippingOrderDB.Query(filter, 0, 100, nil)
		if resultQueryDataShippingOrder.Status != common.APIStatus.Ok {
			_ = backup.Push(dataHandover, "query_shipping_order_fail")
			return nil
		}
		current := time.Now()
		shippingOrder := resultQueryDataShippingOrder.Data.([]*model.ShippingOrder)[0]
		hubShipping := model.HubShippingOrder{
			VersionNo:           uuid.New().String(),
			HUBCode:             dataHandover.ToDepartmentCode,
			ReferenceCode:       shippingOrder.ReferenceCode,
			TrackingCode:        shippingOrder.TrackingCode,
			TplCode:             shippingOrder.TplCode,
			TplName:             shippingOrder.TplName,
			Status:              dataHandover.HubStatus,
			ActionTime:          shippingOrder.ActionTime,
			FromCustomerName:    dataHandover.FromDepartmentName,
			FromCustomerCode:    dataHandover.FromDepartmentCode,
			FromCustomerPhone:   shippingOrder.FromCustomerPhone,
			FromCustomerEmail:   shippingOrder.FromCustomerEmail,
			FromCustomerAddress: shippingOrder.FromCustomerAddress,
			FromWardCode:        shippingOrder.FromWardCode,
			FromWardName:        shippingOrder.FromWardName,
			FromDistrictCode:    shippingOrder.FromDistrictCode,
			FromDistrictName:    shippingOrder.FromDistrictName,
			FromProvinceCode:    shippingOrder.FromProvinceCode,
			FromProvinceName:    shippingOrder.FromProvinceName,
			ToCustomerName:      shippingOrder.CustomerName,
			ToCustomerCode:      shippingOrder.CustomerCode,
			ToCustomerAddress:   shippingOrder.CustomerShippingAddress,
			ToCustomerPhone:     shippingOrder.CustomerPhone,
			ToCustomerEmail:     shippingOrder.CustomerEmail,
			ToWardCode:          shippingOrder.CustomerWardCode,
			ToWardName:          shippingOrder.CustomerWardName,
			ToDistrictCode:      shippingOrder.CustomerDistrictCode,
			ToDistrictName:      shippingOrder.CustomerDistrictName,
			ToProvinceCode:      shippingOrder.CustomerProvinceCode,
			ToProvinceName:      shippingOrder.CustomerProvinceName,
			NeedVerifyReceiver:  shippingOrder.NeedVerifyReceiver,
			VerificationCode:    shippingOrder.VerificationCode,
			ActionName:          dataHandover.ActionName,
			Type:                dataHandover.HubOrderType,
			Action:              dataHandover.Action,
			Height:              shippingOrder.Height,
			Width:               shippingOrder.Width,
			Length:              shippingOrder.Length,
			Weight:              shippingOrder.Weight,
			NumPackage:          shippingOrder.NumPackage,
			PrivateNote:         shippingOrder.PrivateNote,
			Note:                shippingOrder.Note,
			TotalAmount:         shippingOrder.TotalAmount,
			DeliveryAmount:      shippingOrder.DeliveryAmount,
			ReceivePackage:      shippingOrder.NumPackage,
			CODAmount:           shippingOrder.CODAmount,
			FeeAmount:           shippingOrder.FeeAmount,
			PaymentMethod:       shippingOrder.PaymentMethod,
			CollectOnDelivery:   shippingOrder.CollectOnDelivery,
			FeeCollectMethod:    shippingOrder.FeeCollectMethod,
			FailReasons:         shippingOrder.FailReasons,
			CreatedTime:         &current,
			ExtraInfo: map[string]interface{}{
				"handoverTicket": dataHandover.TicketID,
				"handoverNote":   dataHandover.HandoverNote,
				"handoverCode":   dataHandover.Code,
			},
			Tags:                       shippingOrder.Tags,
			ReceiveSessionCode:         shippingOrder.ReceiveSessionCode,
			TotalCollectReceiverAmount: shippingOrder.TotalCollectReceiverAmount,
			TotalCollectSenderAmount:   shippingOrder.TotalCollectSenderAmount,
			FeeSenderAmount:            shippingOrder.FeeSenderAmount,
			FeeReceiverAmount:          shippingOrder.FeeReceiverAmount,
			TotalDebtAmount:            shippingOrder.TotalDebtAmount,
			FeeDebtAmount:              shippingOrder.FeeDebtAmount,
			ParentReferenceCode:        shippingOrder.ParentReferenceCode,
			ParentReceiveSessionCode:   shippingOrder.ParentReceiveSessionCode,
			OrderValue:                 shippingOrder.OrderValue,
			MergeStatus:                shippingOrder.MergeStatus,
			ExpiredAt:                  shippingOrder.ExpiredAt,
			DeliveryLeadTime:           shippingOrder.DeliveryLeadTime,
			Baskets:                    shippingOrder.Baskets,
			ProductType:                shippingOrder.ProductType,
			ProductTypes:               shippingOrder.ProductTypes,
			References:                 shippingOrder.References,
		}

		// Nếu loại đơn là lấy bin thì đếm số products đã quét để tăng khối lượng
		if shippingOrder.ShippingType != nil && *shippingOrder.ShippingType == enum.ShippingOrderType.PICKUP {
			var weight float64 = 0
			for _, p := range dataHandover.Products {
				if p.Status != nil &&
					*p.Status != enum.ProductStatus.LOST &&
					*p.Status != enum.ProductStatus.REMOVED {
					weight += p.Weight
				}
			}
			hubShipping.Weight = weight
			hubShipping.SubType = &enum.SubType.PICKUP
		}

		if shippingOrder.ShippingType != nil && *shippingOrder.ShippingType == enum.ShippingOrderType.RETURN {
			hubShipping.SubType = &enum.SubType.RETURN
		}

		if shippingOrder.ShippingType != nil && *shippingOrder.ShippingType == enum.ShippingOrderType.INTERNAL_TRANS {
			hubShipping.SubType = &enum.SubType.INTERNAL_TRANS
		}

		if shippingOrder.ShippingType != nil && *shippingOrder.ShippingType == enum.ShippingOrderType.FMPO {
			hubShipping.SubType = &enum.SubType.FMPO
		}

		if shippingOrder.ShippingType != nil && *shippingOrder.ShippingType == enum.ShippingOrderType.EO {
			hubShipping.SubType = &enum.SubType.EO
		}

		if dataHandover.ReceivePackage != 0 {
			hubShipping.ReceivePackage = dataHandover.ReceivePackage
		}

		if dataHandover.HandoverType != nil {
			hubShipping.ExtraInfo["handoverType"] = *dataHandover.HandoverType
		}

		if len(dataHandover.Products) > 0 {
			hubShipping.Products = dataHandover.Products
			hubShipping.NumPackage = dataHandover.ReceivePackage
		}

		// Nếu loại đơn là DELIVERY hoặc RETURN thì lấy product theo shipping order
		if shippingOrder.ShippingType != nil &&
			*shippingOrder.ShippingType == enum.ShippingOrderType.RETURN ||
			*shippingOrder.ShippingType == enum.ShippingOrderType.DELIVERY ||
			*shippingOrder.ShippingType == enum.ShippingOrderType.INTERNAL_TRANS ||
			*shippingOrder.ShippingType == enum.ShippingOrderType.FMPO {
			hubShipping.Products = shippingOrder.Products
		}

		createResult := model.HUBShippingOrderDB.Upsert(bson.M{"reference_code": shippingOrder.ReferenceCode, "hub_code": dataHandover.ToDepartmentCode}, hubShipping)
		if createResult.Status != common.APIStatus.Ok {
			if item.FailCount%10 == 0 {
				syncDataByte, _ := json.Marshal(hubShipping)
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "CREATE_HUB_SHIPPING_ORDER_FAILED",
					Title:   fmt.Sprintf("CREATE_FAIL_HUB_SHIPPING_ORDER %v - %d", shippingOrder.ReferenceCode, item.FailCount),
					Message: "================================ " + string(syncDataByte) + "\n" + createResult.Message,
				})
			}

			if item.FailCount == 50 {
				return nil
			}

			time.Sleep(1000 * time.Millisecond)
			return fmt.Errorf(createResult.Message)
		}

		// Nếu fee = 0 thì phải update hub order thẳng bằng bson
		if shippingOrder.FeeAmount == 0 {
			updateFeeResult := model.HUBShippingOrderDB.UpdateOne(bson.M{
				"reference_code": filter["reference_code"],
				"hub_code":       dataHandover.ToDepartmentCode,
			}, bson.M{
				"fee_amount": 0.0,
			})

			if updateFeeResult.Status != common.APIStatus.Ok {
				syncDataByte, _ := json.Marshal(hubShipping)
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "UPDATE_FEE_FAILED",
					Title:   fmt.Sprintf("UPDATE_FEE_FAILED %v", shippingOrder.ReferenceCode),
					Message: string(syncDataByte) + "\n" + updateFeeResult.Message,
				})
			}
		}

		updateResult := model.ShippingOrderDB.UpdateOne(bson.M{
			"reference_code": shippingOrder.ReferenceCode,
		}, bson.M{
			"scope": append(shippingOrder.Scope, hubShipping.HUBCode),
		})
		if updateResult.Status != common.APIStatus.Ok {
			if item.FailCount%10 == 0 {
				syncDataByte, _ := json.Marshal(hubShipping)
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "UPDATE_SHIPPING_ORDER_FAILED",
					Title:   fmt.Sprintf("UPDATE_SHIPPING_ORDER_FAILED %v - %d", shippingOrder.ReferenceCode, item.FailCount),
					Message: "================================ " + string(syncDataByte) + "\n" + updateResult.Message,
				})
			}

			if item.FailCount == 50 {
				return nil
			}

			time.Sleep(1000 * time.Millisecond)
			return fmt.Errorf(updateResult.Message)
		}

		return nil
	})
}

func (j *ExecutorJob) setCurrentHub() {
	j.Job.SetTopicConsumer(setCurrentHubShippingOrder, func(item *job.JobItem) (err error) {
		var dataByte []byte
		dataByte, err = bson.Marshal(item.Data)
		if err != nil {
			return
		}

		updateRequest := request.UpdateShippingOrderRequest{}
		err = bson.Unmarshal(dataByte, &updateRequest)
		if err != nil {
			return
		}

		resultQueryDataShippingOrder := model.ShippingOrderDB.QueryOne(bson.M{
			"reference_code": updateRequest.ReferenceCode,
		})
		if resultQueryDataShippingOrder.Status != common.APIStatus.Ok {
			_ = backup.Push(updateRequest, "query_shipping_order_fail")
			return nil
		}
		shippingOrder := resultQueryDataShippingOrder.Data.([]*model.ShippingOrder)[0]

		if shippingOrder.Status != nil && (*shippingOrder.Status != enum.TPLCallbackStatus.DELIVERED ||
			*shippingOrder.Status != enum.TPLCallbackStatus.COMPLETED) {
			if updateRequest.CurrentHub != "" {
				shippingOrder.CurrentHub = updateRequest.CurrentHub
			}
		}
		shippingOrder.HandoverTime = updateRequest.HandoverTime
		shippingOrder.EstimateDeliveringTime = updateRequest.LeadTimeDelivering
		shippingOrder.EstimatePickingTime = updateRequest.LeadTimePicking
		shippingOrder.EstimateReturningTime = updateRequest.LeadTimeReturning
		updateResult := model.ShippingOrderDB.UpdateOne(&model.ShippingOrder{
			ReferenceCode: shippingOrder.ReferenceCode,
		}, shippingOrder, nil)
		if updateResult.Status != common.APIStatus.Ok {
			if item.FailCount%10 == 0 {
				syncDataByte, _ := json.Marshal(shippingOrder)
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "CREATE_HUB_SHIPPING_ORDER_FAILED",
					Title:   fmt.Sprintf("CREATE_FAIL_HUB_SHIPPING_ORDER %v - %d", shippingOrder.ReferenceCode, item.FailCount),
					Message: "================================ " + string(syncDataByte) + "\n" + updateResult.Message,
				})
			}

			if item.FailCount == 50 {
				return nil
			}

			time.Sleep(1000 * time.Millisecond)
			return fmt.Errorf(updateResult.Message)
		}
		return nil
	})
}

func CreateReconcile(hubShippingOrder *model.HubShippingOrder, reconcileType string) (*common.APIResponse, bool) {

	carrierCode := string(*hubShippingOrder.TplCode)
	// Lấy carrier cha nếu có
	response := model.CarrierDB.QueryOne(&bson.M{"carrier_code": carrierCode})
	if response.Status == common.APIStatus.Ok {
		carrier := response.Data.([]*model.Carrier)[0]
		if carrier.ParentCode != nil && *carrier.ParentCode != "" {
			carrierCode = string(*carrier.ParentCode)
		}
	}

	createReconcileSession := &request.CreateReconcileSessionRequest{
		CarrierCode:   carrierCode,
		ReconcileType: reconcileType,
		HubCode:       hubShippingOrder.HUBCode,
		UserId:        int(hubShippingOrder.DriverID),
		Fullname:      hubShippingOrder.DriverName,
	}

	response = client.Services.AccountingClient.CreateReconcileSession(createReconcileSession)
	if response.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Tạo đối soát thất bại.",
		}, true
	} else {
		deliveredTime := time.Unix(hubShippingOrder.DeliveredTime, 0)

		paymentMethod := "COD"
		if hubShippingOrder.PaymentMethod != nil {
			paymentMethod = string(*hubShippingOrder.PaymentMethod)
		}
		createReconcileSessionOrder := &request.CreateReconcileSessionOrderRequest{
			HubCode:        hubShippingOrder.HUBCode,
			UserId:         int(hubShippingOrder.DriverID),
			ReferenceCode:  hubShippingOrder.ReferenceCode,
			TrackingCode:   hubShippingOrder.TrackingCode,
			ReconcileType:  reconcileType,
			CODAmount:      hubShippingOrder.CODAmount,
			TplCode:        hubShippingOrder.TplCode,
			PaymentMethod:  paymentMethod,
			CarrierCode:    string(*hubShippingOrder.TplCode),
			DeliveredTime:  &deliveredTime,
			DeliveryAmount: hubShippingOrder.DeliveryAmount,
		}
		if (strings.HasPrefix(hubShippingOrder.ReferenceCode, "SO") ||
			strings.HasPrefix(hubShippingOrder.ReferenceCode, "LO") ||
			strings.HasPrefix(hubShippingOrder.ReferenceCode, "RO") ||
			strings.HasPrefix(hubShippingOrder.ReferenceCode, "CO") ||
			strings.HasPrefix(hubShippingOrder.ReferenceCode, "TO")) &&
			(strings.Contains(hubShippingOrder.ReferenceCode, "-P") ||
				strings.Contains(hubShippingOrder.ReferenceCode, "-F")) {
			createReconcileSessionOrder.ParentReferenceCode = hubShippingOrder.ParentReferenceCode
		}

		response = client.Services.AccountingClient.CreateReconcileSessionOrder(createReconcileSessionOrder)
		if response.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Ok,
				Message: "Tạo đối soát thất bại.",
			}, true
		}
	}

	return nil, false
}
