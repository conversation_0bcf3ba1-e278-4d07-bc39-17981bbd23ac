package request

type OneSignalNotify struct {
	AppID                  string          `json:"app_id"`
	IncludeExternalUserIds []string        `json:"include_external_user_ids"`
	Headings               LangDescription `json:"headings"`
	Contents               LangDescription `json:"contents"`
	Data                   ExtraData       `json:"data"`
}

type LangDescription struct {
	En string `json:"en"`
	Vi string `json:"vi"`
}

type ExtraData struct {
	RefId        string `json:"refId"`
	Injection    string `json:"injection"`
	DefaultParam string `json:"defaultParam"`
	CategoryId   int64  `json:"categoryId"`
}
