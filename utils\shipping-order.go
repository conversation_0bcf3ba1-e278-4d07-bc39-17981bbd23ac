package utils

import (
	"time"

	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
)

func BuildHubOrderByShippingOrder(shippingOrder *model.ShippingOrder) *model.HubShippingOrder {
	current := time.Now()
	hubShipping := &model.HubShippingOrder{
		HUBCode:           shippingOrder.CurrentHub,
		ReferenceCode:     shippingOrder.ReferenceCode,
		TrackingCode:      shippingOrder.TrackingCode,
		TplCode:           shippingOrder.TplCode,
		TplName:           shippingOrder.TplName,
		Status:            &enum.HubShippingOrderStatus.STORING,
		ActionTime:        shippingOrder.ActionTime,
		FromCustomerName:  shippingOrder.FromCustomerName,
		FromCustomerCode:  shippingOrder.FromCustomerCode,
		FromProvinceCode:  shippingOrder.FromProvinceCode,
		FromProvinceName:  shippingOrder.FromProvinceName,
		FromDistrictCode:  shippingOrder.FromDistrictCode,
		FromDistrictName:  shippingOrder.FromDistrictName,
		FromWardCode:      shippingOrder.FromWardCode,
		FromWardName:      shippingOrder.FromWardName,
		ToCustomerName:    shippingOrder.CustomerName,
		ToCustomerCode:    shippingOrder.CustomerCode,
		ToCustomerAddress: shippingOrder.CustomerShippingAddress,
		ToCustomerPhone:   shippingOrder.CustomerPhone,
		ToCustomerEmail:   shippingOrder.CustomerEmail,
		ToWardCode:        shippingOrder.CustomerWardCode,
		ToWardName:        shippingOrder.CustomerWardName,
		ToDistrictCode:    shippingOrder.CustomerDistrictCode,
		ToDistrictName:    shippingOrder.CustomerDistrictName,
		ToProvinceCode:    shippingOrder.CustomerProvinceCode,
		ActionName:        "Đã nhập kho " + shippingOrder.CurrentHub,
		StoredTime:        current.Unix(),
		Type:              &enum.HubOrderType.TRANSPORTING,
		Action:            "IN",
		ToProvinceName:    shippingOrder.CustomerProvinceName,
		Height:            shippingOrder.Height,
		Width:             shippingOrder.Width,
		Length:            shippingOrder.Length,
		Weight:            shippingOrder.Weight,
		NumPackage:        shippingOrder.NumPackage,
		PrivateNote:       shippingOrder.PrivateNote,
		Note:              shippingOrder.Note,
		TotalAmount:       shippingOrder.TotalAmount,
		DeliveryAmount:    shippingOrder.DeliveryAmount,
		CODAmount:         shippingOrder.CODAmount,
		FeeAmount:         shippingOrder.FeeAmount,
		PaymentMethod:     shippingOrder.PaymentMethod,
		CollectOnDelivery: shippingOrder.CollectOnDelivery,
		FeeCollectMethod:  shippingOrder.FeeCollectMethod,
		CreatedTime:       &current,
		ExtraInfo:         map[string]interface{}{},

		TotalCollectReceiverAmount: shippingOrder.TotalCollectReceiverAmount,
		TotalCollectSenderAmount:   shippingOrder.TotalCollectSenderAmount,
		FeeReceiverAmount:          shippingOrder.FeeReceiverAmount,
		FeeSenderAmount:            shippingOrder.FeeSenderAmount,
		TotalDebtAmount:            shippingOrder.TotalDebtAmount,
		FeeDebtAmount:              shippingOrder.FeeDebtAmount,
		References:                 shippingOrder.References,

		Products: shippingOrder.Products,
	}

	if shippingOrder.Status != nil && *shippingOrder.Status == enum.TPLCallbackStatus.READY_TO_PICK {
		hubShipping.Status = &enum.HubShippingOrderStatus.READY_TO_PICK
		hubShipping.Action = "PICKING"
		hubShipping.ActionName = "Lấy hàng"
		if shippingOrder.ShippingType != nil &&
			*shippingOrder.ShippingType == enum.ShippingOrderType.PICKUP {
			hubShipping.Type = &enum.HubOrderType.PICKUP
			hubShipping.SubType = &enum.SubType.PICKUP
		}

	}
	return hubShipping
}
