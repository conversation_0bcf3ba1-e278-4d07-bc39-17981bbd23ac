package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"go.mongodb.org/mongo-driver/mongo"
)

type ConfigLeadTime struct {
	ID primitive.ObjectID `json:"id,omitempty" bson:"_id,omitempty"`
	// base info
	CreatedBy       string     `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	CreatedTime     *time.Time `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedBy   string     `json:"lastUpdatedBy,omitempty" bson:"last_updated_by,omitempty"`
	LastUpdatedTime *time.Time `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	// config
	CarrierCode    string                    `json:"carrierCode,omitempty" bson:"carrier_code,omitempty"`
	CarrierName    string                    `json:"carrierName,omitempty" bson:"carrier_name,omitempty"`
	Type           *enum.LeadTimeConfigValue `json:"type,omitempty" bson:"type,omitempty"`
	ToCode         string                    `json:"toCode,omitempty" bson:"to_code,omitempty"`
	ToName         string                    `json:"toName,omitempty" bson:"to_name,omitempty"`
	Level          int64                     `json:"level,omitempty" bson:"level,omitempty"`
	CommitmentTime int64                     `json:"commitmentTime,omitempty" bson:"commitment_time,omitempty"`
	FromCode       string                    `json:"fromCode,omitempty" bson:"from_code,omitempty"`
	FromName       string                    `json:"fromName,omitempty" bson:"from_name,omitempty"`
	Active         *bool                     `json:"active,omitempty" bson:"active,omitempty"`

	// LeadTime v2
	SuggestLeadTime    int64                            `json:"suggestLeadTime,omitempty" bson:"suggest_lead_time,omitempty"`
	ConfiguredLeadTime int64                            `json:"configuredLeadTime,omitempty" bson:"configured_lead_time,omitempty"`
	FromHub            string                           `json:"fromHub,omitempty" bson:"from_hub,omitempty"`
	ShippingOrderType  *enum.ShippingOrderTypeValue     `json:"shippingOrderType,omitempty" bson:"shipping_order_type,omitempty"`
	TimeFrame          *enum.TimeFrameValue             `json:"timeFrame,omitempty" bson:"time_frame,omitempty"`
	Province           *ConfigLeadTimeProvince          `json:"province,omitempty" bson:"province,omitempty"`
	ComputeMethod      *enum.ComputeLeadTimeMethodValue `json:"computeMethod,omitempty" bson:"compute_method,omitempty"`
	IsDeleted          *bool                            `json:"isDeleted,omitempty" bson:"is_deleted,omitempty"`
}

type ConfigLeadTimeProvince struct {
	Code               string                   `json:"code,omitempty" bson:"code,omitempty"`
	Name               string                   `json:"name,omitempty" bson:"name,omitempty"`
	SuggestLeadTime    int64                    `json:"suggestLeadTime,omitempty" bson:"suggest_lead_time,omitempty"`
	ConfiguredLeadTime int64                    `json:"configuredLeadTime,omitempty" bson:"configured_lead_time,omitempty"`
	Districts          []ConfigLeadTimeDistrict `json:"districts,omitempty" bson:"districts,omitempty"`
}

type ConfigLeadTimeDistrict struct {
	Code               string               `json:"code,omitempty" bson:"code,omitempty"`
	Name               string               `json:"name,omitempty" bson:"name,omitempty"`
	SuggestLeadTime    int64                `json:"suggestLeadTime,omitempty" bson:"suggest_lead_time,omitempty"`
	ConfiguredLeadTime int64                `json:"configuredLeadTime,omitempty" bson:"configured_lead_time,omitempty"`
	Wards              []ConfigLeadTimeWard `json:"wards,omitempty" bson:"wards,omitempty"`
}

type ConfigLeadTimeWard struct {
	Code               string `json:"code,omitempty" bson:"code,omitempty"`
	Name               string `json:"name,omitempty" bson:"name,omitempty"`
	SuggestLeadTime    int64  `json:"suggestLeadTime,omitempty" bson:"suggest_lead_time,omitempty"`
	ConfiguredLeadTime int64  `json:"configuredLeadTime,omitempty" bson:"configured_lead_time,omitempty"`
}

var ConfigLeadTimeDB = &db.Instance{
	ColName:        "config_lead_time",
	TemplateObject: &ConfigLeadTime{},
}

type ProvinceLeadTime struct {
	ProvinceCode            string
	ProvinceName            string
	SumLeadTime             int64
	TotalDistrict           int64
	AvgLeadTime             int64
	DistrictLeadTimeConfigs map[string]*DistrictLeadTime
}

type DistrictLeadTime struct {
	DistrictCode        string
	DistrictName        string
	SuggestLeadTime     int64
	SumLeadTime         int64
	TotalOrder          int64
	TotalWard           int64
	AvgLeadTime         int64
	WardLeadTimeConfigs map[string]*WardLeadTime
}

type WardLeadTime struct {
	WardCode        string
	WardName        string
	SuggestLeadTime int64
	SumLeadTime     int64
	TotalOrder      int64
	AvgLeadTime     int64
}

func InitConfigLeadTimeModel(s *mongo.Database) {

	ConfigLeadTimeDB.ApplyDatabase(s)
	//t := true
	//
	//ConfigLeadTimeDB.CreateIndex(bson.D{
	//	{"to_code", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})
	//
	//ConfigLeadTimeDB.CreateIndex(bson.D{
	//	{"carrier_code", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})
	//
	//ConfigLeadTimeDB.CreateIndex(bson.D{
	//	{"from_code", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})
}
