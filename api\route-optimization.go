package api

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
)

func RouteOptimization(req sdk.APIRequest, resp sdk.APIResponder) error {
	type roRequest struct {
		Depots    []*model.Address   `json:"depots"`
		Points    []*model.Address   `json:"points"`
		Constrain model.VRPConstrain `json:"constrain"`
		Algorithm string             `json:"algorithm"`
	}

	var input roRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	result, solveErr := action.SolveVRP(input.Depots, input.Points, input.Constrain, input.Algorithm)

	if solveErr != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: solveErr.Message,
		})
	}

	return resp.Respond(&common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   result,
	})

}
