package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"go.mongodb.org/mongo-driver/bson"
)

func CreateVoucher(input *model.Voucher) *common.APIResponse {
	return model.VoucherDB.Create(input)
}

func GetVoucher(input model.Voucher, offset, limit int64, getTotal bool) *common.APIResponse {
	filter := bson.M{}
	if input.Code != "" {
		filter["code"] = input.Code
	}

	if input.IsActive != nil {
		filter["is_active"] = input.IsActive
	}

	result := model.VoucherDB.Query(
		filter,
		offset,
		limit,
		nil)
	if getTotal {
		countResult := model.VoucherDB.Count(filter)
		result.Total = countResult.Total
	}
	return result
}

func UpdateVoucher(input *model.Voucher) *common.APIResponse {
	if input.Code == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã voucher không được để trống",
		}
	}

	updateResult := model.VoucherDB.UpdateOne(bson.M{
		"code": input.Code,
	}, input)

	return &common.APIResponse{
		Status:  updateResult.Status,
		Message: updateResult.Message,
	}
}
