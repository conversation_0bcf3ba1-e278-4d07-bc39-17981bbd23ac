package notification

import (
	"encoding/json"
	"time"

	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"go.mongodb.org/mongo-driver/mongo"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
)

const (
	pathCreateNotification = "/api/v1/notifications"
)

type NotificationClient struct {
	svc     *client.RestClient
	headers map[string]string
}

// NewNotificationServiceClient ...
func NewServiceClient(hostName, logName string, session *mongo.Database) *NotificationClient {
	notifyClient := &NotificationClient{
		svc:     client.NewRESTClient(hostName, logName, 3*time.Second, 1, 3*time.Second),
		headers: conf.Config.OneSignalConfig.Header,
	}
	notifyClient.svc.SetDBLog(session)
	return notifyClient
}

// SendNotification is func send new Notification
func (cli *NotificationClient) SendNotification(notificationTitle, notificationDescription string, userNames []string, refId string, params []string) *common.APIResponse {
	if len(userNames) < 1 {
		return nil
	}
	reqBody := request.OneSignalNotify{
		AppID:                  conf.Config.OneSignalConfig.AppID,
		IncludeExternalUserIds: userNames,
		Headings: request.LangDescription{
			En: notificationTitle,
			Vi: notificationTitle,
		},
		Contents: request.LangDescription{
			En: notificationDescription,
			Vi: notificationDescription,
		},
		Data: request.ExtraData{
			RefId: refId,
		},
	}

	var defaultParamByte []byte
	if refId == "JourneyDelivery" {
		reqBody.Data.Injection = "TAB_JOURNEY_DELIVERY_STATUS|0"
		reqBody.Data.CategoryId = 0
	} else if refId == "JourneyPick" {
		reqBody.Data.Injection = "TAB_JOURNEY_PICK_STATUS|0"
		reqBody.Data.CategoryId = 101
	} else if refId == "OrderDetail" {
		reqBody.Data.Injection = "RELOAD_ORDER_DETAIL|1"
		reqBody.Data.CategoryId = 0
	} else if refId == "ReconcileDetail" {
		reqBody.Data.Injection = "RELOAD_RECONCILE_DETAIL|1"
		defaultParamByte, _ = json.Marshal(map[string]interface{}{
			"reconcileCode": params[0],
		})
	} else if refId == "HubReconcileDetail" {
		reqBody.Data.Injection = "RELOAD_HUB_RECONCILE_DETAIL|1"
		defaultParamByte, _ = json.Marshal(map[string]interface{}{
			"hubReconcileCode": params[0],
		})
	}

	if len(defaultParamByte) == 0 {
		if len(params) > 1 {
			defaultParamByte, _ = json.Marshal(map[string]interface{}{
				"referenceCodes": params,
			})
		} else if len(params) == 1 {
			defaultParamByte, _ = json.Marshal(map[string]interface{}{
				"referenceCode": params[0],
			})
		} else {
			defaultParamByte, _ = json.Marshal(map[string]interface{}{
				"referenceCode": "",
			})
		}
	}

	reqBody.Data.DefaultParam = string(defaultParamByte)

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, &reqBody, pathCreateNotification, nil)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_NOTIFICATION",
		}
	}

	if res.Code != 200 {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   res.Body,
			ErrorCode: "MAKE_REQUEST_NOTIFICATION",
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "GỬI THÔNG BÁO THÀNH CÔNG",
	}
}
