package ghtk

import (
	"encoding/json"
	"fmt"
	"regexp"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/utils"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathBookGHTKService   = "/services/shipment/order/?ver=1.5"
	pathCancelGHTKService = "/services/shipment/cancel/partner_id"
	pathGetOrderDetail    = "/services/shipment/v2"
	pathGetShippingFee    = "/services/shipment/fee"
	pathGetLevel4Address  = "/services/address/getAddressLevel4"
)

type Client struct {
	svc     *client.RestClient
	headers map[string]string
}

func NewServiceClient(apiHost, logName string, session *mongo.Database) *Client {
	if apiHost == "" {
		return nil
	}
	newClient := &Client{
		svc: client.NewRESTClient(
			apiHost,
			logName,
			180*time.Second,
			0,
			180*time.Second,
		),
		headers: map[string]string{},
	}
	newClient.svc.SetDBLog(session)
	return newClient
}

func (cli *Client) ChangeInformation(carrierInfo *model.Carrier) {
	cli.headers = map[string]string{
		"Token": carrierInfo.ExtraData.AccessToken,
	}
	if carrierInfo.ExtraData.SubAccountToken != "" {
		cli.headers = map[string]string{
			"X-Refer-Token": carrierInfo.ExtraData.AccessToken,
			"Token":         carrierInfo.ExtraData.SubAccountToken,
		}
	}
}

func (cli *Client) CreateTrackingGHTK(createTrackingRequest request.BookGHTKRequest) (tracking *model.ShippingInfo, err error) {
	type response struct {
		Message string `json:"message"`
		Success bool   `json:"success"`
		Order   struct {
			PartnerId            string `json:"partner_id"`
			Label                string `json:"label"`
			Fee                  int64  `json:"fee"`
			InsuranceFee         int64  `json:"insurance_fee"`
			EstimatedPickTime    string `json:"estimated_pick_time"`
			EstimatedDeliverTime string `json:"estimated_deliver_time"`
		} `json:"order"`
	}

	query := map[string]string{}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, query, createTrackingRequest, pathBookGHTKService, nil)
	if err != nil {
		return
	}
	resBody := new(response)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return
	}

	if !resBody.Success {
		err = fmt.Errorf("%v", resBody)
		return
	}

	tracking = &model.ShippingInfo{
		TrackingNumber: resBody.Order.Label,
		FeeAmount:      float64(resBody.Order.Fee + resBody.Order.InsuranceFee),
	}

	if resBody.Order.EstimatedDeliverTime != "" {
		regex := "[1-9][0-9][0-9]{2}-([0][1-9]|[1][0-2])-([1-2][0-9]|[0][1-9]|[3][0-1])"
		r, _ := regexp.Compile(regex)
		result := r.FindString(resBody.Order.EstimatedDeliverTime)
		if result != "" {
			estimatedDeliverTime, parseErr := time.Parse("2006-01-02", result)
			if parseErr == nil {
				tracking.ExpectedPickupTime = &estimatedDeliverTime
			}
		}
	}

	return
}

func (cli *Client) CancelGHTK(so string) (err error) {
	type response struct {
		Message string `json:"message"`
		Success bool   `json:"success"`
	}

	query := map[string]string{}

	pathCancel := pathCancelGHTKService + ":" + so

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, query, nil, pathCancel, nil)
	if err != nil {
		return
	}
	resBody := new(response)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return
	}

	if !resBody.Success {
		err = fmt.Errorf("%v-%v", resBody, resBody.Message)
		return
	}

	return
}

func (cli *Client) GetDetailOrder(orderId string) (result *model.GHTKOrder, err error) {
	type response struct {
		Message string           `json:"message"`
		Success bool             `json:"success"`
		Order   *model.GHTKOrder `json:"order"`
	}

	query := map[string]string{}

	pathGetDetail := pathGetOrderDetail + "/" + orderId

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, query, nil, pathGetDetail, nil)
	if err != nil {
		return
	}
	resBody := new(response)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return
	}

	if !resBody.Success {
		err = fmt.Errorf("%v-%v", resBody, resBody.Message)
		return
	}

	result = resBody.Order

	return
}

func (cli *Client) GetShippingFee(req *request.GetShippingService) (shippingFee float64, err error) {
	type response struct {
		Message string `json:"message"`
		Success bool   `json:"success"`
		Fee     struct {
			Fee float64 `json:"fee"`
		} `json:"fee"`
	}

	query := map[string]string{
		"pick_province":  req.FromProvince,
		"pick_district":  req.FromDistrict,
		"pick_ward":      req.FromWard,
		"province":       req.Province,
		"district":       req.District,
		"ward":           req.Ward,
		"weight":         fmt.Sprintf("%f", req.Weight),
		"deliver_option": "none",
		"value":          fmt.Sprintf("%d", req.Value),
		"pick_money":     fmt.Sprintf("%d", req.PickMoney),
	}

	if req.OPM == true {
		query["opm"] = "1"
	}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, query, nil, pathGetShippingFee, nil)
	if err != nil {
		return
	}
	resBody := new(response)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return
	}

	if !resBody.Success {
		err = fmt.Errorf("%v-%v", resBody, resBody.Message)
		return
	}

	shippingFee = resBody.Fee.Fee
	return

}

func (cli *Client) GetLevel4Address(provinceName, districtName, wardName, address string) (string, error) {
	type response struct {
		Message string   `json:"message"`
		Success bool     `json:"success"`
		Data    []string `json:"data"`
	}

	query := map[string]string{
		"address":     address,
		"province":    provinceName,
		"district":    districtName,
		"ward_street": wardName,
	}

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, query, nil, pathGetLevel4Address, nil)
	if err != nil {
		return "", err
	}

	resBody := new(response)
	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return "", err
	}

	if len(resBody.Data) == 0 {
		err = fmt.Errorf("Không tìm thấy địa chỉ level 4")
		return "", err
	}

	if len(resBody.Data) == 1 {
		return resBody.Data[0], nil
	}

	// So sánh với những địa chỉ cấp 4 xem địa chỉ nào có tỉ lệ tương đồng cao nhất sẽ chọn làm kết quả
	var highestScore = 0.0
	var highestScoreAt = 0
	for i, respAddress := range resBody.Data {
		likelihood := utils.CompareStringDistance(address, respAddress)
		if likelihood > highestScore {
			highestScore = likelihood
			highestScoreAt = i
		}
	}

	return resBody.Data[highestScoreAt], nil
}
