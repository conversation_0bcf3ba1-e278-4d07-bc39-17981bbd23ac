package action

import (
	"strconv"
	"strings"
	"sync"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func countCustomerOrderWrapper(wg *sync.WaitGroup, customers []*model.Customer, countFunc func(customer []*model.Customer)) {
	defer wg.Done()
	countFunc(customers)
}

func GetCustomer(input request.GetCustomerRequest, offset, limit int64, getTotal bool) *common.APIResponse {
	filter := bson.M{}

	if input.Code != "" {
		filter["code"] = input.Code
	}

	if input.CustomerId != 0 {
		filter["customer_id"] = input.CustomerId
	}

	if input.CustomerType != nil {
		filter["customer_type"] = input.CustomerType
	}

	if input.Keyword != "" {
		filter["keyword"] = bson.M{
			"$regex": utils.ToRawText(input.Keyword),
		}
	}

	if input.Tags != nil && len(*input.Tags) > 0 {
		filter["tags"] = bson.M{
			"$in": *input.Tags,
		}
	}

	result := model.CustomerDB.Query(
		filter,
		offset,
		limit,
		nil)

	if result.Status != common.APIStatus.Ok {
		result.Total = 0
		return result
	}

	if result.Status == common.APIStatus.Ok &&
		input.NeedCountOrder != nil &&
		*input.NeedCountOrder {
		wg := sync.WaitGroup{}
		wg.Add(4)
		customers := result.Data.([]*model.Customer)
		go countCustomerOrderWrapper(&wg, customers, CountCustomersReadyToPickOrders)
		go countCustomerOrderWrapper(&wg, customers, CountCustomersPickedOrders)
		go countCustomerOrderWrapper(&wg, customers, CountCustomersDeliveringOrders)
		go countCustomerOrderWrapper(&wg, customers, CountCustomersDeliveredOrders)
		wg.Wait()
	}

	if getTotal {
		countResult := model.CustomerDB.Count(filter)
		result.Total = countResult.Total
	}

	return result
}

func UpdateCustomer(input *model.Customer) *common.APIResponse {
	if input.CustomerId == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã khách hàng không được để trống",
		}
	}

	customerRaw := model.CustomerDB.QueryOne(bson.M{
		"customer_id": input.CustomerId,
	})

	if customerRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Khách hàng không tồn tại",
		}
	}

	RecomputeFee(input)

	if input.CustomerBankInfo != nil {
		countField := 0
		if input.CustomerBankInfo.BankName == "" {
			countField += 1
		}

		if input.CustomerBankInfo.BankBranch == "" {
			countField += 1
		}

		if input.CustomerBankInfo.BankAccountName == "" {
			countField += 1
		}

		if input.CustomerBankInfo.BankAccountID == "" {
			countField += 1
		}

		if countField != 0 && countField != 4 {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Nhập thiếu thông tin ngân hàng",
			}
		}
	}

	if input.Tags != nil && len(*input.Tags) > 0 {
		delayCustomerTagConfig, err := GetDelayCustomerTagConfig()
		if err != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: err.Error(),
			}
		}

		countExistsDelayCustomerTag := 0
		for _, tag := range *input.Tags {
			if utils.CheckContainValue(delayCustomerTagConfig, tag) {
				countExistsDelayCustomerTag += 1
			}
		}

		if countExistsDelayCustomerTag > 1 {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không thể thêm nhiều tag khách hàng trễ",
			}
		}
	}

	return model.CustomerDB.UpdateOne(bson.M{
		"customer_id": input.CustomerId,
	}, input)
}

func MigrateCustomer() *common.APIResponse {
	customersRaw := model.CustomerDB.QueryAll()
	customers := customersRaw.Data.([]*model.Customer)
	for _, customer := range customers {
		if customer.CustomerId == 0 {
			customer.CustomerId = model.GenId("CUSTOMER_ID")
			model.CustomerDB.UpdateOne(bson.M{
				"code": customer.Code,
			}, customer)
		}
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}
}

func MigrateCustomerDetail() *common.APIResponse {
	customersRaw := model.CustomerDB.QueryAll()
	customers := customersRaw.Data.([]*model.Customer)
	for _, customer := range customers {
		seller, err := client.Services.SellerClient.GetSellersInformation(request.GetSellerRequest{SellerCodes: customer.Code})
		if err != nil || len(seller) == 0 {
			continue
		} // sellerClass
		customer.Name = seller[0].Name
		customer.Phone = seller[0].Phone
		if seller[0].SellerClass != "" {
			if seller[0].SellerClass == "EXTERNAL" {
				customer.CustomerType = &enum.CustomerType.SELLER
			} else {
				customer.CustomerType = &enum.CustomerType.VENDOR
			}
		}
		newKeyword, keyErr := utils.GenKeyword(customer.Name, customer.Code, customer.CustomerId)
		if keyErr == nil {
			customer.Keyword = newKeyword
		}
		model.CustomerDB.UpdateOne(bson.M{
			"code": customer.Code,
		}, customer)
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}
}

func RecomputeFee(updateCustomer *model.Customer) error {
	if updateCustomer.AppliedFees == nil || len(*updateCustomer.AppliedFees) == 0 {
		return nil
	}

	var configFeeIds []int64
	configFeeM := map[enum.ConfigFeeValue]*model.ConfigFee{}
	for _, appliedFee := range *updateCustomer.AppliedFees {
		configFeeIds = append(configFeeIds, appliedFee.ConfigFeeId)
	}

	// Phi lay hang tien tai cho
	for _, appliedFee := range *updateCustomer.AppliedFees {
		configFeeRaw := model.ConfigFeeDB.QueryOne(bson.M{
			"config_id": appliedFee.ConfigFeeId,
		})

		if configFeeRaw.Status != common.APIStatus.Ok {
			continue
		}

		configFee := configFeeRaw.Data.([]*model.ConfigFee)[0]
		query := bson.M{}
		if appliedFee.ConfigFeeType == enum.ConfigFeeType.PICK_N_TRANSPORT {
			query["status"] = enum.TPLCallbackStatus.READY_TO_PICK
			query["fee_collect_method"] = enum.FeeCollectMethod.SENDER_PAY
			query["from_customer_code"] = updateCustomer.Code
			query["is_book_drop_off"] = false
			configFeeM[enum.ConfigFeeType.PICK_N_TRANSPORT] = configFee
		}

		if appliedFee.ConfigFeeType == enum.ConfigFeeType.TRANSPORT {
			query["status"] = enum.TPLCallbackStatus.PICKED
			query["fee_collect_method"] = enum.FeeCollectMethod.SENDER_PAY
			query["from_customer_code"] = updateCustomer.Code
			query["is_book_drop_off"] = true
			configFeeM[enum.ConfigFeeType.TRANSPORT] = configFee
		}

		// TODO: add more config fee type here
		if len(query) == 0 {
			continue
		}

		shippingOrdersRaw := model.ShippingOrderDB.Query(query, 0, 1000, nil)

		if shippingOrdersRaw.Status != common.APIStatus.Ok {
			continue
		}

		shippingOrders := shippingOrdersRaw.Data.([]*model.ShippingOrder)
		for _, shippingOrder := range shippingOrders {
			if shippingOrder.SplitFromCode != "" {
				continue
			}
			bookRequest := shippingOrderToBookShippingReq(shippingOrder)
			feeAmount, err := CalculateOrderFee(bookRequest, configFee)
			if err != nil {
				continue
			}
			if feeAmount != shippingOrder.FeeAmount {
				updater := bson.M{
					"fee_amount":                  feeAmount,
					"total_collect_sender_amount": feeAmount,
					"cod_amount":                  feeAmount,
					"fee_sender_amount":           feeAmount,
				}
				model.ShippingOrderDB.UpdateOne(bson.M{
					"reference_code": shippingOrder.ReferenceCode,
				}, updater)

				model.HUBShippingOrderDB.UpdateMany(bson.M{
					"reference_code": shippingOrder.ReferenceCode,
				}, updater)

			}

		}
	}

	// Phi debt
	reconcileOrders, err := client.Services.AccountingClient.GetReconcileOrderOfCustomer(updateCustomer.Code, enum.ReconcileOrderStatus.Init)
	if len(reconcileOrders) != 0 && err == nil {
		shippingOrdersStr := []string{}
		reconcileOrderM := map[string]int64{}
		for _, reconcileOrder := range reconcileOrders {
			shippingOrdersStr = append(shippingOrdersStr, reconcileOrder.ReferenceCode)
			reconcileOrderM[reconcileOrder.ReferenceCode] = reconcileOrder.CODAmount
		}

		shippingOrdersRaw := model.ShippingOrderDB.Query(bson.M{
			"reference_code": bson.M{
				"$in": shippingOrdersStr,
			},
		}, 0, int64(len(shippingOrdersStr)), nil)
		if shippingOrdersRaw.Status != common.APIStatus.Ok {
			return nil
		}
		shippingOrders := shippingOrdersRaw.Data.([]*model.ShippingOrder)
		needGroupTransport := false
		needGroupPickNTransport := false
		groupShippingOrder := map[enum.ConfigFeeValue][]*model.ShippingOrder{}
		if value, ok := configFeeM[enum.ConfigFeeType.TRANSPORT]; ok &&
			DoesConfigFeeContainsQuota(value) {
			needGroupTransport = true
		}

		if value, ok := configFeeM[enum.ConfigFeeType.PICK_N_TRANSPORT]; ok &&
			DoesConfigFeeContainsQuota(value) {
			needGroupPickNTransport = true
		}

		for _, shippingOrder := range shippingOrders {
			var configType enum.ConfigFeeValue
			if shippingOrder.IsBookDropOff != nil &&
				*shippingOrder.IsBookDropOff {
				if needGroupTransport {
					groupShippingOrder[enum.ConfigFeeType.TRANSPORT] =
						append(groupShippingOrder[enum.ConfigFeeType.TRANSPORT], shippingOrder)
					continue
				}
				configType = enum.ConfigFeeType.TRANSPORT
			}

			if shippingOrder.IsBookDropOff != nil &&
				!*shippingOrder.IsBookDropOff {
				if needGroupPickNTransport {
					groupShippingOrder[enum.ConfigFeeType.PICK_N_TRANSPORT] =
						append(groupShippingOrder[enum.ConfigFeeType.PICK_N_TRANSPORT], shippingOrder)
					continue
				}
				configType = enum.ConfigFeeType.PICK_N_TRANSPORT
			}

			if value, ok := configFeeM[configType]; ok {
				bookRequest := shippingOrderToBookShippingReq(shippingOrder)
				feeAmount, err := CalculateOrderFee(bookRequest, value)
				if err != nil {
					continue
				}
				if int64(feeAmount) != reconcileOrderM[shippingOrder.ReferenceCode] ||
					(feeAmount == 0 && reconcileOrderM[shippingOrder.ReferenceCode] == 0) {
					parsePickedTime := time.Unix(shippingOrder.PickedTime, 0)
					updateReconcileOrders := []*request.CreateReconcileSessionOrderRequest{{
						ReferenceCode:       shippingOrder.ReferenceCode,
						PickedTime:          &parsePickedTime,
						CustomerCode:        shippingOrder.FromCustomerCode,
						CarrierCode:         string(*shippingOrder.TplCode),
						CODAmount:           feeAmount,
						IsFeeBeenCalculated: &enum.True,
						OrderValue:          shippingOrder.OrderValue,
						IsBookDropOff:       shippingOrder.IsBookDropOff,
						NumPackage:          shippingOrder.NumPackage,
					}}
					_ = client.Services.AccountingClient.AddReconcileFeeSession(
						nil,
						updateReconcileOrders,
						nil)
				}
			}
		}

		for key, value := range groupShippingOrder {
			needUpdateShippingOder, calErr := CalculateOrdersFee(value, configFeeM[key])
			if len(needUpdateShippingOder) != 0 && calErr == nil {
				var updateReconcileOrders []*request.CreateReconcileSessionOrderRequest
				for _, needUpdateOrder := range needUpdateShippingOder {
					parsePickedTime := time.Unix(needUpdateOrder.PickedTime, 0)
					updateReconcileOrder := &request.CreateReconcileSessionOrderRequest{
						ReferenceCode:       needUpdateOrder.ReferenceCode,
						PickedTime:          &parsePickedTime,
						CustomerCode:        needUpdateOrder.FromCustomerCode,
						CarrierCode:         string(*needUpdateOrder.TplCode),
						CODAmount:           needUpdateOrder.FeeAmount,
						IsFeeBeenCalculated: &enum.True,
						NumPackage:          needUpdateOrder.NumPackage,
					}
					updateReconcileOrders = append(updateReconcileOrders, updateReconcileOrder)
				}

				_ = client.Services.AccountingClient.AddReconcileFeeSession(
					nil,
					updateReconcileOrders,
					nil)
			}
		}

	}

	return nil
}

func shippingOrderToBookShippingReq(shippingOrder *model.ShippingOrder) request.BookShippingOrder {
	req := request.BookShippingOrder{
		From: &model.Address{
			Address:      shippingOrder.FromCustomerAddress,
			Code:         shippingOrder.FromCustomerCode,
			Name:         shippingOrder.FromCustomerName,
			Phone:        shippingOrder.FromCustomerPhone,
			ProvinceCode: shippingOrder.FromProvinceCode,
			DistrictCode: shippingOrder.FromDistrictCode,
			WardCode:     shippingOrder.FromWardCode,
			ProvinceName: shippingOrder.FromProvinceName,
			DistrictName: shippingOrder.FromDistrictName,
			WardName:     shippingOrder.FromWardName,
		},
		To: &model.Address{
			Address:      shippingOrder.CustomerShippingAddress,
			Code:         shippingOrder.CustomerCode,
			Name:         shippingOrder.CustomerName,
			Phone:        shippingOrder.CustomerPhone,
			ProvinceCode: shippingOrder.CustomerProvinceCode,
			DistrictCode: shippingOrder.CustomerDistrictCode,
			WardCode:     shippingOrder.CustomerWardCode,
			ProvinceName: shippingOrder.CustomerProvinceName,
			DistrictName: shippingOrder.CustomerDistrictName,
			WardName:     shippingOrder.CustomerWardName,
		},
		ShippingType:        shippingOrder.ShippingType,
		ParentReferenceCode: shippingOrder.ParentReferenceCode,
		FeeCollectedOn:      shippingOrder.FeeCollectMethod,
		NumPackage:          shippingOrder.NumPackage,
		Weight:              shippingOrder.Weight,
		VoucherCode:         shippingOrder.VoucherCode,
		CODAmount:           shippingOrder.CODAmount,
		ReferenceCode:       shippingOrder.ReferenceCode,
	}

	if shippingOrder.OrderValue != nil {
		req.OrderValue = *shippingOrder.OrderValue
	}

	if shippingOrder.IsBookDropOff == nil || !*shippingOrder.IsBookDropOff {
		req.IsBookDropOff = false
	} else {
		req.IsBookDropOff = true
		if shippingOrder.ShippingType != nil &&
			*shippingOrder.ShippingType == enum.ShippingOrderType.FMPO {
			fmHub := shippingOrder.Scope[0]
			fmHubRaw := model.HubDB.QueryOne(bson.M{
				"code": fmHub,
			})
			if fmHubRaw.Status == common.APIStatus.Ok {
				hub := fmHubRaw.Data.([]*model.Hub)[0]
				req.From.Address = hub.Address.Address
				req.From.Code = hub.Code
				req.From.ProvinceCode = hub.Address.ProvinceCode
				req.From.DistrictCode = hub.Address.DistrictCode
				req.From.WardCode = hub.Address.WardCode
				req.From.ProvinceName = hub.Address.ProvinceName
				req.From.DistrictName = hub.Address.DistrictName
				req.From.WardName = hub.Address.WardName
			}
		}
	}

	if shippingOrder.ShippingType != nil &&
		*shippingOrder.ShippingType == enum.ShippingOrderType.FMPO {
		lmHub := shippingOrder.CustomerCode
		lmHubRaw := model.HubDB.QueryOne(bson.M{
			"warehouse_reference_code": lmHub,
		})
		if lmHubRaw.Status == common.APIStatus.Ok {
			hub := lmHubRaw.Data.([]*model.Hub)[0]
			req.To.Address = hub.Address.Address
			req.To.Code = hub.Code
			req.To.ProvinceCode = hub.Address.ProvinceCode
			req.To.DistrictCode = hub.Address.DistrictCode
			req.To.WardCode = hub.Address.WardCode
			req.To.ProvinceName = hub.Address.ProvinceName
			req.To.DistrictName = hub.Address.DistrictName
			req.To.WardName = hub.Address.WardName
			req.ToHubCode = hub.Code
		}
	}

	if shippingOrder.CheckinNumPack != 0 {
		req.NumPackage = shippingOrder.CheckinNumPack
	}

	if shippingOrder.IsDropOffAtFMHub ||
		(shippingOrder.IsBookDropOff != nil && *shippingOrder.IsBookDropOff) {
		req.DropOffAtHubCode = shippingOrder.FirstMileHubCode

	}
	if shippingOrder.IsReceiveAtLMHub {
		req.ReceiveAtHubCode = shippingOrder.LastMileHubCode
	}
	if len(shippingOrder.ProductTypes) > 0 &&
		isContainsProductType(shippingOrder.ProductTypes, enum.ProductType.FRAGILE_PRODUCT) {
		req.AdditionalFees = []model.AdditionalFee{
			{
				AdditionalFeeType: enum.AdditionalFeeType.FRAGILE_PRODUCT,
			},
		}
	}
	if shippingOrder.ShippingType != nil &&
		*shippingOrder.ShippingType == enum.ShippingOrderType.DELIVERY {
		if strings.HasPrefix(shippingOrder.ReferenceCode, "RO") {
			req.ShippingType = &enum.ShippingOrderType.RO
		}
		if strings.HasPrefix(shippingOrder.ReferenceCode, "RONS") {
			req.ShippingType = &enum.ShippingOrderType.RONS
		}

		if shippingOrder.LastMileHubCode != "" {
			req.ToHubCode = shippingOrder.LastMileHubCode
		}
		if shippingOrder.FirstMileHubCode != "" {
			req.FromHubCode = shippingOrder.FirstMileHubCode
		}
	}

	return req
}

func DoesConfigFeeContainsQuota(configFee *model.ConfigFee) bool {
	for _, freeStruct := range configFee.FeeStructures {
		for _, condition := range freeStruct.FeeConditions {
			if condition.ConditionType == enum.FeeConditionType.TOTAL_ORDER ||
				condition.ConditionType == enum.FeeConditionType.TOTAL_PACKAGE {
				return true
			}
		}
	}
	return false
}

func CreateCustomers(customers []*model.Customer, createdBy int64) *common.APIResponse {
	if len(customers) == 0 {
		return nil
	}

	var customerCodes []string
	var customerPhones []string
	var sellerCodeQuery = ""
	var sellerCounter = 0
	var sellerMap = map[string]bool{}
	var sellerTypeMap = map[string]enum.CustomerTypeValue{}
	var mkpAccountIds []int
	var mkpAccountMap = map[int]bool{}
	var invalidCustomerCode = []string{}

	for _, c := range customers {
		if c.Code == "" || c.Phone == "" || c.Name == "" {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Mã khách hàng, tên, số điện thoại không được để trống",
			}
		}

		if !utils.CheckPhoneFormat(c.Phone) {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Số điện thoại không hợp lệ: " + c.Phone,
			}
		}

		if c.CustomerType == nil ||
			*c.CustomerType == "" ||
			!utils.CheckExistInEnum(*c.CustomerType, *enum.CustomerType) {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Loại khách hàng không hợp lệ",
			}
		}

		if _, ok := sellerMap[c.Code]; !ok &&
			(*c.CustomerType == enum.CustomerType.SELLER ||
				*c.CustomerType == enum.CustomerType.VENDOR) {
			sellerCodeQuery += c.Code + ","
			sellerCounter++
			sellerMap[c.Code] = false
			sellerTypeMap[c.Code] = *c.CustomerType
		}

		if *c.CustomerType == enum.CustomerType.INTERNAL {
			customerId, err := strconv.Atoi(c.Code)
			if err != nil {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Format mã khách hàng không hợp lệ: " + c.Code,
				}
			}

			mkpAccountIds = append(mkpAccountIds, customerId)
			mkpAccountMap[customerId] = false
		}

		customerCodes = append(customerCodes, c.Code)
		customerPhones = append(customerPhones, c.Phone)
	}

	dupCodeCustomerRaw := model.CustomerDB.QueryOne(bson.M{
		"$or": []bson.M{
			{"code": bson.M{"$in": customerCodes}},
			{"phone": bson.M{"$in": customerPhones}},
		},
	})
	if dupCodeCustomerRaw.Status == common.APIStatus.Ok {
		dupCodeCustomer := dupCodeCustomerRaw.Data.([]*model.Customer)
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Trùng mã khách hàng/ số điện thoại: " + dupCodeCustomer[0].Name,
		}
	}

	if sellerCodeQuery != "" {
		sellers, _ := client.Services.SellerClient.GetSellersInformation(
			request.GetSellerRequest{
				SellerCodes: sellerCodeQuery,
				Offset:      0,
				Limit:       sellerCounter,
			},
		)

		for _, seller := range sellers {
			if _, ok := sellerMap[seller.Code]; ok {
				sellerMap[seller.Code] = true
			}
		}

		for sellerCode, isExists := range sellerMap {
			if !isExists {
				invalidCustomerCode = append(invalidCustomerCode, sellerCode)
			}
		}
	}

	if len(mkpAccountIds) > 0 {
		mkpAccounts, _ := client.Services.MkpClient.GetListMkpAccount(request.MkpAccountRequest{
			Ids: mkpAccountIds,
		})

		for _, mkpAccount := range mkpAccounts {
			if _, ok := mkpAccountMap[mkpAccount.CustomerId]; ok {
				mkpAccountMap[mkpAccount.CustomerId] = true
			}
		}

		for mkpAccountId, isExists := range mkpAccountMap {
			if !isExists {
				invalidCustomerCode = append(invalidCustomerCode, strconv.Itoa(mkpAccountId))
			}
		}
	}

	if len(invalidCustomerCode) > 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã khách hàng không hợp lệ",
			ErrorCode: "INVALID_CUSTOMER_CODE",
			Data:      invalidCustomerCode,
		}
	}

	for _, c := range customers {
		c.CustomerId = model.GenId("CUSTOMER_ID")
		newKeyword, keyErr := utils.GenKeyword(c.Name, c.Code, c.Phone)
		if keyErr != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không thể sinh mã cho khách hàng: " + c.Name,
			}
		}
		c.Keyword = newKeyword
	}

	return model.CustomerDB.CreateMany(customers)
}

func UpdateCustomersPayment(customerIds []int64, paymentMethod *enum.PreferPaymentMethodValue) *common.APIResponse {
	if paymentMethod == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing Payment Method",
			ErrorCode: "PAYMENT_METHOD_REQUIRED",
		}
	}

	if len(customerIds) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing CustomerIDs",
			ErrorCode: "CUSTOMERIDS_REQUIRED",
		}
	}

	return model.CustomerDB.UpdateMany(bson.M{
		"customer_id": bson.M{"$in": customerIds},
	}, bson.M{
		"prefer_payment_method": paymentMethod,
	})
}

type CountCustomersOrders struct {
	CustomerCode string
	Count        int
}

type SumCustomerOrder struct {
	Id         string `json:"_id,omitempty" bson:"_id,omitempty"`
	TotalOrder int    `json:"totalOrder,omitempty" bson:"total_order,omitempty"`
}

func CountCustomersReadyToPickOrders(customers []*model.Customer) {
	customerCodes := []string{}
	for _, c := range customers {
		customerCodes = append(customerCodes, c.Code)
	}

	filter := bson.M{}
	readyToPickStatuses := []enum.TPLCallbackStatusValue{
		enum.TPLCallbackStatus.READY_TO_PICK,
		enum.TPLCallbackStatus.PICKING,
		enum.TPLCallbackStatus.PICK_FAIL,
	}
	filter["status"] = bson.M{"$in": readyToPickStatuses}

	orderTypes := []enum.ShippingOrderTypeValue{
		enum.ShippingOrderType.PICKUP,
		enum.ShippingOrderType.FMPO,
		enum.ShippingOrderType.RETURN,
	}
	filter["type"] = bson.M{"$in": orderTypes}
	filter["customer_info.code"] = bson.M{"$in": customerCodes}

	matchStage := bson.D{{"$match", filter}}
	groupStage := bson.D{{
		"$group", bson.D{
			{"_id", "$customer_info.code"},
			{"total_order", bson.D{
				{"$sum", 1}},
			},
		},
	}}

	var result []SumCustomerOrder
	model.ShippingOrderDB.SecondaryInstance.Aggregate(mongo.Pipeline{
		matchStage,
		groupStage,
	}, &result)

	resultMap := make(map[string]int64)
	for _, count := range result {
		resultMap[count.Id] = int64(count.TotalOrder)
	}

	for _, customer := range customers {
		if count, ok := resultMap[customer.Code]; ok {
			customer.TotalReadyToPickOrds = count
		}
	}
}

func CountCustomersPickedOrders(customers []*model.Customer) {
	customerCodes := []string{}
	for _, c := range customers {
		customerCodes = append(customerCodes, c.Code)
	}
	filter := bson.M{}
	pickedStatuses := []enum.TPLCallbackStatusValue{
		enum.TPLCallbackStatus.PICKED,
		enum.TPLCallbackStatus.STORING,
		enum.TPLCallbackStatus.TRANSPORTING,
		enum.TPLCallbackStatus.DELIVERING,
		enum.TPLCallbackStatus.DELIVERED,
		enum.TPLCallbackStatus.LOST,
		enum.TPLCallbackStatus.COMPLETED,
	}
	filter["status"] = bson.M{"$in": pickedStatuses}

	orderTypes := []enum.ShippingOrderTypeValue{
		enum.ShippingOrderType.PICKUP,
		enum.ShippingOrderType.FMPO,
		enum.ShippingOrderType.RETURN,
	}
	filter["type"] = bson.M{"$in": orderTypes}

	filter["customer_info.code"] = bson.M{"$in": customerCodes}
	matchStage := bson.D{{"$match", filter}}
	groupStage := bson.D{{
		"$group", bson.D{
			{"_id", "$customer_info.code"},
			{"total_order", bson.D{
				{"$sum", 1}},
			},
		},
	}}

	var result []SumCustomerOrder
	model.ShippingOrderDB.SecondaryInstance.Aggregate(mongo.Pipeline{
		matchStage,
		groupStage,
	}, &result)

	resultMap := make(map[string]int64)
	for _, count := range result {
		resultMap[count.Id] = int64(count.TotalOrder)
	}

	for _, customer := range customers {
		if count, ok := resultMap[customer.Code]; ok {
			customer.TotalPickedOrds = count
		}
	}
}

func CountCustomersDeliveringOrders(customers []*model.Customer) {
	customerCodes := []string{}
	for _, c := range customers {
		customerCodes = append(customerCodes, c.Code)
	}
	filter := bson.M{}
	deliveringStatuses := []enum.TPLCallbackStatusValue{
		enum.TPLCallbackStatus.READY_TO_PICK,
		enum.TPLCallbackStatus.PICKING,
		enum.TPLCallbackStatus.PICKED,
		enum.TPLCallbackStatus.TRANSPORTING,
		enum.TPLCallbackStatus.STORING,
		enum.TPLCallbackStatus.DELIVERING,
		enum.TPLCallbackStatus.DELIVERY_FAIL,
		enum.TPLCallbackStatus.PICK_FAIL,
	}
	filter["status"] = bson.M{"$in": deliveringStatuses}

	filter["type"] = enum.ShippingOrderType.DELIVERY

	filter["customer_info.code"] = bson.M{"$in": customerCodes}

	matchStage := bson.D{{"$match", filter}}
	groupStage := bson.D{{
		"$group", bson.D{
			{"_id", "$customer_info.code"},
			{"total_order", bson.D{
				{"$sum", 1}},
			},
		},
	}}

	var result []SumCustomerOrder
	model.ShippingOrderDB.SecondaryInstance.Aggregate(mongo.Pipeline{
		matchStage,
		groupStage,
	}, &result)

	resultMap := make(map[string]int64)
	for _, count := range result {
		resultMap[count.Id] = int64(count.TotalOrder)
	}

	for _, customer := range customers {
		if count, ok := resultMap[customer.Code]; ok {
			customer.TotalWaitToDeliveryOrds = count
		}
	}
}

func CountCustomersDeliveredOrders(customers []*model.Customer) {
	customerCodes := []string{}
	for _, c := range customers {
		customerCodes = append(customerCodes, c.Code)
	}
	filter := bson.M{}
	deliveredStatuses := []enum.TPLCallbackStatusValue{
		enum.TPLCallbackStatus.DELIVERED,
		enum.TPLCallbackStatus.COD_COLLECTED,
		enum.TPLCallbackStatus.COMPLETED,
	}
	filter["status"] = bson.M{"$in": deliveredStatuses}

	filter["type"] = enum.ShippingOrderType.DELIVERY

	filter["customer_info.code"] = bson.M{"$in": customerCodes}

	matchStage := bson.D{{"$match", filter}}
	groupStage := bson.D{{
		"$group", bson.D{
			{"_id", "$customer_info.code"},
			{"total_order", bson.D{
				{"$sum", 1}},
			},
		},
	}}

	var result []SumCustomerOrder
	model.ShippingOrderDB.SecondaryInstance.Aggregate(mongo.Pipeline{
		matchStage,
		groupStage,
	}, &result)

	resultMap := make(map[string]int64)
	for _, count := range result {
		resultMap[count.Id] = int64(count.TotalOrder)
	}

	for _, customer := range customers {
		if count, ok := resultMap[customer.Code]; ok {
			customer.TotalDeliveredOrds = count
		}
	}
}

func UpsertCustomers(customers []*model.Customer, createdBy int64) *common.APIResponse {
	if len(customers) == 0 {
		return nil
	}

	var customerCodes []string
	var newCustomers []*model.Customer
	var updateCustomers []*model.Customer
	existedCustomersMap := make(map[string]*model.Customer)

	for _, c := range customers {
		if c.Code == "" || c.Phone == "" || c.Name == "" {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Mã khách hàng, tên, số điện thoại không được để trống",
			}
		}

		if c.CustomerType == nil || *c.CustomerType == "" {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Loại khách hàng không được để trống",
			}
		}

		if c.Code != "" {
			customerCodes = append(customerCodes, c.Code)
		}
	}

	customerRaws := model.CustomerDB.Query(bson.M{"code": bson.M{"$in": customerCodes}}, 0, int64(len(customerCodes)), nil)
	if customerRaws.Status == common.APIStatus.Ok {
		existedCustomers := customerRaws.Data.([]*model.Customer)
		for _, c := range existedCustomers {
			existedCustomersMap[c.Code] = c
		}

		for _, c := range customers {
			if _, ok := existedCustomersMap[c.Code]; ok {
				updateCustomers = append(updateCustomers, c)
			} else {
				newCustomers = append(newCustomers, c)
			}
		}
	} else {
		newCustomers = customers
	}

	for _, c := range newCustomers {
		c.CustomerId = model.GenId("CUSTOMER_ID")
		newKeyword, keyErr := utils.GenKeyword(c.Name, c.Code, c.Phone)
		if keyErr == nil {
			c.Keyword = newKeyword
		}
		if c.Code != "" {
			customerCodes = append(customerCodes, c.Code)
		}
	}

	newCustomerResult := model.CustomerDB.CreateMany(customers)
	if newCustomerResult.Status != common.APIStatus.Ok {
		return newCustomerResult
	}

	for _, c := range updateCustomers {
		if c.AppliedFees != nil && len(*c.AppliedFees) > 0 {

		}
		model.CustomerDB.UpdateOne(bson.M{"code": c.Code}, bson.M{})
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}
}

func ImportCustomerTags(importCustomers []request.ImportCustomerRequest) *common.APIResponse {
	// Validate
	if len(importCustomers) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Dữ liệu không được để trống",
		}
	}
	customerFilters := []bson.M{}
	customerCodeTagMap := map[string][]string{}
	customerCodeNoteMap := map[string]string{}
	for _, c := range importCustomers {
		if c.Code == "" || c.CustomerType == "" {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Mã khách hàng không được để trống",
			}
		}
		if !utils.CheckExistInEnum(c.CustomerType, *enum.CustomerType) {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Loại khách hàng không hợp lệ",
			}
		}
		if c.Tags == nil || len(c.Tags) == 0 {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Tags không được để trống",
			}
		}
		if c.Code != "" {
			customerFilters = append(customerFilters, bson.M{
				"code":          c.Code,
				"customer_type": c.CustomerType,
			})
			customerCodeTagMap[c.Code] = c.Tags
			if c.Note != "" {
				customerCodeNoteMap[c.Code] = c.Note
			}
		}
	}

	var customers []*model.Customer
	if len(customerFilters) > 0 {
		customerRaw := model.CustomerDB.Query(
			bson.M{"$or": customerFilters},
			0, int64(len(customerFilters)),
			nil)
		if customerRaw.Status != common.APIStatus.Ok {
			return customerRaw
		}
		cs := customerRaw.Data.([]*model.Customer)
		customers = append(customers, cs...)
	}

	if len(customers) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không tìm thấy khách hàng",
		}
	}

	needUpdateCustomers := []model.Customer{}
	for _, c := range customers {
		tags := customerCodeTagMap[c.Code]
		if tags == nil || len(tags) == 0 {
			continue
		}
		if c.Tags == nil {
			c.Tags = &tags
		} else {
			t := utils.MergeUniqueStringSlice(*c.Tags, tags)
			c.Tags = &t
		}

		if note, ok := customerCodeNoteMap[c.Code]; ok {
			c.Note = &note
		}
		needUpdateCustomers = append(needUpdateCustomers, *c)
	}
	for _, c := range needUpdateCustomers {
		if c.Code == "" {
			continue
		}
		updater := bson.M{"tags": c.Tags}
		if c.Note != nil && *c.Note != "" {
			updater["note"] = c.Note
		}
		go model.CustomerDB.UpdateOne(
			bson.M{"_id": c.Id},
			updater,
		)
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}
}
