package vtp

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/response"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathBookVTPService   = "/v2/order/createOrder"
	pathCancelVTPService = "/v2/order/UpdateOrder"
	pathGetProvince      = "/v2/categories/listProvinceById"
	pathGetDistrict      = "/v2/categories/listDistrict"
	pathGetWard          = "/v2/categories/listWards"
	pathGetPrice         = "/v2/order/getPrice"
	pathGetAllPrice      = "/v2/order/getPriceAll"
)

type Client struct {
	svc     *client.RestClient
	headers map[string]string
}

func NewServiceClient(apiHost, logName string, session *mongo.Database) *Client {
	if apiHost == "" {
		return nil
	}
	newClient := &Client{
		svc: client.NewRESTClient(
			apiHost,
			logName,
			180*time.Second,
			0,
			180*time.Second,
		),
		headers: map[string]string{},
	}
	newClient.svc.SetDBLog(session)
	return newClient
}

func (cli *Client) ChangeInformation(carrierInfo *model.Carrier) {
	cli.headers = map[string]string{
		"Token": carrierInfo.ExtraData.AccessToken,
	}
}

func (cli *Client) CreateTrackingVTP(createTrackingRequest request.BookVTP) (tracking *model.ShippingInfo, err error) {
	type myResponse struct {
		Message string            `json:"message"`
		No      string            `json:"no"`
		Success bool              `json:"success"`
		Data    *response.BookVTP `json:"data"`
	}

	query := map[string]string{}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, query, createTrackingRequest, pathBookVTPService, nil)
	if err != nil {
		return
	}
	resBody := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return
	}

	if resBody.Success || resBody.Data == nil {
		err = fmt.Errorf("%v", resBody)
		return
	}

	tracking = &model.ShippingInfo{
		CODAmount:      float64(resBody.Data.MoneyCollection),
		TrackingNumber: resBody.Data.OrderNumber,
		FeeAmount:      float64(resBody.Data.MoneyTotalFee),
		VatAmount:      float64(resBody.Data.MoneyVat),
	}

	return
}

func (cli *Client) CancelVTP(cancelTrackingRequest request.UpdateTrackingVTP) (err error) {
	type myResponse struct {
		Message string `json:"message"`
		Status  int    `json:"status"`
		Success bool   `json:"success"`
		Error   bool   `json:"error"`
	}

	query := map[string]string{}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, query, cancelTrackingRequest, pathCancelVTPService, nil)
	if err != nil {
		return
	}
	resBody := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return
	}

	if resBody.Status != 200 {
		err = fmt.Errorf("%v", resBody)
		return
	}

	return
}

func (cli *Client) GetProvince() (result []*model.ProvinceVTP, err error) {
	type myResponse struct {
		Message string               `json:"message"`
		Status  int                  `json:"status"`
		Error   bool                 `json:"error"`
		Data    []*model.ProvinceVTP `json:"data"`
	}

	query := map[string]string{
		"provinceId": strconv.Itoa(-1),
	}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, query, nil, pathGetProvince, nil)
	if err != nil {
		return
	}
	resBody := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return
	}

	if resBody.Error {
		err = fmt.Errorf("%v-%v", resBody, resBody.Message)
		return
	}

	if len(resBody.Data) > 0 {
		result = resBody.Data
	}

	return
}
func (cli *Client) GetDistrict(provinceId int64) (result []*model.DistrictVTP, err error) {
	type myResponse struct {
		Message string               `json:"message"`
		Status  int                  `json:"status"`
		Error   bool                 `json:"error"`
		Data    []*model.DistrictVTP `json:"data"`
	}

	query := map[string]string{
		"provinceId": strconv.Itoa(int(provinceId)),
	}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, query, nil, pathGetDistrict, nil)
	if err != nil {
		return
	}
	resBody := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return
	}

	if resBody.Error {
		err = fmt.Errorf("%v-%v", resBody, resBody.Message)
		return
	}

	if len(resBody.Data) > 0 {
		result = resBody.Data
	}

	return
}

func (cli *Client) GetWard(districtId int64) (result []*model.WardVTP, err error) {
	type myResponse struct {
		Message string           `json:"message"`
		Status  int              `json:"status"`
		Error   bool             `json:"error"`
		Data    []*model.WardVTP `json:"data"`
	}

	query := map[string]string{
		"districtId": strconv.Itoa(int(districtId)),
	}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, query, nil, pathGetWard, nil)
	if err != nil {
		return
	}
	resBody := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return
	}

	if resBody.Error {
		err = fmt.Errorf("%v-%v", resBody, resBody.Message)
		return
	}

	if len(resBody.Data) > 0 {
		result = resBody.Data
	}

	return
}

func (cli *Client) GetShippingFee(data request.GetVTPShippingFee) (fee float64, err error) {
	type myResponse struct {
		Message string            `json:"message"`
		Status  int               `json:"status"`
		Error   bool              `json:"error"`
		Data    *response.BookVTP `json:"data"`
	}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, data, pathGetPrice, nil)
	if err != nil {
		return
	}
	resBody := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return
	}

	if resBody.Error {
		err = fmt.Errorf("%v-%v", resBody, resBody.Message)
		return
	}

	fee = float64(resBody.Data.MoneyTotalFee)
	return
}

func (cli *Client) GetShippingAllPrice(data request.GetVTPShippingFee) ([]*response.GetVTPShippingService, error) {

	var res *client.RestResult
	var err error
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, data, pathGetAllPrice, nil)
	if err != nil {
		return nil, err
	}
	resBody := []*response.GetVTPShippingService{}

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return nil, err
	}

	if len(resBody) == 0 {
		err = fmt.Errorf("Hiênt tại VTP chưa hỗ trợ giao hàng tại khu vực này")
		return nil, err
	}

	return resBody, nil
}
