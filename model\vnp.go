package model

import "time"

type VNPOrder struct {
	Id                               string     `json:"Id"`
	CustomerId                       string     `json:"CustomerId"`
	ItemCode                         string     `json:"ItemCode"`
	OrderCode                        string     `json:"OrderCode"`
	OrderStatusId                    int64      `json:"OrderStatusId"`
	SenderFullname                   string     `json:"SenderFullname"`
	SenderAddress                    string     `json:"SenderAddress"`
	SenderTel                        string     `json:"sender_tel"`
	ReceiverFullname                 string     `json:"ReceiverFullname"`
	ReceiverAddress                  string     `json:"ReceiverAddress"`
	ReceiverTel                      string     `json:"ReceiverTel"`
	SenderProvinceId                 string     `json:"SenderProvinceId"`
	SenderDistrictId                 string     `json:"SenderDistrictId"`
	ReceiverProvinceId               string     `json:"ReceiverProvinceId"`
	ReceiverDistrictId               string     `json:"ReceiverDistrictId"`
	OrderAmount                      float64    `json:"OrderAmount"`
	CodAmount                        float64    `json:"CodAmount"`
	CodAmountEvaluation              float64    `json:"CodAmountEvaluation"`
	TotalFreightIncludeVat           float64    `json:"TotalFreightIncludeVat"`
	CodFreight                       float64    `json:"CodFreight"`
	TotalFreightExcludeVatEvaluation float64    `json:"TotalFreightExcludeVatEvaluation"`
	TotalFreightIncludeVatEvaluation float64    `json:"TotalFreightIncludeVatEvaluation"`
	VatFreightEvaluation             float64    `json:"VatFreightEvaluation"`
	ShippingFreightEvaluation        float64    `json:"ShippingFreightEvaluation"`
	VasFreightEvaluation             float64    `json:"VasFreightEvaluation"`
	CodFreightEvaluation             float64    `json:"CodFreightEvaluation"`
	FuelFreightEvaluation            float64    `json:"FuelFreightEvaluation"`
	RegionFreightEvaluation          float64    `json:"RegionFreightEvaluation"`
	PackageContent                   string     `json:"PackageContent"`
	PickupType                       int        `json:"PickupType"`
	IsPackageViewable                bool       `json:"IsPackageViewable"`
	Weight                           float64    `json:"weight"`
	WeightConvert                    float64    `json:"WeightConvert"`
	Length                           float64    `json:"length"`
	Height                           float64    `json:"Height"`
	AcceptancePoscode                string     `json:"acceptance_poscode"`
	ToPOSCode                        string     `json:"ToPOSCode"`
	DeliveryTimes                    int        `json:"DeliveryTimes"`
	DeliveryNote                     string     `json:"DeliveryNote"`
	CauseName                        string     `json:"CauseName"`
	InputTime                        *time.Time `json:"InputTime"`
	SendingTime                      *time.Time `json:"SendingTime"`
	CreateTime                       *time.Time `json:"CreateTime"`
	LastUpdateTime                   *time.Time `json:"LastUpdateTime"`
	CustomerNote                     string     `json:"customer_note"`
}
