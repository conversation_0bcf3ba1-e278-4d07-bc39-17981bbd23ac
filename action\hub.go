package action

import (
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// CreateHub action
func CreateHub(input *model.Hub) *common.APIResponse {
	if input.HubId == 0 {
		input.HubId = model.GenId("HUB_ID")
	} else {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "ID hub không hợp lệ",
		}
	}

	if input.Name == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Tên hub không được để trống",
		}
	}

	if input.Code == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Mã hub không được để trống",
		}
	}

	if len(input.ListCarrierRefId) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Danh sách nhà vận chuyển không được để trống",
		}
	}

	if input.DefaultCarrierId == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Nhà vận chuyển mặc định không được để trống",
		}
	}

	if input.LimitStatus == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mặc định của tắt/mở hạn mức không được để trống",
		}
	}

	if input.Address != nil && input.Address.Phone != "" {
		if !utils.CheckPhoneFormat(input.Address.Phone) {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Số điện thoại không hợp lệ",
			}
		}
	}

	if input.CompanyBankInfo.BankAccountName == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Thiếu thông tin chủ tài khoản công ty",
		}
	}
	if input.CompanyBankInfo.BankAccountID == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Thiếu thông tin số tài khoản công ty",
		}

	}
	if input.CompanyBankInfo.BankName == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Thiếu thông tin tài khoản ngân hàng để giao dịch",
		}
	}

	getHub := model.HubDB.Query(bson.M{"code": input.Code}, 0, 100, nil)

	if getHub.Status == common.APIStatus.Ok && getHub.Data != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Hub " + input.Code + " đã tồn tại",
		}
	}

	getHub = model.HubDB.Query(bson.M{"name": input.Name}, 0, 100, nil)

	if getHub.Status == common.APIStatus.Ok && getHub.Data != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Hub tên " + input.Name + " đã tồn tại",
		}
	}

	getHub = model.HubDB.Query(bson.M{
		"warehouse_reference_code": input.WarehouseReferenceCode,
	}, 0, 100, nil)

	if getHub.Status == common.APIStatus.Ok && getHub.Data != nil {
		hub := getHub.Data.([]*model.Hub)[0]
		if hub.HubId != input.HubId {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Hub của kho " + input.WarehouseReferenceCode + " đã tồn tại",
			}
		}
	}

	key, err := utils.GenKeyword(input.Name, input.Code, input.HubId)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không thể tạo từ khóa cho hub",
		}
	}
	input.Keyword = key

	if input.Address.ProvinceCode == "" || input.Address.DistrictCode == "" || input.Address.WardCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Thiếu mã thành phố hoặc mã quận huyện hoặc mã phường xã",
		}
	}

	ward, err := client.Services.WarehouseCoreClient.GetWard("", input.Address.WardCode, input.Address.DistrictCode, input.Address.ProvinceCode)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Địa chỉ không hợp lệ",
		}
	}
	input.Address.ProvinceName = ward[0].ProvinceName
	input.Address.DistrictName = ward[0].DistrictName
	input.Address.WardName = ward[0].Name
	if !utils.CheckValidCodeFormat(input.Code) {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Format code không hợp lệ",
		}
	}

	if len(input.Zones) > 0 {
		isZoneAvailable := isZonesAvailableToAssign(input.Zones, 0)
		if isZoneAvailable != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: isZoneAvailable.Message,
			}
		}
	}

	input.VersionNo = uuid.NewString()
	if len(input.LimitAmountRequest) > 0 {
		input.LimitAmountByWeekday = map[string]float64{}
		for _, v := range input.LimitAmountRequest {

			if enum.MapWeekday[v.From] > enum.MapWeekday[v.To] {
				return &common.APIResponse{
					Status:  common.APIStatus.Error,
					Message: "Thời gian bắt đầu không được lớn hơn thời gian kết thúc",
				}
			}
			isValid := false
			for _, day := range enum.WeekendDay {
				if day == v.From {
					if _, exist := input.LimitAmountByWeekday[day]; !exist {
						isValid = true
					} else {
						return &common.APIResponse{
							Status:  common.APIStatus.Invalid,
							Message: fmt.Sprintf("Hạn mức này %s đã tồn tại", day),
						}
					}
				}
				if isValid {
					if day != v.To {
						input.LimitAmountByWeekday[day] = v.LimitAmount
					}
					if day == v.To {
						input.LimitAmountByWeekday[day] = v.LimitAmount
						isValid = false
					}
				}
			}
		}
		if len(input.LimitAmountByWeekday) < len(enum.WeekendDay) {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Chưa thiết lập hạn mức đủ các ngày trong tuần",
			}
		}
	}

	// check department existence
	getDepartmentResp := client.Services.HrmClient.GetDepartmentByCode(input.Code)
	if getDepartmentResp.Status != common.APIStatus.NotFound {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã phòng ban đã tồn tại, chọn mã hub khác để tiếp tục",
		}
	}

	if input.AutoDoneReconcile == nil {
		input.AutoDoneReconcile = &enum.False
	}

	createCallbackResult := model.HubDB.Create(input)

	if len(input.Zones) > 0 {
		result := model.ZoneDB.UpdateMany(bson.M{
			"is_deleted": false,
			"code": bson.M{
				"$in": input.Zones,
			},
		}, bson.M{
			"is_assign_to_hub": true,
		})

		if result.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: result.Message,
			}
		}
	}

	if createCallbackResult.Status != common.APIStatus.Ok {
		return createCallbackResult
	}

	departmentAddress := input.Address.Address + ", " + input.Address.WardName + ", " + input.Address.DistrictName + ", " + input.Address.ProvinceName
	createDepartment := model.Department{
		Code:        input.Code,
		Name:        input.Name,
		ParentCode:  &conf.Config.DefaultHubParentCode,
		RoleList:    &conf.Config.DefaultHubRole,
		Tag:         &conf.Config.DefaultHubTag,
		CompanyCode: enum.CompanyCode.BUYMED_LOGISTICS,
		Address:     departmentAddress,
	}

	createDepartmentResponse := client.Services.HrmClient.CreateDepartment(createDepartment)
	if createDepartmentResponse.Status != common.APIStatus.Ok {
		return createDepartmentResponse
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Tạo Hub thành công",
	}
}

// GetHub action
func GetHub(query *request.HubQuery, offset int64, limit int64, getTotal bool) *common.APIResponse {
	filter := bson.M{}
	if query.ParentCode != nil {
		filter["parent_code"] = *query.ParentCode
	}

	if len(query.ListCarrierRefId) > 0 {
		filter["list_carrier_id"] = bson.M{
			"$in": query.ListCarrierRefId,
		}
	}

	if query.Name != "" {
		filter["name"] = bson.M{
			"$regex": query.Name,
		}
	}

	if query.Keyword != "" {
		filter["keyword"] = bson.M{
			"$regex": utils.ToRawText(query.Keyword),
		}
	}

	if query.HubId != 0 {
		filter["hub_id"] = query.HubId
	}

	if query.Code != "" {
		filter["code"] = query.Code
	}

	if query.Active != nil {
		filter["active"] = *query.Active
	}

	if query.WarehouseReferenceCode != "" {
		filter["warehouse_reference_code"] = query.WarehouseReferenceCode
	}

	if len(query.HubCodes) > 0 {
		filter["code"] = bson.M{
			"$in": query.HubCodes,
		}
	}

	if query.IsFMHub != nil {
		filter["is_fm_hub"] = query.IsFMHub
	}

	if query.AllowDropOffHub != nil {
		filter["allow_drop_off_hub"] = *query.AllowDropOffHub
	}

	result := model.HubDB.Query(filter, offset, limit, &primitive.M{"_id": -1})
	if getTotal {
		countResult := model.HubDB.Count(filter)
		result.Total = countResult.Total
	}
	return result
}

// UpdateHub action
func UpdateHub(input *model.Hub) *common.APIResponse {

	if input.HubId == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã Hub không được để trống",
		}
	}

	if input.Address != nil && input.Address.Phone != "" {
		if !utils.CheckPhoneFormat(input.Address.Phone) {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Số điện thoại không hợp lệ",
			}
		}
	}

	if len(input.ListCarrierRefId) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Danh sách nhà vận chuyển không được để trống",
		}
	}

	if input.DefaultCarrierId == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Nhà vận chuyển mặc định không được để trống",
		}
	}

	if input.LimitStatus == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mặc định của tắt/mở hạn mức không được để trống",
		}
	}

	if input.CompanyBankInfo.BankAccountName == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Thiếu thông tin chủ tài khoản công ty",
		}
	}
	if input.CompanyBankInfo.BankAccountID == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Thiếu thông tin số tài khoản công ty",
		}

	}
	if input.CompanyBankInfo.BankName == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Thiếu thông tin tài khoản ngân hàng để giao dịch",
		}
	}

	if len(input.LimitAmountRequest) > 0 {
		input.LimitAmountByWeekday = map[string]float64{}
		for _, v := range input.LimitAmountRequest {

			if enum.MapWeekday[v.From] > enum.MapWeekday[v.To] {
				return &common.APIResponse{
					Status:  common.APIStatus.Error,
					Message: "Thời gian bắt đầu không được lớn hơn thời gian kết thúc",
				}
			}
			isValid := false
			for _, day := range enum.WeekendDay {
				if day == v.From {
					if _, exist := input.LimitAmountByWeekday[day]; !exist {
						isValid = true
					} else {
						return &common.APIResponse{
							Status:  common.APIStatus.Invalid,
							Message: fmt.Sprintf("Hạn mức này %s đã tồn tại", day),
						}
					}
				}
				if isValid {
					if day != v.To {
						input.LimitAmountByWeekday[day] = v.LimitAmount
					}
					if day == v.To {
						input.LimitAmountByWeekday[day] = v.LimitAmount
						isValid = false
					}
				}
			}
		}
		if len(input.LimitAmountByWeekday) < len(enum.WeekendDay) {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Chưa thiết lập hạn mức đủ các ngày trong tuần",
			}
		}
	}

	if input.WarehouseReferenceCode != "" {
		getHub := model.HubDB.Query(bson.M{
			"warehouse_reference_code": input.WarehouseReferenceCode,
		}, 0, 100, nil)

		if getHub.Status == common.APIStatus.Ok && getHub.Data != nil {
			hub := getHub.Data.([]*model.Hub)[0]
			if hub.HubId != input.HubId {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Hub của kho " + input.WarehouseReferenceCode + " đã tồn tại",
				}
			}
		}
	}

	hubRaw := model.HubDB.QueryOne(bson.M{
		"hub_id": input.HubId,
	})

	if hubRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: hubRaw.Message,
		}
	}

	hub := hubRaw.Data.([]*model.Hub)[0]
	if len(input.LimitAmountByWeekday) > 0 {

		hubLimit := getSmallestHubLimitAmount(input.LimitAmountByWeekday, hub.LimitAmountByWeekday[time.Now().Weekday().String()])
		// Note: tiền hub đã gắn > tiền cần update
		if hub.LimitAssignedAmount > hubLimit || hubLimit < 0 {
			return limitErrorResponse(fmt.Sprintf("Rất tiếc, hạn mức cập nhật không được nhỏ hơn hạn mức đã gán %sđ", utils.MoneyFormat(hub.LimitAssignedAmount)))
		}
	}

	// if FE pass empty array means remove all zones in hub
	if len(input.Zones) == 0 && len(hub.Zones) > 0 {
		model.ZoneDB.UpdateMany(bson.M{
			"is_deleted": false,
			"code": bson.M{
				"$in": hub.Zones,
			},
		}, bson.M{
			"is_assign_to_hub": false,
		})
	}

	if len(input.Zones) > 0 {
		if hub.Active != nil &&
			!*hub.Active &&
			(input.Active == nil ||
				!*input.Active) {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Hub đã ngừng hoạt động, không thể cập nhật khu vực",
			}
		}
		isZoneAvailable := isZonesAvailableToAssign(input.Zones, input.HubId)
		if isZoneAvailable != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: isZoneAvailable.Message,
			}
		}

		// new asssigned zone
		resultNewAssignedZone := model.ZoneDB.UpdateMany(bson.M{
			"is_deleted": false,
			"code": bson.M{
				"$in": input.Zones,
			},
		}, bson.M{
			"is_assign_to_hub": true,
		})

		if resultNewAssignedZone.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: resultNewAssignedZone.Message,
			}
		}
		// remove after update zone
		removedZone := utils.GetRemovedElement(hub.Zones, input.Zones)
		if len(removedZone) > 0 {
			model.ZoneDB.UpdateMany(bson.M{
				"is_deleted": false,
				"code": bson.M{
					"$in": removedZone,
				},
			}, bson.M{
				"is_assign_to_hub": false,
			})
		}
	}

	if input.Name != "" {
		hubRaw := model.HubDB.QueryOne(bson.M{
			"hub_id": input.HubId,
		})
		if hubRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Hub id không hợp lệ",
			}
		}
		hub := hubRaw.Data.([]*model.Hub)[0]

		newKeyword, err := utils.GenKeyword(input.Name, hub.Code, hub.HubId)
		if err != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không thể tạo từ khóa cho hub",
			}
		}
		input.Keyword = newKeyword
	}

	if input.Active != nil {
		if !*input.Active { // Deactivate Hub
			updateRoutes := RemoveHubOutOfRoutes(input.Code)
			if updateRoutes.Status != common.APIStatus.Ok {
				return updateRoutes
			}

			if len(hub.Zones) > 0 {
				model.ZoneDB.UpdateMany(bson.M{
					"is_deleted": false,
					"code": bson.M{
						"$in": hub.Zones,
					},
				}, bson.M{
					"is_assign_to_hub": false,
				})
			}
			input.Zones = []string{}

		} else { // Activate Hub
			if len(input.Zones) == 0 {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Cần thiết lập zone mà hub sẽ quản lý",
				}
			}
		}
	}

	if input.LimitStatus == "ACTIVE" {
		input.TotalReceivedAmount = CalculateHubShippingCodAmount(hub.Code, 0, []string{string(enum.HubShippingOrderStatus.DELIVERED), string(enum.HubShippingOrderStatus.COD_COLLECTED)})
	}

	afterOption := options.After
	createCallbackResult := model.HubDB.UpdateOne(
		bson.M{
			"hub_id": input.HubId,
		},
		input,
		&options.FindOneAndUpdateOptions{
			ReturnDocument: &afterOption,
		})

	if createCallbackResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Bad request when Update hub",
		}
	}

	if input.Active != nil && !*input.Active {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Cập nhật trạng thái của hub thành công, các khu vực mà Hub quản lý sẽ được gỡ bỏ",
			ErrorCode: "ZONES_REMOVE_WARNING",
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật thông tin hub thành công",
	}
}

// DeleteHub action
func DeleteHub(hubId int64) *common.APIResponse {
	hubRaw := model.HubDB.QueryOne(bson.M{
		"hub_id": hubId,
	})

	if hubRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: hubRaw.Message,
		}
	}
	hub := hubRaw.Data.([]*model.Hub)[0]

	if len(hub.Zones) > 0 {
		model.ZoneDB.UpdateMany(bson.M{
			"is_deleted": false,
			"code": bson.M{
				"$in": hub.Zones,
			},
		}, bson.M{
			"is_assign_to_hub": false,
		})
	}

	updateRoutes := RemoveHubOutOfRoutes(hub.Code)
	if updateRoutes.Status != common.APIStatus.Ok {
		return updateRoutes
	}

	return model.HubDB.Delete(
		bson.M{
			"hub_id": hubId,
		})
}
func GenHubKeyword() *common.APIResponse {
	hubsRaw := model.HubDB.QueryAll()
	hubs := hubsRaw.Data.([]*model.Hub)
	for _, hub := range hubs {
		hub.Keyword, _ = utils.GenKeyword(hub.Name, hub.Code, hub.HubId)
		model.HubDB.UpdateOne(
			bson.M{
				"code": hub.Code,
			},
			bson.M{
				"keyword": hub.Keyword,
			},
		)
	}

	return &common.APIResponse{
		Message: "Successfully genrated",
	}
}
func isZonesAvailableToAssign(zoneId []string, hubId int64) *common.Error {
	// is zone exist
	zones := model.ZoneDB.Count(bson.M{
		"is_deleted": false,
		"code": bson.M{
			"$in": zoneId,
		},
	})

	if int(zones.Total) < len(zoneId) {
		return &common.Error{
			Message: "Danh sách khu vực không hợp lệ",
		}
	}
	// No hub can own dup zone exept it self
	query := bson.M{
		"zones": bson.M{
			"$in": zoneId,
		},
	}
	if hubId != 0 {
		query["hub_id"] = bson.M{
			"$ne": hubId,
		}
	}
	hub := model.HubDB.QueryOne(query)

	if hub.Status == common.APIStatus.Ok {
		return &common.Error{
			Message: "Một trong những khu vực hiện đã được gán vào hub",
		}
	}

	return nil
}

func FindNearestHubOfAddress(address model.Address) *model.Hub {
	if address.WardCode == "" {
		return nil
	}
	// Query zone which contains ward code in ward index array => []*model.Zone
	zoneRaw := model.ZoneDB.QueryOne(bson.M{
		"is_deleted":       false,
		"is_assign_to_hub": true,
		"ward_indexes":     address.WardCode,
		"is_last_child":    true,
	})

	if zoneRaw.Status != common.APIStatus.Ok {
		return nil
	}
	zone := zoneRaw.Data.([]*model.Zone)[0]

	closestHub := model.HubDB.QueryOne(bson.M{
		"zones": zone.Code,
	})

	if closestHub.Status != common.APIStatus.Ok {
		return nil
	}

	return closestHub.Data.([]*model.Hub)[0]
}

func FindNearestHubOfAddressV2(address model.Address) *model.Hub {
	matchStage := bson.M{
		"is_deleted":       false,
		"is_assign_to_hub": true,
		"ward_indexes":     address.WardCode,
	}

	projectStage := bson.M{
		"_id":         1,
		"code":        1,
		"index_count": bson.M{"$size": "$ward_indexes"},
	}

	sortStage := bson.M{"index_count": 1}

	if address.WardCode == "" {
		matchStage = bson.M{
			"is_deleted":       false,
			"is_assign_to_hub": true,
			"district_indexes": address.DistrictCode,
		}

		projectStage = bson.M{
			"_id":         1,
			"code":        1,
			"index_count": bson.M{"$size": "$district_indexes"},
		}
	}

	var pipeline = []bson.M{
		{"$match": matchStage},
		{"$project": projectStage},
		{"$sort": sortStage},
		{"$limit": 1},
	}
	type Output struct {
		Id         primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
		Code       string             `json:"code,omitempty" bson:"code,omitempty"`
		IndexCount int64              `json:"indexCount,omitempty" bson:"index_count,omitempty"`
	}
	var result []Output
	output := model.ZoneDB.Aggregate(pipeline, &result)
	if output.Status != common.APIStatus.Ok || len(result) == 0 {
		return nil
	}

	closestHub := model.HubDB.QueryOne(bson.M{
		"zones": result[0].Code,
	})

	if closestHub.Status != common.APIStatus.Ok {
		return nil
	}

	return closestHub.Data.([]*model.Hub)[0]
}

func MigrateDefaultCarrier() *common.APIResponse {
	hubsRaw := model.HubDB.QueryAll()
	hubs := hubsRaw.Data.([]*model.Hub)
	for _, hub := range hubs {
		if len(hub.ListCarrierRefId) == 0 {
			continue
		}
		model.HubDB.UpdateOne(
			bson.M{
				"code": hub.Code,
			},
			bson.M{
				"default_carrier_id": hub.ListCarrierRefId[0],
			},
		)
	}
	return &common.APIResponse{
		Message: "Successfully genrated",
	}
}

func CalculateHubShippingCodAmount(hubCode string, driverId int64, statuses []string) (totalAmount float64) {
	type SumByStatusModel struct {
		TotalAmount float64 `json:"totalAmount,omitempty" bson:"total_amount,omitempty"`
	}

	filterBson := bson.M{}
	filterBson["status"] = bson.M{"$in": statuses}
	if driverId != 0 {
		filterBson["driver_id"] = driverId
	}
	if hubCode != "" {
		filterBson["hub_code"] = hubCode
	}

	hubShippingOrderQuery := model.HUBShippingOrderDB.Query(filterBson, 0, 1000, nil)
	if hubShippingOrderQuery.Status != common.APIStatus.Ok {
		return 0
	}

	hubShippingOrders := hubShippingOrderQuery.Data.([]*model.HubShippingOrder)
	for _, val := range hubShippingOrders {
		totalAmount += val.CODAmount
	}

	return totalAmount
}

func CalculateHubAssignedLimitAmount(hubCode string) (totalAmount float64) {
	type SumByStatusModel struct {
		TotalAmount float64 `json:"totalAmount,omitempty" bson:"total_amount,omitempty"`
	}

	filterBson := bson.M{}
	if hubCode != "" {
		filterBson["hub_code"] = hubCode
	}

	accountQuery := model.AccountDB.Query(filterBson, 0, 5000, nil)
	if accountQuery.Status != common.APIStatus.Ok {
		return 0
	}

	accounts := accountQuery.Data.([]*model.Account)
	for _, val := range accounts {
		totalAmount += val.LimitAmount
	}

	return totalAmount
}

func MigrateHubLatLong(isMigrateAll *bool, listHub []string) *common.APIResponse {
	var hubsRaw *common.APIResponse

	if *isMigrateAll == true {
		hubsRaw = model.HubDB.QueryAll()
	} else {
		hubsRaw = model.HubDB.Query(bson.M{
			"code": bson.M{
				"$in": listHub,
			},
		}, 0, int64(len(listHub)), nil)
	}

	hubs := hubsRaw.Data.([]*model.Hub)

	for _, hub := range hubs {
		if hub.Address == nil {
			_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
				CmdType: "NOT_FOUND_LAT_LONG",
				Title:   "NOT FOUND LAT LONG",
				Message: "Không tìm thấy tọa độ hub: " + hub.Code,
			})
			continue
		}
		fullAddress := fmt.Sprintf("%s, %s, %s, %s", hub.Address.Address, hub.Address.WardName, hub.Address.DistrictName, hub.Address.ProvinceName)
		var err error
		if hub.Address.Latitude != 0 || hub.Address.Longitude != 0 {
			hub.Address.Latitude, hub.Address.Longitude, err = client.Services.GeocodeClient.GetLatLng(fullAddress)
		}
		if err != nil {
			_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
				CmdType: "NOT_FOUND_LAT_LONG",
				Title:   "NOT FOUND LAT LONG",
				Message: "Không tìm thấy tọa độ hub: " + hub.Code,
			})
			continue
		}
		_ = model.HubDB.UpdateOne(bson.M{
			"code": hub.Code,
		}, bson.M{
			"address": hub.Address,
		})

		newShippingAddress := model.ShippingAddress{
			ID:              model.GenId("SHIPPING_ADDRESS_ID"),
			CreatedTime:     utils.Now(),
			CustomerCode:    hub.Code,
			CustomerPhone:   hub.Address.Phone,
			WardName:        hub.Address.WardName,
			WardCode:        hub.Address.WardCode,
			DistrictName:    hub.Address.DistrictName,
			DistrictCode:    hub.Address.DistrictCode,
			ProvinceName:    hub.Address.ProvinceName,
			ProvinceCode:    hub.Address.ProvinceCode,
			CustomerAddress: hub.Address.Address,
		}

		_ = model.ShippingAddressDB.Create(newShippingAddress)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Ok",
	}
}

func GetRelatedTripOfHub(hubCode string) *common.APIResponse {
	filterRoute := bson.M{}
	filterRoute["drop_off_points.code"] = hubCode
	filterRoute["status"] = enum.RouteStatus.ACTIVE

	routeRaw := model.RouteDB.Query(filterRoute, 0, 10000, nil)
	if routeRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Data:    []interface{}{},
			Message: "Hub không thuộc tuyến nào",
		}
	}

	routes := routeRaw.Data.([]*model.Route)

	var filter = bson.M{}
	filter["$or"] = bson.A{
		bson.M{
			"to_department_code": hubCode,
		},
		bson.M{
			"from_department_code": hubCode,
		},
	}

	filter["trip_id"] = bson.M{
		"$exists": true,
	}

	filter["so_list"] = bson.M{
		"$exists": true,
	}

	count := model.HandoverTicketDB.Count(filter).Total

	handoverTicketsRaw := model.HandoverTicketDB.Query(filter, 0, count, nil)

	type RelateTrip struct {
		RouteName           string   `json:"routeName"`
		HandoversUnaffected []string `json:"handoverTicketsUnaffected,omitempty"`
		HandoversAffected   []string `json:"handoverTicketsAffected,omitempty"`
	}
	var DataResponse []*RelateTrip

	if handoverTicketsRaw.Status != common.APIStatus.Ok {
		for _, route := range routes {
			DataResponse = append(DataResponse, &RelateTrip{RouteName: route.RouteName})
		}
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Data:    DataResponse,
			Message: "Hub thuộc tuyến đang hoạt động",
		}
	}
	handoverTickets := handoverTicketsRaw.Data.([]*model.HandoverTicket)

	mapRoutes := make(map[string][]*model.HandoverTicket)

	// Get list handoverticket on each route
	isAffected := false

	for _, route := range routes {
		mapRoutes[route.RouteName] = []*model.HandoverTicket{}
	}

	for _, handoverTicket := range handoverTickets {
		mapRoutes[handoverTicket.RouteName] = append(mapRoutes[handoverTicket.RouteName], handoverTicket)
	}

	for routeName, handoverTickets := range mapRoutes {
		handoverUnaffected := []string{}
		handoverAffected := []string{}
		for _, handoverTicket := range handoverTickets {
			// if have handovertickets affected
			if *handoverTicket.Status == enum.HandoverStatus.DRAFT ||
				*handoverTicket.Status == enum.HandoverStatus.TRANSPORTING ||
				*handoverTicket.Status == enum.HandoverStatus.WAIT_TO_CHECK ||
				*handoverTicket.Status == enum.HandoverStatus.CHECKING {
				handoverAffected = append(handoverAffected, handoverTicket.Code)
				isAffected = true
			} else {
				handoverUnaffected = append(handoverUnaffected, handoverTicket.Code)
			}
		}

		DataResponse = append(DataResponse, &RelateTrip{
			RouteName:           routeName,
			HandoversUnaffected: handoverUnaffected,
			HandoversAffected:   handoverAffected})
	}

	var message string
	if isAffected {
		message = "Có phiếu bàn giao đang hoạt động, không thể Deactive hub"
	} else {
		message = "Deactive hub không ảnh hưởng đến các phiếu bàn giao"
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Data:    DataResponse,
		Message: message,
	}
}

func RemoveHubOutOfRoutes(hubCode string) *common.APIResponse {
	filter := bson.M{}
	filter["drop_off_points.code"] = hubCode
	filter["status"] = enum.RouteStatus.ACTIVE
	routesRaw := model.RouteDB.Query(filter, 0, 1000, nil)
	if routesRaw.Status == common.APIStatus.Ok {
		type Route struct {
			Name string `json:"name,omitempty"`
			Code string `json:"code,omitempty"`
		}

		// If only have 1 or 2 drop off points then return route code/name
		routes := routesRaw.Data.([]*model.Route)
		var DataResponse []*Route

		routeCodes := []string{}

		for _, route := range routes {
			routeCodes = append(routeCodes, route.RouteCode)
			if route.TotalDropOffPoint == 2 {
				DataResponse = append(DataResponse, &Route{Name: route.RouteName, Code: route.RouteCode})
			}
		}

		if len(DataResponse) != 0 {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Data:    DataResponse,
				Message: "Không thể deactive hay xóa hub thuộc các tuyến chỉ có 2 điểm thả hàng",
			}
		}

		for _, route := range routes {
			updateRoute := route
			index := 0
			newDropOffPoints := []model.DropOffPoint{}
			for i, hub := range route.DropOffPoints {
				if hub.Code == hubCode {
					if i == len(route.DropOffPoints)-1 {
						*route.DropOffPoints[i-1].Type = enum.DropOffPointType.END
					}
					continue
				} else {
					index += 1
					hub.Index = int64(index)
					if index == 1 {
						*hub.Type = enum.DropOffPointType.START
					}
					if i == len(route.DropOffPoints)-1 {
						*hub.Type = enum.DropOffPointType.END
					}
					newDropOffPoints = append(newDropOffPoints, hub)
				}
			}
			updateRoute.DropOffPoints = newDropOffPoints

			result := UpdateRoute(updateRoute)

			if result.Status != common.APIStatus.Ok {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: result.Message,
				}
			}
		}
	}

	return &common.APIResponse{
		Status: common.APIStatus.Ok,
	}
}

func GetReportProductivity(hubCode string, driverId int64, viewMode *enum.ViewModeValue) *common.APIResponse {
	filter := bson.M{}
	filter["hub_code"] = hubCode
	filter["driver_id"] = driverId

	now := time.Now()

	y, m, d := now.Date()

	var start, end time.Time

	if *viewMode == enum.ViewMode.DOM {
		start = time.Date(y, m, d-int(now.Weekday())+1, 0, 0, 0, 0, now.Location())
		end = start.AddDate(0, 0, 7)
	} else if *viewMode == enum.ViewMode.MOY {
		start = time.Date(y, time.January, 1, 0, 0, 0, 0, now.Location())
		end = time.Date(y, time.December, 31, 0, 0, 0, 0, now.Location())
	} else {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Loại thống kê không hợp lệ",
		}
	}

	fromMonth := start.Month()

	filter["year"] = y
	filter["month"] = bson.M{
		"$gte": int(fromMonth),
	}

	dataRaw := model.CarrierProductivityDB.Query(filter, 0, 100, nil)
	if dataRaw.Status != common.APIStatus.Ok {
		return dataRaw
	}

	type Column struct {
		ViewData        int     `json:"viewData"`
		SuccessDelivery int64   `json:"successDelivery"`
		TotalDelivery   int64   `json:"totalDelivery"`
		RateDelivery    float64 `json:"rateDelivery"`
		SuccessPickup   int64   `json:"successPickup"`
		TotalPickup     int64   `json:"totalPickup"`
		RatePickup      float64 `json:"ratePickup"`
		RateOverall     float64 `json:"rateOverall"`
	}

	reports := dataRaw.Data.([]*model.CarrierProductivity)
	response := []Column{}

	monthData := make(map[int]*model.CarrierProductivity)
	for _, report := range reports {
		monthData[report.Month] = report
	}

	if *viewMode == enum.ViewMode.DOM {
		current := start
		for current.Before(end) {
			_, month, day := current.Date()
			var overallDeli, successDeli, overallPick, successPick int64 = 0, 0, 0, 0

			column := Column{
				ViewData: day,
			}

			for _, orderType := range monthData[int(month)].Reports[day-1] {
				for _, orderSubType := range orderType {
					successDeli += orderSubType.SuccessDelivery
					overallDeli += orderSubType.TotalDelivery
					successPick += orderSubType.SuccessPickup
					overallPick += orderSubType.TotalPickup
				}
			}

			if overallDeli+overallPick != 0 {
				if overallDeli != 0 {
					column.SuccessDelivery = successDeli
					column.TotalDelivery = overallDeli
					column.RateDelivery = float64(successDeli) / float64(overallDeli) * 100
				}

				if overallPick != 0 {
					column.SuccessPickup = successPick
					column.TotalPickup = overallPick
					column.RatePickup = float64(successPick) / float64(overallPick) * 100
				}

				column.RateOverall = float64(successDeli+successPick) / float64(overallDeli+overallPick) * 100
			}

			response = append(response, column)
			current = current.Add(24 * time.Hour)
		}

	} else if *viewMode == enum.ViewMode.MOY {
		for month := 1; month <= 12; month++ {
			column := Column{
				ViewData: month,
			}

			if _, ok := monthData[month]; !ok {
				response = append(response, column)
				continue
			}

			var overallDeli, successDeli, overallPick, successPick int64 = 0, 0, 0, 0
			report := monthData[month]

			for _, day := range report.Reports {
				for _, orderType := range day {
					for _, orderSubType := range orderType {
						successDeli += orderSubType.SuccessDelivery
						overallDeli += orderSubType.TotalDelivery
						successPick += orderSubType.SuccessPickup
						overallPick += orderSubType.TotalPickup
					}
				}
			}

			if overallDeli+overallPick != 0 {
				if overallDeli != 0 {
					column.SuccessDelivery = successDeli
					column.TotalDelivery = overallDeli
					column.RateDelivery = float64(successDeli) / float64(overallDeli) * 100
				}

				if overallPick != 0 {
					column.SuccessPickup = successPick
					column.TotalPickup = overallPick
					column.RatePickup = float64(successPick) / float64(overallPick) * 100
				}

				column.RateOverall = float64(successDeli+successPick) / float64(overallDeli+overallPick) * 100
			}

			response = append(response, column)
		}
	}

	//var pipeline []bson.M
	//var match = bson.M{}
	//if *viewMode == enum.ViewMode.DOM {
	//
	//}
	//
	//if *viewMode == enum.ViewMode.MOY {
	//
	//}

	return &common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   response,
	}
}

func ContainsInt(s []int, v int) bool {
	for _, i := range s {
		if i == v {
			return true
		}
	}
	return false
}

func FindPickUpCarrierOfCustomer(numPackage int64, weight float64, customerId int64) (hub *model.Hub, carrierId int64) {
	filter := bson.M{
		"customer_id": customerId,
		"is_active":   true,
	}

	getCustomerCarrierResp := model.CustomerCarriersDB.QueryOne(filter)
	if getCustomerCarrierResp.Status != common.APIStatus.Ok {
		return
	}

	customerCarrier := getCustomerCarrierResp.Data.([]*model.CustomerCarrier)[0]
	if customerCarrier.PickUpCarriers == nil {
		return
	}

	// only allow config 1 carrier pick up
	pickUpCarrier := customerCarrier.PickUpCarriers[0]

	carrierIds := CheckCondition([]*model.Available{&pickUpCarrier.Available}, numPackage, weight)
	if len(carrierIds) == 0 {
		return
	}

	// only allow config 1 carrier pick up
	carrierId = carrierIds[0]

	// find hub in config
	if pickUpCarrier.HubCode != "" {
		getHubResp := model.HubDB.QueryOne(bson.M{
			"code": pickUpCarrier.HubCode,
		})
		if getHubResp.Status == common.APIStatus.Ok {
			hub = getHubResp.Data.([]*model.Hub)[0]
			return
		}
	}

	// find hub have default carrier
	getHubResp := model.HubDB.QueryOne(bson.M{
		"default_carrier_id": carrierId,
	})
	if getHubResp.Status == common.APIStatus.Ok {
		hub = getHubResp.Data.([]*model.Hub)[0]
		return
	}

	// find lastest hub added carrier
	getListHubResp := model.HubDB.Query(bson.M{
		"list_carrier_id": carrierId,
	}, 0, 1, &primitive.M{"last_updated_time": -1})
	if getListHubResp.Status == common.APIStatus.Ok {
		hub = getListHubResp.Data.([]*model.Hub)[0]
		return
	}

	return
}
