package request

import (
	"time"

	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
)

type DoneTransportForPO struct {
	TransportCode  string           `json:"transportCode"`
	FromHubCode    string           `json:"fromHubCode"`
	FromWHCode     string           `json:"fromWarehouseCode"`
	ToHubCode      string           `json:"toHubCode"`
	ToWHCode       string           `json:"toWarehouseCode"`
	DoneType       string           `json:"type"`
	Employee       string           `json:"employee"`
	EmployeeId     int64            `json:"employeeId"`
	ReceivedTime   *time.Time       `json:"receivedTime"`
	CreatedBy      int64            `json:"createdBy"`
	TransportItems []*TransportItem `json:"transportItems"`
}

type DoneTransportForReceiveSession struct {
	PoCode                  string `json:"poCode"`
	ReceivedPackageQuantity int64  `json:"receivedPackageQuantity"`
	WarehouseCode           string `json:"warehouseCode"`
}

type DoneTransportForReturn struct {
	CheckInById    int64            `json:"checkInById"`
	CheckInByName  string           `json:"checkInByName"`
	TransportItems []*TransportItem `json:"items"`
	WarehouseCode  string           `json:"warehouseCode"`
}

type DoneTransportForTO struct {
	TransportCode   string           `json:"transportCode"`
	ToWarehouseCode string           `json:"toWarehouseCode"`
	EmployeeId      int64            `json:"employeeId"`
	Employee        string           `json:"employee"`
	ReceivedTime    *time.Time       `json:"receivedTime"`
	TransportItems  []*TransportItem `json:"transportItems"`
}

type TransportItem struct {
	SessionCode       string `json:"receiptSessionCode"`
	PackageQuantity   int64  `json:"packageQuantity"`
	CheckInCode       string `json:"checkInCode"`
	SO                string `json:"so"`
	DeliveryOrderCode string `json:"deliveryOrderCode"`
	PackageNum        int64  `json:"packageNum"`
	POCOde            string `json:"poCode"`
}

type ROItem struct {
	OrderCode     string `json:"orderCode"`
	WarehouseCode string `json:"warehouseCode"`
}

type Employee struct {
	AccountId int64 `json:"accountId"`
}

type DoneTransportForRO struct {
	Items    []*ROItem      `json:"items"`
	Employee *model.Account `json:"employee"`
}
