package action

import (
	"fmt"
	"hash/fnv"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/utils"
	"go.mongodb.org/mongo-driver/bson"
)

// GetShippingAddress func
func GetShippingAddress(req *model.ShippingAddress, offset, limit int64) *common.APIResponse {
	if limit == 0 || limit > 100 {
		limit = 100
	}
	return model.ShippingAddressDB.Query(req, offset, limit, nil)
}

// UpdateShippingAddress func
func UpdateShippingAddress(req *model.ShippingAddress) *common.APIResponse {

	if req.ID < 1 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã vị trí đang để trống",
		}
	}

	queryResult := model.ShippingAddressDB.QueryOne(&bson.M{
		"_id": req.ID,
	})
	if queryResult.Status != common.APIStatus.Ok {
		// if update return NOT_FOUND
		req.ID = model.GenId("SHIPPING_ADDRESS_ID")
		wards, err := client.Services.WarehouseCoreClient.GetWard("", req.WardCode, "", "")
		if err != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: err.Error(),
			}
		}

		ward := wards[0]
		req.WardName = ward.Name
		req.DistrictCode = ward.DistrictCode
		req.DistrictName = ward.DistrictName
		req.ProvinceCode = ward.ProvinceCode
		req.ProvinceName = ward.ProvinceName
		if req.NearestOrder != "" {
			shippingOrderResult := model.ShippingOrderDB.QueryOne(bson.M{"reference_code": req.NearestOrder})
			if shippingOrderResult.Status == common.APIStatus.Ok {
				shippingOrders := shippingOrderResult.Data.([]*model.ShippingOrder)
				if shippingOrders != nil {
					shippingOrder := shippingOrders[0]

					if shippingOrder.ShippingType == nil || *shippingOrder.ShippingType == enum.ShippingOrderType.DELIVERY {
						req.CustomerAddress = shippingOrder.CustomerShippingAddress
						req.CustomerCode = shippingOrder.CustomerCode
						req.CustomerPhone = shippingOrder.CustomerPhone
						req.CustomerEmail = shippingOrder.CustomerEmail
						req.CustomerName = shippingOrder.CustomerName
						req.Verified = &enum.True
					} else {
						req.CustomerAddress = shippingOrder.FromCustomerAddress
						req.CustomerCode = shippingOrder.FromCustomerCode
						req.CustomerPhone = shippingOrder.FromCustomerPhone
						req.CustomerEmail = shippingOrder.FromCustomerEmail
						req.CustomerName = shippingOrder.FromCustomerName
						req.Verified = &enum.True
					}
				}
			}
		}
	}

	return model.ShippingAddressDB.Upsert(&bson.M{
		"_id": req.ID,
	}, req)
}

func BuildAddress(address *model.Address) *common.Error {
	if (address.ProvinceCode == "" && address.ProvinceName == "") ||
		(address.DistrictCode == "" && address.DistrictName == "") ||
		(address.WardCode == "" && address.WardName == "") {
		return &common.Error{
			Message: "Địa chỉ không hợp lệ",
		}
	}
	// get province code
	if address.ProvinceCode == "" {
		provinceRaw := model.ProvinceMappingDB.QueryOne(
			bson.M{
				"dictionary": address.ProvinceName,
			},
		)

		if provinceRaw.Status != common.APIStatus.Ok {
			return &common.Error{
				Message: "Tên tỉnh, thành phố không hợp lệ",
			}
		}

		address.ProvinceCode = provinceRaw.Data.([]*model.ProvinceMapping)[0].Code
	}
	// get province name
	if address.ProvinceName == "" {
		province, err := client.Services.WarehouseCoreClient.GetProvinceByCode(address.ProvinceCode)
		if err != nil {
			return &common.Error{
				Message: "Mã tỉnh, thành phố không hợp lệ",
			}
		}
		address.ProvinceName = province[0].Name
	}
	// get district code
	if address.DistrictCode == "" {
		districtRaw := model.DistrictMappingDB.QueryOne(bson.M{
			"dictionary":    address.DistrictName,
			"province_code": address.ProvinceCode,
		})

		if districtRaw.Status != common.APIStatus.Ok {
			return &common.Error{
				Message: "Tên quận huyện không hợp lệ",
			}
		}
		address.DistrictCode = districtRaw.Data.([]*model.DistrictMapping)[0].Code
	}
	// get district name
	if address.DistrictName == "" {
		district, err := client.Services.WarehouseCoreClient.GetDistrictByCode(address.ProvinceCode, address.DistrictCode)
		if err != nil {
			return &common.Error{
				Message: "Mã quận huyện không hợp lệ",
			}
		}
		address.DistrictName = district[0].Name
	}
	// get ward code
	if address.WardCode == "" {
		wardRaw := model.WardMappingDB.QueryOne(bson.M{
			"dictionary":    address.WardName,
			"district_code": address.DistrictCode,
		})
		if wardRaw.Status != common.APIStatus.Ok {
			return &common.Error{
				Message: "Tên phường xã không hợp lệ",
			}
		}
		address.WardCode = wardRaw.Data.([]*model.WardMapping)[0].Code
	}
	// get ward name
	if address.WardName == "" {
		ward, err := client.Services.WarehouseCoreClient.GetWard("", address.WardCode, address.DistrictCode, address.ProvinceCode)
		if err != nil {
			return &common.Error{
				Message: "Mã phường xã không hợp lệ",
			}
		}
		address.WardName = ward[0].Name
	}

	getLocation := model.ShippingAddressDB.Query(&bson.M{
		"ward_code":     address.WardCode,
		"customer_code": address.Code,
	}, 0, 10, nil)

	if getLocation.Status != common.APIStatus.Ok {
		fullAddress := fmt.Sprintf("%s, %s, %s, %s", address.Address, address.WardName, address.DistrictName, address.ProvinceName)
		address.Latitude, address.Longitude, _ = client.Services.GeocodeClient.GetLatLng(fullAddress)
		return nil
	}

	savedLocation := getLocation.Data.([]*model.ShippingAddress)
	for _, loc := range savedLocation {
		if utils.ConvertToRawText(loc.CustomerAddress) == utils.ConvertToRawText(address.Address) {
			address.Latitude = loc.Latitude
			address.Longitude = loc.Longitude
			return nil
		}
	}

	fullAddress := fmt.Sprintf("%s, %s, %s, %s", address.Address, address.WardName, address.DistrictName, address.ProvinceName)
	address.Latitude, address.Longitude, _ = client.Services.GeocodeClient.GetLatLng(fullAddress)

	return nil
}

func MigrateShippingAddress() *common.APIResponse {
	var limit int64 = 100
	filter := bson.M{}

	for {
		shippingAddressRaw := model.ShippingAddressDB.Query(filter, 0, limit, &bson.M{"_id": -1})
		if shippingAddressRaw.Status != common.APIStatus.Ok {
			break
		}
		shippingAddresses := shippingAddressRaw.Data.([]*model.ShippingAddress)

		smallestId := shippingAddresses[len(shippingAddresses)-1].ID
		filter["_id"] = bson.M{
			"$lt": smallestId,
		}

		for _, shippingAddress := range shippingAddresses {
			if shippingAddress.Latitude == 0 || shippingAddress.Longitude == 0 {
				continue
			}

			fullAddress := fmt.Sprintf("%s, %s, %s, %s", shippingAddress.CustomerAddress, shippingAddress.WardName, shippingAddress.DistrictName, shippingAddress.ProvinceName)
			// Calculate hash
			h := fnv.New64a()
			h.Write([]byte(fullAddress))
			addressId := int64(h.Sum64())

			_ = model.LatLongCacheDB.Create(model.LatLongCache{
				AddressId:   addressId,
				FullAddress: fullAddress,
				Latitude:    shippingAddress.Latitude,
				Longitude:   shippingAddress.Longitude,
			})
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}
}
