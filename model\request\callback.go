package request

import (
	"time"

	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
)

type Callback struct {
	CreatedSource           *enum.PartnerValue           `json:"createdSource,omitempty" bson:"created_source,omitempty" `
	SO                      string                       `json:"so,omitempty" bson:"so,omitempty"`
	HubCode                 string                       `json:"hubCode" bson:"hub_code,omitempty"`
	TPLCode                 string                       `json:"tplCode,omitempty" bson:"tpl_code,omitempty"`
	Status                  *enum.TPLCallbackStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	TPLStatus               string                       `json:"tplStatus,omitempty" bson:"tpl_status,omitempty"`
	StatusName              string                       `json:"statusName,omitempty" bson:"status_name,omitempty"`
	TPLStatusName           string                       `json:"tplStatusName,omitempty" bson:"tpl_status_name,omitempty"`
	Weight                  float64                      `json:"weight,omitempty" bson:"weight,omitempty"`
	Reason                  string                       `json:"reason,omitempty" bson:"reason,omitempty"`
	Action                  string                       `json:"action" bson:"action,omitempty"`
	ActionName              string                       `json:"actionName" bson:"action_name,omitempty"`
	ReasonCode              string                       `json:"reasonCode,omitempty" bson:"reason_code,omitempty"`
	TotalFee                float64                      `json:"totalFee,omitempty" bson:"total_fee,omitempty"`
	Height                  int                          `json:"height,omitempty" bson:"height,omitempty"`
	Width                   int                          `json:"width,omitempty" bson:"width,omitempty"`
	Length                  int                          `json:"length,omitempty" bson:"length,omitempty"`
	COD                     float64                      `json:"cod,omitempty" bson:"cod,omitempty"`
	ActionTime              *time.Time                   `json:"actionTime,omitempty" bson:"action_time,omitempty"`
	ExternalTPLName         string                       `json:"externalTPLName" bson:"external_tpl_name,omitempty"`
	ExternalTPLId           int64                        `json:"externalTplId" bson:"external_tpl_id,omitempty"`
	NumPackage              int64                        `json:"numPackage"  bson:"num_package,omitempty"`
	DeliverID               int64                        `json:"deliverId" bson:"deliver_id,omitempty"`
	DeliverName             string                       `json:"deliverName" bson:"deliver_name,omitempty"`
	ExtraCallback           map[string]interface{}       `json:"extraCallback,omitempty" bson:"extra_callback,omitempty"`
	WarehouseCode           string                       `json:"warehouseCode" bson:"warehouse_code,omitempty"`
	FromName                string                       `json:"fromName,omitempty" bson:"from_name,omitempty"`
	FromAddress             string                       `json:"fromAddress,omitempty" bson:"from_address,omitempty"`
	ToName                  string                       `json:"toName,omitempty" bson:"to_name,omitempty"`
	ToAddress               string                       `json:"toAddress,omitempty" bson:"to_address,omitempty"`
	ReferenceCode           string                       `json:"referenceCode,omitempty" bson:"reference_code,omitempty"`
	PartnerCode             string                       `json:"partnerCode,omitempty" bson:"partner_code,omitempty"`
	DriverId                int                          `json:"driverId,omitempty" bson:"driver_id,omitempty"`
	DriverName              string                       `json:"driverName,omitempty" bson:"driver_name,omitempty"`
	TrackingCode            string                       `json:"trackingCode,omitempty" bson:"tracking_code,omitempty"`
	Type                    *enum.ShippingOrderTypeValue `json:"type,omitempty" bson:"type,omitempty"`
	RescheduledDeliveryTime int64                        `json:"rescheduledDeliveryTime,omitempty" bson:"rescheduled_delivery_time,omitempty"`
	RescheduledPickupTime   int64                        `json:"rescheduledPickupTime,omitempty" bson:"rescheduled_pickup_time,omitempty"`
	CallbackUrl             string                       `json:"callbackUrl,omitempty" bson:"callback_url,omitempty"`
	PoCode                  string                       `json:"poCode,omitempty" bson:"po_code,omitempty"`
	Images                  []string                     `json:"images,omitempty" bson:"images,omitempty"`
	ParentReferenceCode     string                       `json:"parentReferenceCode,omitempty" bson:"parent_reference_code,omitempty"`
	JsonStrData             string                       `json:"data,omitempty" bson:"data,omitempty"`
	HashedData              string                       `json:"hashedData,omitempty" bson:"hashed_data,omitempty"`
}

type AssignCallback struct {
	ListReferenceCode []string `json:"ListReferenceCode" bson:"list_reference_code"`
	AccountId         int      `json:"accountId" bson:"account_id"`
	AccountName       string   `json:"accountName" bson:"account_name"`
}
