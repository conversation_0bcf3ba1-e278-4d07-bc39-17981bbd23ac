package action

import (
	"encoding/json"
	"fmt"
	"strings"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"

	"go.mongodb.org/mongo-driver/bson"
)

func GetConfigLogistic(key string) *common.APIResponse {
	if key == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Key is required",
		}
	}

	return model.ConfigLogisticDB.QueryOne(bson.M{
		"key": key,
	})
}

func UpsertConfigLogistic(ConfigLogistic *model.ConfigLogistic) *common.APIResponse {
	updateResp := &common.APIResponse{
		Status: common.APIStatus.Invalid,
	}

	if len(ConfigLogistic.Tooltip) > 0 && len(ConfigLogistic.Configs) == 0 {
		updateResp = model.ConfigLogisticDB.UpdateOneWithOption(
			bson.M{
				"key": ConfigLogistic.Key,
			},
			bson.M{
				"$set": bson.M{
					"tooltip": ConfigLogistic.Tooltip,
				},
			},
		)
	}

	if len(ConfigLogistic.Tooltip) == 0 && len(ConfigLogistic.Configs) > 0 {
		updateResp = model.ConfigLogisticDB.UpdateOneWithOption(
			bson.M{
				"key": ConfigLogistic.Key,
			},
			bson.M{
				"$set": bson.M{
					"configs": ConfigLogistic.Configs,
				},
			},
		)
	}

	if updateResp.Status == common.APIStatus.Ok {
		return updateResp
	}

	return model.ConfigLogisticDB.Upsert(
		bson.M{
			"key": ConfigLogistic.Key,
		},
		ConfigLogistic,
	)
}

func MigrateDefaultHubConfig(configLogistic *model.ConfigLogistic) *common.APIResponse {
	hubRaw := model.HubDB.Query(bson.M{
		"active": true,
	}, 0, 200, nil)

	var err []*common.Error
	hubs := hubRaw.Data.([]*model.Hub)
	for _, hub := range hubs {
		updateResult := model.ConfigLogisticDB.Upsert(bson.M{
			"key": hub.Code,
		}, bson.M{
			"configs": configLogistic.Configs,
		})
		if updateResult.Status != common.APIStatus.Ok {
			err = append(err, &common.Error{
				Message: updateResult.Message,
			})
		}
	}

	if len(err) > 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Đã có lỗi xảy ra khi cập nhật config",
			Data:    err,
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Successfully genrated",
	}
}

func GetAllowedExternalCarrierHubConfig() (map[string][]string, error) {
	config := model.ConfigLogisticDB.QueryOne(bson.M{"key": "LOGISTIC"})
	if config.Status != common.APIStatus.Ok {
		return nil, fmt.Errorf("model.ConfigLogisticDB.QueryOne: %v", config.Status)
	}
	allowedCarriers, ok := config.Data.([]*model.ConfigLogistic)[0].Configs["ALLOW_EXTERNAL_CARRIER_HUB"]
	if !ok {
		return nil, fmt.Errorf("get key ALLOW_EXTERNAL_CARRIER_HUB")
	}
	carriersRaw, ok := allowedCarriers.(bson.A)
	allowedCarriersMap := make(map[string][]string)
	if !ok {
		carriersRaw, ok := allowedCarriers.(string)
		if ok {
			err := json.Unmarshal([]byte(carriersRaw), &allowedCarriersMap)
			if err != nil {
				return nil, err
			}
			return allowedCarriersMap, err
		}
		return nil, fmt.Errorf("can't convert ALLOW_EXTERNAL_CARRIER_HUB setting")
	}
	var builder strings.Builder
	for i, v := range carriersRaw {
		if str, ok := v.(string); ok {
			builder.WriteString(str)
			if i == len(carriersRaw)-1 {
				continue
			}
			builder.WriteString(",")
		}
	}
	err := json.Unmarshal([]byte(builder.String()), &allowedCarriersMap)
	if err != nil {
		return nil, fmt.Errorf("json.Unmarshal %w", err)
	}
	return allowedCarriersMap, nil
}

func GetWarehouseHubConfig() ([]model.WarehouseHubConfig, error) {
	config := model.ConfigLogisticDB.QueryOne(bson.M{"key": "LOGISTIC"})
	if config.Status != common.APIStatus.Ok {
		return nil, &common.Error{
			Type:    "CONFIG_ERROR",
			Message: "Thiếu thông tin config logistic",
		}
	}
	warehouseHubConfigRaw, ok := config.Data.([]*model.ConfigLogistic)[0].Configs["WAREHOUSE_HUB_MAP"]
	if !ok {
		return nil, &common.Error{
			Type:    "CONFIG_ERROR",
			Message: "Thiếu thông tin config mapping hub và kho",
		}
	}

	warehouseHubConfigArr, ok := warehouseHubConfigRaw.(bson.A)
	if !ok {
		return nil, &common.Error{
			Type:    "CONFIG_ERROR",
			Message: "Định dạng config mapping hub và kho không hợp lệ",
		}
	}

	warehouseHubConfig := []model.WarehouseHubConfig{}
	var builder strings.Builder
	for i, v := range warehouseHubConfigArr {
		if str, ok := v.(string); ok {
			builder.WriteString(str)
			if i == len(warehouseHubConfigArr)-1 {
				continue
			}
			builder.WriteString(",")
		}
	}
	err := json.Unmarshal([]byte(builder.String()), &warehouseHubConfig)
	if err != nil {
		return nil, &common.Error{
			Type:    "CONFIG_ERROR",
			Message: "Định dạng config mapping hub và kho không hợp lệ",
		}
	}

	return warehouseHubConfig, nil
}

func GetDelayCustomerTagConfig() ([]string, error) {
	config := model.ConfigLogisticDB.QueryOne(bson.M{"key": "LOGISTIC"})
	if config.Status != common.APIStatus.Ok {
		return nil, fmt.Errorf("model.ConfigLogisticDB.QueryOne: %v", config.Status)
	}
	delayCustomerTagData, ok := config.Data.([]*model.ConfigLogistic)[0].Configs["DELAY_TIME_BY_TAG"]
	if !ok {
		return nil, fmt.Errorf("get key DELAY_TIME_BY_TAG")
	}
	delayCustomerTagRaw, ok := delayCustomerTagData.(bson.A)
	delayCustomerTags := []string{}
	if !ok {
		carriersRaw, ok := delayCustomerTagData.(string)
		if ok {
			delayCustomerTags = append(delayCustomerTags, carriersRaw)
			return delayCustomerTags, nil
		}
		return nil, fmt.Errorf("can't convert DELAY_TIME_BY_TAG setting")
	}
	for _, v := range delayCustomerTagRaw {
		if str, ok := v.(string); ok {
			delayCustomerTags = append(delayCustomerTags, str)
		}
	}
	return delayCustomerTags, nil
}
