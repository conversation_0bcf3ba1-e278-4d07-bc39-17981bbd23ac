package action

import (
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/shipping"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"go.mongodb.org/mongo-driver/bson"
)

// [ Address1, Address2, Address3 ]
// -> [ Address 2, Address 3, Address 1 ]

func SolveVRP(
	depots []*model.Address,
	points []*model.Address,
	constrain model.VRPConstrain,
	algorithm string) ([]*model.Trip, *common.Error) {

	if algorithm == "" {
		return nil, &common.Error{
			Message: "Loại lời gi<PERSON>i không được để trống",
		}
	}

	if len(depots) == 0 || len(points) == 0 {
		return nil, &common.Error{
			Message: "<PERSON>i<PERSON><PERSON> giao hàng không được để trống",
		}
	}

	fillAddress := append(depots, points...)
	fillAddressErr := FillLatLongOfAddress(fillAddress)
	if fillAddressErr != nil {
		return nil, &common.Error{
			Message: "Địa chỉ không hợp lệ.",
		}
	}

	if algorithm == "SDVRP" {
		return SolveSDVRP(depots, points, constrain)
	}

	//TODO: Add TWVRP algorithm if we want to ship within a time window

	return nil, &common.Error{
		Message: "Thuật toán chưa được hỗ trợ",
	}
}

func FillLatLongOfAddress(addresses []*model.Address) *common.Error {
	if len(addresses) == 0 {
		return &common.Error{
			Message: "Danh sách địa chỉ không được để trống",
		}
	}

	addressesLength := len(addresses)
	//Note: Google api have rate limit request per second
	numberOfWorker := 2
	jobs := make(chan *model.Address, addressesLength)
	results := make(chan *common.Error, addressesLength)

	for i := 0; i < numberOfWorker; i++ {
		go func(jobs <-chan *model.Address, results chan<- *common.Error) {
			for job := range jobs {
				// If address already have lat long, we dont need to fill it
				if job.Latitude != 0 && job.Longitude != 0 {
					results <- nil
				} else {
					var err error
					fullAddress := fmt.Sprintf("%s, %s, %s, %s", job.Address, job.WardName, job.DistrictName, job.ProvinceName)
					job.Latitude, job.Longitude, err = client.Services.GeocodeClient.GetLatLng(fullAddress)
					if err != nil {
						results <- &common.Error{
							Message: err.Error(),
						}
					} else {
						results <- nil
					}
				}
			}
		}(jobs, results)
	}

	for _, address := range addresses {
		jobs <- address
	}

	close(jobs)
	var err []*common.Error
	for i := 0; i < addressesLength; i++ {
		result := <-results
		if result != nil {
			err = append(err, result)
		}
	}

	close(results)
	if len(err) > 0 {
		return err[0]
	}

	return nil
}

func FillLatLongOfHubOrder(hubOrders []*model.HubShippingOrder) []*common.Error {
	if len(hubOrders) == 0 {
		return []*common.Error{
			{Message: "Danh sách đơn không được để trống"},
		}
	}

	hubOrderLength := len(hubOrders)
	//Note: Google api have rate limit request per second
	numberOfWorker := 2
	jobs := make(chan *model.HubShippingOrder, hubOrderLength)
	results := make(chan *common.Error, hubOrderLength)

	for i := 0; i < numberOfWorker; i++ {
		go func(jobs <-chan *model.HubShippingOrder, results chan<- *common.Error) {
			for job := range jobs {
				// If order already have lat long we dont have to call geocode api
				if job.Type != nil &&
					(*job.Type == enum.HubOrderType.PICKUP || *job.Type == enum.HubOrderType.RETURN) &&
					job.FromLatitude != 0 &&
					job.FromLongitude != 0 {
					results <- nil
				} else if job.Type != nil &&
					(*job.Type == enum.HubOrderType.DELIVERY || *job.Type == enum.HubOrderType.TRANSPORTING) &&
					job.ToLongitude != 0 &&
					job.ToLatitude != 0 {
					results <- nil
				} else {
					var fullAddress string
					var err error
					if job.Type != nil &&
						(*job.Type == enum.HubOrderType.PICKUP || *job.Type == enum.HubOrderType.RETURN) {
						fullAddress = fmt.Sprintf("%s, %s, %s, %s", job.FromCustomerAddress, job.FromWardName, job.FromDistrictName, job.FromProvinceName)
						job.FromLatitude, job.FromLongitude, err = client.Services.GeocodeClient.GetLatLng(fullAddress)
						if err != nil {
							results <- &common.Error{
								Message: err.Error(),
							}
						} else {
							results <- nil
						}
					} else {
						fullAddress = fmt.Sprintf("%s, %s, %s, %s", job.ToCustomerAddress, job.ToWardName, job.ToDistrictName, job.ToProvinceName)
						job.ToLatitude, job.ToLongitude, err = client.Services.GeocodeClient.GetLatLng(fullAddress)
						if err != nil {
							results <- &common.Error{
								Message: err.Error(),
							}
						} else {
							results <- nil
						}
					}

				}
			}
		}(jobs, results)
	}

	for _, hubOrder := range hubOrders {
		jobs <- hubOrder
	}

	close(jobs)
	var err []*common.Error
	for i := 0; i < hubOrderLength; i++ {
		result := <-results
		if result != nil {
			err = append(err, result)
		}
	}

	close(results)
	if len(err) > 0 {
		return err
	}

	return nil
}

func SolveSDVRP(
	depots []*model.Address,
	points []*model.Address,
	constrain model.VRPConstrain) (
	[]*model.Trip, *common.Error) {
	// Check cache

	// Send request
	ahamoveDepots := [][]float64{{depots[0].Latitude, depots[0].Longitude}}
	var ahamovePoints [][]float64
	for i, point := range points {
		var indexType float64
		point.Index = i
		switch point.Type {
		case "DELIVERY", "TRANSPORTING", "PICKUP":
			indexType = 1
			break

			// Currently ahamove only support delivery type but pick up without capacity is the same as delivery
		//case "PICKUP":
		//	indexType = 0
		//	break

		default:
			indexType = 1
			break
		}

		ahamovePoints = append(ahamovePoints, []float64{point.Latitude, point.Longitude, indexType})
	}

	ROReq := request.AhamoveRouteOptimizationRequest{
		Depots:           ahamoveDepots,
		Points:           ahamovePoints,
		VehicleNumber:    constrain.VehicleNumber,
		MaxDistance:      constrain.MaxDistance,
		MinParcels:       constrain.MinParcels,
		MaxParcels:       constrain.MaxParcels,
		PointMaxDistance: constrain.PointMaxDistance,
		MinVehicles:      constrain.MinVehicles,
		Dimensions:       constrain.Dimensions,
		TransportMode:    "1N",    // Default: 1N for all. [1N, N1, 1N1]
		ResultMode:       "INDEX", // Default:
	}

	routeOptimizationResp := shipping.TplShippingClient.AhamoveOnWheelClient.GetRoutePlanning(ROReq)
	if routeOptimizationResp == nil {
		return nil, &common.Error{
			Message: "Sắp xếp vị trí thất bại",
		}
	}

	// Cluster to trip
	var trips []*model.Trip
	for i, cluster := range routeOptimizationResp.Cluster {
		trip := &model.Trip{}

		// Skip first element of cluster
		for _, index := range cluster[1:] {
			trip.Addresses = append(trip.Addresses, points[index])
		}

		// Skip last element, calculate cost
		for j := 0; j < len(routeOptimizationResp.Metrics[i])-1; j++ {
			trip.Addresses[j].Distance = routeOptimizationResp.Metrics[i][j]
			trip.TotalDistance += routeOptimizationResp.Metrics[i][j]
		}

		trip.IsOptimized = &enum.True
		trips = append(trips, trip)
	}

	return trips, nil
}

func PrepareTripForHubOrder(
	hubOrders []*model.HubShippingOrder, // 1, 2, 3
	hubCode string,
	driverId int,
) ([]*model.HubShippingOrder, *common.Error) {
	hubRaw := model.HubDB.QueryOne(bson.M{
		"code": hubCode,
	})

	if hubRaw.Status != common.APIStatus.Ok {
		return nil, &common.Error{
			Message: "Mã hub không tồn tại",
		}
	}

	hub := hubRaw.Data.([]*model.Hub)[0]
	depots := []*model.Address{
		hub.Address,
	}

	// TODO: If support pickup, add a switch here to change FromAddress instead of ToAddress
	var points []*model.Address
	for _, hubOrder := range hubOrders {
		points = append(points, &model.Address{
			Address:      hubOrder.ToCustomerAddress,
			ProvinceName: hubOrder.ToProvinceName,
			ProvinceCode: hubOrder.ToProvinceCode,
			DistrictName: hubOrder.ToDistrictName,
			DistrictCode: hubOrder.ToDistrictCode,
			WardName:     hubOrder.ToWardName,
			WardCode:     hubOrder.ToWardCode,
			Latitude:     hubOrder.ToLatitude,
			Longitude:    hubOrder.ToLongitude,
			Code:         hubOrder.ReferenceCode,
		})
	}

	// Default constrain will always result to one trip only, override constrain to have multiple trip
	constrain := model.VRPConstrain{
		VehicleNumber:    10,
		MaxDistance:      10000000,
		MaxParcels:       1000,
		PointMaxDistance: 30000,
		MinParcels:       0,
		MinVehicles:      true,
	}

	trips, err := SolveVRP(
		depots,
		points,
		constrain,
		"SDVRP",
	)

	// if cant route optimize for trip, create new trip with the given hub order and IsOptimized set to false
	if err != nil {
		trips = []*model.Trip{
			{
				Addresses:     points,
				IsOptimized:   &enum.False,
				TotalDistance: 0,
			},
		}
	}

	// trips obj:
	//	[
	//		[ address1, address2 ],
	//		[ address3, address4, address5 ],
	// 		[ address7, address8, address9 ],
	//	]

	for _, trip := range trips {
		trip.TripId = model.GenId("TRIP_ID")
		trip.DriverID = driverId
		trip.Status = &enum.TripStatus.DRAFT
		createTripResult := model.TripDB.Create(trip)
		if createTripResult.Status != common.APIStatus.Ok {
			return nil, &common.Error{
				Message: createTripResult.Message,
			}
		}

		// assign trip to hub order
		for _, address := range trip.Addresses {
			hubOrders[address.Index].TripId = trip.TripId
		}
	}

	updateErr := map[string]string{}

	// Update hub order trip id
	for _, hubOrder := range hubOrders {
		result := model.HUBShippingOrderDB.UpdateOne(bson.M{
			"reference_code": hubOrder.ReferenceCode,
			"hub_code":       hubOrder.HUBCode,
		}, bson.M{
			"trip_id": hubOrder.TripId,
		})

		if result.Status != common.APIStatus.Ok {
			updateErr[hubOrder.ReferenceCode] = result.Message
		}
	}

	if len(updateErr) > 0 {
		return hubOrders, &common.Error{
			Message: "Đã có lỗi xảy ra khi tạo chuyến giao hàng cho tài xế",
			Data:    updateErr,
		}
	}

	return hubOrders, nil
}

func SortHubOrderByDistance(
	hubOrders []*model.HubShippingOrder,
	depots []*model.Address) ([]*model.HubShippingOrder, *common.Error) {

	var invalidAddressHubOrder []*model.HubShippingOrder
	var validAddressHubOrder []*model.HubShippingOrder

	for _, hubOrder := range hubOrders {
		if hubOrder.Type == nil {
			invalidAddressHubOrder = append(invalidAddressHubOrder, hubOrder)
			continue
		}

		if // Pick up, return
		(*hubOrder.Type == enum.HubOrderType.PICKUP || *hubOrder.Type == enum.HubOrderType.RETURN) &&
			(hubOrder.FromLatitude == 0 || hubOrder.FromLongitude == 0) ||
			// Delivery, transport
			((*hubOrder.Type == enum.HubOrderType.DELIVERY || *hubOrder.Type == enum.HubOrderType.TRANSPORTING) &&
				(hubOrder.ToLatitude == 0 || hubOrder.ToLongitude == 0)) {
			// Invalid address will be appended at the end of sorted hub order
			invalidAddressHubOrder = append(invalidAddressHubOrder, hubOrder)
		} else {
			// Only sort hub order with valid address
			validAddressHubOrder = append(validAddressHubOrder, hubOrder)
		}
	}
	hubOrders = validAddressHubOrder

	var points []*model.Address
	for _, hubOrder := range hubOrders {
		if hubOrder.Type != nil &&
			(*hubOrder.Type == enum.HubOrderType.PICKUP || *hubOrder.Type == enum.HubOrderType.RETURN) {
			points = append(points, &model.Address{
				ProvinceName: hubOrder.FromProvinceName,
				ProvinceCode: hubOrder.FromProvinceCode,
				DistrictName: hubOrder.FromDistrictName,
				DistrictCode: hubOrder.FromDistrictCode,
				WardName:     hubOrder.FromWardName,
				WardCode:     hubOrder.FromWardCode,
				Latitude:     hubOrder.FromLatitude,
				Longitude:    hubOrder.FromLongitude,
				Code:         hubOrder.ReferenceCode,
			})
		} else {
			points = append(points, &model.Address{
				ProvinceName: hubOrder.ToProvinceName,
				ProvinceCode: hubOrder.ToProvinceCode,
				DistrictName: hubOrder.ToDistrictName,
				DistrictCode: hubOrder.ToDistrictCode,
				WardName:     hubOrder.ToWardName,
				WardCode:     hubOrder.ToWardCode,
				Latitude:     hubOrder.ToLatitude,
				Longitude:    hubOrder.ToLongitude,
				Code:         hubOrder.ReferenceCode,
			})
		}
	}

	// Default constrain will always lead to solution with one trip
	constrain := model.VRPConstrain{
		VehicleNumber:    1,
		MaxDistance:      1000000000,
		MaxParcels:       1000,
		PointMaxDistance: 300000000,
		MinParcels:       0,
		MinVehicles:      true,
	}

	trips, err := SolveVRP(
		depots,
		points,
		constrain,
		"SDVRP",
	)

	if err != nil {
		return nil, err
	}

	var sortedHubOrder []*model.HubShippingOrder
	for _, address := range trips[0].Addresses {
		sortedHubOrder = append(sortedHubOrder, hubOrders[address.Index])
	}

	// Append the invalid address
	sortedHubOrder = append(sortedHubOrder, invalidAddressHubOrder...)

	return sortedHubOrder, nil
}
