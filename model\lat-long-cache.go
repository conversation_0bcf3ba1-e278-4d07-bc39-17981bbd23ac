package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type LatLongCache struct {
	AddressId   int64   `json:"id,omitempty" bson:"_id,omitempty"`
	FullAddress string  `json:"fullAddress,omitempty" bson:"full_address,omitempty"`
	Latitude    float64 `json:"latitude,omitempty" bson:"latitude,omitempty"`
	Longitude   float64 `json:"longitude,omitempty" bson:"longitude,omitempty"`

	CreatedTime *time.Time `json:"createdTime,omitempty" bson:"created_time,omitempty"`
}

var LatLongCacheDB = &db.Instance{
	ColName:        "lat_long_cache",
	TemplateObject: &LatLongCache{},
}

func InitLatLongCacheModel(s *mongo.Database) {
	LatLongCacheDB.ApplyDatabase(s)

	var expiredAfter int32
	expiredAfter = 60 * 60 * 24 * 365 // 1 year
	LatLongCacheDB.CreateIndex(bson.D{
		{"created_time", 1},
	}, &options.IndexOptions{
		ExpireAfterSeconds: &expiredAfter,
	})
}
