package ninjavan

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathCancelNinjaVan = "/2.2/orders"
	pathBookNinjaVan   = "/4.1/orders"
	pathGetToken       = "/2.0/oauth/access_token"
)

type Client struct {
	svc     *client.RestClient
	headers map[string]string
}

func NewServiceClient(apiHost, logName string, session *mongo.Database) *Client {
	if apiHost == "" {
		return nil
	}
	newClient := &Client{
		svc: client.NewRESTClient(
			apiHost,
			logName,
			30*time.Second,
			0,
			30*time.Second,
		),
		headers: map[string]string{},
	}

	newClient.svc.SetDBLog(session)
	return newClient
}

func (cli *Client) ChangeInformation(carrierInfo *model.Carrier) {
	cli.headers = map[string]string{
		"Authorization": "Bearer " + carrierInfo.ExtraData.AccessToken,
	}
}

func (cli *Client) CreateTrackingNinjaVan(createTrackingRequest request.BookNinjaVanRequest, warehouseCode string) (tracking *model.ShippingInfo, err error) {
	type response struct {
		TrackingNumber string `json:"tracking_number"`
	}

	query := map[string]string{}

	path := "VN"

	if conf.Config.Env == "stg" {
		path = "SG"
	}

	path += pathBookNinjaVan

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, query, createTrackingRequest, path, nil)
	if err != nil {
		return
	}
	resBody := new(response)
	log.Print(string(res.Body))
	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return
	}

	tracking = &model.ShippingInfo{
		TrackingNumber: resBody.TrackingNumber,
	}

	return
}

func (cli *Client) CancelNinjaVan(trackingNumber string) (err error) {
	type response struct {
		Message string `json:"message"`
		Success bool   `json:"success"`
	}

	query := map[string]string{}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Delete, cli.headers, query, nil, pathCancelNinjaVan+"/"+trackingNumber, nil)
	if err != nil {
		return
	}
	resBody := new(response)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return
	}

	if !resBody.Success {
		err = fmt.Errorf("%v-%v", resBody, resBody.Message)
		return
	}

	return
}

func (cli *Client) GetToken() (err error) {
	type response struct {
		Message string `json:"message"`
		Success bool   `json:"success"`
	}

	query := map[string]string{}

	path := "VN"

	if conf.Config.Env == "stg" {
		path = "SG"
	}

	path += pathGetToken

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, query, nil, pathGetToken, nil)
	if err != nil {
		return
	}
	resBody := new(response)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return
	}

	if !resBody.Success {
		err = fmt.Errorf("%v-%v", resBody, resBody.Message)
		return
	}

	return
}
