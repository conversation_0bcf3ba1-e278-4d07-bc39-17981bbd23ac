package api

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
)

func ConfigCustomerCarriers(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.CustomerCarrier
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.ConfigCustomerCarriers(input))
}

func GetConfigCustomerCarriers(req sdk.APIRequest, resp sdk.APIResponder) error {
	var str = sdk.ParseInt64(req.GetParam("customerId"), 0)
	var limit = sdk.ParseInt64(req.GetParam("limit"), 100)
	var offset = sdk.ParseInt64(req.GetParam("offset"), 0)
	var getTotal = req.GetParam("getTotal") == "true"

	return resp.Respond(action.GetConfigCustomerCarriers(str, limit, offset, getTotal))
}

func DeleteConfigCustomerCarriers(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.CustomerCarrier
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.DeleteConfigCustomerCarriers(input))
}
