package vnp

import (
	"encoding/json"
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathBookVNP        = "/CustomerConnect/CreateOrder"
	pathBookVNPBash    = "/Order/CreateOrderInBatch"
	pathCancelVNP      = "/CustomerConnect/CancelOrder"
	pathGetTracking    = "/Order/GetOrder"
	pathGetAllProvince = "/TinhThanh/GetAll"
	pathGetAllDistrict = "/QuanHuyen/GetAll"
	pathGetAllWard     = "/PhuongXa/GetAll"
)

type Client struct {
	svc     *client.RestClient
	headers map[string]string
}

func NewServiceClient(apiHost, logName string, session *mongo.Database) *Client {
	if apiHost == "" {
		return nil
	}
	newClient := &Client{
		svc: client.NewRESTClient(
			apiHost,
			logName,
			180*time.Second,
			0,
			180*time.Second,
		),
		headers: map[string]string{},
	}
	newClient.svc.SetDBLog(session)
	return newClient
}

func (cli *Client) ChangeInformation(carrierInfo *model.Carrier) {
	cli.headers = map[string]string{
		"h-token": carrierInfo.ExtraData.AccessToken,
	}
}

func (cli *Client) CreateTracking(createTrackingRequest request.BookVNPost) (tracking *model.ShippingInfo, err error) {
	query := map[string]string{}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, query, createTrackingRequest, pathBookVNP, nil)
	if err != nil {
		return
	}

	resBody := new(model.VNPOrder)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		err = fmt.Errorf(string(res.Body))
		return
	}

	tracking = &model.ShippingInfo{
		TrackingNumber: resBody.ItemCode,
		CODAmount:      resBody.CodAmountEvaluation,
		FeeAmount:      resBody.TotalFreightIncludeVat,
	}
	tracking.ExtraInfo = map[string]interface{}{}
	tracking.ExtraInfo["order_id"] = resBody.Id

	return
}

func (cli *Client) CreateTrackingBash(createTrackingRequest request.BookVNPost) (trackingInfos []*model.ShippingInfo, err error) {
	query := map[string]string{}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, query, createTrackingRequest, pathBookVNPBash, nil)
	if err != nil {
		return
	}

	resBody := new([]*model.VNPOrder)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		err = fmt.Errorf(string(res.Body))
		return
	}

	if len(*resBody) == 0 {
		return nil, fmt.Errorf("Thông tin đơn hàng của VNP không hợp lệ ")
	}

	for _, order := range *resBody {
		shippingInfo := &model.ShippingInfo{
			TrackingNumber: order.ItemCode,
			CODAmount:      order.CodAmountEvaluation,
			FeeAmount:      order.TotalFreightExcludeVatEvaluation,
		}

		shippingInfo.ExtraInfo = map[string]interface{}{}
		shippingInfo.ExtraInfo["order_id"] = order.Id

		trackingInfos = append(trackingInfos, shippingInfo)
	}
	return
}

func (cli *Client) CancelTracking(cancelRequest request.CancelVNPost) (err error) {
	query := map[string]string{}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, query, cancelRequest, pathCancelVNP, nil)
	if err != nil {
		return
	}
	if string(res.Body) != "" {
		return fmt.Errorf(string(res.Body))
	}
	return nil
}

func (cli *Client) GetDetailOrder(id string) (result string, err error) {
	query := map[string]string{}

	path := pathGetTracking + "/" + id

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, query, nil, path, nil)
	if err != nil {
		return
	}

	result = res.Body

	return
}

func (cli *Client) GetProvince() (result []*model.ProvinceVNP, err error) {

	query := map[string]string{}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, query, nil, pathGetAllProvince, nil)
	if err != nil {
		return
	}

	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return
	}

	return
}
func (cli *Client) GetDistrict() (result []*model.DistrictVNP, err error) {

	query := map[string]string{}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, query, nil, pathGetAllDistrict, nil)
	if err != nil {
		return
	}

	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return
	}

	return
}
func (cli *Client) GetWard() (result []*model.WardVNP, err error) {
	type myResponse struct {
		Data []*model.WardVNP `json:"data"`
	}

	query := map[string]string{}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, query, nil, pathGetAllWard, nil)
	if err != nil {
		return
	}

	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return
	}

	return
}
