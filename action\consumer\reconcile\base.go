package reconcile

import (
	"log"
	"sync"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"go.mongodb.org/mongo-driver/mongo"
)

type ExecutorJob struct {
	Job *job.Executor
}

var (
	instance                     map[string]*ExecutorJob
	onceInit                     map[string]*sync.Once
	defaultTopic                 = "add_order_to_reconcile"
	updateOrderFeeReconcileTopic = "update_order_fee_reconcile"
)

func init() {
	onceInit = map[string]*sync.Once{}
	instance = map[string]*ExecutorJob{}
	defaultTopic = conf.Config.Topics["add_order_to_reconcile"]
	updateOrderFeeReconcileTopic = conf.Config.Topics["update_order_fee_reconcile"]
}

func InitReconcileExecutor(dbSession *mongo.Database, Database string, Collection string) {
	instanceName := defaultTopic
	if onceInit[instanceName] == nil {
		onceInit[instanceName] = &sync.Once{}
	}

	onceInit[instanceName].Do(func() {
		instance[instanceName] = &ExecutorJob{
			&job.Executor{ColName: defaultTopic},
		}

		instance[instanceName].Job.InitWithConfig(dbSession,
			Database,
			&job.ExecutorConfiguration{
				ParallelTopicProcessing: false,
				FailThreshold:           50,
				ChannelCount:            1,
				SortedItem:              false,
				LogSize:                 5,
				SelectorDelayMS:         100,
				WaitForReadyTime:        false,
				UniqueItem:              false,
				ConsumedExipredTime:     time.Duration(7) * time.Hour,
				CurVersionTimeoutS:      30,
				OldVersionTimeoutS:      30,
				MaximumWaitToRetryS:     15,
			},
		)

		log.Println("StartConsume", instanceName)
		instance[instanceName].addOrderToReconcileSession()
		instance[instanceName].updateOrderFeeToReconcileSession()
		instance[instanceName].Job.StartConsume()
	})
}

func PushReconcileOrder(data interface{}, sortedKey string) (err error) {
	return instance[defaultTopic].Job.Push(
		data, &job.JobItemMetadata{
			Topic:     defaultTopic,
			SortedKey: sortedKey,
		})
}

func PushUpdateOrderFee(data interface{}, sortedKey string) (err error) {
	return instance[defaultTopic].Job.Push(
		data, &job.JobItemMetadata{
			Topic:     updateOrderFeeReconcileTopic,
			SortedKey: sortedKey,
		})
}

type AddOrderToReconcileSession struct {
	ReferenceCode string `json:"referenceCode"`
	CustomerCode  string `json:"customerCode"`
}

type UpdateOrderFeeSession struct {
	StoredShippingOrder model.ShippingOrder `json:"storedShippingOrder" bson:"stored_shipping_order"`
}
