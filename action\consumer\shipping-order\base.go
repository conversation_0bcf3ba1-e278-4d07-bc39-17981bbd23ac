package shipping_order

import (
	"log"
	"sync"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"go.mongodb.org/mongo-driver/mongo"
)

type ExecutorJob struct {
	Job *job.Executor
}

var (
	instance                               map[string]*ExecutorJob
	onceInit                               map[string]*sync.Once
	defaultTopic                           = "create_extend_shipping_order"
	createExtendCompleteHandoverOrderTopic = "create_extend_complete_handover_order"
	updateFeeTopic                         = "update_fee_topic"
	checkinInboundToTopic                  = "checkin_inbound_to"
	// Remove here one done change queue
	oldOnceInit map[string]*sync.Once
	oldInstance map[string]*ExecutorJob
)

func init() {
	onceInit = map[string]*sync.Once{}
	instance = map[string]*ExecutorJob{}
	defaultTopic = conf.Config.Topics["create_extend_shipping_order"]
	createExtendCompleteHandoverOrderTopic = conf.Config.Topics["create_extend_complete_handover_order"]
	updateFeeTopic = conf.Config.Topics["update_fee_topic"]
	checkinInboundToTopic = conf.Config.Topics["checkin_inbound_to"]
	// Remove here one done change queue
	oldOnceInit = map[string]*sync.Once{}
	oldInstance = map[string]*ExecutorJob{}
}

// InitShippingAddressExecutor func
func InitPickupShippingOrderExecutor(dbSession *mongo.Database, Database, Collection string) {
	instanceName := defaultTopic
	if oldOnceInit[instanceName] == nil {
		oldOnceInit[instanceName] = &sync.Once{}
	}

	oldOnceInit[instanceName].Do(func() {
		oldInstance[instanceName] = &ExecutorJob{
			&job.Executor{ColName: Collection},
		}

		oldInstance[instanceName].Job.InitWithConfig(dbSession,
			Database,
			&job.ExecutorConfiguration{
				ParallelTopicProcessing: false,
				FailThreshold:           50,
				ChannelCount:            5,
				SortedItem:              false,
				LogSize:                 5,
				SelectorDelayMS:         100,
				WaitForReadyTime:        false,
				UniqueItem:              false,
				ConsumedExipredTime:     time.Duration(7) * time.Hour,
				CurVersionTimeoutS:      30,
				OldVersionTimeoutS:      30,
				MaximumWaitToRetryS:     15,
			},
		)

		log.Println("StartConsume", instanceName)
		oldInstance[instanceName].createExtendShippingOrder()
		oldInstance[instanceName].createExtendCompleteHandoverOrder()
		oldInstance[instanceName].updateFee()
		oldInstance[instanceName].checkinInboundTo()
		oldInstance[instanceName].Job.StartConsume()
	})
}

func InitPickupShippingOrderJob(dbSession *mongo.Database, Database, Collection string) {
	instanceName := defaultTopic
	if onceInit[instanceName] == nil {
		onceInit[instanceName] = &sync.Once{}
	}

	onceInit[instanceName].Do(func() {
		instance[instanceName] = &ExecutorJob{
			&job.Executor{ColName: Collection},
		}

		instance[instanceName].Job.InitWithConfig(dbSession,
			Database,
			&job.ExecutorConfiguration{
				ParallelTopicProcessing: false,
				FailThreshold:           50,
				ChannelCount:            5,
				SortedItem:              false,
				LogSize:                 5,
				SelectorDelayMS:         100,
				WaitForReadyTime:        false,
				UniqueItem:              false,
				ConsumedExipredTime:     time.Duration(7) * time.Hour,
				CurVersionTimeoutS:      30,
				OldVersionTimeoutS:      30,
				MaximumWaitToRetryS:     15,
			},
		)

		log.Println("StartConsume", instanceName)
		instance[instanceName].createExtendShippingOrder()
		instance[instanceName].createExtendCompleteHandoverOrder()
		instance[instanceName].updateFee()
		instance[instanceName].checkinInboundTo()
		instance[instanceName].Job.StartConsume()
	})
}

func Push(data interface{}, sortedKey string) (err error) {
	return instance[defaultTopic].Job.Push(
		data, &job.JobItemMetadata{
			Topic:     defaultTopic,
			SortedKey: sortedKey,
		})
}

func PushCreateHubShippingOrder(data interface{}, sortedKey string) (err error) {
	return instance[defaultTopic].Job.Push(
		data, &job.JobItemMetadata{
			Topic:     createExtendCompleteHandoverOrderTopic,
			SortedKey: sortedKey,
		})
}

func PushUpdateFee(data interface{}, sortedKey string) (err error) {
	return instance[defaultTopic].Job.Push(
		data, &job.JobItemMetadata{
			Topic:     updateFeeTopic,
			SortedKey: sortedKey,
		})
}

func PushCheckinTo(data interface{}, sortedKey string) (err error) {
	return instance[defaultTopic].Job.Push(
		data, &job.JobItemMetadata{
			Topic:     checkinInboundToTopic,
			SortedKey: sortedKey,
		})
}

type RequestBody struct {
	BodyData []model.HubShippingOrder `json:"bodyData"`
}

type RequestBodyHandover struct {
	HubOrder       model.HubShippingOrder `json:"hubOrder"`
	RemovedBinList []*model.Product       `json:"removedBinList"`
	ShouldComplete bool                   `json:"shouldComplete"`
}

type SplitOrderRequest struct {
	RemainPackageQuantity int64               `json:"remainPackageQuantity"`
	ParentShippingOrder   model.ShippingOrder `json:"parentShippingOrder"`
}
