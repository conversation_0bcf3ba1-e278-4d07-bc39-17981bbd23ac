package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func CreateConfig(input *model.Config) *common.APIResponse {
	if input.WarehouseCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Mã kho không được để trống",
		}
	}

	if input.ProvinceCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Mã Tỉnh không được để trống",
		}
	}

	if len(input.DistrictRoute) > 0 {
		districts, err := client.Services.WarehouseCoreClient.GetDistrictByProvinceCode(input.ProvinceCode, "")
		if err != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Không tìm thấy danh sách huyện của của tỉnh " + input.ProvinceName,
			}
		}

		var districtCodes []string

		for _, district := range districts {
			districtCodes = append(districtCodes, district.Code)
		}

		for _, district := range input.DistrictRoute {
			if !CheckItemInArray(district.DistrictCode, districtCodes) {
				return &common.APIResponse{
					Status:  common.APIStatus.Error,
					Message: "Huyện " + district.DistrictName + "(" + district.DistrictCode + ")" + " không nằm trong tỉnh " + input.ProvinceName,
				}
			}
		}
	}

	createConfig := model.ConfigDB.Create(input)
	if createConfig.Status != common.APIStatus.Ok {
		return createConfig
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Tạo config chuyển thành công",
	}
}

func UpdateConfig(input *model.Config) *common.APIResponse {
	if input.WarehouseCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Mã kho không được để trống",
		}
	}

	if input.ProvinceCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Mã Tỉnh không được để trống",
		}
	}

	if len(input.DistrictRoute) > 0 {
		districts, err := client.Services.WarehouseCoreClient.GetDistrictByProvinceCode(input.ProvinceCode, "")
		if err != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Không tìm thấy danh sách huyện của của tỉnh " + input.ProvinceName,
			}
		}

		var districtCodes []string

		for _, district := range districts {
			districtCodes = append(districtCodes, district.Code)
		}

		for _, district := range input.DistrictRoute {
			if !CheckItemInArray(district.DistrictCode, districtCodes) {
				return &common.APIResponse{
					Status:  common.APIStatus.Error,
					Message: "Huyện " + district.DistrictName + "(" + district.DistrictCode + ")" + " không nằm trong tỉnh " + input.ProvinceName,
				}
			}
		}
	}

	afterOption := options.After

	UpdateConfigResult := model.ConfigDB.UpdateOne(
		bson.M{
			"province_code":  input.ProvinceCode,
			"warehouse_code": input.WarehouseCode,
		},
		input,
		&options.FindOneAndUpdateOptions{
			Upsert:         &enum.False,
			ReturnDocument: &afterOption,
		})

	if UpdateConfigResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Bad request when Update config",
		}
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật config thành công",
	}
}

func GetConfig(provinceCode string) *common.APIResponse {
	if provinceCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Parameter province_code must not empty.",
			ErrorCode: "PROVINCE_CODE_REQUIRED",
		}
	}

	return model.ConfigDB.QueryOne(&model.Config{
		ProvinceCode: provinceCode,
	})
}

// GetListConfig func
func GetListConfig(query model.Config, offset, limit int64, getTotal bool) *common.APIResponse {
	filter := bson.M{}
	if query.ProvinceCode != "" {
		filter["province_code"] = query.ProvinceCode
	}

	if query.WarehouseCode != "" {
		filter["warehouse_code"] = query.WarehouseCode
	}

	result := model.ConfigDB.Query(
		filter,
		offset,
		limit,
		nil)
	if getTotal {
		countResult := model.ConfigDB.Count(filter)
		result.Total = countResult.Total
	}

	return result
}

func DeleteConfig(provinceCode, warehouseCode string) *common.APIResponse {
	return model.ConfigDB.Delete(
		bson.M{
			"province_code":  provinceCode,
			"warehouse_code": warehouseCode,
		})
}

func DeleteConfigById(id string) *common.APIResponse {
	_id, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: err.Error(),
		}
	}

	return model.ConfigDB.Delete(
		bson.M{
			"_id": _id,
		})
}

func GetFailReasons() *common.APIResponse {
	var failReasons []model.FailReason
	// TODO: add ENG translation here
	for key, value := range enum.VietnamReasonCode {
		failReasons = append(failReasons, model.FailReason{
			ReasonCode: key,
			ReasonType: enum.FailReasonTypeValue(enum.FailReasonCodeToType[key]),
			ReasonName: value,
		})
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Get fail reasons successfully",
		Data:    failReasons,
	}
}
