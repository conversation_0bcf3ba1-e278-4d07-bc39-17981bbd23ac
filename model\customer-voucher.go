package model

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/mongo"
)

type CustomerVoucher struct {
	CustomerCode string `json:"customerCode,omitempty" bson:"customer_code,omitempty"`
	VoucherCode  string `json:"voucherCode,omitempty" bson:"voucher_code,omitempty"`
	RemainAmount int    `json:"remainAmount,omitempty" bson:"remain_amount,omitempty"`
}

var CustomerVoucherDB = &db.Instance{
	ColName:        "customer_voucher",
	TemplateObject: &CustomerVoucher{},
}

func InitCustomerVoucherModel(s *mongo.Database) {
	CustomerVoucherDB.ApplyDatabase(s)
}
