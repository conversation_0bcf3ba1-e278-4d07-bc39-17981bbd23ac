package model

import "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"

type Department struct {
	Code          string                `json:"code,omitempty" bson:"code,omitempty"`
	CompanyCode   enum.CompanyCodeValue `json:"companyCode,omitempty" bson:"company_code,omitempty""`
	Name          string                `json:"name,omitempty" bson:"name,omitempty"`
	ParentCode    *string               `json:"parentCode,omitempty" bson:"parent_code,omitempty"` // parent organization
	RoleList      *[]string             `json:"roleList,omitempty" bson:"role_list,omitempty"`
	Tag           *[]string             `json:"tag,omitempty" bson:"tag,omitempty"`
	Address       string                `json:"address,omitempty" bson:"address,omitempty"`
	EmployeeCount int                   `json:"employeeCount,omitempty" bson:"employee_count,omitempty"`
}
