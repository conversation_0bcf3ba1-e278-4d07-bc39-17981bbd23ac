package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type HubShippingOrder struct {
	ID primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	// base info
	VersionNo       string     `json:"versionNo,omitempty" bson:"version_no,omitempty"`
	CreatedBy       string     `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	CreatedTime     *time.Time `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedBy   string     `json:"lastUpdatedBy,omitempty" bson:"last_updated_by,omitempty"`
	LastUpdatedTime *time.Time `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	// Tracking info
	HUBCode       string                            `json:"HUBCode,omitempty" bson:"hub_code,omitempty"`
	ReferenceCode string                            `json:"referenceCode,omitempty" bson:"reference_code,omitempty"`
	TrackingCode  string                            `json:"trackingCode,omitempty" bson:"tracking_code,omitempty"`
	TplCode       *enum.PartnerValue                `json:"tplCode,omitempty" bson:"tpl_code,omitempty"`
	TplName       string                            `json:"tplName,omitempty" bson:"tpl_name,omitempty"`
	Status        *enum.HubShippingOrderStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	Type          *enum.HubOrderTypeValue           `json:"type,omitempty" bson:"type,omitempty"`
	SubType       *enum.SubTypeValue                `json:"subType,omitempty" bson:"sub_type,omitempty"`
	ActionTime    int64                             `json:"actionTime,omitempty" bson:"action_time,omitempty"`
	Action        string                            `json:"action" bson:"action,omitempty"`
	ActionName    string                            `json:"actionName" bson:"action_name,omitempty"`

	FromCustomerName    string  `json:"fromCustomerName,omitempty" bson:"from_customer_name,omitempty"`
	FromCustomerCode    string  `json:"fromCustomerCode,omitempty" bson:"from_customer_code,omitempty"`
	FromCustomerPhone   string  `json:"fromCustomerPhone,omitempty" bson:"from_customer_phone,omitempty"`
	FromCustomerEmail   string  `json:"fromCustomerEmail,omitempty" bson:"from_customer_email,omitempty"`
	FromCustomerAddress string  `json:"fromCustomerAddress,omitempty" bson:"from_customer_address,omitempty"`
	FromLatitude        float64 `json:"fromLatitude,omitempty" bson:"from_latitude,omitempty"`
	FromLongitude       float64 `json:"fromLongitude,omitempty" bson:"from_longitude,omitempty"`
	FromWardCode        string  `json:"fromWardCode,omitempty" bson:"from_ward_code,omitempty"`
	FromWardName        string  `json:"fromWardName,omitempty" bson:"from_ward_name,omitempty"`
	FromDistrictCode    string  `json:"fromDistrictCode,omitempty" bson:"from_district_code,omitempty"`
	FromDistrictName    string  `json:"fromDistrictName,omitempty" bson:"from_district_name,omitempty"`
	FromProvinceCode    string  `json:"fromProvinceCode,omitempty" bson:"from_province_code,omitempty"`
	FromProvinceName    string  `json:"fromProvinceName,omitempty" bson:"from_province_name,omitempty"`
	// Add FromAddressId can help verify both from address and to address (if we open our api)
	FromAddressId int64 `json:"fromAddressId,omitempty" bson:"from_address_id,omitempty"`

	ToCustomerName    string  `json:"toCustomerName,omitempty" bson:"to_customer_name,omitempty"`
	ToCustomerCode    string  `json:"toCustomerCode,omitempty" bson:"to_customer_code,omitempty"`
	ToCustomerAddress string  `json:"toCustomerAddress,omitempty" bson:"to_customer_address,omitempty"`
	ToCustomerPhone   string  `json:"toCustomerPhone,omitempty" bson:"to_customer_phone,omitempty"`
	ToCustomerEmail   string  `json:"toCustomerEmail,omitempty" bson:"to_customer_email,omitempty"`
	ToLatitude        float64 `json:"toLatitude,omitempty" bson:"to_latitude,omitempty"`
	ToLongitude       float64 `json:"toLongitude,omitempty" bson:"to_longitude,omitempty"`
	ToWardCode        string  `json:"toWardCode,omitempty" bson:"to_ward_code,omitempty"`
	ToWardName        string  `json:"toWardName,omitempty" bson:"to_ward_name,omitempty"`
	ToDistrictCode    string  `json:"toDistrictCode,omitempty" bson:"to_district_code,omitempty"`
	ToDistrictName    string  `json:"toDistrictName,omitempty" bson:"to_district_name,omitempty"`
	ToProvinceCode    string  `json:"toProvinceCode,omitempty" bson:"to_province_code,omitempty"`
	ToProvinceName    string  `json:"toProvinceName,omitempty" bson:"to_province_name,omitempty"`
	ToAddressID       int64   `json:"toAddressId,omitempty" bson:"to_address_id,omitempty"`

	Height                   float64 `json:"height,omitempty" bson:"height,omitempty"`
	Width                    float64 `json:"width,omitempty" bson:"width,omitempty"`
	Length                   float64 `json:"length,omitempty" bson:"length,omitempty"`
	Weight                   float64 `json:"weight,omitempty" bson:"weight,omitempty"`
	NumPackage               int64   `json:"numPackage,omitempty" bson:"num_package,omitempty"`
	ReceivePackage           int64   `json:"receivePackage,omitempty" bson:"receive_ package,omitempty"`
	EstimateDeliveringTime   int64   `json:"estimateDeliveringTime" bson:"estimate_delivering_time,omitempty"`
	Distance                 float64 `json:"distance" bson:"distance,omitempty"`
	NeedVerifyAddress        bool    `json:"needVerifyAddress,omitempty" bson:"need_verify_address,omitempty"`
	ParentReferenceCode      string  `json:"parentReferenceCode,omitempty" bson:"parent_reference_code,omitempty"`
	ReceiveSessionCode       string  `json:"receiveSessionCode,omitempty" bson:"receive_session_code,omitempty"`
	ParentReceiveSessionCode string  `json:"parentReceiveSessionCode,omitempty" bson:"parent_receive_session_code,omitempty"`

	// DriverID
	DriverID   int64  `json:"driverId,omitempty" bson:"driver_id,omitempty"`
	DriverName string `json:"driverName,omitempty" bson:"driver_name,omitempty"`

	PrivateNote string `json:"privateNote,omitempty" bson:"private_note,omitempty"`
	Note        string `json:"note,omitempty" bson:"note,omitempty"`

	TotalAmount                float64                     `json:"totalAmount,omitempty" bson:"total_amount,omitempty"`
	TotalCollectSenderAmount   *float64                    `json:"totalCollectSenderAmount,omitempty" bson:"total_collect_sender_amount,omitempty"`
	TotalCollectReceiverAmount *float64                    `json:"totalCollectReceiverAmount,omitempty" bson:"total_collect_receiver_amount,omitempty"`
	TotalDebtAmount            *float64                    `json:"totalDebtAmount,omitempty" bson:"total_debt_amount,omitempty"`
	DeliveryNote               []*DeliveryNote             `json:"deliveryNote,omitempty" bson:"delivery_note,omitempty"`
	DeliveryAmount             float64                     `json:"deliveryAmount,omitempty" bson:"delivery_amount,omitempty"`
	CODAmount                  float64                     `json:"codAmount,omitempty" bson:"cod_amount,omitempty"`
	FeeAmount                  float64                     `json:"feeAmount,omitempty" bson:"fee_amount,omitempty"`
	FeeSenderAmount            *float64                    `json:"feeSenderAmount,omitempty" bson:"fee_sender_amount,omitempty"`
	FeeReceiverAmount          *float64                    `json:"feeReceiverAmount,omitempty" bson:"fee_receiver_amount,omitempty"`
	FeeDebtAmount              *float64                    `json:"feeDebtAmount,omitempty" bson:"fee_debt_amount,omitempty"`
	PaymentMethod              *enum.PaymentMethodValue    `json:"paymentMethod,omitempty" bson:"payment_method,omitempty"`
	CollectOnDelivery          *bool                       `json:"collectOnDelivery,omitempty" bson:"collect_on_delivery,omitempty"`
	FeeCollectMethod           *enum.FeeCollectMethodValue `json:"feeCollectMethod,omitempty" bson:"fee_collect_method,omitempty"`

	Logs               []HubShippingOrder            `json:"logs,omitempty" bson:"logs,omitempty"`
	ProductivityAction *enum.ProductivityActionValue `json:"productivityAction,omitempty" bson:"productivity_action,omitempty"`
	Reason             string                        `json:"reason,omitempty" bson:"reason,omitempty"`
	ReasonCode         string                        `json:"reasonCode,omitempty" bson:"reason_code,omitempty"`
	Images             []string                      `json:"images,omitempty" bson:"images,omitempty"`
	ExtraInfo          map[string]interface{}        `json:"extraInfo,omitempty" bson:"extra_info,omitempty"`
	StoredTime         int64                         `json:"storedTime,omitempty" bson:"stored_time,omitempty"`
	DeliveredTime      int64                         `json:"deliveredTime,omitempty" bson:"delivered_time,omitempty"`
	CompletedTime      int64                         `json:"completedTime,omitempty" bson:"completed_time,omitempty"`

	IsDropOffAtFMHub   bool   `json:"isDropOffAtFMHub,omitempty" bson:"is_drop_drop_at_lm_hub,omitempty"`
	IsReceiveAtLMHub   bool   `json:"isReceiveAtLMHub,omitempty" bson:"is_receive_at_hub,omitempty"`
	NeedVerifyReceiver bool   `json:"needVerifyReceiver,omitempty" bson:"need_verify_receiver,omitempty"`
	VerificationCode   string `json:"-" bson:"verification_code,omitempty"`
	IsAutoAssign       *bool  `json:"isAutoAssign,omitempty" bson:"is_auto_assign,omitempty"`

	ExpectedDeliveryTime    int64      `json:"expectedDeliveryTime,omitempty" bson:"expected_delivery_time,omitempty"`
	ExpectedPickupTime      int64      `json:"expectedPickupTime,omitempty" bson:"expected_pickup_time,omitempty"`
	RescheduledDeliveryTime int64      `json:"rescheduled	DeliveryTime,omitempty" bson:"rescheduled_delivery_time,omitempty"`
	RescheduledPickupTime   int64      `json:"rescheduledPickupTime,omitempty" bson:"rescheduled_pickup_time,omitempty"`
	Products                []*Product `json:"products,omitempty" bson:"products,omitempty"`

	LateLeadTimeLevel int        `json:"lateLeadTimeLevel,omitempty" bson:"late_lead_time_level,omitempty"`
	PickupLeadTime    *time.Time `json:"pickupLeadTime,omitempty" bson:"pickup_lead_time,omitempty"`
	DeliveryLeadTime  *time.Time `json:"deliveryLeadTime,omitempty" bson:"delivery_lead_time,omitempty"`
	TransportLeadTime *time.Time `json:"transportLeadTime,omitempty" bson:"transport_lead_time,omitempty"`

	FailReasons []FailReason `json:"failReasons,omitempty" bson:"fail_reasons,omitempty"`

	TripId            int64                   `json:"tripId,omitempty" bson:"trip_id,omitempty"`
	Tags              []string                `json:"tags,omitempty" bson:"tags,omitempty"`
	OrderValue        *float64                `json:"orderValue,omitempty" bson:"order_value,omitempty"`
	MergeStatus       *enum.MergeStatusValue  `json:"mergeStatus,omitempty" bson:"merge_status,omitempty"`
	ExpiredAt         *time.Time              `json:"expiredAt,omitempty" bson:"expired_at,omitempty"`
	ProductType       enum.ProductTypeValue   `json:"productType,omitempty" bson:"product_type,omitempty"`
	ProductTypes      []enum.ProductTypeValue `json:"productTypes,omitempty" bson:"product_types,omitempty"`
	PreviousDriverIds []*HubShippingOrder     `json:"previousDriverIds,omitempty" bson:"previous_driver_ids,omitempty"`
	Baskets           []Basket                `json:"baskets,omitempty" bson:"baskets,omitempty"`

	References []string `json:"references,omitempty" bson:"references,omitempty"`
}

var HUBShippingOrderDB = &CustomInstance{
	Instance: db.Instance{
		ColName:        "hub_shipping_order",
		TemplateObject: &HubShippingOrder{},
	},
	SecondaryInstance: db.Instance{
		ColName:        "hub_shipping_order",
		TemplateObject: &HubShippingOrder{},
	},
}

func InitSecondaryHubShippingOrderModel(s *mongo.Database) {
	HUBShippingOrderDB.SecondaryInstance.ApplyDatabase(s)
}

func InitHUBShippingOrderModel(s *mongo.Database) {
	HUBShippingOrderDB.ApplyDatabase(s)

	t := true
	HUBShippingOrderDB.CreateIndex(bson.D{
		{"hub_code", 1},
		{"extra_info.handoverCode", 1},
	}, &options.IndexOptions{
		Background: &t,
	})
	HUBShippingOrderDB.CreateIndex(bson.D{
		{"hub_code", 1},
		{"extra_info.handoverCode", 1},
	}, &options.IndexOptions{
		Background: &t,
	})
	//HUBShippingOrderDB.CreateIndex(bson.D{
	//	{"hub_code", 1},
	//	{"status", 1},
	//	{"type", 1},
	//	{"sub_type", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})

	//HUBShippingOrderDB.CreateIndex(bson.D{
	//	{"reference_code", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})
	//
	//HUBShippingOrderDB.CreateIndex(bson.D{
	//	{"reference_code", 1},
	//	{"hub_code", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//	Unique:     &t,
	//})
	//
	//HUBShippingOrderDB.CreateIndex(bson.D{
	//	{"hub_code", 1},
	//	{"status", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//	Unique:     &t,
	//})
	//
	//HUBShippingOrderDB.CreateIndex(bson.D{
	//	{"tpl_code", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})
	//
	//HUBShippingOrderDB.CreateIndex(bson.D{
	//	{"status", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})
	//
	//HUBShippingOrderDB.CreateIndex(bson.D{
	//	{"hub_code", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})
	//
	//HUBShippingOrderDB.CreateIndex(bson.D{
	//	{"hub_code", 1},
	//	{"to_ward_code", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})
	//
	//HUBShippingOrderDB.CreateIndex(bson.D{
	//	{"hub_code", 1},
	//	{"to_province_code", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})
	//
	//HUBShippingOrderDB.CreateIndex(bson.D{
	//	{"tracking_code", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})
	//
	//HUBShippingOrderDB.CreateIndex(bson.D{
	//	{"driver_id", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})

	HUBShippingOrderDB.CreateIndex(bson.D{
		{"hub_code", 1},
		{"_id", -1},
	}, &options.IndexOptions{
		Background: &t,
	})

	HUBShippingOrderDB.CreateIndex(bson.D{
		{"references", 1},
		{"status", 1},
		{"action_time", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	HUBShippingOrderDB.CreateIndex(bson.D{
		{"hub_code", 1},
		{"action_time", 1},
		{"status", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	HUBShippingOrderDB.CreateIndex(bson.D{
		{"hub_code", 1},
		{"merge_status", 1},
		{"status", 1},
		{"action_time", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	HUBShippingOrderDB.CreateIndex(bson.D{
		{"parent_receive_session_code", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	HUBShippingOrderDB.CreateIndex(bson.D{
		{"expected_pickup_time", 1},
		{"status", 1},
		{"hub_code", 1},
		{"driver_id", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	HUBShippingOrderDB.CreateIndex(bson.D{
		{"rescheduled_pickup_time", 1},
		{"status", 1},
		{"hub_code", 1},
		{"driver_id", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	HUBShippingOrderDB.CreateIndex(bson.D{
		{"expected_delivery_time", 1},
		{"status", 1},
		{"hub_code", 1},
		{"driver_id", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	HUBShippingOrderDB.CreateIndex(bson.D{
		{"rescheduled_delivery_time", 1},
		{"status", 1},
		{"hub_code", 1},
		{"driver_id", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	HUBShippingOrderDB.CreateIndex(bson.D{
		{"hub_code", 1},
		{"tags", 1},
		{"status", 1},
		{"action_time", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	HUBShippingOrderDB.CreateIndex(bson.D{
		{"hub_code", 1},
		{"delivery_lead_time", 1},
		{"status", 1},
		{"action_time", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	HUBShippingOrderDB.CreateIndex(bson.D{
		{"reference_code", 1},
		{"sub_type", 1},
		{"status", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	HUBShippingOrderDB.CreateIndex(bson.D{
		{"reference_code", 1},
		{"type", 1},
		{"status", 1},
		{"to_customer_code", 1},
		{"created_time", 1},
	}, &options.IndexOptions{
		Background: &t,
	})
}
