package snappy

import (
	"encoding/json"
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathBookSnappyService   = "/snappy/trackings/create"
	pathCancelSnappyService = "/snappy/trackings/cancel"
	pathGetTracking         = "/snappy/trackings"
)

type Client struct {
	svc     *client.RestClient
	headers map[string]string
}

func NewServiceClient(apiHost, logName string, session *mongo.Database) *Client {
	if apiHost == "" {
		return nil
	}
	newClient := &Client{
		svc: client.NewRESTClient(
			apiHost,
			logName,
			180*time.Second,
			0,
			180*time.Second,
		),
		headers: map[string]string{},
	}

	newClient.svc.SetDBLog(session)
	return newClient
}

func (cli *Client) CreateTrackingSnappy(createTrackingRequest request.BookSnappy, carrierInfo *model.Carrier) (tracking *model.ShippingInfo, err error) {
	type response struct {
		Message  string `json:"message"`
		No       string `json:"no"`
		Success  bool   `json:"success"`
		Tracking struct {
			CustomerTrackingId string `json:"customer_tracking_id"`
			Id                 string `json:"id"`
			Services           struct {
				CODService struct {
					Amount int64 `json:"amount"`
				} `json:"cod_service"`
			} `json:"services"`
		} `json:"tracking"`
	}

	query := map[string]string{
		"access_token": carrierInfo.ExtraData.AccessToken,
	}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, query, createTrackingRequest, pathBookSnappyService, nil)
	if err != nil {
		return
	}
	resBody := new(response)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return
	}

	if !resBody.Success {
		err = fmt.Errorf("%v", resBody)
		return
	}

	tracking = &model.ShippingInfo{
		CODAmount:      float64(resBody.Tracking.Services.CODService.Amount),
		TrackingNumber: resBody.Tracking.Id,
	}

	return
}

func (cli *Client) CancelSnappy(cancelSnappyRequest request.CancelSnappyRequest, carrierInfo *model.Carrier) (err error) {
	type response struct {
		Message string `json:"message"`
		Success bool   `json:"success"`
	}

	query := map[string]string{
		"access_token": carrierInfo.ExtraData.AccessToken,
	}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Delete, cli.headers, query, cancelSnappyRequest, pathCancelSnappyService, nil)
	if err != nil {
		return
	}
	resBody := new(response)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return
	}

	if !resBody.Success {
		err = fmt.Errorf("%v-%v", resBody, resBody.Message)
		return
	}

	return
}

func (cli *Client) GetDetailOrder(id string, carrierInfo *model.Carrier) (result *request.Snappy, err error) {
	type response struct {
		Message  string          `json:"message"`
		Success  bool            `json:"success"`
		Tracking *request.Snappy `json:"tracking"`
	}

	query := map[string]string{
		"access_token": carrierInfo.ExtraData.AccessToken,
	}

	path := pathGetTracking + "/" + id

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, query, nil, path, nil)
	if err != nil {
		return
	}
	resBody := new(response)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return
	}

	if !resBody.Success || resBody.Tracking == nil {
		err = fmt.Errorf("%v-%v", resBody, resBody.Message)
		return
	}

	result = resBody.Tracking

	return
}
