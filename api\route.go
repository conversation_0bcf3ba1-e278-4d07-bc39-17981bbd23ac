package api

import (
	"encoding/json"
	"strconv"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
)

// GetRoute api
func GetRoute(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q        = req.GetParam("q")
		offset   = int64(sdk.ParseInt(req.GetParam("offset"), 0))
		limit    = int64(sdk.ParseInt(req.GetParam("limit"), 100))
		getTotal = req.GetParam("getTotal") == "true"
	)
	if q == "" {
		q = "{}"
	}
	var query *model.RouteQuery
	err := json.Unmarshal([]byte(q), &query)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data." + err.Error(),
			ErrorCode: "JSON_INVALID",
		})
	}

	return resp.Respond(action.GetRoute(query, offset, limit, getTotal))
}

// GetRoute api
func InitRoute(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input *model.Route
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data." + err.Error(),
			ErrorCode: "JSON_INVALID",
		})
	}

	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}
	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	input.CreatedBy = strconv.Itoa(int(UserInfo.Account.AccountID))

	return resp.Respond(action.InitRoute(input))
}

// UpdateRoute api
func UpdateRoute(req sdk.APIRequest, resp sdk.APIResponder) error {
	var updateReq *model.Route
	err := req.GetContent(&updateReq)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data." + err.Error(),
			ErrorCode: "JSON_INVALID",
		})
	}

	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}
	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	updateReq.LastUpdatedBy = strconv.Itoa(int(UserInfo.Account.AccountID))
	return resp.Respond(action.UpdateRoute(updateReq))
}
