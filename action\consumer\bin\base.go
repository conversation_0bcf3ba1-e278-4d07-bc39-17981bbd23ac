package bin

import (
	"log"
	"sync"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
	"go.mongodb.org/mongo-driver/mongo"
)

type ExecutorJob struct {
	Job *job.Executor
}

var (
	instance     map[string]*ExecutorJob
	onceInit     map[string]*sync.Once
	defaultTopic = "complete_bin_order"
	// Remove here one done change queue
	oldOnceInit map[string]*sync.Once
	oldInstance map[string]*ExecutorJob
)

func init() {
	onceInit = map[string]*sync.Once{}
	instance = map[string]*ExecutorJob{}
	defaultTopic = conf.Config.Topics["complete_bin_order"]
	// Remove here one done change queue
	oldOnceInit = map[string]*sync.Once{}
	oldInstance = map[string]*ExecutorJob{}
}

func InitBinExecutor(dbSession *mongo.Database, Database, Collection string) {
	instanceName := defaultTopic
	if oldOnceInit[instanceName] == nil {
		oldOnceInit[instanceName] = &sync.Once{}
	}

	oldOnceInit[instanceName].Do(func() {
		oldInstance[instanceName] = &ExecutorJob{
			&job.Executor{ColName: Collection},
		}

		oldInstance[instanceName].Job.InitWithConfig(dbSession,
			Database,
			&job.ExecutorConfiguration{
				ParallelTopicProcessing: false,
				FailThreshold:           50,
				ChannelCount:            5,
				SortedItem:              false,
				LogSize:                 5,
				SelectorDelayMS:         100,
				WaitForReadyTime:        false,
				UniqueItem:              false,
				ConsumedExipredTime:     time.Duration(7) * time.Hour,
				CurVersionTimeoutS:      30,
				OldVersionTimeoutS:      30,
				MaximumWaitToRetryS:     15,
			},
		)

		log.Println("StartConsume", instanceName)
		oldInstance[instanceName].completeBinOrder()
		oldInstance[instanceName].Job.StartConsume()
	})
}

func InitBinJob(dbSession *mongo.Database, Database, Collection string) {
	instanceName := defaultTopic
	if onceInit[instanceName] == nil {
		onceInit[instanceName] = &sync.Once{}
	}

	onceInit[instanceName].Do(func() {
		instance[instanceName] = &ExecutorJob{
			&job.Executor{ColName: Collection},
		}

		instance[instanceName].Job.InitWithConfig(dbSession,
			Database,
			&job.ExecutorConfiguration{
				ParallelTopicProcessing: false,
				FailThreshold:           50,
				ChannelCount:            5,
				SortedItem:              false,
				LogSize:                 5,
				SelectorDelayMS:         100,
				WaitForReadyTime:        false,
				UniqueItem:              false,
				ConsumedExipredTime:     time.Duration(7) * time.Hour,
				CurVersionTimeoutS:      30,
				OldVersionTimeoutS:      30,
				MaximumWaitToRetryS:     15,
			},
		)

		log.Println("StartConsume", instanceName)
		instance[instanceName].completeBinOrder()
		instance[instanceName].Job.StartConsume()
	})
}

func PushCompleteBin(data interface{}, sortedKey string) (err error) {
	return instance[defaultTopic].Job.Push(
		data, &job.JobItemMetadata{
			Topic:     defaultTopic,
			SortedKey: sortedKey,
		})
}
