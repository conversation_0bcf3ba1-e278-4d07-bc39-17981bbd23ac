package model

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"go.mongodb.org/mongo-driver/mongo"
)

type ConfigLogistic struct {
	Key     string                 `json:"key" bson:"key, omitempty"`
	Configs map[string]interface{} `json:"configs" bson:"configs, omitempty"`
	Tooltip map[string]interface{} `json:"tooltip" bson:"tooltip, omitempty"`
}

type WarehouseHubConfig struct {
	WarehouseCode string                 `json:"warehouseCode"`
	HubCode       string                 `json:"hubCode"`
	MergeStatus   *enum.MergeStatusValue `json:"mergeStatus,omitempty" bson:"merge_status,omitempty"`
}

type DropOffFeeByWarehouse struct {
	WarehouseCode       string  `json:"warehouseCode"`
	ROSellerFeeAmount   float64 `json:"ROSellerFeeAmount"`
	ROVendorFeeAmount   float64 `json:"ROVendorFeeAmount"`
	RONSSellerFeeAmount float64 `json:"RONSSellerFeeAmount"`
	RONSVendorFeeAmount float64 `json:"RONSVendorFeeAmount"`
	FeeAmount           float64 `json:"feeAmount"`
}

type PrefixConfig struct {
	Prefix     string  `json:"prefix"`
	CarrierIds []int64 `json:"carrierIds"`
}

var ConfigLogisticDB = &db.Instance{
	ColName:        "config_logistic",
	TemplateObject: &ConfigLogistic{},
}

func InitConfigLogisticModel(s *mongo.Database) {
	ConfigLogisticDB.ApplyDatabase(s)
}
