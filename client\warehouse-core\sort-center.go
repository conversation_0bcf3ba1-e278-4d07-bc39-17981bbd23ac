package warehouse_core

import (
	"encoding/json"
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
)

func (cli *Client) SendDoneReceipt(doneTransport *request.DoneTransportForPO) error {
	var res *client.RestResult
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, doneTransport, pathInboundWarehouse, nil)
	if err != nil {
		return err
	}

	type myResponse struct {
		Status  string `json:"status"`
		Code    string `json:"code"`
		Message string `json:"message"`
	}

	response := new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return err
	}

	if response.Status != common.APIStatus.Ok {
		return fmt.Errorf("%v", response.Message)
	}

	return nil
}
