package ahamove

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathBookAhamoveService   = "/v1/order/create"
	pathCancelAhamoveService = "/v1/order/cancel"
	patGetDetaiOrder         = "/v1/order/detail"
	pathEstimateOrderFee     = "/v1/order/estimated_fee"
)

type Client struct {
	svc     *client.RestClient
	headers map[string]string
}

func NewServiceClient(apiHost, logName string, session *mongo.Database) *Client {
	if apiHost == "" {
		return nil
	}
	newClient := &Client{
		svc: client.NewRESTClient(
			apiHost,
			logName,
			180*time.Second,
			0,
			180*time.Second,
		),
		headers: map[string]string{},
	}
	newClient.svc.SetDBLog(session)
	return newClient
}

func (cli *Client) ChangeInformation(carrierInfo *model.Carrier) {
	cli.headers = map[string]string{
		"Api-Key":      carrierInfo.ExtraData.ApiKey,
		"Content-Type": "application/x-www-form-urlencoded",
	}
}

func (cli *Client) CreateTrackingAhamove(body request.BookAhamoveRequest) (trackingInfo *model.ShippingInfo, err error) {
	type myResponse struct {
		OrderId    string `json:"order_id"`
		Title      string `json:"title"`
		SharedLink string `json:"shared_link"`
		Order      struct {
			TotalFee float64 `json:"total_fee"`
			Id       string  `json:"_id"`
			Distance float64 `json:"distance"`
		} `json:"order"`
	}

	path, err := json.Marshal(body.Path)

	if err != nil {
		return
	}

	query := map[string]string{
		"token":          body.Token,
		"order_time":     strconv.Itoa(int(body.OrderTime)),
		"idle_until":     strconv.Itoa(int(body.IdleUntil)),
		"payment_method": body.PaymentMethod,
		"service_id":     body.ServiceId,
		"path":           string(path),
	}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, query, nil, pathBookAhamoveService, nil)
	if err != nil {
		return
	}

	resBody := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return
	}

	if res.Code != 200 {
		err = fmt.Errorf("%v", resBody.Title)
		return
	}

	trackingInfo = &model.ShippingInfo{
		TrackingNumber: resBody.OrderId,
		FeeAmount:      resBody.Order.TotalFee,
		Distance:       resBody.Order.Distance,
	}

	trackingInfo.ExtraInfo = map[string]interface{}{}
	trackingInfo.ExtraInfo["shared_link"] = resBody.SharedLink

	return
}

func (cli *Client) CancelAhamove(body *request.CancelAhamoveRequest) (err error) {
	type myResponse struct {
		Title string `json:"title"`
	}

	query := map[string]string{
		"token":    body.Token,
		"order_id": body.OrderId,
		"comment":  body.Comment,
	}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, query, nil, pathCancelAhamoveService, nil)
	if err != nil {
		return
	}

	resBody := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return
	}

	if res.Code != 200 {
		err = fmt.Errorf("%v", resBody.Title)
		return
	}

	return
}

func (cli *Client) GetDetailOrder(token, orderId string) (result *request.AhamoveCallback, err error) {
	query := map[string]string{
		"token":    token,
		"order_id": orderId,
	}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, query, nil, patGetDetaiOrder, nil)
	if err != nil {
		return
	}

	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return
	}

	if res.Code != 200 {
		err = fmt.Errorf("%v", "Order Not Found")
		return
	}

	return
}

func (cli *Client) CreateTrackingAhamoveV2(
	body request.BookAhamoveRequest,
	carrier *model.Carrier) (trackingInfo *model.ShippingInfo, err error) {
	type myResponse struct {
		OrderId    string `json:"order_id"`
		Title      string `json:"title"`
		SharedLink string `json:"shared_link"`
		Order      struct {
			TotalFee   float64 `json:"total_fee"`
			TotalPrice float64 `json:"total_price"`
			Id         string  `json:"_id"`
			Distance   float64 `json:"distance"`
		} `json:"order"`
	}

	path, err := json.Marshal(body.Path)
	if err != nil {
		return
	}

	query := map[string]string{
		"token":          body.Token,
		"order_time":     strconv.Itoa(int(body.OrderTime)),
		"idle_until":     strconv.Itoa(int(body.IdleUntil)),
		"payment_method": body.PaymentMethod,
		"service_id":     body.ServiceId,
		"path":           string(path),
	}

	if len(body.Requests) > 0 {
		requestByte, marshalErr := json.Marshal(body.Requests)
		if marshalErr != nil {
			return
		}
		query["requests"] = string(requestByte)
	}

	header := map[string]string{
		"Api-Key":      carrier.ExtraData.ApiKey,
		"Content-Type": "application/x-www-form-urlencoded",
	}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, header, query, nil, pathBookAhamoveService, nil)
	if err != nil {
		return
	}

	resBody := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return
	}

	if res.Code != 200 {
		err = fmt.Errorf("%v", resBody.Title)
		return
	}

	trackingInfo = &model.ShippingInfo{
		TrackingNumber: resBody.OrderId,
		FeeAmount:      resBody.Order.TotalPrice,
		Distance:       resBody.Order.Distance,
	}

	trackingInfo.ExtraInfo = map[string]interface{}{}
	trackingInfo.ExtraInfo["shared_link"] = resBody.SharedLink

	return
}

func (cli *Client) EstimateOrderFee(
	body request.BookAhamoveRequest,
	carrier *model.Carrier) (trackingInfo *model.ShippingInfo, err error) {
	type myResponse struct {
		Title      string  `json:"title"`
		TotalPrice float64 `json:"total_price"`
	}

	path, err := json.Marshal(body.Path)

	if err != nil {
		return
	}

	query := map[string]string{
		"token":      body.Token,
		"order_time": strconv.Itoa(int(body.OrderTime)),
		"service_id": body.ServiceId,
		"path":       string(path),
	}

	header := map[string]string{
		"Api-Key":      carrier.ExtraData.ApiKey,
		"Content-Type": "application/x-www-form-urlencoded",
	}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, header, query, nil, pathEstimateOrderFee, nil)
	if err != nil {
		return
	}

	resBody := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return
	}

	if res.Code != 200 {
		err = fmt.Errorf("%v", resBody.Title)
		return
	}

	trackingInfo = &model.ShippingInfo{
		FeeAmount: resBody.TotalPrice,
	}
	return
}
