package request

import "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"

type BookVTP struct {
	OrderNumber        string     `json:"ORDER_NUMBER"`
	OrderReference     string     `json:"ORDER_REFERENCE"`
	GroupAddressId     string     `json:"GROUPADDRESS_ID"`
	CusId              int64      `json:"CUS_ID"`
	DeliveryDate       string     `json:"DELIVERY_DATE"` // format delivery date : dd//mm//yyyy H:m:s
	SenderFullName     string     `json:"SENDER_FULLNAME"`
	SenderAddress      string     `json:"SENDER_ADDRESS"`
	SenderPhone        string     `json:"SENDER_PHONE"`
	SenderEmail        string     `json:"SENDER_EMAIL"`
	SenderWard         int64      `json:"SENDER_WARD"`
	SenderDistrict     int64      `json:"SENDER_DISTRICT"`
	SenderProvince     int64      `json:"SENDER_PROVINCE"`
	SenderLatitude     int64      `json:"SENDER_LATITUDE"`
	SenderLongitude    int64      `json:"SENDER_LONGITUDE"`
	ReceiverFullName   string     `json:"RECEIVER_FULLNAME"`
	ReceiverAddress    string     `json:"RECEIVER_ADDRESS"`
	ReceiverPhone      string     `json:"RECEIVER_PHONE"`
	ReceiverEmail      string     `json:"RECEIVER_EMAIL"`
	ReceiverWard       int64      `json:"RECEIVER_WARD"`
	ReceiverDistrict   int64      `json:"RECEIVER_DISTRICT"`
	ReceiverProvince   int64      `json:"RECEIVER_PROVINCE"`
	ReceiverLatitude   int64      `json:"RECEIVER_LATITUDE"`
	ReceiverLongitude  int64      `json:"RECEIVER_LONGITUDE"`
	ProductName        string     `json:"PRODUCT_NAME"`
	ProductDescription string     `json:"PRODUCT_DESCRIPTION"`
	ProductQuantity    int64      `json:"PRODUCT_QUANTITY"`
	ProductPrice       int64      `json:"PRODUCT_PRICE"`
	ProductWeight      float64    `json:"PRODUCT_WEIGHT"`
	ProductLength      int64      `json:"PRODUCT_LENGTH"`
	ProductWidth       int64      `json:"PRODUCT_WIDTH"`
	ProductHeight      int64      `json:"PRODUCT_HEIGHT"`
	ProductType        string     `json:"PRODUCT_TYPE"`
	OrderPayment       int        `json:"ORDER_PAYMENT"`
	OrderService       string     `json:"ORDER_SERVICE"`
	OrderServiceAdd    string     `json:"ORDER_SERVICE_ADD"`
	OrderVoucher       string     `json:"ORDER_VOUCHER"`
	OrderNote          string     `json:"ORDER_NOTE"`
	MoneyCollection    float64    `json:"MONEY_COLLECTION"` // COD
	MoneyTotalFee      int64      `json:"MONEY_TOTALFEE"`
	MoneyFeeCod        int64      `json:"MONEY_FEECOD"`
	MoneyFeeVas        int64      `json:"MONEY_FEEVAS"`
	MoneyFeeInsurance  int64      `json:"MONEY_FEEINSURANCE"`
	MoneyFee           int64      `json:"MONEY_FEE"`
	MoneyFeeOther      int64      `json:"MONEY_FEEOTHER"`
	MoneyTotalVat      int64      `json:"MONEY_TOTALVAT"`
	MoneyTotal         int64      `json:"MONEY_TOTAL"`
	ListItem           []*VTPItem `json:"LIST_ITEM"`
}

type VTPItem struct {
	ProductName     string  `json:"PRODUCT_NAME"`
	ProductPrice    float64 `json:"PRODUCT_PRICE"`
	ProductWeight   float64 `json:"PRODUCT_WEIGHT"`
	ProductQuantity int64   `json:"PRODUCT_QUANTITY"`
}

type UpdateTrackingVTP struct {
	Type        *enum.VTPUpdateTrackingStatusValue `json:"TYPE"`
	OrderNumber int64                              `json:"ORDER_NUMBER"`
	Note        string                             `json:"NOTE"`
	Date        string                             `json:"DATE"`
}

type VTPCallback struct {
	Data  *DataViettel `json:"DATA"`
	Token string       `json:"TOKEN"`
}

type DataViettel struct {
	OrderNumber      string               `json:"ORDER_NUMBER"`
	OrderReference   string               `json:"ORDER_REFERENCE"`
	OrderStatusDate  string               `json:"ORDER_STATUSDATE"`
	OrderStatus      *enum.VTPStatusValue `json:"ORDER_STATUS"`
	Note             string               `json:"NOTE"`
	MoneyCollection  float64              `json:"MONEY_COLLECTION"`
	MoneyFeeCOD      float64              `json:"MONEY_FEECOD"`
	MoneyTotal       float64              `json:"MONEY_TOTAL"`
	ExpectedDelivery string               `json:"EXPECTED_DELIVERY"`
	ProductWeight    float64              `json:"PRODUCT_WEIGHT"`
	OrderService     string               `json:"ORDER_SERVICE"`
}

type GetVTPShippingFee struct {
	ProductWeight    int64  `json:"PRODUCT_WEIGHT"`
	ProductPrice     int64  `json:"PRODUCT_PRICE"`
	MoneyCollection  int64  `json:"MONEY_COLLECTION"`
	OrderService     string `json:"ORDER_SERVICE"`
	SenderProvince   int64  `json:"SENDER_PROVINCE"`
	SenderDistrict   int64  `json:"SENDER_DISTRICT"`
	ReceiverProvince int64  `json:"RECEIVER_PROVINCE"`
	ReceiverDistrict int64  `json:"RECEIVER_DISTRICT"`
	ProductType      string `json:"PRODUCT_TYPE"`
	NationalType     int64  `json:"NATIONAL_TYPE"`
	Type             int    `json:"TYPE"`
}
