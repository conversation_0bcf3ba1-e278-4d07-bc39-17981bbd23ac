package bin

import (
	"encoding/json"
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"go.mongodb.org/mongo-driver/bson"
)

func (j *ExecutorJob) completeBinOrder() {
	j.Job.SetTopicConsumer(defaultTopic, func(item *job.JobItem) (err error) {
		var dataByte []byte
		dataByte, err = bson.Marshal(item.Data)
		if err != nil {
			return
		}

		updateHubOrderRequest := request.UpdateHubOrder{}
		err = bson.Unmarshal(dataByte, &updateHubOrderRequest)
		if err != nil {
			return
		}

		filter := bson.M{}
		filter["reference_code"] = updateHubOrderRequest.ReferenceCode
		filter["hub_code"] = updateHubOrderRequest.HubCode
		getHubShippingOrder := model.HUBShippingOrderDB.QueryOne(filter)

		if getHubShippingOrder.Status != common.APIStatus.Ok {
			return nil
		}
		hubShippingOrder := getHubShippingOrder.Data.([]*model.HubShippingOrder)[0]

		if updateHubOrderRequest.Status != nil {
			// Check shipping order type
			shippingOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
				"reference_code": updateHubOrderRequest.ReferenceCode,
			})

			if shippingOrderRaw.Status != common.APIStatus.Ok {
				syncDataByte, _ := json.Marshal(hubShippingOrder)
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "NOT_FOUND_SHIPPING_ORDER",
					Title:   fmt.Sprintf("Không tìm thấy shipping order cho đơn %v", updateHubOrderRequest.ReferenceCode),
					Message: string(syncDataByte),
				})
				return nil
			}

			shippingOrder := shippingOrderRaw.Data.([]*model.ShippingOrder)[0]

			// Kiểm tra Hub nguồn
			getHub := model.HubDB.QueryOne(bson.M{"code": updateHubOrderRequest.HubCode})
			hub := getHub.Data.([]*model.Hub)[0]
			var failProducts []string
			// Chỉ update sang complete khi nhận luân chuyển sang hub nguồn và loại đơn là lấy hàng
			if hub.WarehouseReferenceCode != "" &&
				shippingOrder.ShippingType != nil &&
				*shippingOrder.ShippingType == enum.ShippingOrderType.PICKUP &&
				hubShippingOrder.Status != nil &&
				*hubShippingOrder.Status == enum.HubShippingOrderStatus.STORING {
				for _, product := range updateHubOrderRequest.Products {
					// Kiểm tra trạng thái sản phẩm
					if product.Status != nil && *product.Status == enum.ProductStatus.STORING {
						_, err := client.Services.WarehouseCoreClient.ResetLocationWarehouse(hub.WarehouseReferenceCode, product.SKU)
						// Kiểm tra sản phẩm có tồn tại bên WH
						if err != nil {
							failProducts = append(failProducts, product.SKU)
						}
					}
				}

				// Nếu có các đơn BIN bị lỗi => thông báo các đơn (BIN) bị lỗi qua telegram
				if len(failProducts) > 0 {
					err = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
						CmdType: "UNABLE_RESET_LOCATION",
						Title:   "CANT RESET LOCATION BIN",
						Message: fmt.Sprintf("%v", failProducts),
					})
				}

				// Cập nhật trạng thái đơn => COMPLETED
				referenceCodes := []string{updateHubOrderRequest.ReferenceCode}
				err := client.Services.TransportingClient.CompleteShippingOrder(referenceCodes)
				if err != nil {
					err = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
						CmdType: "UNABLE_COMPLETE_SHIPPING_ORDER",
						Title:   "CANT COMPLETE SHIPPING ORDER",
						Message: fmt.Sprintf("%v", failProducts),
					})
				}
			}
		}

		return nil
	})
}
