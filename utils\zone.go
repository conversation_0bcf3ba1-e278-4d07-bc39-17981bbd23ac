package utils

import "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"

func DistrictsToDistrictArea(districts []*model.District) (districtArea []*model.DistrictArea) {
	for _, district := range districts {
		districtArea = append(districtArea, &model.DistrictArea{
			DistrictCode: district.Code,
			DistrictName: district.Name,
			WardArea:     nil,
		})
	}
	return
}

func WardsToWardArea(wards []*model.Ward) (wardArea []*model.WardArea, wardIndexes []string) {
	for _, ward := range wards {
		wardArea = append(wardArea, &model.WardArea{
			WardCode: ward.Code,
			WardName: ward.Name,
			Extra:    nil,
		})
		wardIndexes = append(wardIndexes, ward.Code)
	}
	return
}
func RemoveWardFromWards(wardCode string, wards []*model.WardArea) (newWards []*model.WardArea) {
	for _, ward := range wards {
		if ward.WardCode != wardCode {
			newWards = append(newWards, ward)
		}
	}
	return newWards
}

func RemoveDistrictFromDistricts(districtCode string, districts []*model.DistrictArea) (newDistricts []*model.DistrictArea) {
	for _, district := range districts {
		if district.DistrictCode != districtCode {
			newDistricts = append(newDistricts, district)
		}
	}
	return newDistricts
}

func RemoveProvinceFromProvinces(provinceCode string, provinces []*model.ProvinceArea) (newProvinces []*model.ProvinceArea) {
	for _, province := range provinces {
		if province.ProvinceCode != provinceCode {
			newProvinces = append(newProvinces, province)
		}
	}
	return newProvinces
}
func IsDupProvince(zone *model.Zone) bool {
	hash := make(map[string]bool)
	for _, province := range zone.Provinces {
		if hash[province.ProvinceCode] {
			return true
		}
		hash[province.ProvinceCode] = true
	}
	return false
}
