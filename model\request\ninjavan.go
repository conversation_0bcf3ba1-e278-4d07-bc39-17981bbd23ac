package request


type BookNinjaVanRequest struct {
	ServiceType             string `json:"service_type"`
	ServiceLevel            string `json:"service_level"`
	RequestedTrackingNumber string `json:"requested_tracking_number"`
	Reference               *NinjaVanReference `json:"reference"`
	From                    *NinjaVanShippingInfo `json:"from"`
	To                      *NinjaVanShippingInfo `json:"to"`
	ParcelJob               *NinjaVanParcel `json:"parcel_job"`
}

type NinjaVanReference struct {
	MerchantOrderNumber string
}

type NinjaVanShippingInfo struct {
	Name            string           `json:"name"`
	PhoneNumber     string           `json:"phone_number"`
	Email           string           `json:"email"`
	Address         *NinjaVanAddress `json:"address"`
	CollectionPoint string           `json:"collection_point"`
}

type NinjaVanAddress struct {
	City        string  `json:"city"`
	Province    string  `json:"province"`
	Postcode    string  `json:"postcode"`
	Township    string  `json:"township"`
	District    string  `json:"district"`
	State       string  `json:"state"`
	Area        string  `json:"area"`
	Subdivision string  `json:"subdivision"`
	SubDistrict string  `json:"sub_district"`
	Ward        string  `json:"ward"`
	Address1    string  `json:"address1"`
	Address2    string  `json:"address2"`
	Country     string  `json:"country"`
	Latitude    float64 `json:"latitude"`
	Longitude   float64 `json:"longitude"`
	AddressType string  `json:"address_type"`
}

type NinjaVanParcel struct {
	AllowSelfCollection  bool                  `json:"allow_self_collection"`
	IsPickupRequired     bool                  `json:"is_pickup_required"`
	PickupAddress        *NinjaVanShippingInfo `json:"pickup_address"`
	PickupAddressId      string                `json:"pickup_address_id"`
	PickupServiceType    string                `json:"pickup_service_type"`
	PickupServiceLevel   string                `json:"pickup_service_level"`
	PickupDate           string                `json:"pickup_date"`
	PickupTimeslot       *NinjaVanTimeSlot     `json:"pickup_timeslot"`
	PickupApproxVolume   string                `json:"pickup_approx_volume"`
	PickupInstructions   string                `json:"pickup_instructions"`
	DeliveryStartDate    string                `json:"delivery_start_date"`
	DeliveryTimeSlot     *NinjaVanTimeSlot     `json:"delivery_time_slot"`
	DeliveryInstructions string                `json:"delivery_instructions"`
	Items                []*NinjaVanItem       `json:"items"`
	AllowWeekendDelivery bool                  `json:"allow_weekend_delivery"`
	CashOnDelivery       float64               `json:"cash_on_delivery"`
	InsuredValue         float64               `json:"insured_value"`
	Dimensions           *NinjaVanPackageInfo  `json:"dimensions"`
}

type NinjaVanTimeSlot struct {
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
	TimeZone  string `json:"time_zone"`
}

type NinjaVanItem struct {
	ItemDescription string `json:"item_description"`
	Quantity        int64  `json:"quantity"`
	IsDangerousGood bool   `json:"is_dangerous_good"`
}

type NinjaVanPackageInfo struct {
	Weight float64 `json:"weight"`
	Height float64 `json:"height"`
	Width  float64 `json:"width"`
	Length float64 `json:"length"`
}
