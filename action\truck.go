package action

import (
	"regexp"
	"sync"
	"time"

	"github.com/google/uuid"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// GetTruck func
func GetTruck(query *model.Truck, offset, limit int64, getTotal bool) *common.APIResponse {
	var filter = bson.M{}
	if query.TruckId > 0 {
		filter["truck_id"] = query.TruckId
	}

	if query.Status != nil {
		filter["status"] = query.Status
	}

	if query.LicensePlate != "" {
		filter["license_plate"] = query.LicensePlate
	}

	if query.Keyword != "" {
		filter["keyword"] = bson.M{
			"$regex": utils.ToRawText(query.Keyword),
		}
	}

	result := model.TruckDB.Query(
		filter,
		offset,
		limit,
		&bson.M{"_id": -1})
	if getTotal {
		countResult := model.TruckDB.Count(filter)
		result.Total = countResult.Total
	}

	return result
}

// SaveTruck action
func SaveTruck(input *model.Truck) *common.APIResponse {
	if input.TruckId == 0 {
		input.TruckId = model.GenId("TRUCK_ID")
	}

	if input.LicensePlate == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "INVALID_LICENSE_PLATE",
			Message:   "Biển số xe không được để trống",
		}
	} else {
		regexPattern := regexp.MustCompile("^[0-9]{2}[A-Z]{0,2}[0-9]?-[0-9]{2,3}[.]?[0-9]{2}$")
		if !regexPattern.MatchString(input.LicensePlate) {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				ErrorCode: "INVALID_LICENSE_PLATE",
				Message:   "Biển số xe không đúng định dạng",
			}
		}
	}

	if input.TruckType == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "INVALID_TRUCK_TYPE",
			Message:   "Loại xe không được để trống",
		}
	}

	if input.Brand == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "INVALID_BRAND_NAME",
			Message:   "Thương hiệu xe không được để trống",
		}
	}

	if input.RegistrationDeadline == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "INVALID_REGISTRATION_DEADLINE",
			Message:   "Hạn đăng kiểm xe không được để trống",
		}
	}

	if input.YearOfManufacture > 0 {
		if input.YearOfManufacture < 1980 || input.YearOfManufacture > int64(time.Now().Year()) {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				ErrorCode: "INVALID_YEAR_OF_MANUFACTURE",
				Message:   "Năm sản xuất không hợp lệ",
			}
		}
	}

	if input.Weight < 100 || input.Weight > 20000 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "INVALID_WEIGHT",
			Message:   "Tải trọng xe phải trong khoảng 100kg -> 20,000 kg",
		}
	}

	getExistLicensePlate := model.TruckDB.Count(bson.M{"license_plate": input.LicensePlate})
	if getExistLicensePlate.Total > 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Biển số xe " + input.LicensePlate + " đã tồn tại",
		}
	}

	if input.BodyWeight == 0 || input.BodyLength == 0 || input.BodyWidth == 0 ||
		input.Weight == 0 || input.Height == 0 || input.Width == 0 ||
		input.Length == 0 || input.FuelNorms == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "INVALID_TECHNICAL_INFO",
			Message:   "Thông số kỹ thật của xe không được để trống",
		}
	}

	input.Keyword, _ = utils.GenKeyword(input.TruckId, input.LicensePlate, input.Brand, input.TruckType)
	input.VersionNo = uuid.New().String()
	input.Status = &enum.TruckStatus.AVAILABLE
	createResult := model.TruckDB.UpdateOne(bson.M{
		"truck_id": input.TruckId,
	}, input, &options.FindOneAndUpdateOptions{
		Upsert: &enum.True,
	})

	if createResult.Status == common.APIStatus.Ok || createResult.Status == common.APIStatus.NotFound {
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Tạo xe thành công.",
		}
	}

	return createResult
}

// UpdateTruck action
func UpdateTruck(updater *model.Truck) *common.APIResponse {
	if updater.TruckId < 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "INVALID_TRUCK_ID",
			Message:   "ID xe không hợp lệ",
		}
	}

	regexPattern := regexp.MustCompile("^[0-9]{2}[A-Z]{0,2}[0-9]?-[0-9]{2,3}[.]?[0-9]{2}$")
	if !regexPattern.MatchString(updater.LicensePlate) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "INVALID_LICENSE_PLATE",
			Message:   "Biển số xe không đúng định dạng",
		}
	}

	// Không cho phép cập nhật biển số xe
	getExistingLicense := model.TruckDB.QueryOne(bson.M{"truck_id": updater.TruckId})
	if getExistingLicense.Status == common.APIStatus.Ok {
		existTruck := getExistingLicense.Data.([]*model.Truck)[0]
		if existTruck.LicensePlate != updater.LicensePlate {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				ErrorCode: "INVALID_LICENSE_PLATE",
				Message:   "Biển số xe không được thay đổi",
			}
		}
	}

	if updater.YearOfManufacture < 1980 || updater.YearOfManufacture > int64(time.Now().Year()) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "INVALID_YEAR_OF_MANUFACTURE",
			Message:   "Năm sản xuất không hợp lệ",
		}
	}

	if updater.Weight < 100 || updater.Weight > 20000 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "INVALID_WEIGHT",
			Message:   "Tải trọng xe phải trong khoảng 100kg -> 20,000 kg",
		}
	}

	if updater.TruckType == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "INVALID_TRUCK_TYPE",
			Message:   "Loại xe không được để trống",
		}
	}

	if updater.Brand == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "INVALID_BRAND_NAME",
			Message:   "Thương hiệu xe không được để trống",
		}
	}

	if updater.RegistrationDeadline == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "INVALID_REGISTRATION_DEADLINE",
			Message:   "Hạn đăng kiểm xe không được để trống",
		}
	}

	if updater.BodyWeight == 0 || updater.BodyLength == 0 || updater.BodyWidth == 0 ||
		updater.Weight == 0 || updater.Height == 0 || updater.Width == 0 ||
		updater.Length == 0 || updater.FuelNorms == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "INVALID_TECHNICAL_INFO",
			Message:   "Thông số kỹ thật của xe không được để trống",
		}
	}

	updater.Keyword, _ = utils.GenKeyword(updater.TruckId, updater.LicensePlate, updater.Brand, updater.TruckType)
	afterOption := options.After
	res := model.TruckDB.UpdateOne(bson.M{
		"truck_id": updater.TruckId,
	},
		updater,
		&options.FindOneAndUpdateOptions{
			ReturnDocument: &afterOption,
		})
	return res
}

func CountTruckByStatus(query *model.Truck) *common.APIResponse {
	type CountTruckModel struct {
		Status   string `json:"status,omitempty" bson:"_id,omitempty"`
		Quantity int64  `json:"quantity" bson:"total"`
	}
	filter := bson.D{}
	if query.LicensePlate != "" {
		filter = append(filter, bson.E{
			Key:   "license_plate",
			Value: query.LicensePlate,
		})
	}

	statuses := utils.EnumToStringSlice(*enum.TruckStatus)
	if query.Status != nil && *query.Status != "" {
		statuses = []string{string(*query.Status)}
	}

	result := make([]*CountTruckModel, len(statuses))
	waitGroup := new(sync.WaitGroup)
	waitGroup.Add(len(statuses))

	for index, status := range statuses {
		go func(i int, s string, f bson.D, r []*CountTruckModel, wg *sync.WaitGroup) {
			defer wg.Done()
			// Don't need to deep clone filter because pass variable to function by value means it is already clone the filter
			copyFilter := append(f, bson.E{
				Key:   "status",
				Value: s,
			})

			countResult := model.TruckDB.Count(copyFilter)
			r[i] = &CountTruckModel{
				Status:   s,
				Quantity: countResult.Total,
			}
		}(index, status, filter, result, waitGroup)
	}

	waitGroup.Wait()

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Thông tin số lượng xe luân chuyển theo trạng thái.",
		Data:    result,
	}
}
