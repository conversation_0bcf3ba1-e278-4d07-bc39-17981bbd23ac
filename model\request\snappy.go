package request

import (
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
)

type Snappy struct {
	BalanceAdjustment  int64                   `json:"balance_adjustment"`
	CurrentStatusEn    *enum.SnappyStatusValue `json:"current_status_en"`
	CurrentStatus      string                  `json:"current_status"`
	CustomId           string                  `json:"custom_id"`
	CustomerTrackingId string                  `json:"customer_tracking_id"`
	Department         *Address                `json:"department"`
	From               *Address                `json:"from"`
	To                 *Address                `json:"to"`
	Id                 string                  `json:"id"`
	InsertedAt         string                  `json:"inserted_at"`
	LastUpdate         *SnappyUpdate           `json:"last_update"`
	PackageInfo        *SnappyPackageInfo      `json:"package_info"`
	Services           *SnappyService          `json:"services"`
	StatusColor        string                  `json:"status_color"`
	ShortId            int64                   `json:"short_id"`
	UpdatedAt          string                  `json:"updated_at"`
	Updates            []*SnappyUpdate         `json:"updates"`
}

type Address struct {
	Address     string `json:"address"`
	CommuneId   string `json:"commune_id"`
	DistrictId  string `json:"district_id"`
	FullAddress string `json:"full_address"`
	Id          string `json:"id"`
	Name        string `json:"name"`
	PhoneNumber string `json:"phone_number"`
	ProvinceId  string `json:"province_id"`
}

type SnappyPackageInfo struct {
	Items       []*SnappyItem `json:"items"`
	Snippet     string        `json:"snippet"`
	TotalWeight int64         `json:"total_weight"`
}

type SnappyUpdate struct {
	Location    string  `json:"location"`
	Note        string  `json:"note"`
	Status      string  `json:"status"`
	StatusColor string  `json:"status_color"`
	UpdatedAt   string  `json:"updated_at"`
	Editor      *Editor `json:"editor"`
	LastEditor  *Editor `json:"last_editor"`
}

type Editor struct {
	AvatarUrl   string `json:"avatar_url"`
	Email       string `json:"email"`
	FbId        string `json:"fb_id"`
	Id          string `json:"id"`
	Name        string `json:"name"`
	Permission  int64  `json:"permission"`
	PhoneNumber string `json:"phone_number"`
}

type BookSnappy struct {
	BusinessId          int64         `json:"business_id"`
	BusinessAddressId   string        `json:"business_address_id"`
	PickupNote          string        `json:"pickup_note"`
	DeliveryNote        string        `json:"delivery_note"`
	ReceiverName        string        `json:"receiver_name"`
	ReceiverPhoneNumber string        `json:"receiver_phone_number"`
	ReceiverFullAddress string        `json:"receiver_full_address"`
	IsSplitPkg          bool          `json:"is_split_pkg"`
	Items               []*SnappyItem `json:"items"`
	ServiceName         string        `json:"service_name"`
	COD                 int64         `json:"cod"`
	Value               int64         `json:"value"`
	Note                string        `json:"note"`
	CustomerTrackingId  string        `json:"customer_tracking_id"`
	IsReceiverPay       bool          `json:"is_receiver_pay"`
	IsAllowCheckingGood bool          `json:"is_allow_checking_good"`
	IsAllowTryOut       bool          `json:"is_allow_try_out"`
	ShopNote            string        `json:"shop_note"`
}

type SnappyService struct {
	CODService struct {
		Amount       int64 `json:"amount"`
		Cost         int64 `json:"cost"`
		IsSaveLogCod bool  `json:"is_save_log_cod"`
		UseCod       bool  `json:"use_cod"`
	} `json:"cod_service"`
	InsuranceCost               int64  `json:"insurance_cost"`
	ReturnCost                  int64  `json:"return_cost"`
	IsAllowCheckingGood         bool   `json:"is_allow_checking_good"`
	IsAllowTryOut               bool   `json:"is_allow_try_out"`
	IsReceiverPay               bool   `json:"is_receiver_pay"`
	IsSaveLogInsuranceCost      bool   `json:"is_save_log_insurance_cost"`
	IsSaveLogReturn             bool   `json:"is_save_log_return"`
	IsSaveLogShippingCost       bool   `json:"is_save_log_shipping_cost"`
	IsSaveLogShippingCostOfShop bool   `json:"is_save_log_shipping_cost_of_shop"`
	Name                        string `json:"name"`
	NameVi                      string `json:"name_vi"`
	ShippingCost                int64  `json:"shipping_cost"`
	ShippingCostOfShop          int64  `json:"shipping_cost_of_shop"`
}

type SnappyItem struct {
	Name     string `json:"name"`
	Weight   int64  `json:"weight"`
	Quantity int64  `json:"quantity"`
}

type CancelSnappyRequest struct {
	AccessToken string `json:"access_token"`
	TrackingId  string `json:"tracking_id"`
	Note        string `json:"note"`
}
