package api

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
)

// CreateHubShippingOrder api
func CreateHubShippingOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.HubShippingOrder
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.CreateHubShippingOrder(&input))

}

// UpdateHubShippingOrder api
func UpdateHubShippingOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.HubShippingOrder
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}

	return resp.Respond(action.UpdateHubShippingOrder(&input, UserInfo.Account.AccountID))
}

// DeleteHubOrder api
func DeleteHubOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.HubShippingOrder
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.DeleteHubOrder(&input))
}

// UpdateStatusHubShippingOrder api
func UpdateStatusHubShippingOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	type myRequest struct {
		ReferenceCode           string                            `json:"referenceCode"`
		HubCode                 string                            `json:"hubCode"`
		Status                  *enum.HubShippingOrderStatusValue `json:"status"`
		ReasonCode              string                            `json:"reasonCode"`
		Reason                  string                            `json:"reason"`
		ExtraInfo               map[string]interface{}            `json:"extraInfo"`
		RescheduledDeliveryTime int64                             `json:"rescheduledDeliveryTime"`
		RescheduledPickupTime   int64                             `json:"rescheduledPickupTime"`
		VerificationCode        string                            `json:"verificationCode"`
		Images                  []string                          `json:"images"`
	}
	var input myRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}

	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	return resp.Respond(action.UpdateStatusHubShippingOrder(&model.HubShippingOrder{
		ReferenceCode:           input.ReferenceCode,
		Status:                  input.Status,
		ReasonCode:              input.ReasonCode,
		Reason:                  input.Reason,
		HUBCode:                 input.HubCode,
		ExtraInfo:               input.ExtraInfo,
		RescheduledDeliveryTime: input.RescheduledDeliveryTime,
		RescheduledPickupTime:   input.RescheduledPickupTime,
		VerificationCode:        input.VerificationCode,
		Images:                  input.Images,
	}, UserInfo))
}

// AdditionNoteHubOrder api
func AdditionNoteHubOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	type myRequest struct {
		Note          string `json:"note"`
		ReferenceCode string `json:"referenceCode"`
	}
	var input myRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	// Check UpdatedBy
	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}
	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}
	return resp.Respond(action.AdditionNoteHubOrder(input.Note, input.ReferenceCode, UserInfo.Account.AccountID))
}

// AssignHubShippingOrder api
func AssignHubShippingOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	type myRequest struct {
		ListReferenceCode []string `json:"listReferenceCode"`
		DriverName        string   `json:"driverName"`
		DriverId          int      `json:"driverId"`
		HubCode           string   `json:"hubCode"`
		IsAutoAssign      *bool    `json:"isAutoAssign"`
	}
	var input myRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	// Check UpdatedBy
	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}
	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}
	return resp.Respond(action.AssignHubShippingOrder(
		input.ListReferenceCode,
		input.DriverName,
		input.HubCode,
		input.DriverId,
		UserInfo.Account.AccountID,
		input.IsAutoAssign))
}

// CountMyHubShippingOrder api
func CountMyHubShippingOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q = req.GetParam("q")
	)

	var query *request.HubShippingOrderQuery
	if q == "" {
		q = "{}"
	}
	err := json.Unmarshal([]byte(q), &query)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}
	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}
	query.DriverId = UserInfo.Account.AccountID
	return resp.Respond(action.CountHubShippingOrderByStatus(query))
}

// CountShippingOrder api
func CountShippingOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q = req.GetParam("q")
	)

	var query *request.HubShippingOrderQuery
	if q == "" {
		q = "{}"
	}
	err := json.Unmarshal([]byte(q), &query)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.CountHubShippingOrderByStatus(query))
}

// CountHubOrderWithinDay api
func CountHubOrderWithinDay(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q = req.GetParam("q")
	)

	var query *request.HubShippingOrderQuery
	if q == "" {
		q = "{}"
	}
	err := json.Unmarshal([]byte(q), &query)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}
	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}
	return resp.Respond(action.CountHubOrderWithinDay(query, UserInfo.Account.AccountID))
}

// StatisticHubOrderWithinDay api
func StatisticHubOrderWithinDay(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q = req.GetParam("q")
	)

	var query *request.HubShippingOrderQuery
	if q == "" {
		q = "{}"
	}
	err := json.Unmarshal([]byte(q), &query)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.StatisticHubOrderWithinDay(query))
}

// GetHubShippingOrder api
func GetHubShippingOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var str = req.GetParam("referenceCode")
	if str != "" {
		return resp.Respond(action.GetShippingOrder(str))
	}

	var (
		q        = req.GetParam("q")
		offset   = int64(sdk.ParseInt(req.GetParam("offset"), 0))
		limit    = int64(sdk.ParseInt(req.GetParam("limit"), 100))
		getTotal = req.GetParam("getTotal") == "true"
	)
	if q == "" {
		q = "{}"
	}

	var sort request.FilterSort
	sortStr := req.GetParam("s")
	if sortStr == "" {
		sortStr = "{}"
	}

	_ = json.Unmarshal([]byte(sortStr), &sort)

	var query request.HubShippingOrderQuery
	err := json.Unmarshal([]byte(q), &query)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data." + err.Error(),
			ErrorCode: "JSON_INVALID",
		})
	}

	return resp.Respond(action.GetHubShippingOrder(&query, &sort, offset, limit, getTotal))
}

// GetMyHubShippingOrder api
func GetMyHubShippingOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q        = req.GetParam("q")
		offset   = int64(sdk.ParseInt(req.GetParam("offset"), 0))
		limit    = int64(sdk.ParseInt(req.GetParam("limit"), 100))
		getTotal = req.GetParam("getTotal") == "true"
	)
	if q == "" {
		q = "{}"
	}
	var query request.HubShippingOrderQuery
	err := json.Unmarshal([]byte(q), &query)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	var sort request.FilterSort
	sortStr := req.GetParam("s")
	if sortStr == "" {
		sortStr = "{}"
	}

	_ = json.Unmarshal([]byte(sortStr), &sort)

	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}
	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}
	query.DriverId = UserInfo.Account.AccountID
	return resp.Respond(action.GetHubShippingOrder(&query, &sort, offset, limit, getTotal))
}

func GetMyTrip(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q        = req.GetParam("q")
		offset   = int64(sdk.ParseInt(req.GetParam("offset"), 0))
		limit    = int64(sdk.ParseInt(req.GetParam("limit"), 100))
		getTotal = req.GetParam("getTotal") == "true"
	)
	if q == "" {
		q = "{}"
	}
	var query request.HubShippingOrderQuery
	err := json.Unmarshal([]byte(q), &query)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	var sort request.FilterSort
	sortStr := req.GetParam("s")
	if sortStr == "" {
		sortStr = "{}"
	}

	_ = json.Unmarshal([]byte(sortStr), &sort)

	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}
	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}
	query.DriverId = UserInfo.Account.AccountID
	return resp.Respond(action.GetMyTrip(&query, &sort, offset, limit, getTotal))

}

// AuditHubOrder api
func AuditHubOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.AuditHubOrderStatus())
}

// MigrateDeliveredAndCompletedTime api
func MigrateDeliveredAndCompletedTime(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.MigrateDeliveredAndCompletedTime())
}

// MigrateSOCheckPointTime api
func MigrateSOCheckPointTime(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.MigrateSOCheckPointTime())
}

func AdjustPackage(req sdk.APIRequest, resp sdk.APIResponder) error {
	type myRequest struct {
		ReferenceCode string                         `json:"referenceCode"`
		HubCode       string                         `json:"hubCode"`
		NumOfPackages int64                          `json:"numOfPackages"`
		ReferenceType enum.ReferenceCodeTypeValue    `json:"referenceType"`
		Action        enum.AdjustReceivePackageValue `json:"action"`
	}

	var input myRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.AdjustReceivedPackage(
		input.ReferenceCode, input.HubCode,
		input.NumOfPackages, input.ReferenceType, input.Action))
}

func UpdateCheckInPickUp(req sdk.APIRequest, resp sdk.APIResponder) error {
	type myRequest struct {
		Session             *request.CheckInPickup  `json:"session"`
		Action              enum.CheckInActionValue `json:"action"`
		CheckInBy           int                     `json:"checkInBy"`
		CustomerInfo        model.Customer          `json:"customer"`
		DesWhCode           string                  `json:"desWhCode,omitempty"`
		ParentReferenceCode string                  `json:"parentReferenceCode"`
		NumPack             int                     `json:"numPack"`
		ReceiveSessionCode  string                  `json:"receiveSessionCode"`
		HubCode             string                  `json:"hubCode"`
		WarehouseCode       string                  `json:"warehouseCode"`
		ReferenceCode       string                  `json:"referenceCode"`
		Note                string                  `json:"note"`
	}

	var input myRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	if input.Action == enum.CheckInAction.DONE {
		return resp.Respond(action.DoneCheckInPickup(input.Session, input.CheckInBy))
	}

	if input.Action == enum.CheckInAction.RESET || input.Action == enum.CheckInAction.RESET_LOST {
		return resp.Respond(action.ResetCheckInPickup(input.Session, input.Action))
	}

	if input.Action == enum.CheckInAction.EXTEND {
		return resp.Respond(action.ExtendPickUp(input.Session))
	}

	if input.Action == enum.CheckInAction.CHECKIN {
		return resp.Respond(action.CheckinPoPackage(
			input.ParentReferenceCode,
			input.ReceiveSessionCode,
			input.HubCode,
			input.WarehouseCode,
			input.DesWhCode,
			input.ReferenceCode,
			input.Note,
			input.NumPack,
			input.CheckInBy))
	}

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Invalid,
		Message: "Loại hành động không hợp lệ",
	})
}

func MigrateReconcileRiderHUB(req sdk.APIRequest, resp sdk.APIResponder) error {
	type myRequest struct {
		ReferenceCodes []string `json:"referenceCodes"`
	}

	var input myRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.MigrateReconcileRiderHUB(input.ReferenceCodes))
}

func IdentifyLateLeadTimeHubOrderTest(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.HubShippingOrder
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.IdentifyLateLeadTimeHubOrderTest(input))
}

func UpdateAddress(req sdk.APIRequest, resp sdk.APIResponder) error {
	// TODO: add update from address if needed here
	var input request.UpdateAddressRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}
	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	return resp.Respond(action.UpdateAddress(&input, UserInfo.Account.AccountID))
}

func UpdateNumPack(req sdk.APIRequest, resp sdk.APIResponder) error {
	type myRequest struct {
		ReferenceCode string  `json:"referenceCode"`
		NumPack       int64   `json:"numPack"`
		Weight        float64 `json:"weight"`
	}

	var input myRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}
	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	return resp.Respond(action.UpdateNumPack(input.ReferenceCode, input.NumPack, input.Weight))
}
