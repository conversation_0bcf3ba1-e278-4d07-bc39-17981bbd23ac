package warehouse_core

import (
	"encoding/json"
	"fmt"
	"log"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
)

func (cli *Client) SendMessage(body *request.SendMessageTelegram) error {
	params := map[string]string{}

	var res *client.RestResult
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, body, pathSendTelegramMessage, nil)
	if err != nil {
		log.Print(err)
		return err
	}

	type myResponse struct {
		Status  string `json:"status"`
		Code    string `json:"code"`
		Message string `json:"message"`
	}

	response := new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return err
	}

	if response.Status != common.APIStatus.Ok {
		return fmt.Errorf("%v", response.Message)
	}

	return nil
}
