package customer

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/utils"
	"go.mongodb.org/mongo-driver/bson"
)

func (j *ExecutorJob) createCustomerConsume() {
	j.Job.SetTopicConsumer(createCustomerTopic, func(item *job.JobItem) (err error) {
		var dataByte []byte
		dataByte, err = bson.Marshal(item.Data)
		if err != nil {
			return
		}

		var reqData model.Customer
		err = bson.Unmarshal(dataByte, &reqData)
		if err != nil {
			return
		}

		if reqData.Code == "" {
			_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
				CmdType: "EMPTY_CUSTOMER_CODE",
				Title:   "Không thể tạo khách hàng",
				Message: "Không thể tạo khách hàng với mã khách hàng trống",
			})
			return nil
		}

		customerRaw := model.CustomerDB.QueryOne(bson.M{
			"code": reqData.Code,
		})

		if customerRaw.Status == common.APIStatus.Ok {
			return nil
		}

		reqData.CustomerId = model.GenId("CUSTOMER_ID")
		newKeyword, keyErr := utils.GenKeyword(reqData.Name, reqData.Code, reqData.CustomerId, reqData.Phone)
		if keyErr != nil {
			return nil
		}
		reqData.Keyword = newKeyword

		model.CustomerDB.Create(reqData)

		return nil
	})
}
