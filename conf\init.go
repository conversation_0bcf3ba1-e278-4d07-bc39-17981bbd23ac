package conf

import (
	"fmt"

	"gitlab.buymed.tech/sdk/golang/configuration"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
)

type config struct {
	Env      string
	Protocol string
	Version  string

	MainDBConf     configuration.Database
	LogDBConf      configuration.Database
	JobDBConf      configuration.Database
	ThuocsiSvcConf configuration.Service

	MapGoogleHost         string
	AhamoveOnWheelHost    string
	AddressSouth          []string
	CallExportInvoice     bool
	PartnerIDs            map[string]string
	Topics                map[string]string
	WarehouseCodes        []string
	CarriersOnePack       []int64
	ExecutorCollection    string
	ConfigCarrier         *ConfigCarrier
	Supporter             map[string]*Supporter
	GeoCodeApiKey         string
	AhamoveOnWheelApiKey  string
	DefaultHubRole        []string
	DefaultHubParentCode  string
	DefaultHubTag         []string
	ValidRegexForCode     string
	ValidRegexForPhone    string
	WebHookConfig         map[string]*WebHookConfig
	HOStatusTransition    map[string][]string
	OneSignalConfig       WebHookConfig
	DelayLateLeadTimeJob  float64
	RepeatLateLeadTimeJob int
	MaxWarningLevel       int
	// Step is configured as second
	StepWarningLevel         float64
	RiderRoleToSyncReceived  map[string]bool
	DefaultHubRoleNotify     map[string][]string
	RepeatSyncAccountTimeJob int
	DefaultShippingFee       ConfigDefaultShippingFee
	TimeZoneAsiaBangkok      string
	DefaultROCarrierId       []int64
	SaleOrderCodeToHubCode   map[string]string
	DefaultPartnerInfo       map[enum.PartnerValue]DefaultCarrierInformation
	TransportingDriverRole   string
	// Default config fee, start here
	DefaultFMPickUpFeeCode        string
	DefaultFMDropOffFeeCode       string
	DefaultEOTransportFeeCode     string
	DefaultEOPickNTransFeeCode    string
	DefaultEOPickNDeliCode        string
	DefaultEOTransNDeliCode       string
	DefaultCrossRegionDropOffFee  string
	DefaultSellerROTransNDeliCode string
	DefaultSellerROTransportCode  string
	DefaultVendorROTransNDeliCode string
	DefaultVendorROTransportCode  string
	// Default config fee, end here
	MaxStatusCount             int
	RepeatCrawlProductivity    int
	RepeatCalculateLeadTimeJob int // Recompute lead time order within 3 months
	RepeatCancelOrderJob       int
}

type ConfigDefaultShippingFee struct {
	FeePerPackage             float64
	MinWeightApplyFeePerOrder float64
	FeePerOrder               []ConfigFee
	FeeTransport              []ConfigFee
}

type ConfigFee struct {
	FromProvinceCode string
	ToProvinceCodes  []string
	Fee              float64
	FeePerPackage    float64
}

type ConfigCarrier struct {
	GHTKConfig     *GHTKConfig
	GHNConfig      *GHNConfig
	NhatTinConfig  *NhatTinConfig
	SnappyConfig   *SnappyConfig
	VTPConfig      *VTPConfig
	AhamoveConfig  *AhamoveConfig
	VNPostConfig   *VNPostConfig
	NinjavanConfig *NinjavanConfig
	BeConfig       *BeConfig
}

type AhamoveConfig struct {
	Token  string
	Domain string
	Apikey string
}

type VNPostConfig struct {
	Token  string
	Domain string
}

type GHTKConfig struct {
	Token  string
	Domain string
}

type Supporter struct {
	EmployeeId int64
	Phone      string
	Email      string
}

type VTPConfig struct {
	Token             string
	Domain            string
	PartnerId         int64
	BusinessAddressId string
}

type GHNConfig struct {
	Token  string
	Domain string
	ShopId int64
}

type NhatTinConfig struct {
	Domain    string
	ClientKey string
	SecretKey string
	PartnerId int64
}

type BeConfig struct {
	Domain string
	Proxy  string
}

type Topic struct {
	Key   string
	Value string
}

type NinjavanConfig struct {
	Domain string
	Token  string
}

type SnappyConfig struct {
	Domain            string
	AccessToken       string
	BusinessId        int64
	BusinessAddressId string
}

type WebHookConfig struct {
	PartnerCode string            `json:"partnerCode"`
	Url         string            `json:"url"`
	Header      map[string]string `json:"header"`
	Host        string            `json:"host"`
	AppID       string            `json:"appID"`
	HashKey     string            `json:"hashKey"`
}

type DefaultCarrierInformation struct {
	TplName      string
	TplServiceId int64
	TplCode      *enum.PartnerValue
}

// Config main config object
var Config *config

func init() {
	env := configuration.Env()
	version := configuration.Version()
	protocol := configuration.Protocol()
	if protocol == "" {
		protocol = "THRIFT"
	}

	thuocsiSvcConfig := configuration.Get("thuocsi-vn-client").ToServiceConfig()
	o2oSvcConfig := configuration.Get("o2o-client").ToServiceConfig()

	mainDB := configuration.Get("db").ToDatabaseConfig()
	logDB := configuration.Get("logDB").ToDatabaseConfig()
	jobDB := configuration.Get("jobDB").ToDatabaseConfig()

	defer println("init config done")

	switch env {
	case "local":
		Config = &config{
			Env:                "local",
			Protocol:           protocol,
			Version:            version,
			MapGoogleHost:      "https://maps.googleapis.com",
			AhamoveOnWheelHost: "https://ep.ahamove.com",
			Supporter: map[string]*Supporter{
				"delivery": &Supporter{
					Phone: "0364266983",
					Email: "<EMAIL>",
				},
			},
			ConfigCarrier:      &ConfigCarrier{},
			ExecutorCollection: "executor_queue",
			WarehouseCodes:     []string{"HCM", "HN", "HCM2"},
			CarriersOnePack:    []int64{19, 2},
			Topics: map[string]string{
				"tpl_callback":                          "integration_dev_tpl-callback.callback.sync_transporting_local",
				"sync_status_tpl_callback":              "integration_dev_tpl-callback.callback.sync_status_tpl_callback_local",
				"post_status_tpl_callback":              "integration_dev_tpl-callback.callback.post_status_tpl_callback_local",
				"backup":                                "backup_dev_local",
				"hand_over_transfer":                    "transporting.complete_hand_over_transfer_dev_local",
				"hub_shipping_order":                    "transporting.hub_shipping_order_local",
				"update_hub_shipping_order":             "transporting.update_hub_shipping_order_local",
				"set_current_hub_shipping_order":        "transporting.set_current_hub_shipping_order_local",
				"assign_tpl_callback":                   "transporting.assign_tpl_callback_local",
				"create_shipping_address":               "transporting.create_shipping_address_local",
				"send_callback":                         "transporting.send_callback_local",
				"assign_shipper_callback":               "transporting.assign_shipper_callback_local",
				"create_extend_shipping_order":          "transporting.create_extend_pickup_shipping_order_local",
				"create_extend_complete_handover_order": "transporting.create_extend_complete_handover_order_local",
				"complete_bin_order":                    "transporting.complete_bin_order_local",
				"update_fee_topic":                      "transporting.update_fee_topic_local",
				"checkin_inbound_to":                    "transporting.checkin_inbound_to_local",
				"done_create_handover":                  "transporting.done_create_handover_local",
				"done_receive_handover":                 "transporting.done_receive_handover_local",
				"retry_reconcile_client":                "transporting.retry_reconcile_client_local",
				"complete_handover_trip":                "transporting.complete_handover_trip_local",
				"add_order_to_reconcile":                "transporting.add_order_to_reconcile_local",
				"update_order_fee_reconcile":            "transporting.update_order_fee_reconcile_local",
				"auto_complete_trip":                    "transporting.auto_complete_trip_local",
				"customer":                              "transporting.customer_local",
				"driver_productivity":                   "transporting.driver_productivity_local",
			},
			AhamoveOnWheelApiKey: "GSWWoGla8OL6PpAUpnleW2QaLHIQ2ed0",
			GeoCodeApiKey:        "AIzaSyDTCM05gqC-iNxk_e1kA1C8RfA_lmE2Yd0",
			DefaultHubRole: []string{
				"WMS_HUB_COORDINATOR",     // WMS_HUB_COORDINATOR
				"HUB_CASH_STAFF",          // HUB_CASH_STAFF
				"HUB_IMPORT_EXPORT_STAFF", // HUB_IMPORT_EXPORT_STAFF
				"WMS_SHIPPER",             // WMS_SHIPPER
			},
			DefaultHubParentCode: "LOGISTIC",
			DefaultHubTag:        []string{"WAREHOUSE"},
			ValidRegexForCode:    `^[A-Z0-9_]+$`,
			ValidRegexForPhone:   `^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,6}$`,
			WebHookConfig: map[string]*WebHookConfig{
				"MEDX": {
					PartnerCode: "MEDX",
					Url:         "https://webhook.site/8c672a37-7f55-4478-995b-11ccc240d9c0",
					Header: map[string]string{
						"Authorization": "some string",
					},
				},
				"INTERNAL": {
					PartnerCode: "INTERNAL",
					Url:         "https://api.v2-stg.thuocsi.vn",
					Header: map[string]string{
						"Authorization": "Basic UEFSVE5FUi92MS5lcnA6MWtzOHI5Y3BBanM=",
					},
				},
				"CIRCA": {
					PartnerCode: "CIRCA",
					Url:         "https://api-dev.circa.vn/v1/invoice/collection-request/thuocsi/callback",
					HashKey:     "Ea6vS2VRqzEtKl7",
				},
				"O2O": {
					PartnerCode: "O2O",
					Url:         o2oSvcConfig.Host,
					Header: map[string]string{
						"Authorization": o2oSvcConfig.Authorization,
					},
				},
			},
			HOStatusTransition: map[string][]string{
				"READY_TO_PICK": {
					"PICKING",
				},
				"STORING": {
					"DAMAGE",
					"LOST",
					"RETURN",
					"DELIVERED",
				},
				"WAIT_TO_DELIVERY": {
					"STORING",
					"DELIVERING",
				},
				"DELIVERING": {
					"DELIVERED",
					"DELIVERY_FAIL",
				},
				"PICKING": {
					"WAIT_TO_STORING",
					"PICK_FAIL",
				},
				"DELIVERY_FAIL": {
					"STORING",
					"RETURN",
				},
				"PICK_FAIL": {
					"READY_TO_PICK",
				},
				"DELIVERED": {
					"COD_COLLECTED",
				},
				"PICKED": {
					"STORING",
				},
				"COD_COLLECTED": {
					"COMPLETED",
				},
				"RETURN": {
					"STORING",
				},
				"LOST": {
					"STORING",
				},
				"DAMAGE": {
					"STORING",
				},
			},
			OneSignalConfig: WebHookConfig{
				Host: "https://onesignal.com",
				Header: map[string]string{
					"Authorization": "Basic YmZkNjlkMzItY2E4Ny00ZmY5LTg0Y2ItYTc0ZDZkN2QzYzk1",
					"Content-Type":  "application/json",
					"Accept":        "application/json",
				},
				AppID: "06bd8666-ed4d-4512-80e0-662812ac5a3f",
			},
			DelayLateLeadTimeJob: 0,
			// After 1 minutes
			RepeatLateLeadTimeJob: 60,
			// 3 minutes
			StepWarningLevel: 60 * 3,
			// 10 times StepWarningLevel
			MaxWarningLevel: 60 * 3 * 10,
			RiderRoleToSyncReceived: map[string]bool{
				"WMS_SHIPPER":         true,
				"WMS_HUB_COORDINATOR": true,
				"HUB_MANAGEMENT":      true,
			},
			DefaultHubRoleNotify: map[string][]string{
				"HUB_MANAGEMENT": {"WMS_HUB_COORDINATOR"},
				"HUB_CASH_STAFF": {},
			},
			RepeatSyncAccountTimeJob: 60 * 3 * 1,
			DefaultShippingFee: ConfigDefaultShippingFee{
				FeePerPackage:             50000.0,
				MinWeightApplyFeePerOrder: 3000,
				FeePerOrder: []ConfigFee{
					{
						FromProvinceCode: "74",
						ToProvinceCodes: []string{
							"74",
						},
						Fee: 20000,
					},
					{
						FromProvinceCode: "74",
						ToProvinceCodes: []string{
							"79", "77", "75", "72", "70",
						},
						Fee: 25000,
					},
					{
						FromProvinceCode: "74",
						ToProvinceCodes: []string{
							"80", "82", "83", "84", "86", "87", "89", "91", "92", "93", "94", "95", "96",
						},
						Fee: 30000,
					},
					{
						FromProvinceCode: "74",
						ToProvinceCodes: []string{
							"48", "49", "51", "52", "54", "56", "58", "60", "62", "64", "66", "67", "68",
						},
						Fee: 35000,
					},
					{
						FromProvinceCode: "74",
						ToProvinceCodes: []string{
							"01", "02", "04", "06", "08", "10", "11", "12", "14", "15", "17", "19", "20", "22", "24", "25", "26", "27", "30", "31", "33", "34", "35", "36", "37", "38", "40", "42", "44", "45", "46",
						},
						Fee: 50000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes: []string{
							"31", "24", "30", "27", "38", "40", "25", "35", "33",
						},
						Fee:           15000,
						FeePerPackage: 40000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes: []string{
							"04", "17", "02", "20", "36", "37", "44", "22", "45", "14", "34", "26", "11", "06", "46", "19", "42",
						},
						Fee:           20000,
						FeePerPackage: 50000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes: []string{
							"15",
						},
						Fee:           20000,
						FeePerPackage: 40000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes:  []string{"10"},
						Fee:              20000,
						FeePerPackage:    30000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes:  []string{"12"},
						Fee:              20000,
						FeePerPackage:    40000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes:  []string{"08"},
						Fee:              20000,
						FeePerPackage:    30000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes:  []string{"75", "72", "77", "70", "79", "74"},
						Fee:              45000,
						FeePerPackage:    50000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes:  []string{"80", "82", "86", "83", "75", "84", "89", "92", "93", "95", "94", "91", "96"},
						Fee:              50000,
						FeePerPackage:    50000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes:  []string{"48", "49", "51", "52", "54", "56", "58", "60", "64", "67", "68", "62", "64"},
						Fee:              40000,
						FeePerPackage:    50000,
					},
				},
			},
			TimeZoneAsiaBangkok: "Asia/Bangkok",
			SaleOrderCodeToHubCode: map[string]string{
				"SOBD": "HUBBD",
				"SOHN": "HUBHANOI",
			},
			DefaultPartnerInfo: map[enum.PartnerValue]DefaultCarrierInformation{
				enum.Partner.GHTK: {
					TplName:      "Giao Hàng Tiết Kiệm",
					TplCode:      &enum.Partner.GHTK,
					TplServiceId: 17,
				},
				enum.Partner.VIETTEL_POST: {
					TplName:      "Viettel Post",
					TplCode:      &enum.Partner.VIETTEL_POST,
					TplServiceId: 28,
				},
			},
			TransportingDriverRole:        "LOGISTIC_TRANSPORT_DRIVER",
			DefaultFMPickUpFeeCode:        "DEFAULT_FM_PICKUP_FEE",
			DefaultFMDropOffFeeCode:       "DEFAULT_FM_DROP_OFF_FEE",
			DefaultEOTransportFeeCode:     "DEFAULT_EO_TRANSPORT_FEE",
			DefaultEOPickNTransFeeCode:    "DEFAULT_EO_PICKUP_AND_TRANSPORT_FEE",
			DefaultEOPickNDeliCode:        "DEFAULT_EO_PICKUP_AND_DELIVERY_FEE",
			DefaultEOTransNDeliCode:       "DEFAULT_EO_TRANSPORT_AND_DELIVERY_FEE",
			DefaultCrossRegionDropOffFee:  "DEFAULT_CROSS_REGION_DROP_OFF_FEE",
			DefaultSellerROTransNDeliCode: "DEFAULT_SELLER_RO_TRANSPORT_AND_DELIVERY_FEE",
			DefaultSellerROTransportCode:  "DEFAULT_SELLER_RO_TRANSPORT_FEE",
			DefaultVendorROTransNDeliCode: "DEFAULT_VENDOR_RO_TRANSPORT_AND_DELIVERY_FEE",
			DefaultVendorROTransportCode:  "DEFAULT_VENDOR_RO_TRANSPORT_FEE",
			MaxStatusCount:                200,
			RepeatCrawlProductivity:       600,
			RepeatCalculateLeadTimeJob:    60 * 3,
			RepeatCancelOrderJob:          60 * 3,
		}
	// config for dev
	case "dev":
		Config = &config{
			Env:                "dev",
			Protocol:           protocol,
			Version:            version,
			MapGoogleHost:      "https://maps.googleapis.com",
			AhamoveOnWheelHost: "https://ep.ahamove.com",
			Supporter: map[string]*Supporter{
				"delivery": &Supporter{
					Phone: "0364266983",
					Email: "<EMAIL>",
				},
			},
			ConfigCarrier:      &ConfigCarrier{},
			ExecutorCollection: "executor_queue",
			WarehouseCodes:     []string{"HCM", "HN", "HCM2"},
			CarriersOnePack:    []int64{19, 2},
			Topics: map[string]string{
				"tpl_callback":                          "integration_dev_tpl-callback.callback.sync_transporting",
				"sync_status_tpl_callback":              "integration_dev_tpl-callback.callback.sync_status_tpl_callback",
				"post_status_tpl_callback":              "integration_dev_tpl-callback.callback.post_status_tpl_callback",
				"backup":                                "backup_dev",
				"hand_over_transfer":                    "transporting.complete_hand_over_transfer_dev",
				"hub_shipping_order":                    "transporting.hub_shipping_order",
				"update_hub_shipping_order":             "transporting.update_hub_shipping_order",
				"set_current_hub_shipping_order":        "transporting.set_current_hub_shipping_order",
				"assign_tpl_callback":                   "transporting.assign_tpl_callback_dev",
				"create_shipping_address":               "transporting.create_shipping_address_dev",
				"send_callback":                         "transporting.send_callback_dev",
				"assign_shipper_callback":               "transporting.assign_shipper_callback_dev",
				"create_extend_shipping_order":          "transporting.create_extend_pickup_shipping_order_dev",
				"create_extend_complete_handover_order": "transporting.create_extend_complete_handover_order_dev",
				"complete_bin_order":                    "transporting.complete_bin_order_dev",
				"update_fee_topic":                      "transporting.update_fee_topic_dev",
				"checkin_inbound_to":                    "transporting.checkin_inbound_to_dev",
				"done_create_handover":                  "transporting.done_create_handover_dev",
				"done_receive_handover":                 "transporting.done_receive_handover_dev",
				"retry_reconcile_client":                "transporting.retry_reconcile_client_dev",
				"complete_handover_trip":                "transporting.complete_handover_trip_dev",
				"add_order_to_reconcile":                "transporting.add_order_to_reconcile_dev",
				"update_order_fee_reconcile":            "transporting.update_order_fee_reconcile_dev",
				"auto_complete_trip":                    "transporting.auto_complete_trip_dev",
				"customer":                              "transporting.customer_dev",
				"driver_productivity":                   "transporting.driver_productivity_dev",
			},
			AhamoveOnWheelApiKey: "GSWWoGla8OL6PpAUpnleW2QaLHIQ2ed0",
			GeoCodeApiKey:        "AIzaSyDTCM05gqC-iNxk_e1kA1C8RfA_lmE2Yd0",
			DefaultHubRole: []string{
				"WMS_HUB_COORDINATOR",     // WMS_HUB_COORDINATOR
				"HUB_CASH_STAFF",          // HUB_CASH_STAFF
				"HUB_IMPORT_EXPORT_STAFF", // HUB_IMPORT_EXPORT_STAFF
				"WMS_SHIPPER",             // WMS_SHIPPER
			},
			DefaultHubParentCode: "LOGISTIC",
			DefaultHubTag:        []string{"WAREHOUSE"},
			ValidRegexForCode:    `^[A-Z0-9_]+$`,
			ValidRegexForPhone:   `^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,6}$`,
			WebHookConfig: map[string]*WebHookConfig{
				"MEDX": {
					PartnerCode: "MEDX",
					Url:         "https://webhook.site/8c672a37-7f55-4478-995b-11ccc240d9c0",
					Header: map[string]string{
						"Authorization": "some string",
					},
				},
				"INTERNAL": {
					PartnerCode: "INTERNAL",
					Url:         "https://api.v2-dev.thuocsi.vn",
					Header: map[string]string{
						"Authorization": "Basic UEFSVE5FUi92Mi5jdXN0b21lci5jdXN0b21lcjpWNHRqTDI5UVQ0",
					},
				},
				"CIRCA": {
					PartnerCode: "CIRCA",
					Url:         "https://api-dev.circa.vn/v1/invoice/collection-request/thuocsi/callback",
					HashKey:     "Ea6vS2VRqzEtKl7",
				},
				"O2O": {
					PartnerCode: "O2O",
					Url:         o2oSvcConfig.Host,
					Header: map[string]string{
						"Authorization": o2oSvcConfig.Authorization,
					},
				},
			},
			HOStatusTransition: map[string][]string{
				"READY_TO_PICK": {
					"PICKING",
				},
				"STORING": {
					"DAMAGE",
					"LOST",
					"RETURN",
					"DELIVERED",
				},
				"WAIT_TO_DELIVERY": {
					"STORING",
					"DELIVERING",
				},
				"DELIVERING": {
					"DELIVERED",
					"DELIVERY_FAIL",
				},
				"PICKING": {
					"WAIT_TO_STORING",
					"PICK_FAIL",
				},
				"DELIVERY_FAIL": {
					"STORING",
					"RETURN",
				},
				"PICK_FAIL": {
					"READY_TO_PICK",
				},
				"DELIVERED": {
					"COD_COLLECTED",
				},
				"PICKED": {
					"STORING",
				},
				"COD_COLLECTED": {
					"COMPLETED",
				},
				"RETURN": {
					"STORING",
				},
				"LOST": {
					"STORING",
				},
				"DAMAGE": {
					"STORING",
				},
			},
			OneSignalConfig: WebHookConfig{
				Host: "https://onesignal.com",
				Header: map[string]string{
					"Authorization": "Basic YmZkNjlkMzItY2E4Ny00ZmY5LTg0Y2ItYTc0ZDZkN2QzYzk1",
					"Content-Type":  "application/json",
					"Accept":        "application/json",
				},
				AppID: "06bd8666-ed4d-4512-80e0-662812ac5a3f",
			},
			DelayLateLeadTimeJob: 0,
			// After 1 minutes
			RepeatLateLeadTimeJob: 60,
			// 3 minutes
			StepWarningLevel: 60 * 3,
			// 10 times StepWarningLevel
			MaxWarningLevel: 60 * 3 * 10,
			RiderRoleToSyncReceived: map[string]bool{
				"WMS_SHIPPER":         true,
				"WMS_HUB_COORDINATOR": true,
				"HUB_MANAGEMENT":      true,
			},
			DefaultHubRoleNotify: map[string][]string{
				"HUB_MANAGEMENT": {"WMS_HUB_COORDINATOR"},
				"HUB_CASH_STAFF": {""},
			},
			RepeatSyncAccountTimeJob: 60 * 3 * 1,
			DefaultShippingFee: ConfigDefaultShippingFee{
				FeePerPackage:             50000.0,
				MinWeightApplyFeePerOrder: 3000,
				FeePerOrder: []ConfigFee{
					{
						FromProvinceCode: "74",
						ToProvinceCodes: []string{
							"74",
						},
						Fee: 20000,
					},
					{
						FromProvinceCode: "74",
						ToProvinceCodes: []string{
							"79", "77", "75", "72", "70",
						},
						Fee: 25000,
					},
					{
						FromProvinceCode: "74",
						ToProvinceCodes: []string{
							"80", "82", "83", "84", "86", "87", "89", "91", "92", "93", "94", "95", "96",
						},
						Fee: 30000,
					},
					{
						FromProvinceCode: "74",
						ToProvinceCodes: []string{
							"48", "49", "51", "52", "54", "56", "58", "60", "62", "64", "66", "67", "68",
						},
						Fee: 35000,
					},
					{
						FromProvinceCode: "74",
						ToProvinceCodes: []string{
							"01", "02", "04", "06", "08", "10", "11", "12", "14", "15", "17", "19", "20", "22", "24", "25", "26", "27", "30", "31", "33", "34", "35", "36", "37", "38", "40", "42", "44", "45", "46",
						},
						Fee: 50000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes: []string{
							"31", "24", "30", "27", "38", "40", "25", "35", "33",
						},
						Fee:           15000,
						FeePerPackage: 40000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes: []string{
							"04", "17", "02", "20", "36", "37", "44", "22", "45", "14", "34", "26", "11", "06", "46", "19", "42",
						},
						Fee:           20000,
						FeePerPackage: 50000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes: []string{
							"15",
						},
						Fee:           20000,
						FeePerPackage: 40000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes:  []string{"10"},
						Fee:              20000,
						FeePerPackage:    30000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes:  []string{"12"},
						Fee:              20000,
						FeePerPackage:    40000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes:  []string{"08"},
						Fee:              20000,
						FeePerPackage:    30000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes:  []string{"75", "72", "77", "70", "79", "74"},
						Fee:              45000,
						FeePerPackage:    50000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes:  []string{"80", "82", "86", "83", "75", "84", "89", "92", "93", "95", "94", "91", "96"},
						Fee:              50000,
						FeePerPackage:    50000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes:  []string{"48", "49", "51", "52", "54", "56", "58", "60", "64", "67", "68", "62", "64"},
						Fee:              40000,
						FeePerPackage:    50000,
					},
				},
			},
			TimeZoneAsiaBangkok: "Asia/Bangkok",
			DefaultROCarrierId:  []int64{},
			SaleOrderCodeToHubCode: map[string]string{
				"SOBD": "HUBBD",
				"SOHN": "HANOI",
			},
			DefaultPartnerInfo: map[enum.PartnerValue]DefaultCarrierInformation{
				enum.Partner.GHTK: {
					TplName:      "Giao Hàng Tiết Kiệm",
					TplCode:      &enum.Partner.GHTK,
					TplServiceId: 4,
				},
				enum.Partner.VIETTEL_POST: {
					TplName:      "Viettel Post",
					TplCode:      &enum.Partner.VIETTEL_POST,
					TplServiceId: 28,
				},
			},
			TransportingDriverRole:        "LOGISTIC_TRANSPORT_DRIVER",
			DefaultFMPickUpFeeCode:        "DEFAULT_FM_PICKUP_FEE",
			DefaultFMDropOffFeeCode:       "DEFAULT_FM_DROP_OFF_FEE",
			DefaultEOTransportFeeCode:     "DEFAULT_EO_TRANSPORT_FEE",
			DefaultEOPickNTransFeeCode:    "DEFAULT_EO_PICKUP_AND_TRANSPORT_FEE",
			DefaultEOPickNDeliCode:        "DEFAULT_EO_PICKUP_AND_DELIVERY_FEE",
			DefaultEOTransNDeliCode:       "DEFAULT_EO_TRANSPORT_AND_DELIVERY_FEE",
			DefaultCrossRegionDropOffFee:  "DEFAULT_CROSS_REGION_DROP_OFF_FEE",
			DefaultSellerROTransNDeliCode: "DEFAULT_SELLER_RO_TRANSPORT_AND_DELIVERY_FEE",
			DefaultSellerROTransportCode:  "DEFAULT_SELLER_RO_TRANSPORT_FEE",
			DefaultVendorROTransNDeliCode: "DEFAULT_VENDOR_RO_TRANSPORT_AND_DELIVERY_FEE",
			DefaultVendorROTransportCode:  "DEFAULT_VENDOR_RO_TRANSPORT_FEE",
			MaxStatusCount:                200,
			RepeatCrawlProductivity:       600,
			RepeatCalculateLeadTimeJob:    60 * 3,
			RepeatCancelOrderJob:          60 * 3,
		}
		break

		// config for staging
	case "stg":
		Config = &config{
			Env:                "stg",
			Protocol:           protocol,
			Version:            version,
			MapGoogleHost:      "https://maps.googleapis.com",
			AhamoveOnWheelHost: "https://ep.ahamove.com",
			ExecutorCollection: "executor_queue",
			Supporter: map[string]*Supporter{
				"delivery": &Supporter{
					Phone: "02873008840",
					Email: "<EMAIL>",
				},
			},
			ConfigCarrier:   &ConfigCarrier{},
			CarriersOnePack: []int64{19, 2},
			WarehouseCodes:  []string{"HCM", "HN"},
			Topics: map[string]string{
				"tpl_callback":                          "integration_stg_tpl-callback.callback.sync_transporting",
				"sync_status_tpl_callback":              "integration_stg_tpl-callback.callback.sync_status_tpl_callback",
				"post_status_tpl_callback":              "integration_stg_tpl-callback.callback.post_status_tpl_callback",
				"backup":                                "backup_stg",
				"hand_over_transfer":                    "transporting.complete_hand_over_transfer_stg",
				"hub_shipping_order":                    "transporting.hub_shipping_order",
				"update_hub_shipping_order":             "transporting.update_hub_shipping_order",
				"set_current_hub_shipping_order":        "transporting.set_current_hub_shipping_order",
				"assign_tpl_callback":                   "transporting.assign_tpl_callback_stg",
				"create_shipping_address":               "transporting.create_shipping_address_stg",
				"send_callback":                         "transporting.send_callback_stg",
				"assign_shipper_callback":               "transporting.assign_shipper_callback_stg",
				"create_extend_shipping_order":          "transporting.create_extend_pickup_shipping_order_stg",
				"create_extend_complete_handover_order": "transporting.create_extend_complete_handover_order_stg",
				"complete_bin_order":                    "transporting.complete_bin_order_stg",
				"update_fee_topic":                      "transporting.update_fee_topic_stg",
				"checkin_inbound_to":                    "transporting.checkin_inbound_to_stg",
				"done_create_handover":                  "transporting.done_create_handover_stg",
				"done_receive_handover":                 "transporting.done_receive_handover_stg",
				"retry_reconcile_client":                "transporting.retry_reconcile_client_stg",
				"complete_handover_trip":                "transporting.complete_handover_trip_stg",
				"add_order_to_reconcile":                "transporting.add_order_to_reconcile_stg",
				"update_order_fee_reconcile":            "transporting.update_order_fee_reconcile_stg",
				"auto_complete_trip":                    "transporting.auto_complete_trip_stg",
				"customer":                              "transporting.customer_stg",
				"driver_productivity":                   "transporting.driver_productivity_stg",
			},
			AhamoveOnWheelApiKey: "GSWWoGla8OL6PpAUpnleW2QaLHIQ2ed0",
			GeoCodeApiKey:        "AIzaSyDTCM05gqC-iNxk_e1kA1C8RfA_lmE2Yd0",
			DefaultHubRole: []string{
				"WMS_HUB_COORDINATOR",     // WMS_HUB_COORDINATOR
				"HUB_CASH_STAFF",          // HUB_CASH_STAFF
				"HUB_IMPORT_EXPORT_STAFF", // HUB_IMPORT_EXPORT_STAFF
				"WMS_SHIPPER",             // WMS_SHIPPER
			},
			DefaultHubParentCode: "LOGISTIC",
			DefaultHubTag:        []string{"WAREHOUSE"},
			ValidRegexForCode:    `^[A-Z0-9_]+$`,
			ValidRegexForPhone:   `^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,6}$`,
			WebHookConfig: map[string]*WebHookConfig{
				"MEDX": {
					PartnerCode: "MEDX",
					Url:         "https://webhook.site/8c672a37-7f55-4478-995b-11ccc240d9c0",
					Header: map[string]string{
						"Authorization": "some string",
					},
				},
				"INTERNAL": {
					PartnerCode: "INTERNAL",
					Url:         "https://api.v2-stg.thuocsi.vn",
					Header: map[string]string{
						"Authorization": "Basic UEFSVE5FUi92MS5lcnA6MWtzOHI5Y3BBanM=",
					},
				},
				"CIRCA": {
					PartnerCode: "CIRCA",
					Url:         "https://api-stg.circa.vn/v1/invoice/collection-request/thuocsi/callback",
					HashKey:     "Ea6vS2VRqzEtKl7",
				},
				"O2O": {
					PartnerCode: "O2O",
					Url:         o2oSvcConfig.Host,
					Header: map[string]string{
						"Authorization": o2oSvcConfig.Authorization,
					},
				},
			},
			HOStatusTransition: map[string][]string{
				"READY_TO_PICK": {
					"PICKING",
				},
				"STORING": {
					"DAMAGE",
					"LOST",
					"RETURN",
					"DELIVERED",
				},
				"WAIT_TO_DELIVERY": {
					"STORING",
					"DELIVERING",
				},
				"DELIVERING": {
					"DELIVERED",
					"DELIVERY_FAIL",
				},
				"PICKING": {
					"WAIT_TO_STORING",
					"PICK_FAIL",
				},
				"DELIVERY_FAIL": {
					"STORING",
					"RETURN",
				},
				"PICK_FAIL": {
					"READY_TO_PICK",
				},
				"DELIVERED": {
					"COD_COLLECTED",
				},
				"PICKED": {
					"STORING",
				},
				"COD_COLLECTED": {
					"COMPLETED",
				},
				"RETURN": {
					"STORING",
				},
				"LOST": {
					"STORING",
				},
				"DAMAGE": {
					"STORING",
				},
			},
			OneSignalConfig: WebHookConfig{
				Host: "https://onesignal.com",
				Header: map[string]string{
					"Authorization": "Basic YmZkNjlkMzItY2E4Ny00ZmY5LTg0Y2ItYTc0ZDZkN2QzYzk1",
					"Content-Type":  "application/json",
					"Accept":        "application/json",
				},
				AppID: "06bd8666-ed4d-4512-80e0-662812ac5a3f",
			},
			DelayLateLeadTimeJob: 0,
			// After 1 minutes
			RepeatLateLeadTimeJob: 60,
			// 3 minutes
			StepWarningLevel: 60 * 3,
			// 10 times StepWarningLevel
			MaxWarningLevel: 60 * 3 * 10,
			RiderRoleToSyncReceived: map[string]bool{
				"WMS_SHIPPER":         true,
				"WMS_HUB_COORDINATOR": true,
				"HUB_MANAGEMENT":      true,
			},
			DefaultHubRoleNotify: map[string][]string{
				"HUB_MANAGEMENT": {"WMS_HUB_COORDINATOR"},
				"HUB_CASH_STAFF": {""},
			},
			// After
			RepeatSyncAccountTimeJob: 60 * 3 * 1,
			DefaultShippingFee: ConfigDefaultShippingFee{
				FeePerPackage:             50000.0,
				MinWeightApplyFeePerOrder: 3000,
				FeePerOrder: []ConfigFee{
					{
						FromProvinceCode: "74",
						ToProvinceCodes: []string{
							"74",
						},
						Fee: 20000,
					},
					{
						FromProvinceCode: "74",
						ToProvinceCodes: []string{
							"79", "77", "75", "72", "70",
						},
						Fee: 25000,
					},
					{
						FromProvinceCode: "74",
						ToProvinceCodes: []string{
							"80", "82", "83", "84", "86", "87", "89", "91", "92", "93", "94", "95", "96",
						},
						Fee: 30000,
					},
					{
						FromProvinceCode: "74",
						ToProvinceCodes: []string{
							"48", "49", "51", "52", "54", "56", "58", "60", "62", "64", "66", "67", "68",
						},
						Fee: 35000,
					},
					{
						FromProvinceCode: "74",
						ToProvinceCodes: []string{
							"01", "02", "04", "06", "08", "10", "11", "12", "14", "15", "17", "19", "20", "22", "24", "25", "26", "27", "30", "31", "33", "34", "35", "36", "37", "38", "40", "42", "44", "45", "46",
						},
						Fee: 50000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes: []string{
							"31", "24", "30", "27", "38", "40", "25", "35", "33",
						},
						Fee:           15000,
						FeePerPackage: 40000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes: []string{
							"04", "17", "02", "20", "36", "37", "44", "22", "45", "14", "34", "26", "11", "06", "46", "19", "42",
						},
						Fee:           20000,
						FeePerPackage: 50000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes: []string{
							"15",
						},
						Fee:           20000,
						FeePerPackage: 40000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes:  []string{"10"},
						Fee:              20000,
						FeePerPackage:    30000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes:  []string{"12"},
						Fee:              20000,
						FeePerPackage:    40000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes:  []string{"08"},
						Fee:              20000,
						FeePerPackage:    30000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes:  []string{"75", "72", "77", "70", "79", "74"},
						Fee:              45000,
						FeePerPackage:    50000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes:  []string{"80", "82", "86", "83", "75", "84", "89", "92", "93", "95", "94", "91", "96"},
						Fee:              50000,
						FeePerPackage:    50000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes:  []string{"48", "49", "51", "52", "54", "56", "58", "60", "64", "67", "68", "62", "64"},
						Fee:              40000,
						FeePerPackage:    50000,
					},
				},
			},
			TimeZoneAsiaBangkok: "Asia/Bangkok",
			DefaultROCarrierId:  []int64{},
			SaleOrderCodeToHubCode: map[string]string{
				"SOBD": "HUBBD",
				"SOHN": "HUBHANOI",
			},
			DefaultPartnerInfo: map[enum.PartnerValue]DefaultCarrierInformation{
				enum.Partner.GHTK: {
					TplName:      "Giao Hàng Tiết Kiệm",
					TplCode:      &enum.Partner.GHTK,
					TplServiceId: 17,
				},
				enum.Partner.VIETTEL_POST: {
					TplName:      "Viettel Post",
					TplCode:      &enum.Partner.VIETTEL_POST,
					TplServiceId: 28,
				},
			},
			TransportingDriverRole:        "LOGISTIC_TRANSPORT_DRIVER",
			DefaultFMPickUpFeeCode:        "DEFAULT_FM_PICKUP_FEE",
			DefaultFMDropOffFeeCode:       "DEFAULT_FM_DROP_OFF_FEE",
			DefaultEOTransportFeeCode:     "DEFAULT_EO_TRANSPORT_FEE",
			DefaultEOPickNTransFeeCode:    "DEFAULT_EO_PICKUP_AND_TRANSPORT_FEE",
			DefaultEOPickNDeliCode:        "DEFAULT_EO_PICKUP_AND_DELIVERY_FEE",
			DefaultEOTransNDeliCode:       "DEFAULT_EO_TRANSPORT_AND_DELIVERY_FEE",
			DefaultCrossRegionDropOffFee:  "DEFAULT_CROSS_REGION_DROP_OFF_FEE",
			DefaultSellerROTransNDeliCode: "DEFAULT_SELLER_RO_TRANSPORT_AND_DELIVERY_FEE",
			DefaultSellerROTransportCode:  "DEFAULT_SELLER_RO_TRANSPORT_FEE",
			DefaultVendorROTransNDeliCode: "DEFAULT_VENDOR_RO_TRANSPORT_AND_DELIVERY_FEE",
			DefaultVendorROTransportCode:  "DEFAULT_VENDOR_RO_TRANSPORT_FEE",
			MaxStatusCount:                200,
			RepeatCrawlProductivity:       600,
			RepeatCalculateLeadTimeJob:    60 * 3,
			RepeatCancelOrderJob:          60 * 3,
		}
		break
	case "uat":
		Config = &config{
			Env:                "uat",
			Protocol:           protocol,
			Version:            version,
			MapGoogleHost:      "https://maps.googleapis.com",
			AhamoveOnWheelHost: "https://ep.ahamove.com",
			WarehouseCodes:     []string{"HCM", "HN"},
			ExecutorCollection: "executor_queue",
			Supporter: map[string]*Supporter{
				"delivery": &Supporter{
					Phone: "02873008840",
					Email: "<EMAIL>",
				},
			},
			ConfigCarrier:   &ConfigCarrier{},
			CarriersOnePack: []int64{22, 33, 34},
			Topics: map[string]string{
				"tpl_callback":                          "integration_uat_tpl-callback.callback.sync_transporting",
				"sync_status_tpl_callback":              "integration_uat_tpl-callback.callback.sync_status_tpl_callback",
				"post_status_tpl_callback":              "integration_uat_tpl-callback.callback.post_status_tpl_callback",
				"backup":                                "backup_uat",
				"hand_over_transfer":                    "transporting.complete_hand_over_transfer_uat",
				"hub_shipping_order":                    "transporting.hub_shipping_order_uat",
				"update_hub_shipping_order":             "transporting.update_hub_shipping_order_uat",
				"set_current_hub_shipping_order":        "transporting.set_current_hub_shipping_order_uat",
				"assign_tpl_callback":                   "transporting.assign_tpl_callback_uat",
				"create_shipping_address":               "transporting.create_shipping_address_uat",
				"send_callback":                         "transporting.send_callback_uat",
				"assign_shipper_callback":               "transporting.assign_shipper_callback_uat",
				"create_extend_shipping_order":          "transporting.create_extend_pickup_shipping_order_uat",
				"create_extend_complete_handover_order": "transporting.create_extend_complete_handover_order_uat",
				"complete_bin_order":                    "transporting.complete_bin_order_uat",
				"update_fee_topic":                      "transporting.update_fee_topic_uat",
				"checkin_inbound_to":                    "transporting.checkin_inbound_to_uat",
				"done_create_handover":                  "transporting.done_create_handover_uat",
				"done_receive_handover":                 "transporting.done_receive_handover_uat",
				"retry_reconcile_client":                "transporting.retry_reconcile_client_uat",
				"complete_handover_trip":                "transporting.complete_handover_trip_uat",
				"add_order_to_reconcile":                "transporting.add_order_to_reconcile_uat",
				"update_order_fee_reconcile":            "transporting.update_order_fee_reconcile_uat",
				"auto_complete_trip":                    "transporting.auto_complete_trip_uat",
				"customer":                              "transporting.customer_uat",
				"driver_productivity":                   "transporting.driver_productivity_uat",
			},
			GeoCodeApiKey:        "AIzaSyDTCM05gqC-iNxk_e1kA1C8RfA_lmE2Yd0",
			AhamoveOnWheelApiKey: "GSWWoGla8OL6PpAUpnleW2QaLHIQ2ed0",
			DefaultHubRole: []string{
				"WMS_HUB_COORDINATOR",     // WMS_HUB_COORDINATOR
				"HUB_CASH_STAFF",          // HUB_CASH_STAFF
				"HUB_IMPORT_EXPORT_STAFF", // HUB_IMPORT_EXPORT_STAFF
				"WMS_SHIPPER",             // WMS_SHIPPER
			},
			DefaultHubParentCode: "LOGISTIC",
			DefaultHubTag:        []string{"WAREHOUSE"},
			ValidRegexForCode:    `^[A-Z0-9_]+$`,
			ValidRegexForPhone:   `^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,6}$`,
			WebHookConfig: map[string]*WebHookConfig{
				"MEDX": {
					PartnerCode: "MEDX",
					Url:         "https://webhook.site/8c672a37-7f55-4478-995b-11ccc240d9c0",
					Header: map[string]string{
						"Authorization": "some string",
					},
				},
				"INTERNAL": {
					PartnerCode: "INTERNAL",
					Url:         "http://proxy-service.frontend-uat",
					Header: map[string]string{
						"Authorization": "Basic UEFSVE5FUi9lcnB2MTphcWl3bnNjMTIzcHdqc2xocjQ1NnBvaXJsNzg5",
					},
				},
				"CIRCA": {
					PartnerCode: "CIRCA",
					Url:         "https://api.circa.vn/v1/invoice/collection-request/thuocsi/callback",
					HashKey:     "Gov7Di5N7PdIe2q",
				},
				"O2O": {
					PartnerCode: "O2O",
					Url:         o2oSvcConfig.Host,
					Header: map[string]string{
						"Authorization": o2oSvcConfig.Authorization,
					},
				},
			},
			HOStatusTransition: map[string][]string{
				"READY_TO_PICK": {
					"PICKING",
				},
				"STORING": {
					"DAMAGE",
					"LOST",
					"RETURN",
					"DELIVERED",
				},
				"WAIT_TO_DELIVERY": {
					"STORING",
					"DELIVERING",
				},
				"DELIVERING": {
					"DELIVERED",
					"DELIVERY_FAIL",
				},
				"PICKING": {
					"WAIT_TO_STORING",
					"PICK_FAIL",
				},
				"DELIVERY_FAIL": {
					"STORING",
					"RETURN",
				},
				"PICK_FAIL": {
					"READY_TO_PICK",
				},
				"DELIVERED": {
					"COD_COLLECTED",
				},
				"PICKED": {
					"STORING",
				},
				"COD_COLLECTED": {
					"COMPLETED",
				},
				"RETURN": {
					"STORING",
				},
				"LOST": {
					"STORING",
				},
				"DAMAGE": {
					"STORING",
				},
			},
			OneSignalConfig: WebHookConfig{
				Host: "https://onesignal.com",
				Header: map[string]string{
					"Authorization": "Basic MzI1MDY5YTgtYTY4Yi00MWFiLWEzMjQtOTM5YjM5MDViNWUz",
					"Content-Type":  "application/json",
					"Accept":        "application/json",
				},
				AppID: "fa8e0ee3-7e1f-463f-a7cd-9c0a5b79d373",
			},
			DelayLateLeadTimeJob: 0,
			// After 1 hour
			RepeatLateLeadTimeJob: 60 * 60,
			// 24 hours
			StepWarningLevel: 60 * 60 * 24,
			// 10 times StepWarningLevel
			MaxWarningLevel: 60 * 60 * 24 * 10,
			RiderRoleToSyncReceived: map[string]bool{
				"WMS_SHIPPER":         true,
				"WMS_HUB_COORDINATOR": true,
				"HUB_MANAGEMENT":      true,
			},
			DefaultHubRoleNotify: map[string][]string{
				"HUB_MANAGEMENT": {"WMS_HUB_COORDINATOR"},
				"HUB_CASH_STAFF": {},
			},
			DefaultShippingFee: ConfigDefaultShippingFee{
				FeePerPackage:             50000.0,
				MinWeightApplyFeePerOrder: 3000,
				FeePerOrder: []ConfigFee{
					{
						FromProvinceCode: "74",
						ToProvinceCodes: []string{
							"74",
						},
						Fee: 20000,
					},
					{
						FromProvinceCode: "74",
						ToProvinceCodes: []string{
							"79", "77", "75", "72", "70",
						},
						Fee: 25000,
					},
					{
						FromProvinceCode: "74",
						ToProvinceCodes: []string{
							"80", "82", "83", "84", "86", "87", "89", "91", "92", "93", "94", "95", "96",
						},
						Fee: 30000,
					},
					{
						FromProvinceCode: "74",
						ToProvinceCodes: []string{
							"48", "49", "51", "52", "54", "56", "58", "60", "62", "64", "66", "67", "68",
						},
						Fee: 35000,
					},
					{
						FromProvinceCode: "74",
						ToProvinceCodes: []string{
							"01", "02", "04", "06", "08", "10", "11", "12", "14", "15", "17", "19", "20", "22", "24", "25", "26", "27", "30", "31", "33", "34", "35", "36", "37", "38", "40", "42", "44", "45", "46",
						},
						Fee: 50000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes: []string{
							"31", "24", "30", "27", "38", "40", "25", "35", "33",
						},
						Fee:           15000,
						FeePerPackage: 40000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes: []string{
							"04", "17", "02", "20", "36", "37", "44", "22", "45", "14", "34", "26", "11", "06", "46", "19", "42",
						},
						Fee:           20000,
						FeePerPackage: 50000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes: []string{
							"15",
						},
						Fee:           20000,
						FeePerPackage: 40000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes:  []string{"10"},
						Fee:              20000,
						FeePerPackage:    30000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes:  []string{"12"},
						Fee:              20000,
						FeePerPackage:    40000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes:  []string{"08"},
						Fee:              20000,
						FeePerPackage:    30000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes:  []string{"75", "72", "77", "70", "79", "74"},
						Fee:              45000,
						FeePerPackage:    50000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes:  []string{"80", "82", "86", "83", "75", "84", "89", "92", "93", "95", "94", "91", "96"},
						Fee:              50000,
						FeePerPackage:    50000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes:  []string{"48", "49", "51", "52", "54", "56", "58", "60", "64", "67", "68", "62", "64"},
						Fee:              40000,
						FeePerPackage:    50000,
					},
				},
			},
			RepeatSyncAccountTimeJob: 60 * 10,
			TimeZoneAsiaBangkok:      "Asia/Bangkok",
			DefaultROCarrierId:       []int64{504},
			SaleOrderCodeToHubCode: map[string]string{
				"SOBD": "HUBVSIPII",
				"SOHN": "HUBTRUONGDINH",
			},
			DefaultPartnerInfo: map[enum.PartnerValue]DefaultCarrierInformation{
				enum.Partner.GHTK: {
					TplName:      "Giao Hàng Tiết Kiệm",
					TplCode:      &enum.Partner.GHTK,
					TplServiceId: 6,
				},
				enum.Partner.VIETTEL_POST: {
					TplName:      "Viettel Post",
					TplCode:      &enum.Partner.VIETTEL_POST,
					TplServiceId: 28,
				},
			},
			TransportingDriverRole:        "LOGISTIC_TRANSPORT_DRIVER",
			DefaultFMPickUpFeeCode:        "DEFAULT_FM_PICKUP_FEE",
			DefaultFMDropOffFeeCode:       "DEFAULT_FM_DROP_OFF_FEE",
			DefaultEOTransportFeeCode:     "DEFAULT_EO_TRANSPORT_FEE",
			DefaultEOPickNTransFeeCode:    "DEFAULT_EO_PICKUP_AND_TRANSPORT_FEE",
			DefaultEOPickNDeliCode:        "DEFAULT_EO_PICKUP_AND_DELIVERY_FEE",
			DefaultEOTransNDeliCode:       "DEFAULT_EO_TRANSPORT_AND_DELIVERY_FEE",
			DefaultCrossRegionDropOffFee:  "DEFAULT_CROSS_REGION_DROP_OFF_FEE",
			DefaultSellerROTransNDeliCode: "DEFAULT_SELLER_RO_TRANSPORT_AND_DELIVERY_FEE",
			DefaultSellerROTransportCode:  "DEFAULT_SELLER_RO_TRANSPORT_FEE",
			DefaultVendorROTransNDeliCode: "DEFAULT_VENDOR_RO_TRANSPORT_AND_DELIVERY_FEE",
			DefaultVendorROTransportCode:  "DEFAULT_VENDOR_RO_TRANSPORT_FEE",
			MaxStatusCount:                5000,
			RepeatCrawlProductivity:       600,
			RepeatCalculateLeadTimeJob:    60 * 60 * 24,
			RepeatCancelOrderJob:          60 * 10,
		}
		break
	case "prd":
		Config = &config{
			Env:                "prd",
			Protocol:           protocol,
			Version:            version,
			MapGoogleHost:      "https://maps.googleapis.com",
			AhamoveOnWheelHost: "https://ep.ahamove.com",
			WarehouseCodes:     []string{"HCM", "HN"},
			ExecutorCollection: "executor_queue",
			Supporter: map[string]*Supporter{
				"delivery": &Supporter{
					Phone: "02873008840",
					Email: "<EMAIL>",
				},
			},
			ConfigCarrier:   &ConfigCarrier{},
			CarriersOnePack: []int64{22, 33, 34},
			Topics: map[string]string{
				"tpl_callback":                          "integration_prd_tpl-callback.callback.sync_transporting",
				"sync_status_tpl_callback":              "integration_prd_tpl-callback.callback.sync_status_tpl_callback",
				"post_status_tpl_callback":              "integration_prd_tpl-callback.callback.post_status_tpl_callback",
				"backup":                                "backup_prd",
				"hand_over_transfer":                    "transporting.complete_hand_over_transfer_prd",
				"hub_shipping_order":                    "transporting.hub_shipping_order",
				"update_hub_shipping_order":             "transporting.update_hub_shipping_order",
				"set_current_hub_shipping_order":        "transporting.set_current_hub_shipping_order",
				"assign_tpl_callback":                   "transporting.assign_tpl_callback",
				"create_shipping_address":               "transporting.create_shipping_address_prd",
				"send_callback":                         "transporting.send_callback_prd",
				"assign_shipper_callback":               "transporting.assign_shipper_callback_prd",
				"create_extend_shipping_order":          "transporting.create_extend_pickup_shipping_order_prd",
				"create_extend_complete_handover_order": "transporting.create_extend_complete_handover_order_prd",
				"complete_bin_order":                    "transporting.complete_bin_order_prd",
				"update_fee_topic":                      "transporting.update_fee_topic_prd",
				"checkin_inbound_to":                    "transporting.checkin_inbound_to_prd",
				"done_create_handover":                  "transporting.done_create_handover_prd",
				"done_receive_handover":                 "transporting.done_receive_handover_prd",
				"retry_reconcile_client":                "transporting.retry_reconcile_client_prd",
				"complete_handover_trip":                "transporting.complete_handover_trip_prd",
				"add_order_to_reconcile":                "transporting.add_order_to_reconcile_prd",
				"update_order_fee_reconcile":            "transporting.update_order_fee_reconcile_prd",
				"auto_complete_trip":                    "transporting.auto_complete_trip_prd",
				"customer":                              "transporting.customer_prd",
				"driver_productivity":                   "transporting.driver_productivity_prd",
			},
			GeoCodeApiKey:        "AIzaSyDTCM05gqC-iNxk_e1kA1C8RfA_lmE2Yd0",
			AhamoveOnWheelApiKey: "GSWWoGla8OL6PpAUpnleW2QaLHIQ2ed0",
			DefaultHubRole: []string{
				"WMS_HUB_COORDINATOR",     // WMS_HUB_COORDINATOR
				"HUB_CASH_STAFF",          // HUB_CASH_STAFF
				"HUB_IMPORT_EXPORT_STAFF", // HUB_IMPORT_EXPORT_STAFF
				"WMS_SHIPPER",             // WMS_SHIPPER
			},
			DefaultHubParentCode: "LOGISTIC",
			DefaultHubTag:        []string{"WAREHOUSE"},
			ValidRegexForCode:    `^[A-Z0-9_]+$`,
			ValidRegexForPhone:   `^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,6}$`,
			WebHookConfig: map[string]*WebHookConfig{
				"MEDX": {
					PartnerCode: "MEDX",
					Url:         "https://webhook.site/8c672a37-7f55-4478-995b-11ccc240d9c0",
					Header: map[string]string{
						"Authorization": "some string",
					},
				},
				"INTERNAL": {
					PartnerCode: "INTERNAL",
					Url:         "http://proxy-service.frontend-prd",
					Header: map[string]string{
						"Authorization": "Basic UEFSVE5FUi9lcnB2MTphcWl3bnNjMTIzcHdqc2xocjQ1NnBvaXJsNzg5",
					},
				},
				"CIRCA": {
					PartnerCode: "CIRCA",
					Url:         "https://api.circa.vn/v1/invoice/collection-request/thuocsi/callback",
					HashKey:     "Gov7Di5N7PdIe2q",
				},
				"O2O": {
					PartnerCode: "O2O",
					Url:         o2oSvcConfig.Host,
					Header: map[string]string{
						"Authorization": o2oSvcConfig.Authorization,
					},
				},
			},
			HOStatusTransition: map[string][]string{
				"READY_TO_PICK": {
					"PICKING",
				},
				"STORING": {
					"DAMAGE",
					"LOST",
					"RETURN",
					"DELIVERED",
				},
				"WAIT_TO_DELIVERY": {
					"STORING",
					"DELIVERING",
				},
				"DELIVERING": {
					"DELIVERED",
					"DELIVERY_FAIL",
				},
				"PICKING": {
					"WAIT_TO_STORING",
					"PICK_FAIL",
				},
				"DELIVERY_FAIL": {
					"STORING",
					"RETURN",
				},
				"PICK_FAIL": {
					"READY_TO_PICK",
				},
				"DELIVERED": {
					"COD_COLLECTED",
				},
				"PICKED": {
					"STORING",
				},
				"COD_COLLECTED": {
					"COMPLETED",
				},
				"RETURN": {
					"STORING",
				},
				"LOST": {
					"STORING",
				},
				"DAMAGE": {
					"STORING",
				},
			},
			OneSignalConfig: WebHookConfig{
				Host: "https://onesignal.com",
				Header: map[string]string{
					"Authorization": "Basic MzI1MDY5YTgtYTY4Yi00MWFiLWEzMjQtOTM5YjM5MDViNWUz",
					"Content-Type":  "application/json",
					"Accept":        "application/json",
				},
				AppID: "fa8e0ee3-7e1f-463f-a7cd-9c0a5b79d373",
			},
			DelayLateLeadTimeJob: 0,
			// After 1 hour
			RepeatLateLeadTimeJob: 60 * 60,
			// 24 hours
			StepWarningLevel: 60 * 60 * 24,
			// 10 times StepWarningLevel
			MaxWarningLevel: 60 * 60 * 24 * 10,
			RiderRoleToSyncReceived: map[string]bool{
				"WMS_SHIPPER":         true,
				"WMS_HUB_COORDINATOR": true,
				"HUB_MANAGEMENT":      true,
			},
			DefaultHubRoleNotify: map[string][]string{
				"HUB_MANAGEMENT": {"WMS_HUB_COORDINATOR"},
				"HUB_CASH_STAFF": {""},
			},
			DefaultShippingFee: ConfigDefaultShippingFee{
				FeePerPackage:             50000.0,
				MinWeightApplyFeePerOrder: 3000,
				FeePerOrder: []ConfigFee{
					{
						FromProvinceCode: "74",
						ToProvinceCodes: []string{
							"74",
						},
						Fee: 20000,
					},
					{
						FromProvinceCode: "74",
						ToProvinceCodes: []string{
							"79", "77", "75", "72", "70",
						},
						Fee: 25000,
					},
					{
						FromProvinceCode: "74",
						ToProvinceCodes: []string{
							"80", "82", "83", "84", "86", "87", "89", "91", "92", "93", "94", "95", "96",
						},
						Fee: 30000,
					},
					{
						FromProvinceCode: "74",
						ToProvinceCodes: []string{
							"48", "49", "51", "52", "54", "56", "58", "60", "62", "64", "66", "67", "68",
						},
						Fee: 35000,
					},
					{
						FromProvinceCode: "74",
						ToProvinceCodes: []string{
							"01", "02", "04", "06", "08", "10", "11", "12", "14", "15", "17", "19", "20", "22", "24", "25", "26", "27", "30", "31", "33", "34", "35", "36", "37", "38", "40", "42", "44", "45", "46",
						},
						Fee: 50000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes: []string{
							"31", "24", "30", "27", "38", "40", "25", "35", "33",
						},
						Fee:           15000,
						FeePerPackage: 40000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes: []string{
							"04", "17", "02", "20", "36", "37", "44", "22", "45", "14", "34", "26", "11", "06", "46", "19", "42",
						},
						Fee:           20000,
						FeePerPackage: 50000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes: []string{
							"15",
						},
						Fee:           20000,
						FeePerPackage: 40000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes:  []string{"10"},
						Fee:              20000,
						FeePerPackage:    30000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes:  []string{"12"},
						Fee:              20000,
						FeePerPackage:    40000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes:  []string{"08"},
						Fee:              20000,
						FeePerPackage:    30000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes:  []string{"75", "72", "77", "70", "79", "74"},
						Fee:              45000,
						FeePerPackage:    50000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes:  []string{"80", "82", "86", "83", "75", "84", "89", "92", "93", "95", "94", "91", "96"},
						Fee:              50000,
						FeePerPackage:    50000,
					},
					{
						FromProvinceCode: "01",
						ToProvinceCodes:  []string{"48", "49", "51", "52", "54", "56", "58", "60", "64", "67", "68", "62", "64"},
						Fee:              40000,
						FeePerPackage:    50000,
					},
				},
			},
			RepeatSyncAccountTimeJob: 60 * 10,
			TimeZoneAsiaBangkok:      "Asia/Bangkok",
			DefaultROCarrierId:       []int64{504},
			SaleOrderCodeToHubCode: map[string]string{
				"SOBD": "HUBVSIPII",
				"SOHN": "HUBTRUONGDINH",
			},
			DefaultPartnerInfo: map[enum.PartnerValue]DefaultCarrierInformation{
				enum.Partner.GHTK: {
					TplName:      "Giao Hàng Tiết Kiệm",
					TplCode:      &enum.Partner.GHTK,
					TplServiceId: 6,
				},
				enum.Partner.VIETTEL_POST: {
					TplName:      "Viettel Post",
					TplCode:      &enum.Partner.VIETTEL_POST,
					TplServiceId: 28,
				},
			},
			TransportingDriverRole:        "LOGISTIC_TRANSPORT_DRIVER",
			DefaultFMPickUpFeeCode:        "DEFAULT_FM_PICKUP_FEE",
			DefaultFMDropOffFeeCode:       "DEFAULT_FM_DROP_OFF_FEE",
			DefaultEOTransportFeeCode:     "DEFAULT_EO_TRANSPORT_FEE",
			DefaultEOPickNTransFeeCode:    "DEFAULT_EO_PICKUP_AND_TRANSPORT_FEE",
			DefaultEOPickNDeliCode:        "DEFAULT_EO_PICKUP_AND_DELIVERY_FEE",
			DefaultEOTransNDeliCode:       "DEFAULT_EO_TRANSPORT_AND_DELIVERY_FEE",
			DefaultCrossRegionDropOffFee:  "DEFAULT_CROSS_REGION_DROP_OFF_FEE",
			DefaultSellerROTransNDeliCode: "DEFAULT_SELLER_RO_TRANSPORT_AND_DELIVERY_FEE",
			DefaultSellerROTransportCode:  "DEFAULT_SELLER_RO_TRANSPORT_FEE",
			DefaultVendorROTransNDeliCode: "DEFAULT_VENDOR_RO_TRANSPORT_AND_DELIVERY_FEE",
			DefaultVendorROTransportCode:  "DEFAULT_VENDOR_RO_TRANSPORT_FEE",
			MaxStatusCount:                5000,
			RepeatCrawlProductivity:       600,
			RepeatCalculateLeadTimeJob:    60 * 60 * 24,
			RepeatCancelOrderJob:          60 * 10,
		}
		break
	}

	dbFormat := "delivery_%s_transporting"
	dbLogFormat := dbFormat + "_log"
	dbJobFormat := dbFormat + "_job"
	mainDB = fineMainDBConfig(mainDB, dbFormat, env)
	logDB = fineLogDBConfig(logDB, dbLogFormat, env)
	jobDB = fineQueueDBConfig(jobDB, dbJobFormat, env)

	Config.MainDBConf = mainDB
	Config.LogDBConf = logDB
	Config.JobDBConf = jobDB
	Config.ThuocsiSvcConf = thuocsiSvcConfig
}

func fineMainDBConfig(_config configuration.Database, dbNameFormat, env string) configuration.Database {
	// UAT và PRD dùng chung db main của prd
	if env == "uat" {
		env = "prd"
	}
	_config.DatabaseName = fmt.Sprintf(dbNameFormat, env)

	if _config.Auth == "" {
		_config.Auth = _config.DatabaseName
	}
	return _config
}

func fineQueueDBConfig(_config configuration.Database, dbNameFormat, env string) configuration.Database {
	// UAT và PRD dùng chung db queue của prd
	if env == "uat" {
		env = "prd"
	}
	_config.DatabaseName = fmt.Sprintf(dbNameFormat, env)

	if _config.Auth == "" {
		_config.Auth = _config.DatabaseName
	}
	return _config
}

func fineLogDBConfig(_config configuration.Database, dbNameFormat, env string) configuration.Database {
	_config.DatabaseName = fmt.Sprintf(dbNameFormat, env)

	if _config.Auth == "" {
		_config.Auth = _config.DatabaseName
	}
	return _config
}
