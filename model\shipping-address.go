package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"go.mongodb.org/mongo-driver/mongo"
)

type ValidatePath struct {
	ProvinceName   string
	DistrictName   string
	WardName       string
	ProvinceCode   string
	DistrictCode   string
	WardCode       string
	OrderIndex     int
	FromOrderIndex []int
	ToOrderIndex   []int
}

type ShippingAddress struct {
	// base info
	VersionNo       string     `json:"versionNo,omitempty" bson:"version_no,omitempty"`
	CreatedBy       string     `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	CreatedTime     *time.Time `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedBy   string     `json:"lastUpdatedBy,omitempty" bson:"last_updated_by,omitempty"`
	LastUpdatedTime *time.Time `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	ID              int64                   `json:"id,omitempty" bson:"_id,omitempty"`
	CustomerName    string                  `json:"customerName,omitempty" bson:"customer_name,omitempty"`
	CustomerCode    string                  `json:"customerCode,omitempty" bson:"customer_code,omitempty"`
	CustomerAddress string                  `json:"customerAddress,omitempty" bson:"customer_address,omitempty"`
	CustomerPhone   string                  `json:"customerPhone,omitempty" bson:"customer_phone,omitempty"`
	CustomerEmail   string                  `json:"customerEmail,omitempty" bson:"customer_email,omitempty"`
	Latitude        float64                 `json:"latitude,omitempty" bson:"latitude,omitempty"`
	Longitude       float64                 `json:"longitude,omitempty" bson:"longitude,omitempty"`
	Verified        *bool                   `json:"verified,omitempty" bson:"verified,omitempty"`
	WardCode        string                  `json:"wardCode,omitempty" bson:"ward_code,omitempty"`
	WardName        string                  `json:"wardName,omitempty" bson:"ward_name,omitempty"`
	DistrictCode    string                  `json:"districtCode,omitempty" bson:"district_code,omitempty"`
	DistrictName    string                  `json:"districtName,omitempty" bson:"district_name,omitempty"`
	ProvinceCode    string                  `json:"provinceCode,omitempty" bson:"province_code,omitempty"`
	ProvinceName    string                  `json:"provinceName,omitempty" bson:"province_name,omitempty"`
	NearestOrder    string                  `json:"nearestOrder,omitempty" bson:"nearest_order,omitempty"`
	HubOrderType    *enum.HubOrderTypeValue `json:"type,omitempty" bson:"type,omitempty"`
	// ExtraData...
	ExtraData []string `json:"extraData,omitempty" bson:"extra_data,omitempty"`
}

var ShippingAddressDB = &db.Instance{
	ColName:        "shipping_address",
	TemplateObject: &ShippingAddress{},
}

func InitShippingAddressModel(s *mongo.Database) {
	ShippingAddressDB.ApplyDatabase(s)

	//t := true
	//ShippingAddressDB.CreateIndex(bson.D{
	//	{"customer_code", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})
}
