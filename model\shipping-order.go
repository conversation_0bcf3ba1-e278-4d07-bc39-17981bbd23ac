package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type ShippingOrder struct {
	ID                       primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	ReferenceCode            string             `json:"referenceCode,omitempty" bson:"reference_code,omitempty"`
	DeliveryOrderCode        string             `json:"deliveryOrderCode,omitempty" bson:"delivery_order_code,omitempty"`
	ParentReferenceCode      string             `json:"parentReferenceCode,omitempty" bson:"parent_reference_code,omitempty"`
	ReceiveSessionCode       string             `json:"receiveSessionCode,omitempty" bson:"receive_session_code,omitempty"`
	ParentReceiveSessionCode string             `json:"parentReceiveSessionCode,omitempty" bson:"parent_receive_session_code,omitempty"`

	SplitFromCode string                       `json:"splitFromCode,omitempty" bson:"split_from_code,omitempty"`
	TrackingCode  string                       `json:"trackingCode,omitempty" bson:"tracking_code,omitempty"`
	TplCode       *enum.PartnerValue           `json:"tplCode,omitempty" bson:"tpl_code,omitempty"`
	TplName       string                       `json:"tplName,omitempty" bson:"tpl_name,omitempty"`
	TplServiceId  int64                        `json:"tplServiceId" bson:"tpl_service_id,omitempty"`
	Status        *enum.TPLCallbackStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	ActionTime    int64                        `json:"actionTime,omitempty" bson:"action_time,omitempty"`
	Action        string                       `json:"action" bson:"action,omitempty"`
	ActionName    string                       `json:"actionName" bson:"action_name,omitempty"`

	CustomerName             string  `json:"customerName,omitempty" bson:"customer_name,omitempty"`
	CustomerCode             string  `json:"customerCode,omitempty" bson:"customer_code,omitempty"`
	CustomerShippingAddress  string  `json:"customerShippingAddress,omitempty" bson:"customer_shipping_address,omitempty"`
	CustomerPhone            string  `json:"customerPhone,omitempty" bson:"customer_phone,omitempty"`
	CustomerEmail            string  `json:"customerEmail,omitempty" bson:"customer_email,omitempty"`
	CustomerWardCode         string  `json:"customerWardCode,omitempty" bson:"customer_ward_code,omitempty"`
	CustomerWardName         string  `json:"customerWardName,omitempty" bson:"customer_ward_name,omitempty"`
	CustomerDistrictCode     string  `json:"customerDistrictCode,omitempty" bson:"customer_district_code,omitempty"`
	CustomerDistrictName     string  `json:"customerDistrictName,omitempty" bson:"customer_district_name,omitempty"`
	CustomerProvinceCode     string  `json:"customerProvinceCode,omitempty" bson:"customer_province_code,omitempty"`
	CustomerProvinceName     string  `json:"customerProvinceName,omitempty" bson:"customer_province_name,omitempty"`
	CustomerLatitude         float64 `json:"customerLatitude,omitempty" bson:"customer_latitude,omitempty"`
	CustomerLongitude        float64 `json:"customerLongitude,omitempty" bson:"customer_longitude,omitempty"`
	CustomerVerificationCode string  `json:"customerVerificationCode,omitempty" bson:"customer_verification_code,omitempty"`

	FromCustomerAddress string  `json:"fromCustomerAddress" bson:"from_customer_address,omitempty"`
	FromCustomerPhone   string  `json:"fromCustomerPhone" bson:"from_customer_phone,omitempty"`
	FromCustomerEmail   string  `json:"fromCustomerEmail" bson:"from_customer_email,omitempty"`
	FromCustomerName    string  `json:"fromCustomerName" bson:"from_customer_name,omitempty"`
	FromCustomerCode    string  `json:"fromCustomerCode" bson:"from_customer_code,omitempty"`
	FromWardCode        string  `json:"fromWardCode" bson:"from_ward_code,omitempty"`
	FromWardName        string  `json:"fromWardName" bson:"from_ward_name,omitempty"`
	FromDistrictCode    string  `json:"fromDistrictCode" bson:"from_district_code,omitempty"`
	FromDistrictName    string  `json:"fromDistrictName" bson:"from_district_name,omitempty"`
	FromProvinceCode    string  `json:"fromProvinceCode" bson:"from_province_code,omitempty"`
	FromProvinceName    string  `json:"fromProvinceName" bson:"from_province_name,omitempty"`
	FromLatitude        float64 `json:"fromLatitude,omitempty" bson:"from_latitude,omitempty"`
	FromLongitude       float64 `json:"fromLongitude,omitempty" bson:"from_longitude,omitempty"`

	Height                 float64 `json:"height,omitempty" bson:"height,omitempty"`
	Width                  float64 `json:"width,omitempty" bson:"width,omitempty"`
	Length                 float64 `json:"length,omitempty" bson:"length,omitempty"`
	Weight                 float64 `json:"weight,omitempty" bson:"weight,omitempty"`
	Distance               float64 `json:"distance" bson:"distance,omitempty"`
	NumPackage             int64   `json:"numPackage,omitempty" bson:"num_package,omitempty"`
	EstimatePickingTime    int64   `json:"estimatePickingTime" bson:"estimate_picking_time,omitempty"`
	EstimateDeliveringTime int64   `json:"estimateDeliveringTime" bson:"estimate_delivering_time,omitempty"`
	EstimateReturningTime  int64   `json:"estimateReturningTime" bson:"estimate_returning_time,omitempty"`

	//time check point
	BookingTime        int64 `json:"bookingTime" bson:"booking_time,omitempty"`
	PickedTime         int64 `json:"pickedTime" bson:"picked_time,omitempty"`
	HandoverTime       int64 `json:"handoverTime" bson:"handover_time,omitempty"`
	StoredAtLMHubTime  int64 `json:"storedAtLMHubTime" bson:"stored_at_lm_hub_time,omitempty"`
	DeliveredTime      int64 `json:"deliveredTime" bson:"delivered_time,omitempty"`
	ReceiveReturnTime  int64 `json:"receiveReturnTime" bson:"receive_return_time,omitempty"`
	StoredFirstHubTime int64 `json:"storedFirstHubTime,omitempty" bson:"stored_first_hub_time,omitempty"`
	LeavedFirstHubTime int64 `json:"leavedFirstHubTime,omitempty" bson:"leaved_first_hub_time,omitempty"`

	PrivateNote     string `json:"privateNote,omitempty" bson:"private_note,omitempty"`
	Note            string `json:"note,omitempty" bson:"note,omitempty"`
	Reason          string `json:"reason,omitempty" bson:"reason,omitempty"`
	ReasonCode      string `json:"reasonCode,omitempty" bson:"reason_code,omitempty"`
	CurrentHub      string `json:"currentHub" bson:"current_hub,omitempty"`
	CurrentLocation string `json:"currentLocation" bson:"current_location,omitempty"`

	TotalAmount                float64                     `json:"totalAmount,omitempty" bson:"total_amount,omitempty"`
	TotalCollectSenderAmount   *float64                    `json:"totalCollectSenderAmount,omitempty" bson:"total_collect_sender_amount,omitempty"`
	TotalCollectReceiverAmount *float64                    `json:"totalCollectReceiverAmount,omitempty" bson:"total_collect_receiver_amount,omitempty"`
	TotalDebtAmount            *float64                    `json:"totalDebtAmount,omitempty" bson:"total_debt_amount,omitempty"`
	DeliveryAmount             float64                     `json:"deliveryAmount,omitempty" bson:"delivery_amount,omitempty"`
	CODAmount                  float64                     `json:"codAmount,omitempty" bson:"cod_amount,omitempty"`
	FeeAmount                  float64                     `json:"feeAmount,omitempty" bson:"fee_amount,omitempty"`
	VatAmount                  float64                     `json:"vatAmount,omitempty" bson:"money_vat,omitempty"`
	FeeSenderAmount            *float64                    `json:"feeSenderAmount,omitempty" bson:"fee_sender_amount,omitempty"`
	FeeReceiverAmount          *float64                    `json:"feeReceiverAmount,omitempty" bson:"fee_receiver_amount,omitempty"`
	FeeDebtAmount              *float64                    `json:"feeDebtAmount,omitempty" bson:"fee_debt_amount,omitempty"`
	FeeCollectMethod           *enum.FeeCollectMethodValue `json:"feeCollectMethod,omitempty" bson:"fee_collect_method,omitempty"`
	PaymentMethod              *enum.PaymentMethodValue    `json:"paymentMethod,omitempty" bson:"payment_method,omitempty"`
	CollectOnDelivery          *bool                       `json:"collectOnDelivery,omitempty" bson:"collect_on_delivery,omitempty"`
	DeliveryNote               []*DeliveryNote             `json:"deliveryNote" bson:"delivery_note"`

	// tracking status
	VersionNo       string                       `json:"versionNo,omitempty" bson:"version_no,omitempty"`
	CreatedBy       string                       `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	CreatedTime     *time.Time                   `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedBy   string                       `json:"lastUpdatedBy,omitempty" bson:"last_updated_by,omitempty"`
	LastUpdatedTime *time.Time                   `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	ReconciledTime  *time.Time                   `json:"reconciledTime,omitempty" bson:"reconciled_time,omitempty"`
	ExtraInfo       map[string]interface{}       `json:"extraInfo" bson:"extra_info,omitempty"`
	ShippingType    *enum.ShippingOrderTypeValue `json:"type,omitempty" bson:"type,omitempty"`
	PartnerCode     string                       `json:"partnerCode,omitempty" bson:"partner_code,omitempty"`

	IsDropOffAtFMHub        bool       `json:"isDropOffAtFMHub,omitempty" bson:"is_drop_off_at_fm_hub,omitempty"`
	IsReceiveAtLMHub        bool       `json:"isReceiveAtLMHub,omitempty" bson:"is_receive_at_lm_hub,omitempty"`
	NeedVerifyReceiver      bool       `json:"needVerifyReceiver,omitempty" bson:"need_verify_receiver,omitempty"`
	VerificationCode        string     `json:"-" bson:"verification_code,omitempty"`
	ExpectedDeliveryTime    int64      `json:"expectedDeliveryTime,omitempty" bson:"expected_delivery_time,omitempty"`
	ExpectedPickupTime      int64      `json:"expectedPickupTime,omitempty" bson:"expected_pickup_time,omitempty"`
	Products                []*Product `json:"products,omitempty" bson:"products,omitempty"`
	RescheduledDeliveryTime int64      `json:"rescheduledDeliveryTime,omitempty" bson:"rescheduled_delivery_time,omitempty"`
	RescheduledPickupTime   int64      `json:"rescheduledPickupTime,omitempty" bson:"rescheduled_pickup_time,omitempty"`

	// Calculated by zone configuration
	FirstMileHubCode   string `json:"firstMileHubCode,omitempty" bson:"first_mile_hub_code,omitempty"`
	FirstMileCarrierId int64  `json:"firstMileCarrierCode,omitempty" bson:"first_mile_carrier_code,omitempty"`
	LastMileHubCode    string `json:"lastMileHubCode,omitempty" bson:"last_mile_hub_code,omitempty"`
	LastMileCarrierId  int64  `json:"lastMileCarrierCode,omitempty" bson:"last_mile_carrier_code,omitempty"`

	FailReasons []FailReason `json:"failReasons,omitempty" bson:"fail_reasons,omitempty"`

	Scope            []string                `json:"scope,omitempty" bson:"scope,omitempty"`
	Tags             []string                `json:"tags,omitempty" bson:"tags,omitempty"`
	CallbackUrl      string                  `json:"callbackUrl,omitempty" bson:"callback_url,omitempty"`
	IsReconciled     *bool                   `json:"isReconciled,omitempty" bson:"is_reconciled,omitempty"`
	IsReconciledFee  *bool                   `json:"isReconcileFee,omitempty" bson:"is_reconcile_fee,omitempty"`
	IsReconciledCod  *bool                   `json:"isReconcileCod,omitempty" bson:"is_reconcile_cod,omitempty"`
	CheckinNumPack   int64                   `json:"checkinNumPack,omitempty" bson:"checkin_num_pack,omitempty"`
	IsBookDropOff    *bool                   `json:"isBookDropOff,omitempty" bson:"is_book_drop_off,omitempty"`
	OrderValue       *float64                `json:"orderValue,omitempty" bson:"order_value,omitempty"`
	VoucherCode      string                  `json:"voucherCode,omitempty" bson:"voucher_code,omitempty"`
	CustomerType     *enum.CustomerTypeValue `json:"customerType,omitempty" bson:"customer_type,omitempty"`
	MergeStatus      *enum.MergeStatusValue  `json:"mergeStatus,omitempty" bson:"merge_status,omitempty"`
	ExpiredAt        *time.Time              `json:"expiredAt,omitempty" bson:"expired_at,omitempty"`
	NeedAutoCancelAt *time.Time              `json:"needAutoCancelAt,omitempty" bson:"need_auto_cancel_at,omitempty"`
	CustomerInfo     *Customer               `json:"customerInfo,omitempty" bson:"customer_info,omitempty"`
	DriverActivities []*DriverActivity       `json:"driverActivities,omitempty" bson:"driver_activities,omitempty"`

	// OPM
	OPM             bool     `json:"opm,omitempty" bson:"opm,omitempty"`
	PickMoney       *float64 `json:"pickMoney,omitempty" bson:"pick_money,omitempty"`
	AccountToken    string   `json:"accountToken,omitempty" bson:"account_token,omitempty"`
	SubAccountToken string   `json:"subAccountToken,omitempty" bson:"sub_account_token,omitempty"`
	// Lead time
	FMHub        string                  `json:"fmHub,omitempty" bson:"fm_hub,omitempty"`
	LMHub        string                  `json:"lmHub,omitempty" bson:"lm_hub,omitempty"`
	ProductType  enum.ProductTypeValue   `json:"productType,omitempty" bson:"product_type,omitempty"`
	DonePackTime int64                   `json:"donePackTime,omitempty" bson:"done_pack_time,omitempty"`
	ProductTypes []enum.ProductTypeValue `json:"productTypes,omitempty" bson:"product_types,omitempty"`

	PickupLeadTime    *time.Time `json:"pickupLeadTime,omitempty" bson:"pickup_lead_time,omitempty"`
	DeliveryLeadTime  *time.Time `json:"deliveryLeadTime,omitempty" bson:"delivery_lead_time,omitempty"`
	TransportLeadTime *time.Time `json:"transportLeadTime,omitempty" bson:"transport_lead_time,omitempty"`
	Baskets           []Basket   `json:"baskets,omitempty" bson:"baskets,omitempty"`

	References []string `json:"references,omitempty" bson:"references,omitempty"`
}

type DeliveryNote struct {
	Description string `json:"description" bson:"description,omitempty"`
	ActionTime  int64  `json:"actionTime" bson:"action_time,omitempty"`
	CreatedBy   int64  `json:"createdBy" bson:"created_by,omitempty"`
}

type DriverActivity struct {
	DriverId           int64                         `json:"driverId,omitempty" bson:"driver_id,omitempty"`
	ActionTime         int64                         `json:"actionTime,omitempty" bson:"action_time,omitempty"`
	ProductivityAction *enum.ProductivityActionValue `json:"productivityAction,omitempty" bson:"productivity_action,omitempty"`
	HubCode            string                        `json:"hubCode,omitempty" bson:"hub_code,omitempty"`
	HubOrderType       *enum.HubOrderTypeValue       `json:"hubOrderType,omitempty" bson:"hub_order_type,omitempty"`
	HubOrderSubType    *enum.SubTypeValue            `json:"hubOrderSubType,omitempty" bson:"hub_order_sub_type,omitempty"`
	CompleteAssignAt   int64                         `json:"completeAssignAt,omitempty" bson:"complete_assign_at,omitempty"`
}

type Basket struct {
	Code string `json:"code,omitempty" bson:"code,omitempty"`
}

type Product struct {
	Name     string                   `json:"name,omitempty" bson:"name,omitempty"`
	Category string                   `json:"category,omitempty" bson:"category,omitempty"`
	Quantity int64                    `json:"quantity,omitempty" bson:"quantity,omitempty"`
	Weight   float64                  `json:"weight,omitempty" bson:"weight,omitempty"`
	Height   float64                  `json:"height,omitempty" bson:"height,omitempty"`
	Width    float64                  `json:"width,omitempty" bson:"width,omitempty"`
	Length   float64                  `json:"length,omitempty" bson:"length,omitempty"`
	SKU      string                   `json:"sku,omitempty" bson:"sku,omitempty"`
	Price    float64                  `json:"price,omitempty" bson:"price,omitempty"`
	Status   *enum.ProductStatusValue `json:"status,omitempty" bson:"status,omitempty"`
}

var ShippingOrderDB = &CustomInstance{
	Instance: db.Instance{
		ColName:        "shipping_order",
		TemplateObject: &ShippingOrder{},
	},
	SecondaryInstance: db.Instance{
		ColName:        "shipping_order",
		TemplateObject: &ShippingOrder{},
	},
}

func InitSecondaryShippingOrderModel(s *mongo.Database) {
	ShippingOrderDB.SecondaryInstance.ApplyDatabase(s)
}

func InitShippingOrderModel(s *mongo.Database) {
	ShippingOrderDB.ApplyDatabase(s)

	t := true
	//ShippingOrderDB.CreateIndex(bson.D{
	//	{"scope", 1},
	//	{"status", 1},
	//	{"type", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})

	//ShippingOrderDB.CreateIndex(bson.D{
	//	{"reference_code", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//	Unique:     &t,
	//})

	//
	//ShippingOrderDB.CreateIndex(bson.D{
	//	{"tpl_code", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})
	//
	//ShippingOrderDB.CreateIndex(bson.D{
	//	{"status", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})
	//
	//ShippingOrderDB.CreateIndex(bson.D{
	//	{"admin_order_id", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})
	//ShippingOrderDB.CreateIndex(bson.D{
	//	{"action_time", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})

	//
	//ShippingOrderDB.CreateIndex(bson.D{
	//	{"estimate_picking_time", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})
	//
	//ShippingOrderDB.CreateIndex(bson.D{
	//	{"estimate_delivering_time", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})
	//
	//ShippingOrderDB.CreateIndex(bson.D{
	//	{"estimate_returning_time", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})
	//
	//ShippingOrderDB.CreateIndex(bson.D{
	//	{"customer_code", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})
	//
	//ShippingOrderDB.CreateIndex(bson.D{
	//	{"driver_id", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})

	ShippingOrderDB.CreateIndex(bson.D{
		{"from_province_code", 1},
		{"type", 1},
		{"status", 1},
		{"action_time", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	ShippingOrderDB.CreateIndex(bson.D{
		{"references", 1},
		{"status", 1},
		{"action_time", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	ShippingOrderDB.CreateIndex(bson.D{
		{"status", 1},
		{"type", 1},
		{"action_time", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	ShippingOrderDB.CreateIndex(bson.D{
		{"tpl_service_id", 1},
		{"status", 1},
		{"action_time", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	ShippingOrderDB.CreateIndex(bson.D{
		{"customer_province_code", 1},
		{"action_time", 1},
		{"status", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	ShippingOrderDB.CreateIndex(bson.D{
		{"merge_status", 1},
		{"status", 1},
		{"action_time", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	ShippingOrderDB.CreateIndex(bson.D{
		{"parent_receive_session_code", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	ShippingOrderDB.CreateIndex(bson.D{
		{"customer_info.code", 1},
		{"type", 1},
		{"status", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	ShippingOrderDB.CreateIndex(bson.D{
		{"driver_activities.driver_id", 1},
		{"driver_activities.hub_code", 1},
		{"driver_activities.productivity_action", 1},
		{"driver_activities.hub_order_type", 1},
		{"driver_activities.action_time", 1},
		{"driver_activities.complete_assign_at", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	ShippingOrderDB.CreateIndex(bson.D{
		{"driver_activities.driver_id", 1},
		{"driver_activities.hub_code", 1},
		{"driver_activities.productivity_action", 1},
		{"driver_activities.hub_order_type", 1},
		{"driver_activities.hub_order_sub_type", 1},
		{"driver_activities.action_time", 1},
		{"driver_activities.complete_assign_at", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	ShippingOrderDB.CreateIndex(bson.D{
		{"type", 1},
		{"tpl_service_id", 1},
		{"delivered_time", 1},
		{"_id", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	ShippingOrderDB.CreateIndex(bson.D{
		{"delivery_lead_time", 1},
		{"status", 1},
		{"_id", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	ShippingOrderDB.CreateIndex(bson.D{
		{"need_auto_cancel_at", 1},
		{"status", 1},
		{"_id", -1},
	}, &options.IndexOptions{
		Background: &t,
	})

	ShippingOrderDB.CreateIndex(bson.D{
		{"products.sku", 1},
		{"status", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	ShippingOrderDB.CreateIndex(bson.D{
		{"type", 1},
		{"baskets.code", 1},
		{"status", 1},
		{"created_time", 1},
	}, &options.IndexOptions{
		Background: &t,
	})
}
