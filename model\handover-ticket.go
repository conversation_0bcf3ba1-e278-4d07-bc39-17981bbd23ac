package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type HandoverSOItem struct {
	SO               string                        `json:"so,omitempty" bson:"so,omitempty"`
	Weight           float64                       `json:"weight,omitempty" bson:"weight,omitempty"`
	NumPackage       int64                         `json:"numPackage,omitempty" bson:"num_package,omitempty"`
	ScannedQuantity  int64                         `json:"scannedQuantity,omitempty" bson:"scanned_quantity,omitempty"`
	ReceivedQuantity int64                         `json:"receivedQuantity" bson:"received_quantity"`
	TrackingCode     string                        `json:"trackingCode,omitempty" bson:"tracking_code,omitempty"`
	Status           *enum.HandoverItemStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	TplCode          string                        `json:"tplCode,omitempty" bson:"tpl_code,omitempty"`
	Note             string                        `json:"note" bson:"note,omitempty"`
	Products         []*Product                    `json:"products,omitempty" bson:"products,omitempty"`
	CheckInCode      string                        `json:"checkInCode,omitempty" bson:"check_in_code,omitempty"`
	ReferenceCode    string                        `json:"referenceCode,omitempty" bson:"reference_code,omitempty"`
	MergeStatus      *enum.MergeStatusValue        `json:"mergeStatus,omitempty" bson:"merge_status,omitempty"`
	ExpiredAt        *time.Time                    `json:"expiredAt,omitempty" bson:"expired_at,omitempty"`
	Baskets          []Basket                      `json:"baskets,omitempty" bson:"baskets,omitempty"`
	Tags             []string                      `json:"tags,omitempty" bson:"tags,omitempty"`
}

type HandoverLogs struct {
	FromDepartmentCode string                    `json:"fromDepartmentCode,omitempty" bson:"from_department_code,omitempty"`
	FromDepartmentName string                    `json:"fromDepartmentName,omitempty" bson:"from_department_name,omitempty"`
	ToDepartmentCode   string                    `json:"toDepartmentCode,omitempty" bson:"to_department_code,omitempty"`
	ToDepartmentName   string                    `json:"toDepartmentName,omitempty" bson:"to_department_name,omitempty"`
	HandoverNote       string                    `json:"handoverNote,omitempty" bson:"handover_note,omitempty"`
	Status             *enum.HandoverStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	ActionTime         *time.Time                `json:"actionTime,omitempty" bson:"action_time,omitempty"`
	ExtraData          map[string]interface{}    `json:"extraData,omitempty" bson:"extra_data,omitempty"`
}

// HandoverTicket model
type HandoverTicket struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	VersionNo       string             `json:"versionNo,omitempty" bson:"version_no,omitempty" `
	CreatedBy       int64              `json:"createdBy,omitempty" bson:"created_by,omitempty" `
	UpdatedBy       int64              `json:"updatedBy,omitempty" bson:"updated_by,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	WarehouseCode      string     `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`
	FromDepartmentCode string     `json:"fromDepartmentCode,omitempty" bson:"from_department_code,omitempty"`
	FromDepartmentName string     `json:"fromDepartmentName,omitempty" bson:"from_department_name,omitempty"`
	ToDepartmentCode   string     `json:"toDepartmentCode,omitempty" bson:"to_department_code,omitempty"`
	ToDepartmentName   string     `json:"toDepartmentName,omitempty" bson:"to_department_name,omitempty"`
	TicketID           int        `json:"ticketId,omitempty" bson:"ticket_id,omitempty"`
	Code               string     `json:"code,omitempty" bson:"code,omitempty"`
	TPLCode            string     `json:"tplCode,omitempty" bson:"tpl_code,omitempty"`
	TPLName            string     `json:"tplName,omitempty" bson:"tpl_name,omitempty"`
	CompletedTime      *time.Time `json:"completedTime,omitempty" bson:"completed_time,omitempty"`
	DeliveredTime      *time.Time `json:"deliveredTime,omitempty" bson:"delivered_time,omitempty"`
	DoneHandoverTime   *time.Time `json:"doneHandoverTime,omitempty" bson:"done_handover_time,omitempty"`
	ActionTime         *time.Time `json:"actionTime,omitempty" bson:"action_time,omitempty"`
	HandoverNote       string     `json:"handoverNote,omitempty" bson:"handover_note,omitempty"`
	ReceiverId         int64      `json:"receiverId,omitempty" bson:"receiver_id,omitempty"`
	ReceiverName       string     `json:"receiverName,omitempty" bson:"receiver_name,omitempty"`

	HandoverType *enum.HandoverTypeValue   `json:"handoverType,omitempty" bson:"handover_type,omitempty"`
	Status       *enum.HandoverStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	SOList       []HandoverSOItem          `json:"soList,omitempty" bson:"so_list,omitempty"`
	Logs         []*HandoverLogs           `json:"logs,omitempty" bson:"logs,omitempty"`

	ItemType *enum.HandoverItemTypeValue `json:"itemType,omitempty" bson:"item_type,omitempty"`

	TripId    int64  `json:"tripId,omitempty" bson:"trip_id,omitempty"`
	TripCode  string `json:"tripCode,omitempty" bson:"trip_code,omitempty"`
	RouteCode string `json:"routeCode,omitempty" bson:"route_code,omitempty"`
	RouteName string `json:"routeName,omitempty" bson:"route_name,omitempty"`

	TruckID      int64  `json:"truckId,omitempty" bson:"truck_id,omitempty"`
	LicensePlate string `json:"licensePlate,omitempty" bson:"license_plate,omitempty"`

	DriverID    int    `json:"driverId,omitempty" bson:"driver_id,omitempty"`
	DriverName  string `json:"driverName,omitempty" bson:"driver_name,omitempty"`
	PhoneNumber string `json:"phoneNumber,omitempty" bson:"phone_number,omitempty"`

	ExtraData map[string]interface{} `json:"extraData,omitempty" bson:"extra_data,omitempty"`

	FromAddress  *Address `json:"fromAddress,omitempty" bson:"from_address,omitempty"`
	ToAddress    *Address `json:"toAddress,omitempty" bson:"to_address,omitempty"`
	CreatedFrom  string   `json:"createdFrom,omitempty" bson:"created_from,omitempty"`
	NumOfBaskets int64    `json:"numOfBaskets,omitempty" bson:"num_of_baskets,omitempty"`

	IsContainsFrozenItem *bool  `json:"isContainsFrozenItem,omitempty" bson:"is_contains_frozen_item,omitempty"`
	CarrierId            int64  `json:"carrierId,omitempty" bson:"carrier_id,omitempty"`
	CarrierName          string `json:"carrierName,omitempty" bson:"carrier_name,omitempty"`
}

// HandoverTicketDB represent DB repo of this model
var HandoverTicketDB = &CustomInstance{
	Instance: db.Instance{
		ColName:        "handover_ticket",
		TemplateObject: &HandoverTicket{},
	},
	SecondaryInstance: db.Instance{
		ColName:        "handover_ticket",
		TemplateObject: &HandoverTicket{},
	},
}

func InitSecondaryHandoverTicketModel(s *mongo.Database) {
	HandoverTicketDB.SecondaryInstance.ApplyDatabase(s)
}

// InitHandoverTicketModel ...
func InitHandoverTicketModel(s *mongo.Database) {
	HandoverTicketDB.ApplyDatabase(s)

	t := true
	HandoverTicketDB.CreateIndex(bson.D{
		{"tpl_code", 1},
		{"status", 1},
	}, &options.IndexOptions{
		Unique:     &t,
		Background: &t,
	})

	//HandoverTicketDB.CreateIndex(bson.D{
	//	{"ticket_id", 1},
	//}, &options.IndexOptions{
	//	Unique:     &t,
	//	Background: &t,
	//})
	//

	//HandoverTicketDB.CreateIndex(bson.D{
	//	{"code", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})
	//
	//HandoverTicketDB.CreateIndex(bson.D{
	//	{"completed_time", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})
	//
	//HandoverTicketDB.CreateIndex(bson.D{
	//	{"to_department_code", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})
	//
	//HandoverTicketDB.CreateIndex(bson.D{
	//	{"status", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})
	//
	HandoverTicketDB.CreateIndex(bson.D{
		{"to_department_code", 1},
		{"status", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	HandoverTicketDB.CreateIndex(bson.D{
		{"from_department_code", 1},
		{"status", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	HandoverTicketDB.CreateIndex(bson.D{
		{"driver_id", 1},
		{"status", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	HandoverTicketDB.CreateIndex(bson.D{
		{"created_time", 1},
		{"status", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	HandoverTicketDB.CreateIndex(bson.D{
		{"from_department_code", 1},
		{"to_department_code", 1},
		{"status", 1},
		{"_id", -1},
	}, &options.IndexOptions{
		Background: &t,
	})

	HandoverTicketDB.CreateIndex(bson.D{
		{"so_list.reference_code", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	HandoverTicketDB.CreateIndex(bson.D{
		{Key: "so_list.so", Value: 1},
		{Key: "so_list.status", Value: 1},
		{Key: "status", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})
}
