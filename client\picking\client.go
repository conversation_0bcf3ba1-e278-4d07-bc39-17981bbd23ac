package picking

import (
	"time"

	"go.mongodb.org/mongo-driver/mongo"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/golang/configuration"
)

const (
	pathCompleteHandOver = "/warehouse/picking/v1/pick-ticket/hand-over"
	pathGetTicket        = "/warehouse/picking/v1/pick-ticket"
)

type PickingClient struct {
	svc     *client.RestClient
	headers map[string]string
}

func NewServiceClient(serviceConfig configuration.Service, logName string, session *mongo.Database) *PickingClient {
	if serviceConfig.Host == "" {
		return nil
	}
	pickClient := &PickingClient{
		svc: client.NewRESTClient(
			serviceConfig.Host,
			logName,
			300*time.Second,
			5,
			3*time.Second,
		),
		headers: map[string]string{
			"Authorization": serviceConfig.Authorization,
		},
	}
	pickClient.svc.SetDBLog(session)
	return pickClient
}
