package api

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
)

// CreateDeliveryTruck api
func CreateDeliveryTruck(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.DeliveryTruck
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.CreateDeliveryTruck(&input))
}

// GetDeliveryTruck api
func GetDeliveryTruck(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input request.DeliveryTruckQuery
	str := req.GetParam("q")
	if str == "" {
		str = "{}"
	}
	var offset = int64(sdk.ParseInt(req.GetParam("offset"), 0))
	var limit = int64(sdk.ParseInt(req.GetParam("limit"), 100))
	var getTotal = req.GetParam("getTotal") == "true"
	err := json.Unmarshal([]byte(str), &input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.GetDeliveryTruck(&input, offset, limit, getTotal))
}

// UpdateDeliveryTruck api
func UpdateDeliveryTruck(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.DeliveryTruck
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.UpdateDeliveryTruck(&input))
}

// DeleteDeliveryTruck api
func DeleteDeliveryTruck(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.DeliveryTruck
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.DeleteDeliveryTruck(input.LicensePlate))
}
