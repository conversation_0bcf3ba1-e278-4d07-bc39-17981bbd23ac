# This is an example goreleaser.yaml file with some sane defaults.
# Make sure to check the documentation at http://goreleaser.com
before:
  hooks:
    - ./gen.sh
    - go install mvdan.cc/garble@v0.9.3

builds:
  -
    id: "s2c"
    binary: s2c
    main: ./s2/cmd/s2c/main.go
    flags:
      - -trimpath
    env:
      - CGO_ENABLED=0
    goos:
      - aix
      - linux
      - freebsd
      - netbsd
      - windows
      - darwin
    goarch:
      - 386
      - amd64
      - arm
      - arm64
      - ppc64
      - ppc64le
      - mips64
      - mips64le
    goarm:
      - 7
    gobinary: garble
  -
    id: "s2d"
    binary: s2d
    main: ./s2/cmd/s2d/main.go
    flags:
      - -trimpath
    env:
      - CGO_ENABLED=0
    goos:
      - aix
      - linux
      - freebsd
      - netbsd
      - windows
      - darwin
    goarch:
      - 386
      - amd64
      - arm
      - arm64
      - ppc64
      - ppc64le
      - mips64
      - mips64le
    goarm:
      - 7
    gobinary: garble
  -
    id: "s2sx"
    binary: s2sx
    main: ./s2/cmd/_s2sx/main.go
    flags:
      - -modfile=s2sx.mod
      - -trimpath
    env:
      - CGO_ENABLED=0
    goos:
      - aix
      - linux
      - freebsd
      - netbsd
      - windows
      - darwin
    goarch:
      - 386
      - amd64
      - arm
      - arm64
      - ppc64
      - ppc64le
      - mips64
      - mips64le
    goarm:
      - 7
    gobinary: garble

archives:
  -
    id: s2-binaries
    name_template: "s2-{{ .Os }}_{{ .Arch }}_{{ .Version }}"
    replacements:
      aix: AIX
      darwin: OSX
      linux: Linux
      windows: Windows
      386: i386
      amd64: x86_64
      freebsd: FreeBSD
      netbsd: NetBSD
    format_overrides:
      - goos: windows
        format: zip
    files:
      - unpack/*
      - s2/LICENSE
      - s2/README.md
checksum:
  name_template: 'checksums.txt'
snapshot:
  name_template: "{{ .Tag }}-next"
changelog:
  sort: asc
  filters:
    exclude:
    - '^doc:'
    - '^docs:'
    - '^test:'
    - '^tests:'
    - '^Update\sREADME.md'

nfpms:
  -
    file_name_template: "s2_package_{{ .Version }}_{{ .Os }}_{{ .Arch }}"
    vendor: Klaus Post
    homepage: https://github.com/klauspost/compress
    maintainer: Klaus Post <<EMAIL>>
    description: S2 Compression Tool
    license: BSD 3-Clause
    formats:
      - deb
      - rpm
    replacements:
      darwin: Darwin
      linux: Linux
      freebsd: FreeBSD
      amd64: x86_64
