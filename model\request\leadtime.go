package request

import (
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
)

type DeleteConfigLeadTimeRequest struct {
	FromHub           string                       `json:"fromHub"`
	ShippingOrderType *enum.ShippingOrderTypeValue `json:"shippingOrderType"`
	CarrierCode       string                       `json:"carrierCode"`
	ProvinceCode      string                       `json:"provinceCode"`
}

type UpdateConfigLeadTimeRequest struct {
	FromHub           string                          `json:"fromHub"`
	ShippingOrderType *enum.ShippingOrderTypeValue    `json:"shippingOrderType"`
	CarrierCode       string                          `json:"carrierCode"`
	CarrierName       string                          `json:"carrierName"`
	Provinces         []*model.ConfigLeadTimeProvince `json:"provinces"`
}
