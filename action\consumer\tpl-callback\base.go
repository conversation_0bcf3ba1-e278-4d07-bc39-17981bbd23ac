package sync_data

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"log"
	"sync"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
	"go.mongodb.org/mongo-driver/mongo"
)

type ExecutorJob struct {
	Job *job.Executor
}

var (
	instance                   map[string]*ExecutorJob
	onceInit                   map[string]*sync.Once
	tplCallbackTopic           = "tpl_callback"
	syncStatusTPLCallbackTopic = "sync_status_tpl_callback"
	postStatusTPLCallbackTopic = "post_status_tpl_callback"
	assignTPLCallbackTopic     = "assign_tpl_callback"
	sendCallbackTopic          = "send_callback"
	assignShipperCallbackTopic = "assign_shipper_callback"
	// Remove here one done change queue
	oldOnceInit map[string]*sync.Once
	oldInstance map[string]*ExecutorJob
)

func init() {
	onceInit = map[string]*sync.Once{}
	instance = map[string]*ExecutorJob{}
	// Remove here one done change queue
	oldOnceInit = map[string]*sync.Once{}
	oldInstance = map[string]*ExecutorJob{}
}

func init() {
	tplCallbackTopic = conf.Config.Topics["tpl_callback"]
	syncStatusTPLCallbackTopic = conf.Config.Topics["sync_status_tpl_callback"]
	postStatusTPLCallbackTopic = conf.Config.Topics["post_status_tpl_callback"]
	assignTPLCallbackTopic = conf.Config.Topics["assign_tpl_callback"]
	sendCallbackTopic = conf.Config.Topics["send_callback"]
	assignShipperCallbackTopic = conf.Config.Topics["assign_shipper_callback"]
}

// InitTPLCallbackExecutor func
func InitTPLCallbackExecutor(dbSession *mongo.Database, Database, Collection string) {
	instanceName := tplCallbackTopic
	if oldOnceInit[instanceName] == nil {
		oldOnceInit[instanceName] = &sync.Once{}
	}
	oldOnceInit[instanceName].Do(func() {
		oldInstance[instanceName] = &ExecutorJob{
			&job.Executor{ColName: Collection},
		}

		oldInstance[instanceName].Job.InitWithConfig(dbSession,
			Database,
			&job.ExecutorConfiguration{
				ParallelTopicProcessing: false,
				FailThreshold:           50,
				ChannelCount:            5,
				SortedItem:              false,
				LogSize:                 5,
				SelectorDelayMS:         100,
				WaitForReadyTime:        false,
				UniqueItem:              false,
				ConsumedExipredTime:     time.Duration(720) * time.Hour,
				CurVersionTimeoutS:      30,
				OldVersionTimeoutS:      30,
				MaximumWaitToRetryS:     15,
			},
		)

		log.Println("StartConsume", instanceName)
		oldInstance[instanceName].tplCallbackConsume()
		oldInstance[instanceName].updateStatusTPLCallback()
		oldInstance[instanceName].postStatusTPLCallback()
		oldInstance[instanceName].assignCallback()
		oldInstance[instanceName].sendCallback()
		oldInstance[instanceName].assignShipperCallback()
		oldInstance[instanceName].Job.StartConsume()
	})
}

func InitTPLCallbackJob(dbSession *mongo.Database, Database, Collection string) {
	instanceName := tplCallbackTopic
	if onceInit[instanceName] == nil {
		onceInit[instanceName] = &sync.Once{}
	}
	onceInit[instanceName].Do(func() {
		instance[instanceName] = &ExecutorJob{
			&job.Executor{ColName: Collection},
		}

		instance[instanceName].Job.InitWithConfig(dbSession,
			Database,
			&job.ExecutorConfiguration{
				ParallelTopicProcessing: false,
				FailThreshold:           50,
				ChannelCount:            5,
				SortedItem:              false,
				LogSize:                 5,
				SelectorDelayMS:         100,
				WaitForReadyTime:        false,
				UniqueItem:              false,
				ConsumedExipredTime:     time.Duration(720) * time.Hour,
				CurVersionTimeoutS:      30,
				OldVersionTimeoutS:      30,
				MaximumWaitToRetryS:     15,
			},
		)

		log.Println("StartConsume", instanceName)
		instance[instanceName].tplCallbackConsume()
		instance[instanceName].updateStatusTPLCallback()
		instance[instanceName].postStatusTPLCallback()
		instance[instanceName].assignCallback()
		instance[instanceName].sendCallback()
		instance[instanceName].assignShipperCallback()
		instance[instanceName].Job.StartConsume()
	})
}

func PushCallbackQueue(data interface{}, sortedKey string) (err error) {
	return instance[tplCallbackTopic].Job.Push(
		data, &job.JobItemMetadata{
			Topic:     tplCallbackTopic,
			SortedKey: sortedKey,
		})
}

func PushSyncStatusTPLCallbackQueue(data interface{}, sortedKey string) (err error) {
	return instance[tplCallbackTopic].Job.Push(
		data, &job.JobItemMetadata{
			Topic:     syncStatusTPLCallbackTopic,
			SortedKey: sortedKey,
		})
}

func PushCreateTPLCallbackQueue(data interface{}, sortedKey string) (err error) {
	return instance[tplCallbackTopic].Job.Push(
		data, &job.JobItemMetadata{
			Topic:     postStatusTPLCallbackTopic,
			SortedKey: sortedKey,
		})
}

func PushAssignTPLCallbackQueue(data interface{}, sortedKey string) (err error) {
	return instance[tplCallbackTopic].Job.Push(
		data, &job.JobItemMetadata{
			Topic:     assignTPLCallbackTopic,
			SortedKey: sortedKey,
		})
}

func PushSendCallbackQueue(data interface{}, sortedKey string) (err error) {
	return instance[tplCallbackTopic].Job.Push(
		data, &job.JobItemMetadata{
			Topic:     sendCallbackTopic,
			SortedKey: sortedKey,
		})
}

func PushAssignShipperCallbackQueue(data interface{}, sortedKey string) (err error) {
	return instance[tplCallbackTopic].Job.Push(
		data, &job.JobItemMetadata{
			Topic:     assignShipperCallbackTopic,
			SortedKey: sortedKey,
		})
}

func GetExecuteQueueDatabase() *db.Instance {
	return instance[tplCallbackTopic].Job.GetJobDB()
}
