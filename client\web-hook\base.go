package web_hook

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
	"go.mongodb.org/mongo-driver/mongo"
)

type WebHookClient struct {
	svc    *client.RestClient
	Config *conf.WebHookConfig
}

func NewServiceClient(apiHost, logName string, config *conf.WebHookConfig, session *mongo.Database) *WebHookClient {
	if apiHost == "" {
		return nil
	}
	webHookClient := &WebHookClient{
		svc: client.NewRESTClient(
			apiHost,
			logName,
			30*time.Second,
			0,
			30*time.Second,
		),
		Config: config,
	}
	webHookClient.svc.SetDBLog(session)

	return webHookClient
}
