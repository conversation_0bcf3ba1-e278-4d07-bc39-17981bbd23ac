package hrm

import (
	"encoding/json"
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
)

func (c *HrmClient) CreateDepartment(createDepartmentRequest model.Department) (response *common.APIResponse) {
	var res *client.RestResult
	res, err := c.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, c.headers, nil, createDepartmentRequest, pathDepartment, nil)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Bad request when create department",
		}
	}

	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Bad request when Unmarshal create department response",
		}
	}

	return
}

func (c *HrmClient) GetDepartmentByCode(code string) (response *common.APIResponse) {
	params := map[string]string{
		"code": code,
	}

	var res *client.RestResult
	res, err := c.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, c.headers, params, nil, pathDepartment, nil)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Bad request when create department",
		}
	}

	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Bad request when Unmarshal create department response",
		}
	}

	return
}

func (c *HrmClient) GetAllUserDepartments(username string) ([]*model.Department, error) {
	params := map[string]string{
		"username": username,
	}
	res, err := c.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, c.headers, params, nil, pathGetAllUserDepartments, nil)
	if err != nil {
		return nil, fmt.Errorf("Đã có lỗi xảy ra khi lấy danh sách phòng ban của nhân viên")
	}

	if res.Code == 404 {
		return nil, fmt.Errorf("Không tìm thấy phòng ban của nhân viên")
	}

	type myResponse struct {
		Status  string              `json:"status"`
		Code    string              `json:"code"`
		Message string              `json:"message"`
		Data    []*model.Department `json:"data"`
	}

	result := new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, fmt.Errorf("Kết quả trả về không hợp lệ")
	}

	return result.Data, nil
}
