package action

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	sync_data "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action/consumer/tpl-callback"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/shipping"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"go.mongodb.org/mongo-driver/bson"
)

func GetDetailOrder(listReferenceCode []string) *common.APIResponse {
	if len(listReferenceCode) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Danh sách mã không được để trống",
		}
	}

	listShippingOrder := model.ShippingOrderDB.Query(bson.M{
		"reference_code": bson.M{
			"$in": listReferenceCode,
		},
	}, 0, 1000, nil)

	if listShippingOrder.Status != common.APIStatus.Ok || listShippingOrder.Data == nil {
		return listShippingOrder
	}

	listShippingOrderInfo := listShippingOrder.Data.([]*model.ShippingOrder)

	err := getDetailOrderCarrier(listShippingOrderInfo)

	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Error(),
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Lấy thông tin đơn hàng từ nhà vận chuyển thành công",
	}
}

func getDetailOrderCarrier(shippingOrders []*model.ShippingOrder) error {
	var listTplServiceId []int64
	mapListTplService := make(map[int64]*model.Carrier)
	mapListTplInternal := make(map[int64]*model.Carrier)

	for _, v := range shippingOrders {
		if !CheckItemInArray(v.TplServiceId, listTplServiceId) {
			listTplServiceId = append(listTplServiceId, v.TplServiceId)
		}
	}

	listCarrier := model.CarrierDB.Query(bson.M{
		"carrier_id": bson.M{
			"$in": listTplServiceId,
		},
	}, 0, 1000, nil)
	if listCarrier.Status != common.APIStatus.Ok || listCarrier.Data == nil {
		var listCarrierErrorId []string
		for _, carrier := range listTplServiceId {
			listCarrierErrorId = append(listCarrierErrorId, strconv.Itoa(int(carrier)))
		}
		return fmt.Errorf(listCarrier.Message + strings.Join(listCarrierErrorId, ","))
	}

	listCarrierInfo := listCarrier.Data.([]*model.Carrier)

	for _, v := range listCarrierInfo {
		if v.ExtraData == nil && v.IsInternal != nil && !*v.IsInternal {
			carrierParent := model.CarrierDB.QueryOne(&model.Carrier{
				CarrierCode: v.ParentCode,
			})

			if carrierParent.Status != common.APIStatus.Ok || carrierParent.Data == nil {
				return fmt.Errorf("Không tìm thấy thông tin của: ", v.ParentCode)
			}

			carrierParentInfo := carrierParent.Data.([]*model.Carrier)[0]
			if carrierParentInfo.ExtraData == nil {
				return fmt.Errorf("Không tìm thấy thông tin nhà vận chuyển ", carrierParentInfo.CarrierCode)
			}
			v.ExtraData = carrierParentInfo.ExtraData
		}
		if v.IsInternal != nil && *v.IsInternal {
			mapListTplInternal[v.CarrierId] = v
		}
		mapListTplService[v.CarrierId] = v
	}

	for _, v := range shippingOrders {
		if mapListTplInternal[v.TplServiceId] != nil {
			continue
		}
		if mapListTplService[v.TplServiceId] == nil || mapListTplService[v.TplServiceId].ExtraData == nil {
			_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
				CmdType: "MIGRATE_SHIPPING-ORDER_CARRIER_INFO_ERROR",
				Title:   strconv.Itoa(int(v.TplServiceId)) + ", SO: " + v.ReferenceCode,
				Message: "Không tìm thấy thông tin dịch vụ vận chuyển",
			})
			continue
		}
		err := shipping.ChangeInfoCarrier(mapListTplService[v.TplServiceId])
		if err != nil {
			_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
				CmdType: "MIGRATE_SHIPPING-ORDER_CARRIER_INFO_ERROR",
				Title:   v.ReferenceCode,
				Message: err.Error(),
			})
			continue
		}
		if shipping.TplShippingClient == nil {
			_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
				CmdType: "MIGRATE_SHIPPING-ORDER_ERR",
				Title:   v.ReferenceCode,
				Message: "Dịch vụ book vận chuyển chưa được tạo",
			})
			continue
		}
		carrierMessage, _ := json.Marshal(mapListTplService[v.TplServiceId])
		switch *v.TplCode {
		case enum.Partner.GHTK:
			if shipping.TplShippingClient.GHTKClient == nil {
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "MIGRATE_SHIPPING-ORDER_ERR",
					Title:   string(carrierMessage),
					Message: "Không tìm thấy thông tin nhà vận chuyển " + string(*v.TplCode),
				})
				continue
			}
			ghtkOrder, err := shipping.TplShippingClient.GHTKClient.GetDetailOrder(v.TrackingCode)
			if err != nil {
				continue
			}

			callbackGHTK := request.GHTKCallback{
				PartnerId:  ghtkOrder.PartnerId,
				LabelId:    ghtkOrder.LabelId,
				StatusId:   ghtkOrder.Status,
				ReasonCode: ghtkOrder.ReasonCode,
				Reason:     ghtkOrder.Reason,
				Weight:     ghtkOrder.Weight,
				Fee:        ghtkOrder.Fee,
				PickMoney:  ghtkOrder.PickMoney,
			}

			if ghtkOrder.Modified != "" {
				ghtkOrder.Modified = strings.ReplaceAll(ghtkOrder.Modified, "-", "/")
				t, err := time.Parse("2006/01/02 15:04:05", ghtkOrder.Modified)
				if err == nil {
					actionTime := t.Add(-time.Hour * 7)
					callbackGHTK.ActionTime = &actionTime
				}
			}
			_ = client.Services.TplCallbackClient.GHTKCallback(callbackGHTK)
			break
		case enum.Partner.VIETTEL_POST:
			break
		case enum.Partner.AHAMOVE:
			if shipping.TplShippingClient.AhamoveClient == nil {
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "MIGRATE_SHIPPING-ORDER_ERR",
					Title:   string(carrierMessage),
					Message: "Không tìm thấy thông tin nhà vận chuyển " + string(*v.TplCode),
				})
				continue
			}
			ahamoveOrder, err := shipping.TplShippingClient.AhamoveClient.GetDetailOrder(mapListTplService[v.TplServiceId].ExtraData.AccessToken, v.TrackingCode)
			if err != nil {
				continue
			}

			_ = client.Services.TplCallbackClient.AhamoveCallback(ahamoveOrder)
			break
		case enum.Partner.NHAT_TIN:
			if shipping.TplShippingClient.NhatTinClient == nil {
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "MIGRATE_SHIPPING-ORDER_ERR",
					Title:   string(carrierMessage),
					Message: "Không tìm thấy thông tin nhà vận chuyển " + string(*v.TplCode),
				})
				continue
			}
			ntOrder, err := shipping.TplShippingClient.NhatTinClient.GetOrderDetail(v.TrackingCode)
			if err != nil {
				continue
			}
			status := enum.NhatTinStatusValue(enum.MappingNhatTinCodeToStatus[ntOrder.BillStatusCode])
			callbackNT := request.NhatTinCallback{
				BillNo:      ntOrder.RefBill,
				RefCode:     v.ReferenceCode,
				StatusId:    &status,
				StatusName:  ntOrder.BilStatus,
				ShippingFee: ntOrder.TotalFee,
			}

			_ = client.Services.TplCallbackClient.NTCallback(callbackNT)
			break
		case enum.Partner.GHN:
			if shipping.TplShippingClient.GHNClient == nil {
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "MIGRATE_SHIPPING-ORDER_ERR",
					Title:   string(carrierMessage),
					Message: "Không tìm thấy thông tin nhà vận chuyển " + string(*v.TplCode),
				})
				continue
			}
			ghnOrder, err := shipping.TplShippingClient.GHNClient.GetOrderInfo(v.TrackingCode)
			if err != nil {
				continue
			}

			callbackGHN := request.GHNCallback{
				CODAmount:       ghnOrder.CodAmount,
				Height:          int(ghnOrder.Height),
				ClientOrderCode: ghnOrder.ClientOrderCode,
				Length:          int(ghnOrder.Length),
				OrderCode:       ghnOrder.OrderCode,
				Status:          ghnOrder.Status,
				Time:            ghnOrder.UpdateDate,
				Warehouse:       ghnOrder.CurrentWarehouse,
				Weight:          ghnOrder.Weight,
				Width:           int(ghnOrder.Width),
			}
			_ = client.Services.TplCallbackClient.GHNCallback(callbackGHN)

			break
		case enum.Partner.SNAPPY:
			if shipping.TplShippingClient.SnappyClient == nil {
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "MIGRATE_SHIPPING-ORDER_ERR",
					Title:   string(carrierMessage),
					Message: "Không tìm thấy thông tin nhà vận chuyển " + string(*v.TplCode),
				})
				continue
			}
			snappyOrder, err := shipping.TplShippingClient.SnappyClient.GetDetailOrder(v.TrackingCode, mapListTplService[v.TplServiceId])
			if err != nil {
				continue
			}

			_ = client.Services.TplCallbackClient.SNAPPYCallback(*snappyOrder)
			break
		case enum.Partner.VNPOST:
			if shipping.TplShippingClient.VNPClient == nil {
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "MIGRATE_SHIPPING-ORDER_ERR",
					Title:   string(carrierMessage),
					Message: "Không tìm thấy thông tin nhà vận chuyển " + string(*v.TplCode),
				})
				continue
			}
			if v.NumPackage == 1 {
				vnpOrder, err := shipping.TplShippingClient.VNPClient.GetDetailOrder(v.ExtraInfo["order_id"].(string))

				if err != nil {
					continue
				}
				var bodyRequest model.VNPOrder
				err = json.Unmarshal([]byte(vnpOrder), &bodyRequest)

				if err != nil {
					continue
				}

				if bodyRequest.ItemCode == v.TrackingCode {
					bodyRequest.OrderCode = v.ReferenceCode
				}

				callback, _ := json.Marshal(bodyRequest)

				_ = client.Services.TplCallbackClient.VNPCallback(string(callback))
			} else {
				extraInfos := v.ExtraInfo["order_id"].(string)
				vnpOrderIds := strings.Split(extraInfos, ",")
				for _, orderId := range vnpOrderIds {
					vnpOrder, err := shipping.TplShippingClient.VNPClient.GetDetailOrder(orderId)
					if err != nil {
						continue
					}
					var bodyRequest model.VNPOrder
					err = json.Unmarshal([]byte(vnpOrder), &bodyRequest)

					if err != nil {
						continue
					}

					if bodyRequest.ItemCode == v.TrackingCode {
						bodyRequest.OrderCode = v.ReferenceCode
					}

					callback, _ := json.Marshal(bodyRequest)
					_ = client.Services.TplCallbackClient.VNPCallback(string(callback))
				}
			}
			break
		}
	}
	return nil
}

func AutoLoadGetDetailCarrier() {
	var offset int64
	query := bson.M{
		"status": bson.M{
			"$in": []string{
				string(enum.TPLCallbackStatus.READY_TO_PICK),
				string(enum.TPLCallbackStatus.PICKING),
				string(enum.TPLCallbackStatus.PICKED),
				string(enum.TPLCallbackStatus.PICK_FAIL),
				string(enum.TPLCallbackStatus.TRANSPORTING),
				string(enum.TPLCallbackStatus.STORING),
				string(enum.TPLCallbackStatus.DELIVERING),
				string(enum.TPLCallbackStatus.RETURN),
				string(enum.TPLCallbackStatus.RETURNING),
			},
		},
	}
	_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
		CmdType: "START_MIGRATE_SHIPPING-ORDER",
		Title:   "START_MIGRATE_SHIPPING-ORDER",
		Message: "Start get detail orders",
	})
LOAD_MORE:
	result := model.ShippingOrderDB.Query(
		query,
		offset,
		1000,
		&bson.M{"_id": -1})

	if result.Status != common.APIStatus.Ok {
		return
	}

	var referenceCodes []string
	shippingOrders := result.Data.([]*model.ShippingOrder)
	for _, shippingOrder := range shippingOrders {
		referenceCodes = append(referenceCodes, shippingOrder.ReferenceCode)
	}

	_ = getDetailOrderCarrier(shippingOrders)

	offset += 1000
	goto LOAD_MORE
}

func CreateCallbackData(
	so, from, to, hubCode string,
	status *enum.TPLCallbackStatusValue,
	shippingOrderType *enum.ShippingOrderTypeValue,
	customStatusName string) {
	actionTime := time.Now()
	callback := request.Callback{
		SO:         so,
		HubCode:    hubCode,
		ActionTime: &actionTime,
		Status:     status,
		TPLStatus:  string(*status),
	}

	if shippingOrderType == nil {
		callback.Type = &enum.ShippingOrderType.DELIVERY
	} else {
		callback.Type = shippingOrderType
	}

	switch *status {
	case enum.TPLCallbackStatus.PICKED:
		callback.StatusName = "Đã lấy hàng."
		if from != "" && to != "" {
			callback.TPLStatusName = to + " Đã lấy hàng từ " + from
		} else {
			callback.TPLStatusName = "Đã lấy hàng."
		}
		break
	case enum.TPLCallbackStatus.TRANSPORTING:
		callback.StatusName = "Đang vận chuyển hàng."
		callback.TPLStatusName = "Đang luân chuyển hàng từ " + from + " đến " + to
		break
	case enum.TPLCallbackStatus.RETURNING:
		callback.StatusName = "Đang Trả hàng."
		callback.TPLStatusName = "Đang trả hàng."
		callback.Action = string(enum.TPLCallbackStatus.TRANSPORTING)
		callback.ActionName = "Đang luân chuyển hàng từ " + from + " đến " + to
		break
	}

	if customStatusName != "" {
		callback.StatusName = customStatusName
		callback.TPLStatusName = customStatusName
	}

	_ = sync_data.PushCreateTPLCallbackQueue(callback, callback.SO)
}
