package utils

import (
	"regexp"

	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
)

func CheckValidCodeFormat(code string) bool {
	isValidCode := regexp.MustCompile(conf.Config.ValidRegexForCode).MatchString
	return isValidCode(code)
}

func CheckPhoneFormat(phone string) bool {
	isValidPhone := regexp.MustCompile(conf.Config.ValidRegexForPhone).MatchString
	return isValidPhone(phone)
}
