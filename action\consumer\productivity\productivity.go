package productivity

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"go.mongodb.org/mongo-driver/bson"
)

func (j *ExecutorJob) driverProductivity() {
	j.Job.SetTopicConsumer(driverProductivity, func(item *job.JobItem) error {
		var dataByte []byte
		var err error
		dataByte, err = bson.Marshal(item.Data)
		if err != nil {
			return err
		}

		driverProductivityRequest := DriverProductivity{}
		err = bson.Unmarshal(dataByte, &driverProductivityRequest)
		if err != nil {
			return err
		}

		shippingOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
			"reference_code": driverProductivityRequest.ReferenceCode,
		})

		if shippingOrderRaw.Status != common.APIStatus.Ok {
			return nil
		}

		shippingOrder := shippingOrderRaw.Data.([]*model.ShippingOrder)[0]

		driverActivity := model.DriverActivity{
			DriverId:           driverProductivityRequest.DriverID,
			ActionTime:         driverProductivityRequest.ActionTime,
			ProductivityAction: &driverProductivityRequest.ProductivityAction,
			HubCode:            driverProductivityRequest.HubCode,
			HubOrderType:       driverProductivityRequest.Type,
			HubOrderSubType:    driverProductivityRequest.SubType,
		}

		// TODO: đơn EO có thể vừa có đơn giao vừa có đơn lấy
		// Trường hợp gán tài xế,
		if driverProductivityRequest.ProductivityAction == enum.ProductivityAction.ASSIGN_DELIVERY ||
			driverProductivityRequest.ProductivityAction == enum.ProductivityAction.ASSIGN_PICK {
			shippingOrder.DriverActivities = append(shippingOrder.DriverActivities, &driverActivity)
		}

		if (driverProductivityRequest.ProductivityAction == enum.ProductivityAction.DELIVERY_FAIL ||
			driverProductivityRequest.ProductivityAction == enum.ProductivityAction.DELIVERED) &&
			len(shippingOrder.DriverActivities) > 0 {
			closestAssignAtIndex := -1
			for i, da := range shippingOrder.DriverActivities {
				if da.ProductivityAction != nil &&
					*da.ProductivityAction == enum.ProductivityAction.ASSIGN_DELIVERY &&
					da.DriverId == driverProductivityRequest.DriverID &&
					da.HubCode == driverProductivityRequest.HubCode {
					closestAssignAtIndex = i
				}
			}
			if closestAssignAtIndex != -1 {
				shippingOrder.DriverActivities[closestAssignAtIndex].CompleteAssignAt =
					driverProductivityRequest.ActionTime
			}

			shippingOrder.DriverActivities = append(shippingOrder.DriverActivities, &driverActivity)
		}

		if (driverProductivityRequest.ProductivityAction == enum.ProductivityAction.PICK_FAIL ||
			driverProductivityRequest.ProductivityAction == enum.ProductivityAction.PICKED) &&
			len(shippingOrder.DriverActivities) > 0 {
			closestAssignAtIndex := -1
			for i, da := range shippingOrder.DriverActivities {
				if da.ProductivityAction != nil &&
					*da.ProductivityAction == enum.ProductivityAction.ASSIGN_PICK &&
					da.DriverId == driverProductivityRequest.DriverID &&
					da.HubCode == driverProductivityRequest.HubCode {
					closestAssignAtIndex = i
				}
			}
			if closestAssignAtIndex != -1 {
				shippingOrder.DriverActivities[closestAssignAtIndex].CompleteAssignAt =
					driverProductivityRequest.ActionTime
			}

			shippingOrder.DriverActivities =
				append(shippingOrder.DriverActivities, &driverActivity)
		}

		model.ShippingOrderDB.UpdateOne(bson.M{
			"reference_code": driverProductivityRequest.ReferenceCode,
		}, bson.M{
			"driver_activities": shippingOrder.DriverActivities,
		})

		return nil
	})
}
