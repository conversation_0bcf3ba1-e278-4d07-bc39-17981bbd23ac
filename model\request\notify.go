package request

type ProcessReconcileNotifyRequest struct {
	// Update status
	ReferenceCodes []string `json:"referenceCodes"`
	HubCode        string   `json:"hubCode"`
	Message        string   `json:"message"`
	ReconcileType  string   `json:"reconcileType"`

	// Notification
	Action    string   `json:"action"`
	UserName  string   `json:"userName"`
	RefId     string   `json:"refId"`
	ShortCode string   `json:"shortCode"`
	Params    []string `json:"params"`
}
