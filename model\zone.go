package model

import (
	"time"

	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/mongo"
)

type Zone struct {
	Code            string          `json:"code" bson:"code,omitempty"`
	Name            string          `json:"name" bson:"name,omitempty"`
	ParentCode      string          `json:"parentCode" bson:"parent_code,omitempty"`
	Description     string          `json:"description" bson:"description,omitempty"`
	Provinces       []*ProvinceArea `json:"provinces" bson:"provinces,omitempty"`
	WardIndexes     []string        `json:"wardIndexes" bson:"ward_indexes,omitempty"`
	DistrictIndexes []string        `json:"districtIndexes" bson:"district_indexes,omitempty"`
	IsDeleted       bool            `json:"isDeleted" bson:"is_deleted"`
	Action          string          `json:"action,omitempty" bson:"-"`
	CreatedBy       string          `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	CreatedTime     *time.Time      `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedBy   string          `json:"lastUpdatedBy,omitempty" bson:"last_updated_by,omitempty"`
	LastUpdatedTime *time.Time      `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	IsAssignToHub   *bool           `json:"isAssignToHub,omitempty" bson:"is_assign_to_hub,omitempty"`
	HubName         string          `json:"hubName,omitempty" bson:"-"`
	IsLastChild     *bool           `json:"isLastChild,omitempty" bson:"is_last_child,omitempty"`
	Keyword         string          `json:"keyword,omitempty" bson:"keyword,omitempty"`
	NearZone        []*NearZone     `json:"nearZone,omitempty" bson:"near_zone,omitempty"`
}

type NearZone struct {
	Code string `json:"code,omitempty" bson:"code,omitempty"`
	Name string `json:"name,omitempty" bson:"name,omitempty"`
}

type ProvinceArea struct {
	ProvinceCode string          `json:"provinceCode" bson:"province_code,omitempty"`
	ProvinceName string          `json:"provinceName" bson:"province_name,omitempty"`
	DistrictArea []*DistrictArea `json:"districts" bson:"districts,omitempty"`
}

type DistrictArea struct {
	DistrictCode string               `json:"districtCode" bson:"district_code,omitempty"`
	DistrictName string               `json:"districtName" bson:"district_name,omitempty"`
	AreaLevel    *enum.AreaLevelValue `json:"areaLevel,omitempty" bson:"area_level,omitempty"`
	WardArea     []*WardArea          `json:"wards" bson:"wards,omitempty"`
}

type WardArea struct {
	WardCode string   `json:"wardCode" bson:"ward_code,omitempty"`
	WardName string   `json:"wardName" bson:"ward_name,omitempty"`
	Extra    []*Extra `json:"extra" bson:"extra,omitempty"`
}

type Extra struct {
	Key   string
	Value string
}

var ZoneDB = &db.Instance{
	ColName:        "zone",
	TemplateObject: &Zone{},
}

func InitZoneModel(s *mongo.Database) {
	ZoneDB.ApplyDatabase(s)

	t := true
	ZoneDB.CreateIndex(bson.D{
		{"ward_indexes", 1},
		{"is_deleted", 1},
		{"is_assign_to_hub", 1},
		{"is_last_child", 1},
	}, &options.IndexOptions{
		Background: &t,
	})
}
