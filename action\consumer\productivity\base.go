package productivity

import (
	"fmt"
	"sync"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"go.mongodb.org/mongo-driver/mongo"
)

type ExecutorJob struct {
	Job *job.Executor
}

var (
	instance           map[string]*ExecutorJob
	onceInit           map[string]*sync.Once
	driverProductivity = "driver_productivity"
)

func init() {
	onceInit = map[string]*sync.Once{}
	instance = map[string]*ExecutorJob{}
	driverProductivity = conf.Config.Topics["driver_productivity"]
}

func InitProductivityExecutor(dbSession *mongo.Database, Database, Collection string) {
	instanceName := driverProductivity
	if onceInit[instanceName] == nil {
		onceInit[instanceName] = &sync.Once{}
	}
	onceInit[instanceName].Do(func() {
		instance[instanceName] = &ExecutorJob{
			&job.Executor{ColName: driverProductivity},
		}

		instance[instanceName].Job.InitWithConfig(dbSession,
			Database,
			&job.ExecutorConfiguration{
				ParallelTopicProcessing: false,
				FailThreshold:           50,
				ChannelCount:            5,
				SortedItem:              false,
				LogSize:                 5,
				SelectorDelayMS:         200,
				WaitForReadyTime:        false,
				UniqueItem:              false,
				ConsumedExipredTime:     time.Duration(720) * time.Hour,
				CurVersionTimeoutS:      30,
				OldVersionTimeoutS:      30,
				MaximumWaitToRetryS:     15,
			},
		)

		fmt.Println("StartConsumeDevHub", instanceName)
		instance[instanceName].driverProductivity()
		instance[instanceName].Job.StartConsume()
	})
}

type DriverProductivity struct {
	ReferenceCode      string                            `json:"referenceCode" bson:"reference_code"`
	HubCode            string                            `json:"hubCode" bson:"hub_code"`
	DriverID           int64                             `json:"driverId" bson:"driver_id"`
	Status             *enum.HubShippingOrderStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	Type               *enum.HubOrderTypeValue           `json:"type,omitempty" bson:"type,omitempty"`
	SubType            *enum.SubTypeValue                `json:"subType,omitempty" bson:"sub_type,omitempty"`
	ActionTime         int64                             `json:"actionTime,omitempty" bson:"action_time,omitempty"`
	ProductivityAction enum.ProductivityActionValue      `json:"productivityAction" bson:"productivity_action"`
}

func PushCalculateProductivity(data interface{}, sortedKey string) (err error) {
	return instance[driverProductivity].Job.Push(
		data, &job.JobItemMetadata{
			Topic:     driverProductivity,
			SortedKey: sortedKey,
		})
}
