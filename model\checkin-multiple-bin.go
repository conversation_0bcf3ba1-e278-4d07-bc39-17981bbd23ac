package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"go.mongodb.org/mongo-driver/mongo"
)

type TransportingToWarehouse struct {
	Code     string   `json:"code,omitempty" bson:"code,omitempty"`
	Bins     []string `json:"bins,omitempty" bson:"bins,omitempty"`
	TotalBin int      `json:"totalBin,omitempty" bson:"total_bin,omitempty"`
}

type CheckinMultipleBin struct {
	Code                       string                     `json:"code,omitempty" bson:"code,omitempty"`
	WarehouseCode              string                     `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`
	CreatedBy                  HrmAccount                 `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	CreatedAt                  time.Time                  `json:"createdAt,omitempty" bson:"create_at,omitempty"`
	Bins                       []string                   `json:"bins,omitempty" bson:"bins,omitempty"`
	TransportingToWarehouse    []*TransportingToWarehouse `json:"transportingToWarehouse,omitempty" bson:"transporting_to_warehouse,omitempty"`
	Delivering                 []string                   `json:"delivering,omitempty" bson:"delivering,omitempty"`
	MissingTransferToWarehouse []string                   `json:"missingTransferToWarehouse,omitempty" bson:"missing_transfer_to_warehouse,omitempty"`
	NotUsed                    []string                   `json:"notUsed,omitempty" bson:"not_used,omitempty"`
	ReceiveAtWareHouse         []*ReceiveAtWarehouse      `json:"receiveAtWarehouse,omitempty" bson:"receive_at_warehouse,omitempty"`
	Lost                       []string                   `json:"lost,omitempty" bson:"lost,omitempty"`
	Unmapping                  []string                   `json:"unmapping,omitempty" bson:"unmapping,omitempty"`
	Duplicated                 []string                   `json:"duplicated,omitempty" bson:"duplicated,omitempty"`
	Other                      []*OtherReason             `json:"other,omitempty" bson:"other,omitempty"`
}

type OtherReason struct {
	Bins   []string `json:"bins,omitempty" bson:"bins,omitempty"`
	Reason string   `json:"reason,omitempty" bson:"reason,omitempty"`
}

type ReceiveAtWarehouse struct {
	ReferenceCode string   `json:"referenceCode"`
	Bins          []string `json:"bins"`
}

var CheckinMultipleBinDB = &db.Instance{
	ColName:        "checkin_multiple_bin",
	TemplateObject: &CheckinMultipleBin{},
}

func InitCheckinMultipleBinModel(s *mongo.Database) {
	CheckinMultipleBinDB.ApplyDatabase(s)

	t := true
	CheckinMultipleBinDB.CreateIndex(bson.D{
		{"bins", 1},
	}, &options.IndexOptions{
		Background: &t,
	})
	CheckinMultipleBinDB.CreateIndex(bson.D{
		{"code", 1},
	}, &options.IndexOptions{
		Background: &t,
	})
}
