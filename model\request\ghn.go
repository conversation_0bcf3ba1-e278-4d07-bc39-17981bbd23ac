package request

import (
	"time"

	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
)

type BookGHNRequest struct {
	Token           string     `json:"token"`
	ShopId          int64      `json:"shop_id"`
	ToName          string     `json:"to_name"`
	ToPhone         string     `json:"to_phone"`
	ToAddress       string     `json:"to_address"`
	ToWardCode      string     `json:"to_ward_code"`
	ToDistrictId    int64      `json:"to_district_id"`
	ReturnPhone     string     `json:"return_phone"`
	OrderValue      int64      `json:"order_value"`
	ReturnAddress   string     `json:"return_address"`
	ClientOrderCode string     `json:"client_order_code"`
	CodAmount       float64    `json:"cod_amount"`
	Content         string     `json:"content"`
	Weight          float64    `json:"weight"` // gram - max 1600000gram
	Length          int64      `json:"length"` // Max 200cm
	Width           int64      `json:"width"`  // Max 200cm
	Height          int64      `json:"height"` // Max 200cm
	PickStationId   int64      `json:"pick_station_id"`
	InsuranceValue  int64      `json:"insurance_value"`
	Coupon          string     `json:"coupon"`
	ServiceTypeId   int64      `json:"service_type_id"`
	ServiceId       int64      `json:"service_id"`
	PaymentTypeId   int64      `json:"payment_type_id"`
	Note            string     `json:"note"`
	RequiredNote    string     `json:"required_note"`
	Items           []*GHNItem `json:"items"`
	Name            string     `json:"name"`
	Quantity        int64      `json:"quantity"`
	Price           int64      `json:"price"`
}

type GHNItem struct {
	Name     string `json:"name"`
	Code     string `json:"code"`
	Quantity int64  `json:"quantity"`
	Price    int64  `json:"price"`
}

type GHNCallback struct {
	CODAmount       float64                 `json:"CODAmount"`
	CODTransferDate *time.Time              `json:"CODTransferDate"`
	ClientOrderCode string                  `json:"ClientOrderCode"`
	ConvertedWeight int                     `json:"ConvertedWeight"`
	Description     string                  `json:"Description"`
	Height          int                     `json:"Height"`
	Length          int                     `json:"Length"`
	OrderCode       string                  `json:"OrderCode"`
	Reason          string                  `json:"Reason"`
	ReasonCode      string                  `json:"ReasonCode"`
	ShipperName     string                  `json:"ShipperName"`
	ShipperPhone    string                  `json:"ShipperPhone"`
	Status          *enum.GHNStatusValue    `json:"Status"`
	Time            *time.Time              `json:"Time"`
	TotalFee        float64                 `json:"TotalFee"`
	Type            *enum.GHNTypeOrderValue `json:"Type"`
	Warehouse       string                  `json:"Warehouse"`
	Weight          float64                 `json:"Weight"`
	Width           int                     `json:"Width"`
}
