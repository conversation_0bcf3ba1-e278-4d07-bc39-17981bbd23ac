package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func ConfigCustomerCarriers(input model.CustomerCarrier) *common.APIResponse {
	returnAfter := options.After
	createConfig := model.CustomerCarriersDB.UpdateOne(
		model.CustomerCarrier{
			CustomerId: input.CustomerId,
		},
		input,
		&options.FindOneAndUpdateOptions{
			Upsert:         &enum.True,
			ReturnDocument: &returnAfter,
		})
	if createConfig.Status != common.APIStatus.Ok {
		return createConfig
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Tạo config thành công",
	}
}

func GetConfigCustomerCarriers(customerId, limit, offset int64, getTotal bool) *common.APIResponse {
	filter := bson.M{}

	if customerId > 0 {
		filter["customer_id"] = customerId
	}

	result := model.CustomerCarriersDB.Query(
		filter,
		offset,
		limit,
		&primitive.M{"_id": -1})
	if getTotal {
		countResult := model.CustomerCarriersDB.Count(filter)
		result.Total = countResult.Total
	}

	return result
}

func DeleteConfigCustomerCarriers(input model.CustomerCarrier) *common.APIResponse {
	if input.CustomerId <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã khách hàng không được để trống",
			ErrorCode: "CUSTOMER_ID_REQUIRED",
		}
	}

	return model.CustomerCarriersDB.Delete(bson.M{
		"customer_id": input.CustomerId,
	})
}

func CleanConfigCustomerCarriersPickup() *common.APIResponse {
	offset := 0
	limit := 1000
	getCustomerCarriers := model.CustomerCarriersDB.Query(
		bson.M{
			"pick_up_carriers": bson.M{
				"$exists": true,
			},
		},
		int64(offset),
		int64(limit),
		&primitive.M{"_id": -1},
	)
	if getCustomerCarriers.Status != common.APIStatus.Ok {
		return getCustomerCarriers
	}

	customerCarriers := getCustomerCarriers.Data.([]*model.CustomerCarrier)

	hubMap := map[string]*model.Hub{}

	for _, customerCarrier := range customerCarriers {
		pickUpConfig := customerCarrier.PickUpCarriers[0]
		hub, ok := hubMap[pickUpConfig.HubCode]
		if !ok {
			getHub := model.HubDB.QueryOne(bson.M{
				"code": pickUpConfig.HubCode,
			})
			if getHub.Status == common.APIStatus.Ok {
				hub = getHub.Data.([]*model.Hub)[0]
				hubMap[hub.Code] = hub
			}
		}

		isExist := false
		for _, carrier := range hub.ListCarrierRefId {
			if carrier == pickUpConfig.CarrierId {
				isExist = true
				break
			}
		}

		if !isExist {
			model.CustomerCarriersDB.UpdateOneWithOption(bson.M{
				"_id": customerCarrier.ID,
			}, bson.M{
				"$unset": bson.M{
					"pick_up_carriers": "",
				},
			})
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Clean config thành công",
	}
}
