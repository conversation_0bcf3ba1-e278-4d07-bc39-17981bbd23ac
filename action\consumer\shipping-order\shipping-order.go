package shipping_order

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	sync_data "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action/consumer/tpl-callback"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"go.mongodb.org/mongo-driver/bson"
)

func (j *ExecutorJob) createExtendShippingOrder() {
	j.Job.SetTopicConsumer(defaultTopic, func(item *job.JobItem) (err error) {
		//defer utils.RecoverFromPanic(item.Data)

		var dataByte []byte
		dataByte, err = bson.Marshal(item.Data)
		if err != nil {
			return
		}

		var reqData RequestBody
		err = bson.Unmarshal(dataByte, &reqData)
		if err != nil {
			return
		}

		if item.FailCount%500 == 0 {
			client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
				CmdType: "ERROR - CREATE_EXTEND_SHIPPING_ORDER_FAILED",
				Title:   "CREATE_EXTEND_SHIPPING_ORDER_FAILED",
				Message: "================================ Create extend shipping order failed with fail count " + fmt.Sprintf("%d", item.FailCount),
			})
		}

		if item.FailCount > 10 {
			return nil
		}

		for _, o := range reqData.BodyData {
			// Todo : Create new shipping order request
			// Get ShippingOrder
			shippingOrderQuery := model.ShippingOrderDB.QueryOne(bson.M{
				"reference_code": o.ReferenceCode,
			})

			if shippingOrderQuery.Status == common.APIStatus.Ok {
				// Get  shipping order
				// Mã parent reference code sẽ được gán với mã reference code của đơn lấy hàng đầu tiên, mã này cũng sẽ bằng tracking code của đơn giao
				shippingOrder := shippingOrderQuery.Data.([]*model.ShippingOrder)[0]
				req := request.BookShippingOrder{
					ShippingType:  &enum.ShippingOrderType.PICKUP,
					ReferenceCode: o.TrackingCode,
					From: &model.Address{
						Address:      o.FromCustomerAddress,
						WardCode:     o.FromWardCode,
						DistrictCode: o.FromDistrictCode,
						ProvinceCode: o.FromProvinceCode,
						Name:         o.FromCustomerName,
						Phone:        o.FromCustomerPhone,
						Email:        o.FromCustomerEmail,
						Code:         o.FromCustomerCode,
					},
					To: &model.Address{
						Address:      shippingOrder.CustomerShippingAddress,
						WardCode:     shippingOrder.CustomerWardCode,
						DistrictCode: shippingOrder.CustomerDistrictCode,
						ProvinceCode: shippingOrder.CustomerProvinceCode,
						Name:         shippingOrder.CustomerName,
						Phone:        shippingOrder.CustomerPhone,
						Email:        shippingOrder.CustomerEmail,
						Code:         shippingOrder.CustomerCode,
					},
					AssignRider:         &request.RiderDetail{},
					ParentReferenceCode: o.ReferenceCode,
					ReBook:              true,
				}

				// Get removed product
				var products []*model.Product
				var productCodes []string
				var defaultWeight float64
				for _, p := range o.Products {
					if *p.Status == enum.ProductStatus.REMOVED {
						p.Status = &enum.ProductStatus.WAIT_TO_STORING
						products = append(products, p)
						productCodes = append(productCodes, p.SKU)
						defaultWeight = p.Weight
					}
				}

				req.NumPackage = int64(len(products))
				req.Products = products
				req.Weight = defaultWeight * float64(req.NumPackage)
				req.Height = shippingOrder.Height
				req.Width = shippingOrder.Width
				req.Length = shippingOrder.Length
				req.AssignRider.DriverName = o.DriverName
				req.AssignRider.DriverId = int(o.DriverID)

				deliveryTrackingCode := shippingOrder.ReferenceCode
				// Nếu tách đơn lần thứ 3 dùng reference code sẽ sai nên dùng parent reference code để tỉm mã tracking của đơn đã giao thành công
				if shippingOrder.ParentReferenceCode != "" {
					deliveryTrackingCode = shippingOrder.ParentReferenceCode
				}
				// Get Delivery Shipping Order Code
				deliveryShippingOrderQuery := model.ShippingOrderDB.QueryOne(bson.M{
					"tracking_code": deliveryTrackingCode,
				})

				if deliveryShippingOrderQuery.Status == common.APIStatus.Ok {
					deliveryShippingOrder := deliveryShippingOrderQuery.Data.([]*model.ShippingOrder)[0]
					req.PickupNote = fmt.Sprintf("[ %s ] Thu hồi BIN %s đơn đã lấy 1 phần BIN", deliveryShippingOrder.ReferenceCode, strings.Join(productCodes, ","))
				}

				err = client.Services.TransportingClient.BookShippingService(req)
			}
		}
		return err
	})
}

func createExtendCompleteHandoverOrderV1(reqData RequestBodyHandover) (err error) {
	// Create new shipping order and hub order => store in DB
	// Query shippingOrder
	shippingOrderQuery := model.ShippingOrderDB.QueryOne(bson.M{
		"reference_code": reqData.HubOrder.ReferenceCode,
	})
	if shippingOrderQuery.Status != common.APIStatus.Ok {
		return err
	}
	shippingOrder := shippingOrderQuery.Data.([]*model.ShippingOrder)[0]

	// Query HUB
	hubQuery := model.HubDB.QueryOne(bson.M{
		"code": reqData.HubOrder.HUBCode,
	})
	if hubQuery.Status != common.APIStatus.Ok {
		return err
	}
	hub := hubQuery.Data.([]*model.Hub)[0]

	var listRemovedBin string
	var totalWeight float64
	var numberOfProduct int64
	for _, p := range reqData.RemovedBinList {
		numberOfProduct++
		listRemovedBin = listRemovedBin + "BIN " + p.SKU + ", "
		p.Status = &enum.ProductStatus.LOST
		totalWeight += p.Weight
	}
	// Nếu toàn bộ products trong đơn đều không quét thì chỉ chuyển trạng thái ban đầu của đơn chứ không tạo đơn mới
	if numberOfProduct == reqData.HubOrder.NumPackage {
		updateShippingOrder := model.ShippingOrderDB.UpdateOne(bson.M{
			"reference_code": shippingOrder.ReferenceCode,
		}, bson.M{
			"status": &enum.TPLCallbackStatus.LOST,
		})

		if updateShippingOrder.Status != common.APIStatus.Ok {
			return errors.New(updateShippingOrder.Message)
		}

		updateHubOrder := model.HUBShippingOrderDB.UpdateOne(bson.M{
			"reference_code": shippingOrder.ReferenceCode,
			"hub_code":       reqData.HubOrder.HUBCode,
		}, bson.M{
			"status": &enum.HubShippingOrderStatus.LOST,
		})

		if updateHubOrder.Status != common.APIStatus.Ok {
			return errors.New(updateHubOrder.Message)
		}
		return nil
	}

	// Gen new Tracking Code
	newTrackingCode := model.GenCode("TRACKING_CODE")
	// Gen new ReferenceCode for new Hub Shipping order and Shipping order (10 random character)
	newReferenceCode := model.GenCode("TRACKING_CODE")

	note := "[" + reqData.HubOrder.ReferenceCode + "] Thu hồi " + listRemovedBin + "đơn đã nhận 1 phần BIN"

	// Assign current HubShippingOrder to new HubShippingOrder, ShippingOrder
	current := time.Now()
	newHubShippingOrder := model.HubShippingOrder{
		ParentReferenceCode: reqData.HubOrder.ReferenceCode,
		ReferenceCode:       newReferenceCode,
		Height:              reqData.HubOrder.Height,
		Width:               reqData.HubOrder.Width,
		Length:              reqData.HubOrder.Length,
		HUBCode:             reqData.HubOrder.HUBCode,
		Status:              &enum.HubShippingOrderStatus.LOST,
		TplCode:             reqData.HubOrder.TplCode,
		TplName:             reqData.HubOrder.TplName,
		TotalAmount:         reqData.HubOrder.TotalAmount,
		DeliveryAmount:      reqData.HubOrder.DeliveryAmount,
		CODAmount:           reqData.HubOrder.CODAmount,
		FeeAmount:           reqData.HubOrder.FeeAmount,
		PaymentMethod:       reqData.HubOrder.PaymentMethod,
		CollectOnDelivery:   reqData.HubOrder.CollectOnDelivery,
		FeeCollectMethod:    reqData.HubOrder.FeeCollectMethod,
		CreatedTime:         &current,
		ActionTime:          current.Unix(),
		Tags:                reqData.HubOrder.Tags,

		ToCustomerName:    shippingOrder.CustomerName,
		ToCustomerCode:    shippingOrder.CustomerCode,
		ToCustomerAddress: shippingOrder.CustomerShippingAddress,
		ToCustomerPhone:   shippingOrder.CustomerPhone,
		ToCustomerEmail:   shippingOrder.CustomerEmail,
		ToWardCode:        shippingOrder.CustomerWardCode,
		ToWardName:        shippingOrder.CustomerWardName,
		ToDistrictCode:    shippingOrder.CustomerDistrictCode,
		ToDistrictName:    shippingOrder.CustomerDistrictName,
		ToProvinceCode:    shippingOrder.CustomerProvinceCode,
		ToProvinceName:    shippingOrder.CustomerProvinceName,
		ToLatitude:        shippingOrder.CustomerLatitude,
		ToLongitude:       shippingOrder.CustomerLongitude,

		FromCustomerName:    hub.Name,
		FromCustomerAddress: hub.Address.Address,
		FromCustomerPhone:   hub.Address.Phone,
		FromCustomerEmail:   hub.Address.Email,
		FromWardCode:        hub.Address.WardCode,
		FromWardName:        hub.Address.WardName,
		FromDistrictCode:    hub.Address.DistrictCode,
		FromDistrictName:    hub.Address.DistrictName,
		FromProvinceCode:    hub.Address.ProvinceCode,
		FromProvinceName:    hub.Address.ProvinceName,
		FromLatitude:        hub.Address.Latitude,
		FromLongitude:       hub.Address.Longitude,
		Type:                &enum.HubOrderType.TRANSPORTING,

		TrackingCode: newTrackingCode,
		Note:         note,
		PrivateNote:  note,
		Products:     reqData.RemovedBinList,
		NumPackage:   int64(len(reqData.RemovedBinList)),
		Weight:       totalWeight,
	}

	// Assign value from HubShippingOrder to ShippingOrder
	newShippingOrder := model.ShippingOrder{
		ReferenceCode:       newReferenceCode,
		ParentReferenceCode: newHubShippingOrder.ParentReferenceCode,
		Products:            newHubShippingOrder.Products,
		Note:                newHubShippingOrder.Note,
		PrivateNote:         newHubShippingOrder.PrivateNote,
		TrackingCode:        newTrackingCode,
		TplServiceId:        shippingOrder.TplServiceId,
		TplCode:             reqData.HubOrder.TplCode,
		TplName:             reqData.HubOrder.TplName,
		Status:              &enum.TPLCallbackStatus.LOST,
		ShippingType:        &enum.ShippingOrderType.PICKUP,
		CurrentHub:          newHubShippingOrder.HUBCode,
		Action:              "IN",
		Height:              reqData.HubOrder.Height,
		Width:               reqData.HubOrder.Width,
		Length:              reqData.HubOrder.Length,
		NumPackage:          int64(len(reqData.RemovedBinList)),
		Weight:              totalWeight,
		TotalAmount:         reqData.HubOrder.TotalAmount,
		DeliveryAmount:      reqData.HubOrder.DeliveryAmount,
		CODAmount:           reqData.HubOrder.CODAmount,
		FeeAmount:           reqData.HubOrder.FeeAmount,
		PaymentMethod:       reqData.HubOrder.PaymentMethod,
		CollectOnDelivery:   reqData.HubOrder.CollectOnDelivery,
		FeeCollectMethod:    reqData.HubOrder.FeeCollectMethod,
		CreatedTime:         &current,
		ActionTime:          current.Unix(),
		Tags:                reqData.HubOrder.Tags,

		CustomerName:             shippingOrder.CustomerName,
		CustomerCode:             shippingOrder.CustomerCode,
		CustomerShippingAddress:  shippingOrder.CustomerShippingAddress,
		CustomerPhone:            shippingOrder.CustomerPhone,
		CustomerEmail:            shippingOrder.CustomerEmail,
		CustomerWardCode:         shippingOrder.CustomerWardCode,
		CustomerWardName:         shippingOrder.CustomerWardName,
		CustomerDistrictCode:     shippingOrder.CustomerDistrictCode,
		CustomerDistrictName:     shippingOrder.CustomerDistrictName,
		CustomerProvinceCode:     shippingOrder.CustomerProvinceCode,
		CustomerProvinceName:     shippingOrder.CustomerProvinceName,
		CustomerLatitude:         shippingOrder.CustomerLatitude,
		CustomerLongitude:        shippingOrder.CustomerLongitude,
		CustomerVerificationCode: shippingOrder.CustomerVerificationCode,

		FromCustomerAddress: hub.Address.Address,
		FromCustomerPhone:   hub.Address.Phone,
		FromCustomerEmail:   hub.Address.Email,
		FromCustomerName:    hub.Name,
		FromWardCode:        hub.Address.WardCode,
		FromWardName:        hub.Address.WardName,
		FromDistrictCode:    hub.Address.DistrictCode,
		FromDistrictName:    hub.Address.DistrictName,
		FromProvinceCode:    hub.Address.ProvinceCode,
		FromProvinceName:    hub.Address.ProvinceName,
		FromLatitude:        hub.Address.Latitude,
		FromLongitude:       hub.Address.Longitude,

		TotalCollectReceiverAmount: reqData.HubOrder.TotalCollectReceiverAmount,
		TotalCollectSenderAmount:   reqData.HubOrder.TotalCollectSenderAmount,
		FeeSenderAmount:            reqData.HubOrder.FeeSenderAmount,
		FeeReceiverAmount:          reqData.HubOrder.FeeReceiverAmount,
		TotalDebtAmount:            reqData.HubOrder.TotalDebtAmount,
		FeeDebtAmount:              reqData.HubOrder.FeeDebtAmount,
		//ActionName:        dataHandover.ActionName,
	}

	// Insert into ShippingOrder Database
	insertShippingResult := model.ShippingOrderDB.Create(newShippingOrder)

	if insertShippingResult.Status != common.APIStatus.Ok {
		return err
	}

	// Upsert into HubShippingOrder Database
	insertHubShippingResult := model.HUBShippingOrderDB.Create(newHubShippingOrder)

	if insertHubShippingResult.Status != common.APIStatus.Ok {
		return err
	}

	// Create Callback
	createCallbackRequest := request.Callback{
		SO:              newShippingOrder.ReferenceCode,
		CreatedSource:   newShippingOrder.TplCode,
		Status:          &enum.TPLCallbackStatus.READY_TO_PICK,
		Weight:          newShippingOrder.Weight,
		TPLCode:         newShippingOrder.TrackingCode,
		ActionTime:      &current,
		StatusName:      "Tạo mới vận đơn",
		TotalFee:        newShippingOrder.TotalAmount,
		ExternalTPLName: newShippingOrder.TplName,
		TPLStatus:       string(enum.TPLCallbackStatus.LOST),
		TPLStatusName:   "Tạo mới vận đơn",
		NumPackage:      newShippingOrder.NumPackage,
		Type:            newShippingOrder.ShippingType,
	}

	err = client.Services.TplCallbackClient.CreateCallback(createCallbackRequest)

	return err
}

func createExtendCompleteHandoverOrderV2(reqData RequestBodyHandover) (err error) {
	//  Create new shipping order and hub order => store in DB
	// Query shippingOrder
	shippingOrderQuery := model.ShippingOrderDB.QueryOne(bson.M{
		"reference_code": reqData.HubOrder.ReferenceCode,
	})
	if shippingOrderQuery.Status != common.APIStatus.Ok {
		return err
	}
	shippingOrder := shippingOrderQuery.Data.([]*model.ShippingOrder)[0]

	// Query HUB
	hubQuery := model.HubDB.QueryOne(bson.M{
		"code": reqData.HubOrder.HUBCode,
	})
	if hubQuery.Status != common.APIStatus.Ok {
		return err
	}
	hub := hubQuery.Data.([]*model.Hub)[0]

	var listRemovedBin string
	var totalWeight float64
	var numberOfProduct int64
	for _, p := range reqData.RemovedBinList {
		numberOfProduct++
		p.Status = &enum.ProductStatus.STORING
		listRemovedBin = listRemovedBin + "BIN " + p.SKU + ", "
		totalWeight += p.Weight
	}
	// Nếu toàn bộ products trong đơn đều không quét thì chỉ chuyển trạng thái ban đầu của đơn chứ không tạo đơn mới
	if numberOfProduct == reqData.HubOrder.NumPackage {
		updateShippingOrder := model.ShippingOrderDB.UpdateOne(bson.M{
			"reference_code": shippingOrder.ReferenceCode,
		}, bson.M{
			"status": &enum.TPLCallbackStatus.COMPLETED,
		})

		if updateShippingOrder.Status != common.APIStatus.Ok {
			return errors.New(updateShippingOrder.Message)
		}

		updateHubOrder := model.HUBShippingOrderDB.UpdateOne(bson.M{
			"reference_code": shippingOrder.ReferenceCode,
			"hub_code":       reqData.HubOrder.HUBCode,
		}, bson.M{
			"status": &enum.HubShippingOrderStatus.COMPLETED,
		})

		if updateHubOrder.Status != common.APIStatus.Ok {
			return errors.New(updateHubOrder.Message)
		}
		return nil
	}

	// Gen new Tracking Code
	newTrackingCode := model.GenCode("TRACKING_CODE")
	// Gen new ReferenceCode for new Hub Shipping order and Shipping order (10 random character)
	newReferenceCode := model.GenCode("TRACKING_CODE")

	note := "[" + reqData.HubOrder.ReferenceCode + "] Thu hồi " + listRemovedBin + "đơn đã nhận 1 phần BIN"

	// Assign current HubShippingOrder to new HubShippingOrder, ShippingOrder
	current := time.Now()
	newHubShippingOrder := model.HubShippingOrder{
		ParentReferenceCode: reqData.HubOrder.ReferenceCode,
		ReferenceCode:       newReferenceCode,
		Height:              reqData.HubOrder.Height,
		Width:               reqData.HubOrder.Width,
		Length:              reqData.HubOrder.Length,
		HUBCode:             reqData.HubOrder.HUBCode,
		Status:              &enum.HubShippingOrderStatus.COMPLETED,
		TplCode:             reqData.HubOrder.TplCode,
		TplName:             reqData.HubOrder.TplName,
		TotalAmount:         reqData.HubOrder.TotalAmount,
		DeliveryAmount:      reqData.HubOrder.DeliveryAmount,
		CODAmount:           reqData.HubOrder.CODAmount,
		FeeAmount:           reqData.HubOrder.FeeAmount,
		PaymentMethod:       reqData.HubOrder.PaymentMethod,
		CollectOnDelivery:   reqData.HubOrder.CollectOnDelivery,
		FeeCollectMethod:    reqData.HubOrder.FeeCollectMethod,
		CreatedTime:         &current,
		ActionTime:          current.Unix(),
		Tags:                reqData.HubOrder.Tags,

		ToCustomerName:    shippingOrder.CustomerName,
		ToCustomerCode:    shippingOrder.CustomerCode,
		ToCustomerAddress: shippingOrder.CustomerShippingAddress,
		ToCustomerPhone:   shippingOrder.CustomerPhone,
		ToCustomerEmail:   shippingOrder.CustomerEmail,
		ToWardCode:        shippingOrder.CustomerWardCode,
		ToWardName:        shippingOrder.CustomerWardName,
		ToDistrictCode:    shippingOrder.CustomerDistrictCode,
		ToDistrictName:    shippingOrder.CustomerDistrictName,
		ToProvinceCode:    shippingOrder.CustomerProvinceCode,
		ToProvinceName:    shippingOrder.CustomerProvinceName,
		ToLatitude:        shippingOrder.CustomerLatitude,
		ToLongitude:       shippingOrder.CustomerLongitude,

		FromCustomerName:    hub.Name,
		FromCustomerAddress: hub.Address.Address,
		FromCustomerPhone:   hub.Address.Phone,
		FromCustomerEmail:   hub.Address.Email,
		FromWardCode:        hub.Address.WardCode,
		FromWardName:        hub.Address.WardName,
		FromDistrictCode:    hub.Address.DistrictCode,
		FromDistrictName:    hub.Address.DistrictName,
		FromProvinceCode:    hub.Address.ProvinceCode,
		FromProvinceName:    hub.Address.ProvinceName,
		FromLatitude:        hub.Address.Latitude,
		FromLongitude:       hub.Address.Longitude,
		Type:                &enum.HubOrderType.TRANSPORTING,

		TrackingCode: newTrackingCode,
		Note:         note,
		PrivateNote:  note,
		Products:     reqData.RemovedBinList,
		NumPackage:   int64(len(reqData.RemovedBinList)),
		Weight:       totalWeight,
		References:   shippingOrder.References,
	}

	// Assign value from HubShippingOrder to ShippingOrder
	newShippingOrder := model.ShippingOrder{
		ReferenceCode:       newReferenceCode,
		ParentReferenceCode: newHubShippingOrder.ParentReferenceCode,
		Products:            newHubShippingOrder.Products,
		Note:                newHubShippingOrder.Note,
		PrivateNote:         newHubShippingOrder.PrivateNote,
		TrackingCode:        newTrackingCode,
		TplServiceId:        shippingOrder.TplServiceId,
		TplCode:             reqData.HubOrder.TplCode,
		TplName:             reqData.HubOrder.TplName,
		Status:              &enum.TPLCallbackStatus.COMPLETED,
		ShippingType:        &enum.ShippingOrderType.PICKUP,
		CurrentHub:          newHubShippingOrder.HUBCode,
		Action:              "IN",
		Height:              reqData.HubOrder.Height,
		Width:               reqData.HubOrder.Width,
		Length:              reqData.HubOrder.Length,
		NumPackage:          int64(len(reqData.RemovedBinList)),
		Weight:              totalWeight,
		TotalAmount:         reqData.HubOrder.TotalAmount,
		DeliveryAmount:      reqData.HubOrder.DeliveryAmount,
		CODAmount:           reqData.HubOrder.CODAmount,
		FeeAmount:           reqData.HubOrder.FeeAmount,
		PaymentMethod:       reqData.HubOrder.PaymentMethod,
		CollectOnDelivery:   reqData.HubOrder.CollectOnDelivery,
		FeeCollectMethod:    reqData.HubOrder.FeeCollectMethod,
		CreatedTime:         &current,
		ActionTime:          current.Unix(),
		Tags:                reqData.HubOrder.Tags,

		CustomerName:             shippingOrder.CustomerName,
		CustomerCode:             shippingOrder.CustomerCode,
		CustomerShippingAddress:  shippingOrder.CustomerShippingAddress,
		CustomerPhone:            shippingOrder.CustomerPhone,
		CustomerEmail:            shippingOrder.CustomerEmail,
		CustomerWardCode:         shippingOrder.CustomerWardCode,
		CustomerWardName:         shippingOrder.CustomerWardName,
		CustomerDistrictCode:     shippingOrder.CustomerDistrictCode,
		CustomerDistrictName:     shippingOrder.CustomerDistrictName,
		CustomerProvinceCode:     shippingOrder.CustomerProvinceCode,
		CustomerProvinceName:     shippingOrder.CustomerProvinceName,
		CustomerLatitude:         shippingOrder.CustomerLatitude,
		CustomerLongitude:        shippingOrder.CustomerLongitude,
		CustomerVerificationCode: shippingOrder.CustomerVerificationCode,

		FromCustomerAddress: hub.Address.Address,
		FromCustomerPhone:   hub.Address.Phone,
		FromCustomerEmail:   hub.Address.Email,
		FromCustomerName:    hub.Name,
		FromWardCode:        hub.Address.WardCode,
		FromWardName:        hub.Address.WardName,
		FromDistrictCode:    hub.Address.DistrictCode,
		FromDistrictName:    hub.Address.DistrictName,
		FromProvinceCode:    hub.Address.ProvinceCode,
		FromProvinceName:    hub.Address.ProvinceName,
		FromLatitude:        hub.Address.Latitude,
		FromLongitude:       hub.Address.Longitude,

		TotalCollectReceiverAmount: reqData.HubOrder.TotalCollectReceiverAmount,
		TotalCollectSenderAmount:   reqData.HubOrder.TotalCollectSenderAmount,
		FeeSenderAmount:            reqData.HubOrder.FeeSenderAmount,
		FeeReceiverAmount:          reqData.HubOrder.FeeReceiverAmount,
		TotalDebtAmount:            reqData.HubOrder.TotalDebtAmount,
		FeeDebtAmount:              reqData.HubOrder.FeeDebtAmount,
		References:                 shippingOrder.References,
		//ActionName:        dataHandover.ActionName,
	}

	// Insert into ShippingOrder Database
	insertShippingResult := model.ShippingOrderDB.Create(newShippingOrder)

	if insertShippingResult.Status != common.APIStatus.Ok {
		return err
	}

	// Upsert into HubShippingOrder Database
	insertHubShippingResult := model.HUBShippingOrderDB.Create(newHubShippingOrder)

	if insertHubShippingResult.Status != common.APIStatus.Ok {
		return err
	}

	// Create Callback
	createCallbackRequest := request.Callback{
		SO:              newShippingOrder.ReferenceCode,
		CreatedSource:   newShippingOrder.TplCode,
		Status:          &enum.TPLCallbackStatus.COMPLETED,
		Weight:          newShippingOrder.Weight,
		TPLCode:         newShippingOrder.TrackingCode,
		ActionTime:      &current,
		StatusName:      "Đã nhập kho",
		TotalFee:        newShippingOrder.TotalAmount,
		ExternalTPLName: newShippingOrder.TplName,
		TPLStatus:       string(enum.TPLCallbackStatus.COMPLETED),
		TPLStatusName:   "Hoàn thành đơn vì BIN được reset",
		NumPackage:      newShippingOrder.NumPackage,
		Type:            newShippingOrder.ShippingType,
	}

	err = client.Services.TplCallbackClient.CreateCallback(createCallbackRequest)

	return err
}

// TODO
func (j *ExecutorJob) createExtendCompleteHandoverOrder() {
	j.Job.SetTopicConsumer(createExtendCompleteHandoverOrderTopic, func(item *job.JobItem) (err error) {
		var dataByte []byte
		dataByte, err = bson.Marshal(item.Data)
		if err != nil {
			return
		}

		var reqData RequestBodyHandover
		err = bson.Unmarshal(dataByte, &reqData)
		if err != nil {
			return
		}

		if reqData.ShouldComplete {
			err = createExtendCompleteHandoverOrderV2(reqData)
		} else {
			err = createExtendCompleteHandoverOrderV1(reqData)
		}

		if err != nil {
			if item.FailCount%50 == 0 {
				syncDataByte, _ := json.Marshal(reqData)
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "ERROR - CREATE_EXTEND_COMPLETE_HANDOVER_ORDER",
					Title:   fmt.Sprintf("ERROR - CREATE_EXTEND_COMPLETE_HANDOVER_ORDER %v - %d", reqData.HubOrder.ReferenceCode, item.FailCount),
					Message: "================================ " + string(syncDataByte) + "\n" + err.Error(),
				})
			}

			time.Sleep(1000 * time.Millisecond)
			return
		}

		return nil
	})
}

func (j *ExecutorJob) updateFee() {
	j.Job.SetTopicConsumer(updateFeeTopic, func(item *job.JobItem) (err error) {
		var dataByte []byte
		dataByte, err = bson.Marshal(item.Data)
		if err != nil {
			return
		}
		var reqData request.ImportFeeRequest
		err = bson.Unmarshal(dataByte, &reqData)

		referenceCodes := make([]string, 0, len(reqData.Lines))
		referenceCodesM := map[string]request.ImportFeeLine{}
		for _, line := range reqData.Lines {
			referenceCodes = append(referenceCodes, line.ReferenceCode)
			referenceCodesM[line.ReferenceCode] = line
		}

		shippingOrdersRaw := model.ShippingOrderDB.Query(bson.M{
			"reference_code": bson.M{
				"$in": referenceCodes,
			},
		}, 0, int64(len(reqData.Lines)), nil)
		if shippingOrdersRaw.Status != common.APIStatus.Ok {
			return nil
		}

		shippingOrders := shippingOrdersRaw.Data.([]*model.ShippingOrder)
		var needUpdateReconcileFee []request.ImportFeeLine
		// Cập nhật phí vận chuyển ở shipping order và hub order
		for _, shippingOrder := range shippingOrders {
			updater := bson.M{}
			if shippingOrder.FeeCollectMethod != nil && *shippingOrder.FeeCollectMethod == enum.FeeCollectMethod.SENDER_PAY {
				updater["fee_amount"] = referenceCodesM[shippingOrder.ReferenceCode].FeeAmount
				updater["fee_sender_amount"] = referenceCodesM[shippingOrder.ReferenceCode].FeeAmount
			} else if shippingOrder.FeeCollectMethod != nil && *shippingOrder.FeeCollectMethod == enum.FeeCollectMethod.RECEIVER_PAY {
				updater["fee_amount"] = referenceCodesM[shippingOrder.ReferenceCode].FeeAmount
				updater["fee_receiver_amount"] = referenceCodesM[shippingOrder.ReferenceCode].FeeAmount
			} else {
				updater["fee_amount"] = referenceCodesM[shippingOrder.ReferenceCode].FeeAmount
			}

			// Check xem có phải loại đơn RO không, nếu loại đơn là RO VÀ payment method là COD VÀ đang ở trạng thái Đã giao thì cần đi cập nhật phiên đối soát có RO
			if shippingOrder.ShippingType != nil &&
				*shippingOrder.ShippingType == enum.ShippingOrderType.DELIVERY {
				saleOrder, getSaleOrderErr := client.Services.WarehouseCoreClient.GetSaleOrder(shippingOrder.ReferenceCode, "")
				if getSaleOrderErr == nil &&
					saleOrder != nil &&
					saleOrder.Type == "RETURN" &&
					!saleOrder.CustomerInfos.Delivery.IsDropOffAtWarehouse {

					// Chỉ update COD khi giao hàng nội bộ
					carrierRaw := model.CarrierDB.QueryOne(bson.M{
						"carrier_id": shippingOrder.TplServiceId,
					})

					if carrierRaw.Status != common.APIStatus.Ok {
						continue
					}

					carrier := carrierRaw.Data.([]*model.Carrier)[0]
					if carrier.IsInternal != nil && *carrier.IsInternal {
						updater["cod_amount"] = referenceCodesM[shippingOrder.ReferenceCode].FeeAmount
						if shippingOrder.FeeCollectMethod != nil && *shippingOrder.FeeCollectMethod == enum.FeeCollectMethod.SENDER_PAY {
							updater["total_collect_sender_amount"] = referenceCodesM[shippingOrder.ReferenceCode].FeeAmount
						}

						if shippingOrder.FeeCollectMethod != nil && *shippingOrder.FeeCollectMethod == enum.FeeCollectMethod.RECEIVER_PAY {
							updater["total_collect_receiver_amount"] = referenceCodesM[shippingOrder.ReferenceCode].FeeAmount
						}

						if shippingOrder.Status != nil &&
							*shippingOrder.Status == enum.TPLCallbackStatus.DELIVERED {
							needUpdateReconcileFee = append(needUpdateReconcileFee, referenceCodesM[shippingOrder.ReferenceCode])
						}
					}

				}
			}
			updateShippingOrder := model.ShippingOrderDB.UpdateOne(bson.M{
				"reference_code": shippingOrder.ReferenceCode,
			}, updater)

			updateHubOrder := model.HUBShippingOrderDB.UpdateMany(bson.M{
				"reference_code": shippingOrder.ReferenceCode,
			}, updater)

			if updateShippingOrder.Status != common.APIStatus.Ok ||
				updateHubOrder.Status != common.APIStatus.Ok {
				syncDataByte, _ := json.Marshal(shippingOrder)
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "UPDATE_STATUS_FAILED",
					Title:   fmt.Sprintf("UPDATE_STATUS_FAILED %v - %d", shippingOrder.ReferenceCode, item.FailCount),
					Message: string(syncDataByte) + "\n" +
						"Update hub order message: " + updateHubOrder.Message + "\n" +
						"Update shipping order message: " + updateShippingOrder.Message,
				})
			}
		}

		// TODO: Cập nhật lại COD của phiên đối soát nếu cần thiết
		if len(needUpdateReconcileFee) > 0 {
			// Đưa danh sách đơn cần cập nhật cod sang kế toán
			resp := client.Services.AccountingClient.UpdateOrdersCOD(request.ImportFeeRequest{Lines: needUpdateReconcileFee})
			if resp.Status != common.APIStatus.Ok {
				syncDataByte, _ := json.Marshal(needUpdateReconcileFee)
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "FAIL_TO_UPDATE_ORDERS_COD",
					Title:   fmt.Sprintf("FAIL_TO_UPDATE_ORDERS_COD"),
					Message: string(syncDataByte) + "\n" +
						"Accounting core resp: " + resp.Message,
				})
			}
		}

		return nil
	})
}

func (j *ExecutorJob) checkinInboundTo() {
	j.Job.SetTopicConsumer(checkinInboundToTopic, func(item *job.JobItem) (err error) {
		var dataByte []byte
		dataByte, err = bson.Marshal(item.Data)
		if err != nil {
			return
		}

		var shippingOrder model.ShippingOrder
		err = bson.Unmarshal(dataByte, &shippingOrder)
		if err != nil {
			return
		}

		hubOrderRaw := model.HUBShippingOrderDB.QueryOne(bson.M{
			"reference_code": shippingOrder.ReferenceCode,
			"hub_code":       shippingOrder.CurrentHub,
		})

		if hubOrderRaw.Status != common.APIStatus.Ok {
			if item.FailCount%10 == 0 {
				syncDataByte, _ := json.Marshal(shippingOrder)
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "QUERY_HUB_ORDER_FAILED",
					Title:   fmt.Sprintf("QUERY_HUB_ORDER_FAILED %v - %d", shippingOrder.ReferenceCode, item.FailCount),
					Message: "================================ " + string(syncDataByte) + "\n" + hubOrderRaw.Message,
				})
			}
			if item.FailCount == 50 {
				return nil
			}
			time.Sleep(1000 * time.Millisecond)
			return fmt.Errorf(hubOrderRaw.Message)
		}
		now := time.Now()
		hubOrder := hubOrderRaw.Data.([]*model.HubShippingOrder)[0]
		hubOrder.DeliveredTime = now.Unix()
		_, _ = CreateReconcile(hubOrder, string(enum.ReconcileTypeRequest.HUB_COMP))
		callback := request.Callback{
			ActionTime: &now,
			SO:         shippingOrder.ReferenceCode,
		}

		if shippingOrder.ShippingType == nil {
			callback.Type = &enum.ShippingOrderType.DELIVERY
		} else {
			callback.Type = shippingOrder.ShippingType
		}

		updateShippingOrder := model.ShippingOrderDB.UpdateOne(bson.M{
			"reference_code": shippingOrder.ReferenceCode,
		}, bson.M{
			"status": enum.TPLCallbackStatus.DELIVERED,
		})

		updateHubOrder := model.HUBShippingOrderDB.UpdateOne(bson.M{
			"reference_code": shippingOrder.ReferenceCode,
			"hub_code":       shippingOrder.CurrentHub,
		}, bson.M{
			"status": enum.HubShippingOrderStatus.COD_COLLECTED,
		})

		if updateShippingOrder.Status != common.APIStatus.Ok ||
			updateHubOrder.Status != common.APIStatus.Ok {
			syncDataByte, _ := json.Marshal(shippingOrder)
			_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
				CmdType: "UPDATE_STATUS_FAILED",
				Title:   fmt.Sprintf("UPDATE_STATUS_FAILED %v - %d", shippingOrder.ReferenceCode, item.FailCount),
				Message: string(syncDataByte) + "\n" + "Update hub order message: " + updateHubOrder.Message + "\n" + "Update shipping order message: " + updateShippingOrder.Message,
			})
		}

		callback.Status = &enum.TPLCallbackStatus.DELIVERED
		callback.StatusName = "Giao hàng thành công"
		callback.TPLStatusName = "Giao hàng thành công"
		callback.TPLStatus = string(enum.TPLCallbackStatus.DELIVERED)
		callback.HubCode = hubOrder.HUBCode
		_ = sync_data.PushCreateTPLCallbackQueue(callback, hubOrder.ReferenceCode)

		return nil
	})
}

func CreateReconcile(hubShippingOrder *model.HubShippingOrder, reconcileType string) (*common.APIResponse, bool) {

	carrierCode := string(*hubShippingOrder.TplCode)
	// Lấy carrier cha nếu có
	response := model.CarrierDB.QueryOne(&bson.M{"carrier_code": carrierCode})
	if response.Status == common.APIStatus.Ok {
		carrier := response.Data.([]*model.Carrier)[0]
		if carrier.ParentCode != nil && *carrier.ParentCode != "" {
			carrierCode = string(*carrier.ParentCode)
		}
	}

	createReconcileSession := &request.CreateReconcileSessionRequest{
		CarrierCode:   carrierCode,
		ReconcileType: reconcileType,
		HubCode:       hubShippingOrder.HUBCode,
		UserId:        int(hubShippingOrder.DriverID),
		Fullname:      hubShippingOrder.DriverName,
	}

	response = client.Services.AccountingClient.CreateReconcileSession(createReconcileSession)
	if response.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Tạo đối soát thất bại.",
		}, true
	} else {
		deliveredTime := time.Unix(hubShippingOrder.DeliveredTime, 0)

		paymentMethod := "COD"
		if hubShippingOrder.PaymentMethod != nil {
			paymentMethod = string(*hubShippingOrder.PaymentMethod)
		}
		createReconcileSessionOrder := &request.CreateReconcileSessionOrderRequest{
			HubCode:        hubShippingOrder.HUBCode,
			UserId:         int(hubShippingOrder.DriverID),
			ReferenceCode:  hubShippingOrder.ReferenceCode,
			TrackingCode:   hubShippingOrder.TrackingCode,
			ReconcileType:  reconcileType,
			CODAmount:      hubShippingOrder.CODAmount,
			TplCode:        hubShippingOrder.TplCode,
			PaymentMethod:  paymentMethod,
			CarrierCode:    string(*hubShippingOrder.TplCode),
			DeliveredTime:  &deliveredTime,
			DeliveryAmount: hubShippingOrder.DeliveryAmount,
		}
		response = client.Services.AccountingClient.CreateReconcileSessionOrder(createReconcileSessionOrder)
		if response.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Ok,
				Message: "Tạo đối soát thất bại.",
			}, true
		}
	}

	return nil, false
}
