package geocode

import (
	"time"

	"go.mongodb.org/mongo-driver/mongo"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
)

const (
	pathGeoCoder = "/maps/api/geocode/json"
)

type GeoCodeClient struct {
	svc     *client.RestClient
	headers map[string]string
	apiKey  string
}

func NewServiceClient(apiHost, key, logName string, session *mongo.Database) *GeoCodeClient {
	if apiHost == "" {
		return nil
	}
	geoClient := &GeoCodeClient{
		svc: client.NewRESTClient(
			apiHost,
			logName,
			300*time.Second,
			1,
			3*time.Second,
		),
		apiKey: key,
	}
	geoClient.svc.SetDBLog(session)
	return geoClient
}
