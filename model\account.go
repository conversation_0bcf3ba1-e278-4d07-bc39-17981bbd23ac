package model

import (
	"time"

	"go.mongodb.org/mongo-driver/mongo/options"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// Account HrmAccount represent user account
type Account struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	VersionNo       string             `json:"versionNo,omitempty" bson:"version_no,omitempty"`
	UpdatedBy       int64              `json:"updatedBy,omitempty" bson:"updated_by,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	LoginTime       *time.Time         `json:"loginTime,omitempty" bson:"login_time,omitempty"`
	LastActive      *time.Time         `json:"lastActive,omitempty" bson:"last_active,omitempty"`
	AccountID       int64              `json:"accountId,omitempty" bson:"account_id,omitempty" `
	Username        string             `json:"username,omitempty" bson:"username,omitempty" `
	Fullname        string             `json:"fullname,omitempty" bson:"fullname,omitempty"`

	Status   string `json:"status,omitempty" bson:"status,omitempty"`
	IsOnTrip *bool  `json:"isOnTrip,omitempty" bson:"is_on_trip,omitempty"`
	// Update/Create non column in DB
	RoleCode       string  `json:"roleCode,omitempty" bson:"role_code,omitempty"`
	HubCode        string  `json:"hubCode,omitempty" bson:"hub_code,omitempty"`
	LimitAmount    float64 `json:"limitAmount" bson:"limit_amount"`
	ReceivedAmount float64 `json:"receivedAmount" bson:"received_amount,omitempty"`
	HashTag        string  `json:"hashTag,omitempty" bson:"hash_tag,omitempty"`
	Email          string  `json:"email,omitempty" bson:"email,omitempty"`
	PhoneNumber    string  `json:"phoneNumber,omitempty" bson:"phone_number,omitempty"`
}

// AccountDB represent DB repo of this model
var AccountDB = &db.Instance{
	ColName:        "account",
	TemplateObject: &Account{},
}

// InitAccountModel ...
func InitAccountModel(s *mongo.Database) {
	AccountDB.ApplyDatabase(s)

	t := true
	AccountDB.CreateIndex(bson.D{
		{"account_id", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	AccountDB.CreateIndex(bson.D{
		{"username", 1},
		{"hub_code", 1},
	}, &options.IndexOptions{
		Unique:     &t,
		Background: &t,
	})
}
