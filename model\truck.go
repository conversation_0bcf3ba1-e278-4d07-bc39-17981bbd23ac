package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"go.mongodb.org/mongo-driver/mongo"
)

type Truck struct {
	TruckId         int64                  `json:"truckId" bson:"truck_id,omitempty" `
	CreatedBy       int64                  `json:"createdBy,omitempty" bson:"created_by,omitempty" `
	LastUpdatedBy   int64                  `json:"lastUpdatedBy,omitempty" bson:"last_updated_by,omitempty" `
	CreatedTime     *time.Time             `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time             `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	VersionNo       string                 `json:"versionNo,omitempty" bson:"version_no,omitempty"`
	Keyword         string                 `json:"keyword,omitempty" bson:"keyword,omitempty"`
	Status          *enum.TruckStatusValue `json:"status,omitempty" bson:"status,omitempty"`

	// Truck information
	LicensePlate         string                `json:"licensePlate,omitempty" bson:"license_plate,omitempty"`
	TruckType            *enum.TruckTypeValue  `json:"truckType,omitempty" bson:"truck_type,omitempty"`
	Brand                *enum.TruckBrandValue `json:"brand,omitempty" bson:"brand,omitempty"`
	YearOfManufacture    int64                 `json:"yearOfManufacture,omitempty" bson:"year_of_manufacture,omitempty"`
	RegistrationDeadline int64                 `json:"registrationDeadline,omitempty" bson:"registration_deadline,omitempty"`
	Images               *[]string             `json:"images,omitempty" bson:"images,omitempty"`

	// Specifications
	Weight    float64 `json:"weight,omitempty" bson:"weight,omitempty"`
	Height    float64 `json:"height,omitempty" bson:"height,omitempty"`
	Width     float64 `json:"width,omitempty" bson:"width,omitempty"`
	Length    float64 `json:"length,omitempty" bson:"length,omitempty"`
	FuelNorms float64 `json:"fuelNorms,omitempty" bson:"fuel_norms,omitempty"`

	// Body size
	BodyWeight float64 `json:"bodyWeight,omitempty" bson:"body_weight,omitempty"`
	BodyWidth  float64 `json:"bodyWidth,omitempty" bson:"body_width,omitempty"`
	BodyLength float64 `json:"bodyLength,omitempty" bson:"body_length,omitempty"`
}

var TruckDB = &db.Instance{
	ColName:        "truck",
	TemplateObject: &Truck{},
}

func InitTruckModel(s *mongo.Database) {
	TruckDB.ApplyDatabase(s)
}
