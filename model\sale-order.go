package model

import (
	"time"

	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
)

type SaleOrder struct {
	WarehouseCode string `json:"warehouseCode,omitempty"`
	AdminId       int    `json:"adminId,omitempty"`
	Type          string `json:"type,omitempty"`
	SaleOrderCode string `json:"saleOrderCode,omitempty"`
	SaleOrderTime int64  `json:"saleOrderTime,omitempty"`
	TrackingCode  string `json:"trackingCode,omitempty" bson:"tracking_code,omitempty"`
	TplCode       string `json:"tplCode,omitempty" bson:"tpl_code,omitempty"`

	Status                *enum.SaleOrderStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	CustomerInfos         *CustomerSaleOrder         `json:"customer,omitempty" bson:"customer,omitempty"`
	TotalAmount           float64                    `json:"totalAmount,omitempty" bson:"total_amount,omitempty"`
	DeliveryAmount        float64                    `json:"deliveryAmount,omitempty" bson:"delivery_amount,omitempty"`
	TotalAmountWithoutVat float64                    `json:"totalAmountWithoutVat,omitempty" bson:"total_amount_without_vat,omitempty"`
	VatAmount             float64                    `json:"vatAmount,omitempty" bson:"vat_amount,omitempty"`
	CODAmount             float64                    `json:"codAmount,omitempty" bson:"cod_amount,omitempty"`
	InternalCODAmount     float64                    `json:"internalCodAmount,omitempty" bson:"internal_cod_amount,omitempty"`
	PaymentMethod         *enum.PaymentMethodValue   `json:"paymentMethod,omitempty" bson:"payment_method,omitempty"`
	PaymentMethodName     string                     `json:"paymentMethodName,omitempty" bson:"payment_method_name,omitempty"`
	CollectOnDelivery     *bool                      `json:"collectOnDelivery,omitempty" bson:"collect_on_delivery,omitempty"`
	Logs                  []*SaleOrderLog            `json:"logs" bson:"logs,omitempty"`
	OrderLines            []*OrderLine               `json:"orderLines,omitempty" bson:"order_lines,omitempty"`
	Tags                  []string                   `json:"tags,omitempty" bson:"tags,omitempty"`
	Fee                   *Fee                       `json:"fee,omitempty" bson:"fee,omitempty"`
	HubCodeReceive        string                     `json:"hubCodeReceive,omitempty" bson:"hub_code_receive,omitempty"`
}

type Fee struct {
	DeliveryFee float64 `json:"deliveryFee,omitempty" bson:"delivery_fee,omitempty"`
	ExtraFee    float64 `json:"extraFee,omitempty" bson:"extra_fee,omitempty"`
}

type CustomerSaleOrder struct {
	Name                 string             `json:"name,omitempty" bson:"name,omitempty"`
	Code                 int                `json:"code,omitempty" bson:"code,omitempty"`
	BusinessName         string             `json:"businessName,omitempty" bson:"business_name,omitempty"`
	Address              string             `json:"address,omitempty" bson:"address,omitempty"`
	Phone                string             `json:"phone,omitempty" bson:"phone,omitempty"`
	Email                string             `json:"email,omitempty" bson:"email,omitempty"`
	Ward                 string             `json:"ward,omitempty" bson:"ward,omitempty"`
	WardCode             string             `json:"wardCode,omitempty" bson:"ward_code,omitempty"`
	District             string             `json:"district,omitempty" bson:"district,omitempty"`
	DistrictCode         string             `json:"districtCode,omitempty" bson:"district_code,omitempty"`
	TaxCode              interface{}        `json:"taxCode,omitempty" bson:"tax_code,omitempty"`
	Province             string             `json:"province,omitempty" bson:"province,omitempty"`
	ProvinceCode         string             `json:"provinceCode,omitempty" bson:"province_code,omitempty"`
	VerificationCode     string             `json:"verificationCode,omitempty" bson:"verification_code,omitempty"`
	IsDropOffAtWarehouse bool               `json:"isDropOffAtWarehouse,omitempty" bson:"is_drop_off_at_warehouse,omitempty"`
	IsReceiveAtLMHub     bool               `json:"isReceiveAtLMHub,omitempty" bson:"is_receive_at_LM_hub,omitempty"`
	Delivery             *CustomerSaleOrder `json:"delivery,omitempty" bson:"delivery,omitempty"`
	Invoice              *CustomerSaleOrder `json:"invoice,omitempty" bson:"invoice,omitempty"`
}
type SaleOrderLog struct {
	Status      *enum.SaleOrderStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	StatusName  string                     `json:"statusName,omitempty" bson:"status_name,omitempty"`
	Description string                     `json:"description,omitempty" bson:"description,omitempty"`
	ActionTime  int64                      `json:"actionTime,omitempty" bson:"action_time,omitempty"`
	ActionBy    int64                      `json:"actionBy,omitempty" bson:"action_by,omitempty"`
	ExtraData   map[string]interface{}     `json:"extraData,omitempty" bson:"extra_data,omitempty"`
}

type OrderLine struct {
	VersionNo       string     `json:"versionNo,omitempty" bson:"version_no,omitempty"`
	CreatedTime     *time.Time `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	CreatedBy       string     `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	UpdatedBy       string     `json:"updatedBy,omitempty" bson:"updated_by,omitempty"`

	AdminId           int64   `json:"adminId,omitempty" bson:"admin_id,omitempty"`
	SaleOrderCode     string  `json:"saleOrderCode,omitempty" bson:"sale_order_code,omitempty"`
	LineId            int64   `json:"lineId,omitempty" bson:"line_id,omitempty"`
	ERPProductId      int     `json:"erpProductId,omitempty" bson:"erp_product_id,omitempty"`
	AdminProductId    int     `json:"adminProductId,omitempty" bson:"admin_product_id,omitempty"`
	ProductId         int     `json:"productId,omitempty" bson:"product_id,omitempty"`
	SKU               string  `json:"sku,omitempty" bson:"sku,omitempty"`
	ProductType       string  `json:"productType,omitempty" bson:"product_type,omitempty"`
	IsRedTag          bool    `json:"isRedTag" bson:"is_red_tag,omitempty"`
	IsImportant       bool    `json:"isImportant" bson:"is_important,omitempty"`
	IsExternalSeller  bool    `json:"isExternalSeller" bson:"is_external_seller,omitempty"`
	Name              string  `json:"name,omitempty" bson:"name,omitempty"`
	ImageURL          string  `json:"imageUrl,omitempty" bson:"image_url,omitempty"`
	ProductName       string  `json:"productName,omitempty" bson:"product_name,omitempty"`
	SellerCode        string  `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	Packaging         string  `json:"packaging,omitempty" bson:"packaging,omitempty"`
	Weight            float64 `json:"weight,omitempty" bson:"weight,omitempty"`
	Quantity          float64 `json:"quantity" bson:"order_quantity,omitempty"`
	ReservedQuantity  float64 `json:"reservedQuantity" bson:"reserved_quantity,omitempty"`
	ScannedQuantity   float64 `json:"scannedQuantity" bson:"scanned_quantity,omitempty"`
	OutboundQuantity  float64 `json:"outboundQuantity" bson:"outbound_quantity,omitempty"`
	DeliveredQuantity float64 `json:"deliveredQuantity" bson:"delivered_quantity,omitempty"`
	ReturnedQuantity  float64 `json:"returnedQuantity" bson:"returned_quantity,omitempty"`
	Location          string  `json:"location,omitempty" bson:"location,omitempty"`
	UnitPrice         float64 `json:"unitPrice,omitempty" bson:"unit_price,omitempty"`
	VatAmount         float64 `json:"vatAmount,omitempty" bson:"vat_amount,omitempty"`
	RateTax           int64   `json:"rateTax,omitempty" bson:"rateTax,omitempty"`
	IsFragile         bool    `json:"isFragile" bson:"is_fragile,omitempty"`
	IsFrozen          bool    `json:"isFrozen" bson:"is_frozen,omitempty"`
	IsNearExp         bool    `json:"isNearExp" bson:"is_near_exp,omitempty"`
}
