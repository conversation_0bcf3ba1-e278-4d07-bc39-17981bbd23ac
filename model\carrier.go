package model

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type Carrier struct {
	ID                       primitive.ObjectID       `json:"-" bson:"_id,omitempty" `
	CarrierId                int64                    `json:"carrierId" bson:"carrier_id,omitempty"`
	CarrierCode              *enum.PartnerValue       `json:"carrierCode" bson:"carrier_code,omitempty"`
	CarrierName              string                   `json:"carrierName" bson:"carrier_name,omitempty"`
	CarrierInternalName      string                   `json:"carrierInternalName" bson:"carrier_internal_name,omitempty"`
	Active                   *bool                    `json:"active" bson:"active,omitempty"`
	IsInternal               *bool                    `json:"isInternal" bson:"is_internal,omitempty"`
	ParentCode               *enum.PartnerValue       `json:"parentCode" bson:"parent_code,omitempty"`
	ExtraData                *ExtraInfo               `json:"extraData" bson:"extra_data,omitempty"`
	Service                  string                   `json:"service" bson:"service,omitempty"`
	OverCOD                  float64                  `json:"overCod" bson:"over_cod,omitempty"`
	InsuranceValue           float64                  `json:"insuranceValue" bson:"insurance_value,omitempty"`
	PickupCode               string                   `json:"pickupCode" bson:"pickup_code,omitempty"`
	PaymentMethod            string                   `json:"paymentMethod" bson:"payment_method,omitempty"`
	PickupTimeRule           map[string][]*TimeRule   `json:"pickupTimeRule" bson:"pickup_time_rule,omitempty"`
	IdleTimeRule             map[string]*IdleTimeRule `json:"idleTimeRule" bson:"idle_time_rule,omitempty"`
	PickupAddress            *Address                 `json:"pickupAddress" bson:"pickup_address,omitempty"`
	PickupType               int64                    `json:"pickupType" bson:"pickup_type,omitempty"`
	Keyword                  string                   `json:"keyword" bson:"keyword,omitempty"`
	DefaultExternalCarrierOf *[]string                `json:"defaultExternalCarrierOf" bson:"default_external_carrier_of,omitempty"`
	IsOPM                    *bool                    `json:"isOPM" bson:"is_opm,omitempty"`
	CarrierType              *enum.CarrierTypeValue   `json:"carrierType" bson:"carrier_type,omitempty"`
	Logos                    []string                 `json:"logos,omitempty" bson:"logos,omitempty"`
}

type ExtraInfo struct {
	PartnerUserId     string `json:"partnerUserId" bson:"partner_user_id,omitempty"`
	ClientId          string `json:"clientId" bson:"client_id,omitempty"`
	AccessToken       string `json:"accessToken" bson:"access_token,omitempty"`
	SubAccountToken   string `json:"subAccountToken" bson:"sub_account_token,omitempty"`
	BusinessAddressId string `json:"businessAddressId" bson:"business_address_id,omitempty"`
	Domain            string `json:"domain" bson:"domain,omitempty"`
	PartnerId         int64  `json:"partnerId" bson:"partner_id,omitempty"`
	SecretKey         string `json:"secretKey" bson:"secret_key,omitempty"`
	ClientKey         string `json:"clientKey" bson:"client_key,omitempty"`
	ApiKey            string `json:"apiKey" bson:"api_key,omitempty"`
	IsFast            *bool  `json:"isFast" bson:"is_fast,omitempty"`
	CodInsurance      int64  `json:"codInsurance" bson:"cod_insurance,omitempty"`
	Proxy             string `json:"proxy" bson:"proxy,omitempty"`
}

type Address struct {
	Name         string                   `json:"name,omitempty" bson:"name,omitempty"`
	Address      string                   `json:"address,omitempty" bson:"address,omitempty"`
	Phone        string                   `json:"phone,omitempty" bson:"phone,omitempty"`
	Email        string                   `json:"email,omitempty" bson:"email,omitempty"`
	WardName     string                   `json:"wardName,omitempty" bson:"ward_name,omitempty"`
	DistrictName string                   `json:"districtName,omitempty" bson:"district_name,omitempty"`
	ProvinceName string                   `json:"provinceName,omitempty" bson:"province_name,omitempty"`
	WardCode     string                   `json:"wardCode,omitempty" bson:"ward_code,omitempty"`
	DistrictCode string                   `json:"districtCode,omitempty" bson:"district_code,omitempty"`
	ProvinceCode string                   `json:"provinceCode,omitempty" bson:"province_code,omitempty"`
	Latitude     float64                  `json:"latitude,omitempty" bson:"latitude,omitempty"`
	Longitude    float64                  `json:"longitude,omitempty" bson:"longitude,omitempty"`
	Default      *bool                    `json:"default" bson:"default,omitempty"`
	Code         string                   `json:"code,omitempty" bson:"code,omitempty"`
	Type         string                   `json:"type,omitempty" bson:"type,omitempty"` // "DELIVERY", "TRANSPORTING", "PICKUP"
	Distance     float64                  `json:"distance,omitempty" bson:"distance,omitempty"`
	Index        int                      `json:"index" bson:"index"`
	Status       *enum.AddressStatusValue `json:"status,omitempty" bson:"status,omitempty"`
}

type TimeRule struct {
	Hour   int `json:"hour" bson:"hour,omitempty"`
	Minute int `json:"minute" bson:"minute,omitempty"`
}

type IdleTimeRule struct {
	IdleTime int `json:"idleTime" bson:"idle_time,omitempty"`
	Hour     int `json:"hour" bson:"hour,omitempty"`
	Minute   int `json:"minute" bson:"minute,omitempty"`
}

var CarrierDB = &db.Instance{
	ColName:        "carrier",
	TemplateObject: &Carrier{},
}

func InitCarrierModel(s *mongo.Database) {
	CarrierDB.ApplyDatabase(s)

	//t := true
	//CarrierDB.CreateIndex(bson.D{
	//	{"carrier_id", 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//	Unique:     &t,
	//})
}
