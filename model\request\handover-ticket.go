package request

import (
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
)

type HandoverTicketCreateHubOrder struct {
	UpdatedBy                int64                             `json:"updatedBy,omitempty" bson:"updated_by,omitempty" `
	WarehouseCode            string                            `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`
	FromDepartmentCode       string                            `json:"fromDepartmentCode,omitempty" bson:"from_department_code,omitempty"`
	FromDepartmentName       string                            `json:"fromDepartmentName,omitempty" bson:"from_department_name,omitempty"`
	ToDepartmentCode         string                            `json:"toDepartmentCode,omitempty" bson:"to_department_code,omitempty"`
	ToDepartmentName         string                            `json:"toDepartmentName,omitempty" bson:"to_department_name,omitempty"`
	TicketID                 int                               `json:"ticketId,omitempty" bson:"ticket_id,omitempty"`
	TPLCode                  string                            `json:"tplCode,omitempty" bson:"tpl_code,omitempty"`
	TPLName                  string                            `json:"tplName,omitempty" bson:"tpl_name,omitempty"`
	HubOrderType             *enum.HubOrderTypeValue           `json:"hubOrderType"`
	Action                   string                            `json:"action"`
	ActionName               string                            `json:"actionName"`
	SO                       string                            `json:"so" bson:"so,omitempty"`
	HubStatus                *enum.HubShippingOrderStatusValue `json:"hubStatus" bson:"hub_status,omitempty"`
	HandoverType             *enum.HandoverTypeValue           `json:"handoverType" bson:"handover_type,omitempty"`
	HandoverNote             string                            `json:"handoverNote" bson:"handover_note,omitempty"`
	Code                     string                            `json:"code" bson:"code,omitempty"`
	ReceivePackage           int64                             `json:"receivePackage"`
	Products                 []*model.Product                  `json:"products"`
	ReferenceCode            string                            `json:"referenceCode,omitempty" bson:"reference_code,omitempty"`
	ParentReceiveSessionCode string                            `json:"parentReceiveSessionCode,omitempty" bson:"parent_receive_session_code,omitempty"`
}

type HandoverItemRequest struct {
	WarehouseCode      string                        `json:"warehouseCode"`
	TicketID           int64                         `json:"ticketId"`
	SO                 string                        `json:"so"`
	ScannedQuantity    int64                         `json:"scannedQuantity"`
	ReceivedQuantity   int64                         `json:"receivedQuantity"`
	NumPackage         int64                         `json:"numPackage"`
	Weight             float64                       `json:"weight"`
	TrackingCode       string                        `json:"trackingCode"`
	TrackingCodes      []string                      `json:"trackingCodes"`
	Status             *enum.HandoverItemStatusValue `json:"status"`
	TplCode            string                        `json:"tplCode"`
	Sku                string                        `json:"sku"`
	Skus               []string                      `json:"skus"`
	CheckInCode        string                        `json:"checkInCode"`
	ReferenceCodes     []string                      `json:"referenceCodes"`
	ItemType           *enum.HandoverItemTypeValue   `json:"itemType,omitempty"`
	ReceiveSessionCode string                        `json:"receiveSessionCode"`

	CarrierName string `json:"carrierName"`
	CarrierId   int64  `json:"carrierId"`
	CarrierCode string `json:"carrierCode"`
}

type ScanHandoverItemRequest struct {
	TicketID           int64  `json:"ticketId"`
	SO                 string `json:"so"`
	ScannedQuantity    int64  `json:"scannedQuantity"`
	ReceivedQuantity   int64  `json:"receivedQuantity"`
	NumPackage         int64  `json:"numPackage"`
	Sku                string `json:"sku"`
	ReceiveSessionCode string `json:"receiveSessionCode"`
}

// GetHandoverTicketRequest model
type GetHandoverTicketRequest struct {
	model.HandoverTicket
	FromTime             int64                       `json:"fromTime"`
	ToTime               int64                       `json:"toTime"`
	FromCreatedTime      int64                       `json:"fromCreatedTime"`
	ToCreatedTime        int64                       `json:"toCreatedTime"`
	FromDoneHandoverTime int64                       `json:"fromDoneHandoverTime"`
	ToDoneHandoverTime   int64                       `json:"toDoneHandoverTime"`
	FromDepartmentCode   string                      `json:"fromDepartmentCode"`
	ToDepartmentCode     string                      `json:"toDepartmentCode"`
	ListTicketId         []int                       `json:"listTicketId"`
	Status               *enum.HandoverStatusValue   `json:"status,omitempty"`
	Statuses             []*enum.HandoverStatusValue `json:"statuses,omitempty"`
	CreatedBy            int64                       `json:"CreatedBy,omitempty"`
	SO                   string                      `json:"so,omitempty"`
	ItemType             *enum.HandoverItemTypeValue `json:"itemType,omitempty"`
	DepartmentCode       string                      `json:"departmentCode,omitempty"`
	ListSo               []string                    `json:"listSo,omitempty"`
	DepartmentCodes      []string                    `json:"departmentCodes,omitempty"`
	ParentReferenceCode  string                      `json:"parentReferenceCode,omitempty"`
	ReadPreference       *enum.ReadPreferenceValue   `json:"readPreference"`
	IsAssignToDriver     *bool                       `json:"isAssignToDriver"`
	ListReferenceCode    []string                    `json:"listReferenceCode"`
}

type CompleteHandOverRequest struct {
	WarehouseCode string `json:"warehouseCode"`
	SO            string `json:"so"`
}

type DoneTransportRequest struct {
	HubCode      string                       `json:"hubCode"`
	DoneAll      bool                         `json:"doneAll"`
	ListTicketId []int64                      `json:"listTicketId"`
	Note         map[string]map[string]string `json:"note"`
	CheckInAt    enum.CheckInAtValue          `json:"checkInAt"`
	IgnoreBins   map[string]bool            `json:"ignoreBins"`
}

type CreateHandoverRequest struct {
	model.HandoverTicket
	DeliveryTruck *model.DeliveryTruck `json:"deliveryTruck"`
}

type UpdateHandoverRequest struct {
	model.HandoverTicket
	SealCode string `json:"sealCode"`
}

type DoneReceiveTicketRequest struct {
	TicketId int64                     `json:"ticketId"`
	HubCode  string                    `json:"hubCode"`
	Status   *enum.HandoverStatusValue `json:"status"`
	Note     map[string]string         `json:"note"`
}
