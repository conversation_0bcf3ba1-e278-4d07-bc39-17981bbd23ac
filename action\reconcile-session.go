package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
)

func GetReconcileSession(input *request.GetReconcileSession) *common.APIResponse {

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Get Reconcile Session Success",
		Data:    nil,
	}
}

//func AddOrderToReconcileSession(input *request.AddOrderToReconcileSession) *common.APIResponse {
//	// TODO: Add Order To Reconcile Session
//
//	return &common.APIResponse{
//		Status:  common.APIStatus.Ok,
//		Message: "Add Order To Reconcile Session Success",
//	}
//}
