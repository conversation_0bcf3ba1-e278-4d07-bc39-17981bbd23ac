package model

import (
	"time"

	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
)

type ShippingInfo struct {
	// TODO: rename json tag here
	CODAmount      float64                `json:"cod_amount"`
	TrackingNumber string                 `json:"tracking_number"`
	FeeAmount      float64                `json:"fee_amount"`
	ExtraInfo      map[string]interface{} `json:"extra_info"`
	Distance       float64                `json:"distance"`

	TplCode              *enum.PartnerValue `json:"tplCode"`
	ExpectReceivedTime   *time.Time         `json:"expectReceivedTime"`
	ReferenceCode        string             `json:"referenceCode"`
	VatAmount            float64            `json:"vatAmount"`
	ExpectedPickupTime   *time.Time         `json:"expectedPickupTime"`
	ExpectedDeliveryTime *time.Time         `json:"expectedDeliveryTime"`
	SecretKey            string             `json:"secretKey"`
}

type ShippingService struct {
	CarrierId    int64  `json:"carrier_id"`
	Name         string `json:"name"`
	InternalName string `json:"internal_name"`
}
