package action

import (
	"errors"
	"fmt"
	"sort"
	"strconv"
	"time"
	_ "time/tzdata"

	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/shipping"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
)

type ahamove struct{}

var Ahamove ahamove

func (ahamove) BookShipping(input *request.BookShipping, saleOrder *model.SaleOrder, carrierModel *model.Carrier, pickupAddress *model.Address) (result *model.ShippingInfo, err error) {
	if shipping.TplShippingClient.AhamoveClient == nil {
		err = fmt.Errorf("Dịch vụ book AHAMOVE chưa được khởi tạo")
		return
	}

	if len(carrierModel.Service) == 0 {
		err = fmt.Errorf("Không tìm thấy dịch vụ vận chuyển phù hợp")
	}

	requestBooking := request.BookAhamoveRequest{
		Token:         carrierModel.ExtraData.AccessToken,
		ServiceId:     carrierModel.Service,
		PaymentMethod: carrierModel.PaymentMethod,
		Path: []*request.AhamovePath{
			{
				Address: pickupAddress.Address + ", " + pickupAddress.WardName + ", " + pickupAddress.DistrictName + ", " + pickupAddress.ProvinceName + ", Vietnam",
				Name:    pickupAddress.Name,
				Mobile:  pickupAddress.Phone,
			},
			{
				Address:        saleOrder.CustomerInfos.Delivery.Address + ", " + saleOrder.CustomerInfos.Delivery.Ward + "," + saleOrder.CustomerInfos.Delivery.District + ", " + saleOrder.CustomerInfos.Delivery.Province + ", Vietnam",
				Name:           saleOrder.CustomerInfos.Delivery.Name,
				Mobile:         saleOrder.CustomerInfos.Delivery.Phone,
				TrackingNumber: input.SO,
				COD:            int64(saleOrder.CODAmount),
				RequirePod:     true,
				PodType:        "photo",
			},
		},
	}

	if saleOrder.PaymentMethod != nil && *saleOrder.PaymentMethod == enum.PaymentMethod.BANK {
		requestBooking.Path[1].COD = 0
	}

	current := time.Now()
	loc, _ := time.LoadLocation(enum.Timezone)
	pickupTimeArray := getPickupTime(current, carrierModel, loc)

	if carrierModel.IdleTimeRule != nil {
		requestBooking.IdleUntil = getIdleTime(current, carrierModel, loc)
	}

	if carrierModel.PickupTimeRule != nil {
		if len(pickupTimeArray) > 0 {
			sort.Ints(pickupTimeArray)
			requestBooking.OrderTime = int64(pickupTimeArray[0])
		} else {
			additionCurrentTime := current.AddDate(0, 0, 1)
			t := time.Date(additionCurrentTime.Year(), additionCurrentTime.Month(), additionCurrentTime.Day(), 0, 0, 0, 0, loc)
			pickupTimeArray = getPickupTime(t, carrierModel, loc)
			if len(pickupTimeArray) > 0 {
				sort.Ints(pickupTimeArray)
				requestBooking.OrderTime = int64(pickupTimeArray[0])
			} else {
				requestBooking.OrderTime = current.Unix()
			}
		}
	} else {
		requestBooking.OrderTime = current.Unix()
	}

	result, err = shipping.TplShippingClient.AhamoveClient.CreateTrackingAhamove(requestBooking)
	if err != nil {
		return
	}

	result.CODAmount = float64(requestBooking.Path[1].COD)

	return
}

func getPickupTime(inputTime time.Time, carrierModel *model.Carrier, loc *time.Location) (pickupTimeArray []int) {
	//set timezone,
	timestamp := time.Unix(inputTime.Unix(), 0).In(loc)

	weekDay := timestamp.Weekday()
	hour := timestamp.Hour()
	minute := timestamp.Minute()
	pickupTimeRules := carrierModel.PickupTimeRule[weekDay.String()]
	for _, pickupTimeRule := range pickupTimeRules {
		if pickupTimeRule.Hour > hour || (pickupTimeRule.Hour == hour && pickupTimeRule.Minute > minute) {
			t := time.Date(timestamp.Year(), timestamp.Month(), timestamp.Day(), hour, minute, 0, 0, loc)
			pickupTimeArray = append(pickupTimeArray, int(t.Unix()))
		}
	}

	return
}

func getIdleTime(inputTime time.Time, carrierModel *model.Carrier, loc *time.Location) (idleTime int64) {
	//set timezone,
	timestamp := time.Unix(inputTime.Unix(), 0).In(loc)

	hour := timestamp.Hour()
	idleTimeRule := carrierModel.IdleTimeRule[timestamp.Weekday().String()]

	if idleTimeRule.Hour == 0 && idleTimeRule.IdleTime == 0 {
		return 0
	}

	if idleTimeRule.IdleTime > hour {
		t := time.Date(timestamp.Year(), timestamp.Month(), timestamp.Day(), idleTimeRule.Hour, idleTimeRule.Minute, 0, 0, loc)
		idleTime = t.Unix()
	} else {
		additionTime := inputTime.Add(time.Hour * 24)
		timestamp = time.Unix(additionTime.Unix(), 0).In(loc)
		idleTimeRule := carrierModel.IdleTimeRule[timestamp.Weekday().String()]
		if idleTimeRule.Hour == 0 && idleTimeRule.IdleTime == 0 {
			return 0
		}
		t := time.Date(timestamp.Year(), timestamp.Month(), timestamp.Day(), idleTimeRule.Hour, idleTimeRule.Minute, 0, 0, loc)
		idleTime = t.Unix()
	}
	return
}

func (ahamove) BookOnWheel(input *request.BookShipping, hubOrders []*model.HubShippingOrder, carrierModel *model.Carrier, pickupAddress *model.Address) (result *model.ShippingInfo, err error) {
	requestBooking := request.BookAhamoveRequest{
		Token:         carrierModel.ExtraData.AccessToken,
		ServiceId:     carrierModel.Service,
		PaymentMethod: carrierModel.PaymentMethod,
		Path: []*request.AhamovePath{
			{
				Address: pickupAddress.Address + ", " + pickupAddress.WardName + ", " + pickupAddress.DistrictName + ", " + pickupAddress.ProvinceName + ", Vietnam",
				Name:    pickupAddress.Name,
				Mobile:  pickupAddress.Phone,
			},
		},
	}

	sumPackage := 0

	for i, hubOrder := range hubOrders {
		sumPackage += int(hubOrder.NumPackage)
		requestBooking.Path = append(requestBooking.Path, &request.AhamovePath{
			Address:        hubOrder.ToCustomerAddress + ", " + hubOrder.ToWardName + "," + hubOrder.ToDistrictName + ", " + hubOrder.ToProvinceName + ", Vietnam",
			Name:           hubOrder.ToCustomerName,
			Mobile:         hubOrder.ToCustomerPhone,
			TrackingNumber: hubOrder.ReferenceCode,
			COD:            int64(hubOrder.CODAmount),
			Remarks:        "Số kiện: " + strconv.Itoa(int(hubOrder.NumPackage)),
			RequirePod:     true,
			PodType:        "photo",
		})

		if hubOrder.PaymentMethod != nil && *hubOrder.PaymentMethod == enum.PaymentMethod.BANK {
			requestBooking.Path[i+1].COD = 0
		}
	}

	requestBooking.Path[0].Remarks = "Tổng số kiện: " + strconv.Itoa(sumPackage)

	if input.SupplierId != 0 {
		requestBooking.Path[0].PartnerField = &request.PartnerAhamove{
			StoreId: strconv.Itoa(input.SupplierId),
		}
	}

	current := time.Now()
	loc, _ := time.LoadLocation(enum.Timezone)
	pickupTimeArray := getPickupTime(current, carrierModel, loc)

	if carrierModel.IdleTimeRule != nil {
		requestBooking.IdleUntil = getIdleTime(current, carrierModel, loc)
	}

	if carrierModel.PickupTimeRule != nil {
		if len(pickupTimeArray) > 0 {
			sort.Ints(pickupTimeArray)
			requestBooking.OrderTime = int64(pickupTimeArray[0])
		} else {
			additionCurrentTime := current.AddDate(0, 0, 1)
			t := time.Date(additionCurrentTime.Year(), additionCurrentTime.Month(), additionCurrentTime.Day(), 0, 0, 0, 0, loc)
			pickupTimeArray = getPickupTime(t, carrierModel, loc)
			if len(pickupTimeArray) > 0 {
				sort.Ints(pickupTimeArray)
				requestBooking.OrderTime = int64(pickupTimeArray[0])
			} else {
				requestBooking.OrderTime = current.Unix()
			}
		}
	} else {
		requestBooking.OrderTime = current.Unix()
	}

	result, err = shipping.TplShippingClient.AhamoveClient.CreateTrackingAhamove(requestBooking)

	if err != nil {
		return
	}

	return
}

func (ahamove) BookShippingOrder(
	input *request.BookShippingOrder,
	carrierModel *model.Carrier) (
	*model.ShippingInfo, error) {
	if shipping.TplShippingClient.AhamoveClient == nil {
		err := errors.New("Dịch vụ book Ahamove chưa được khởi tạo")
		return nil, err
	}
	if len(carrierModel.Service) == 0 {
		err := fmt.Errorf("Không tìm thấy dịch vụ vận chuyển phù hợp")
		return nil, err
	}

	requestBooking := request.BookAhamoveRequest{
		Token:         carrierModel.ExtraData.AccessToken,
		ServiceId:     carrierModel.Service,
		PaymentMethod: carrierModel.PaymentMethod,
		Requests: []*request.AhamoveRequest{
			{
				Id: carrierModel.Service + "-TRANSFER-COD",
			},
		},
		Path: []*request.AhamovePath{
			{
				Address: input.From.Address + ", " +
					input.From.WardName + ", " +
					input.From.DistrictName + ", " +
					input.From.ProvinceName + ", Vietnam",
				Name:   input.From.Name,
				Mobile: input.From.Phone,
			},
			{
				Address:        input.To.Address + ", " + input.To.WardName + "," + input.To.DistrictName + ", " + input.To.ProvinceName + ", Vietnam",
				Name:           input.To.Name,
				Mobile:         input.To.Phone,
				TrackingNumber: input.ReferenceCode,
				// TODO: Change here to support multiple method
				COD:        int64(input.CODAmount),
				RequirePod: true,
				PodType:    "photo",
			},
		},
	}

	if requestBooking.Path[1].COD == 0 {
		requestBooking.Path[1].ItemValue = int64(input.OrderValue)
		requestBooking.Requests = append(
			requestBooking.Requests, &request.AhamoveRequest{
				Id: carrierModel.Service + "-INSURANCE",
			})
	}

	if requestBooking.Path[1].COD > carrierModel.ExtraData.CodInsurance {
		requestBooking.Requests = append(
			requestBooking.Requests, &request.AhamoveRequest{
				Id: carrierModel.Service + "-INSURANCE",
			})
	}

	current := time.Now()
	loc, _ := time.LoadLocation(enum.Timezone)
	pickupTimeArray := getPickupTime(current, carrierModel, loc)
	if carrierModel.IdleTimeRule != nil {
		requestBooking.IdleUntil = getIdleTime(current, carrierModel, loc)
	}
	if carrierModel.PickupTimeRule != nil {
		if len(pickupTimeArray) > 0 {
			sort.Ints(pickupTimeArray)
			requestBooking.OrderTime = int64(pickupTimeArray[0])
		} else {
			additionCurrentTime := current.AddDate(0, 0, 1)
			t := time.Date(additionCurrentTime.Year(), additionCurrentTime.Month(), additionCurrentTime.Day(), 0, 0, 0, 0, loc)
			pickupTimeArray = getPickupTime(t, carrierModel, loc)
			if len(pickupTimeArray) > 0 {
				sort.Ints(pickupTimeArray)
				requestBooking.OrderTime = int64(pickupTimeArray[0])
			} else {
				requestBooking.OrderTime = current.Unix()
			}
		}
	} else {
		requestBooking.OrderTime = 0
	}
	result, err := shipping.
		TplShippingClient.
		AhamoveClient.
		CreateTrackingAhamoveV2(requestBooking, carrierModel)
	if err != nil {
		return nil, err
	}

	result.CODAmount = float64(requestBooking.Path[1].COD)
	return result, nil
}
