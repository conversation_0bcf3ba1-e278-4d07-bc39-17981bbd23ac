package warehouse_core

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/response"
)

// UpdateSaleOrder
func (cli *Client) UpdateSaleOrder(UpdateSaleOrderRequest model.SaleOrder) (response *common.APIResponse) {
	query := map[string]string{}

	var res *client.RestResult
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, query, UpdateSaleOrderRequest, pathSaleOrder, nil)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Bad request when update sale order",
		}
	}

	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Bad request when Unmarshal update sale order response",
		}
	}

	return
}

func (cli *Client) GetSaleOrders(saleOrderCode []string) (result []*model.SaleOrder, err error) {
	type myQueryRequest struct {
		SaleOrderCodes []string `json:"saleOrderCodes"`
	}
	queryParam := myQueryRequest{
		SaleOrderCodes: saleOrderCode,
	}

	q, _ := json.Marshal(queryParam)

	query := map[string]string{
		"q": string(q),
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, query, nil, pathGetSaleOrder, nil)
	if err != nil {
		return
	}

	type myResponse struct {
		Status  string             `json:"status"`
		Code    string             `json:"code"`
		Message string             `json:"message"`
		Data    []*model.SaleOrder `json:"data"`
	}

	var resp = new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &resp)

	if resp != nil && resp.Status != common.APIStatus.Ok {
		err = fmt.Errorf("%v - %v - %v", resp.Status, resp.Code, resp.Message)
		return
	}

	if resp.Data != nil {
		result = resp.Data
	}

	return
}

func (cli *Client) GetSaleOrder(saleOrderCode, warehouseCode string) (result *model.SaleOrder, err error) {
	if strings.HasPrefix(saleOrderCode, "RONS") {
		saleOrderCode = strings.Split(saleOrderCode, "-")[0]
	}

	if (strings.HasPrefix(saleOrderCode, "SO") ||
		strings.HasPrefix(saleOrderCode, "LO") ||
		strings.HasPrefix(saleOrderCode, "RO") ||
		strings.HasPrefix(saleOrderCode, "TO") ||
		strings.HasPrefix(saleOrderCode, "CO")) &&
		(strings.Contains(saleOrderCode, "-P") ||
			strings.Contains(saleOrderCode, "-F")) {
		return cli.DeliveryOrderCodeToSaleOrder(saleOrderCode)
	}

	query := map[string]string{
		"saleOrderCode": saleOrderCode,
		"warehouseCode": warehouseCode,
	}

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, query, nil, pathSaleOrder, nil)
	if err != nil {
		return
	}

	type myResponse struct {
		Status  string            `json:"status"`
		Code    string            `json:"code"`
		Message string            `json:"message"`
		Data    []model.SaleOrder `json:"data"`
	}

	var resp = new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &resp)

	if resp != nil && resp.Status != common.APIStatus.Ok {
		err = fmt.Errorf("%v - %v", resp.Status, resp.Message)
		return
	}

	if resp.Data != nil {
		result = &resp.Data[0]
	}

	return
}

// TODO: Change completely to DO when all order changed to DO
func (cli *Client) GetDeliveryOrder(doCode string) (*response.DeliveryOrder, error) {
	type myQueryRequest struct {
		DeliveryOrderCode string `json:"deliveryOrderCode"`
	}
	queryParam := myQueryRequest{
		DeliveryOrderCode: doCode,
	}

	q, _ := json.Marshal(queryParam)

	query := map[string]string{
		"q": string(q),
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, query, nil, pathDeliveryOrder, nil)
	if err != nil {
		return nil, err
	}

	type myResponse struct {
		Status  string                   `json:"status"`
		Code    string                   `json:"code"`
		Message string                   `json:"message"`
		Data    []response.DeliveryOrder `json:"data"`
	}

	var resp = new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &resp)

	if resp != nil && resp.Status != common.APIStatus.Ok {
		err = fmt.Errorf("%v - %v", resp.Status, resp.Message)
		return nil, err
	}

	if resp.Data != nil {
		return &resp.Data[0], nil
	}

	return nil, errors.New("not found")
}

func (cli *Client) GetDeliveryOrderItem(doCode string) ([]*response.DeliveryOrderItem, error) {
	type myQueryRequest struct {
		DeliveryOrderCode string `json:"deliveryOrderCode"`
	}
	queryParam := myQueryRequest{
		DeliveryOrderCode: doCode,
	}

	q, _ := json.Marshal(queryParam)

	query := map[string]string{
		"q":      string(q),
		"offset": fmt.Sprintf("%d", 0),
		"limit":  fmt.Sprintf("%d", 200),
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, query, nil, pathDeliveryOrderItem, nil)
	if err != nil {
		return nil, err
	}

	type myResponse struct {
		Status  string                        `json:"status"`
		Code    string                        `json:"code"`
		Message string                        `json:"message"`
		Data    []*response.DeliveryOrderItem `json:"data"`
	}

	var resp = new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &resp)

	if resp != nil && resp.Status != common.APIStatus.Ok {
		err = fmt.Errorf("%v - %v", resp.Status, resp.Message)
		return nil, err
	}

	if resp.Data != nil {
		return resp.Data, nil
	}

	return nil, errors.New("not found")
}

func (cli *Client) DeliveryOrderCodeToSaleOrder(doCode string) (*model.SaleOrder, error) {
	do, doErr := cli.GetDeliveryOrder(doCode)
	if doErr != nil {
		return nil, doErr
	}

	doItems, doItemsErr := cli.GetDeliveryOrderItem(doCode)
	if doItemsErr != nil {
		return nil, doItemsErr
	}

	if (strings.HasPrefix(do.SaleOrderCode, "SO") ||
		strings.HasPrefix(do.SaleOrderCode, "LO") ||
		strings.HasPrefix(do.SaleOrderCode, "CO") ||
		strings.HasPrefix(do.SaleOrderCode, "RO") ||
		strings.HasPrefix(do.SaleOrderCode, "TO")) &&
		(strings.Contains(do.SaleOrderCode, "-P") ||
			strings.Contains(do.SaleOrderCode, "-F")) {
		return nil, errors.New("SaleOrderCode should not be DOCode!!!")
	}

	saleOrder, saleOrderErr := cli.GetSaleOrder(do.SaleOrderCode, "")
	if saleOrderErr != nil {
		return nil, saleOrderErr
	}

	saleOrder.CODAmount = do.CODAmount
	saleOrder.DeliveryAmount = do.DeliveryAmount
	saleOrder.WarehouseCode = do.WarehouseCode
	orderLines := make([]*model.OrderLine, len(doItems))
	for i, item := range doItems {
		orderLines[i] = &model.OrderLine{
			SKU:             item.SKU,
			Name:            item.Name,
			Quantity:        float64(item.OutboundQuantity),
			Weight:          item.Weight,
			UnitPrice:       item.UnitPrice,
			ScannedQuantity: float64(item.OutboundQuantity),
			ProductName:     item.Name,
		}
	}
	saleOrder.OrderLines = orderLines
	saleOrder.Tags = do.Tags
	return saleOrder, nil
}
