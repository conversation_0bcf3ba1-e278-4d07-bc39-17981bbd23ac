package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type Hub struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	VersionNo       string             `json:"versionNo,omitempty" bson:"version_no,omitempty" `
	CreatedBy       int64              `json:"createdBy,omitempty" bson:"created_by,omitempty" `
	UpdatedBy       int64              `json:"updatedBy,omitempty" bson:"updated_by,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Code                   string              `json:"code" bson:"code,omitempty"`
	HubId                  int64               `json:"hubId" bson:"hub_id,omitempty"`
	Name                   string              `json:"name" bson:"name,omitempty"`
	Active                 *bool               `json:"active" bson:"active,omitempty"`
	ParentCode             *string             `json:"parentCode" bson:"parent_code,omitempty"`
	Address                *Address            `json:"address" bson:"address,omitempty"`
	ListCarrierRefId       []int64             `json:"listCarrierRefId" bson:"list_carrier_ref_id"`
	WarehouseReferenceCode string              `json:"warehouseReferenceCode" bson:"warehouse_reference_code,omitempty"`
	Keyword                string              `json:"keyword,omitempty" bson:"keyword,omitempty"`
	Zones                  []string            `json:"zones" bson:"zones"`
	DefaultCarrierId       int64               `json:"defaultCarrierId,omitempty" bson:"default_carrier_id,omitempty"`
	LimitAssignedAmount    float64             `json:"limitAssignedAmount" bson:"limit_assigned_amount,omitempty"`
	TotalReceivedAmount    float64             `json:"totalReceivedAmount" bson:"total_received_amount"`
	LimitStatus            string              `json:"limitStatus" bson:"limit_status,omitempty"`
	LimitAmountByWeekday   map[string]float64  `json:"limitAmountByWeekday,omitempty" bson:"limit_amount_by_weekday,omitempty"`
	LimitAmountRequest     []*LimitAmountInput `json:"limitAmountRequest,omitempty" bson:"limit_amount_request,omitempty"`
	CompanyBankInfo        struct {
		BankName        string `json:"bankName" bson:"bank_name,omitempty"`
		BankAccountName string `json:"bankAccountName" bson:"bank_account_name,omitempty"`
		BankAccountID   string `json:"bankAccountID" bson:"bank_account_id,omitempty"`
	} `json:"companyBankInfo,omitempty" bson:"company_bank_info,omitempty"`
	AutoDoneReconcile *bool  `json:"autoDoneReconcile,omitempty" bson:"auto_done_reconcile,omitempty"`
	FMDriverIds       *[]int `json:"fmDriverIds,omitempty" bson:"fm_driver_ids,omitempty"`
	IsFMHub           *bool  `json:"isFMHub,omitempty" bson:"is_fm_hub,omitempty"`
	AllowDropOffHub   *bool  `json:"allowDropOffHub,omitempty" bson:"allow_drop_off_hub,omitempty"`
}

type LimitAmountInput struct {
	From        string  `json:"from"`
	To          string  `json:"to"`
	LimitAmount float64 `json:"limitAmount"`
}

var HubDB = &db.Instance{
	ColName:        "hub",
	TemplateObject: &Hub{},
}

func InitHubModel(s *mongo.Database) {
	HubDB.ApplyDatabase(s)
}
