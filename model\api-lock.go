package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Cache struct {
	Key         string     `json:"key,omitempty" bson:"key,omitempty"`
	Type        string     `json:"type,omitempty" bson:"type,omitempty"`
	CreatedTime *time.Time `json:"createdTime,omitempty" bson:"created_time,omitempty"`
}

var CacheDB = &db.Instance{
	ColName:        "cache",
	TemplateObject: &Cache{},
}

func InitCacheModel(s *mongo.Database) {
	CacheDB.ApplyDatabase(s)
	var ttl int32 = 10

	CacheDB.CreateIndex(bson.D{
		{"created_time", 1},
	}, &options.IndexOptions{
		ExpireAfterSeconds: &ttl,
	})

	CacheDB.CreateIndex(bson.D{
		{"key", 1},
		{"type", 1},
	}, &options.IndexOptions{
		Unique: &enum.True,
	})
}
