package response

import (
	"time"

	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ReconcileSession model
type ReconcileSession struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	VersionNo       string             `json:"versionNo,omitempty" bson:"version_no,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	CreatedBy       int64              `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	UpdatedBy       int64              `json:"updatedBy,omitempty" bson:"updated_by,omitempty"`

	Code                      string                     `json:"code" bson:"code,omitempty"`
	Status                    *enum.ReconcileStatusValue `json:"status" bson:"status,omitempty"`
	ConfirmedTime             *time.Time                 `json:"confirmedTime" bson:"confirmed_time,omitempty"`
	ReconciledTime            *time.Time                 `json:"reconciledTime" bson:"reconciled_time,omitempty"`
	ReconciledBy              int64                      `json:"reconciledBy,omitempty" bson:"reconciled_by,omitempty"`
	TotalOrder                int64                      `json:"totalOrder" bson:"total_order,omitempty"`
	TotalAmount               int64                      `json:"totalAmount" bson:"total_amount,omitempty"`
	TotalRequestOrder         int64                      `json:"totalRequestOrder" bson:"total_request_order,omitempty"`
	TotalRequestAmount        int64                      `json:"totalRequestAmount" bson:"total_request_amount,omitempty"`
	TotalReceivedOrder        int64                      `json:"totalReceivedOrder" bson:"total_received_order,omitempty"`
	TotalReceivedAmount       int64                      `json:"totalReceivedAmount" bson:"total_received_amount,omitempty"`
	CarrierCode               string                     `json:"carrierCode" bson:"carrier_code,omitempty"`
	ShortCode                 string                     `json:"shortCode" bson:"short_code,omitempty"`
	BankProofLink             []string                   `json:"bankProofLink" bson:"bank_proof_link"`
	BankAmount                int64                      `json:"bankAmount" bson:"bank_amount"`
	TransactionCode           string                     `json:"transactionCode" bson:"transaction_code"`
	AccountantAmount          int64                      `json:"accountantAmount" bson:"accountant_amount,omitempty"`
	AccountantTransactionCode string                     `json:"accountantTransactionCode" bson:"accountant_transaction_code,omitempty"`

	ReconcileType *enum.ReconcileTypeRequestValue `json:"reconcileType" bson:"reconcile_type,omitempty"`
	HubCode       string                          `json:"hubCode,omitempty" bson:"hub_code,omitempty"`
	UserId        int64                           `json:"userId,omitempty" bson:"user_id,omitempty"`
	Note          string                          `json:"note,omitempty" bson:"note,omitempty"`

	AutoDoneReconcile *bool `json:"autoDoneReconcile,omitempty" bson:"-"`
}

// ReconcileSessionOrder model
type ReconcileSessionOrder struct {
	ID                primitive.ObjectID              `json:"-" bson:"_id,omitempty"`
	VersionNo         string                          `json:"versionNo,omitempty" bson:"version_no,omitempty"`
	CreatedTime       *time.Time                      `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime   *time.Time                      `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	CreatedBy         int64                           `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	UpdatedBy         int64                           `json:"updatedBy,omitempty" bson:"updated_by,omitempty"`
	LineID            int64                           `json:"lineID,omitempty" bson:"line_id,omitempty"`
	ReconcileCode     string                          `json:"reconcileCode,omitempty" bson:"reconcile_code,omitempty"`
	Status            *enum.ReconcileOrderStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	OrderID           int64                           `json:"orderID,omitempty" bson:"order_id,omitempty"`
	ReferenceCode     string                          `json:"referenceCode,omitempty" bson:"reference_code,omitempty"`
	TrackingCode      string                          `json:"trackingCode,omitempty" bson:"tracking_code,omitempty"`
	HubCode           string                          `json:"hubCode,omitempty" bson:"hub_code,omitempty"`
	CODAmount         int64                           `json:"codAmount,omitempty" bson:"cod_amount,omitempty"`
	CarrierCODAmount  *int64                          `json:"carrierCodAmount,omitempty" bson:"carrier_cod_amount,omitempty"`
	ReceivedCODAmount *int64                          `json:"receivedCODAmount,omitempty" bson:"received_cod_amount,omitempty"`
	PaymentMethod     string                          `json:"paymentMethod,omitempty" bson:"payment_method,omitempty"`
	CarrierCode       string                          `json:"carrierCode,omitempty" bson:"carrier_code,omitempty"`
	Note              string                          `json:"note,omitempty" bson:"note,omitempty"`
	ReconciledTime    *time.Time                      `json:"reconciledTime,omitempty" bson:"reconciled_time,omitempty"`
	DeliveredTime     *time.Time                      `json:"deliveredTime,omitempty" bson:"delivered_time,omitempty"`
	DeliveryAmount    float64                         `json:"deliveryAmount,omitempty" bson:"delivery_amount,omitempty"`
	IsConflictAmount  bool                            `json:"isConflictAmount,omitempty" bson:"is_conflict_amount,omitempty"`
}
