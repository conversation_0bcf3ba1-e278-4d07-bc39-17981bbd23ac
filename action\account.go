package action

import (
	"encoding/json"
	"fmt"
	"math"
	"sync"
	"time"

	"github.com/google/uuid"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func GetMyAccount(driverId int64, hubCode string) *common.APIResponse {

	if hubCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "NOT_FOUND_HUB",
			Message:   "Không tìm thấy mã HUB",
		}
	}
	response := model.AccountDB.QueryOne(bson.M{
		"account_id": driverId,
		"hub_code":   hubCode,
	})

	if response.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "NOT_FOUND_EMPLOYEE",
			Message:   "Không tìm thấy nhân viên",
		}
	}

	return response
}

func GetAccount(query *request.Account, offset int64, limit int64, getTotal bool) *common.APIResponse {
	filter := bson.M{}
	if query.AccountID != 0 {
		filter["account_id"] = query.AccountID
	}
	if query.HubCode != "" {
		filter["hub_code"] = query.HubCode
	}

	if query.Status != "" {
		filter["status"] = query.Status
	}

	if query.RoleCode != "" {
		filter["role_code"] = query.RoleCode
	}

	if query.HashTag != "" {
		filter["hash_tag"] = bson.M{
			"$regex": ".*" + utils.NormalizeString(query.HashTag) + ".*",
		}
	}
	if query.IsOnTrip != nil {
		if *query.IsOnTrip == false {
			filter["$or"] = bson.A{
				bson.M{
					"is_on_trip": nil,
				},
				bson.M{
					"is_on_trip": false,
				},
			}
		} else {
			filter["is_on_trip"] = true
		}
	}

	if len(query.RoleCodes) > 0 {
		filter["role_code"] = bson.M{"$in": query.RoleCodes}
	}

	result := model.AccountDB.Query(filter, offset, limit, &primitive.M{"_id": -1})
	if getTotal {
		countResult := model.AccountDB.Count(filter)
		result.Total = countResult.Total
	}
	return result
}

func GetLogisticHubLimitInformation(hubCode string) *common.APIResponse {
	// hạn mức của HUB
	getHub := model.HubDB.QueryOne(bson.M{"code": hubCode})
	if getHub.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "NOT_FOUND_HUB",
			Message:   "Không tìm thấy hub",
		}
	}

	// Handle rider limit
	hub := getHub.Data.([]*model.Hub)[0]
	type DataResponse struct {
		HubLimit           float64 `json:"hubLimit"`
		HubAssignedAmount  float64 `json:"hubAssignedAmount"`
		TotalRider         int64   `json:"totalRider"`
		TotalRiderActive   int64   `json:"totalRiderActive"`
		TotalRiderSetLimit int64   `json:"totalRiderSetLimit"`
	}

	dataResponse := &DataResponse{
		HubLimit:          getSmallestHubLimitAmount(hub.LimitAmountByWeekday, hub.LimitAmountByWeekday[time.Now().Weekday().String()]),
		HubAssignedAmount: hub.LimitAssignedAmount,
	}

	var roleCodes []string
	for k, _ := range conf.Config.RiderRoleToSyncReceived {
		roleCodes = append(roleCodes, k)
	}

	filter := bson.M{"hub_code": hubCode, "role_code": bson.M{"$in": roleCodes}}
	total := model.AccountDB.Count(filter).Total
	if total > 0 {
		dataResponse.TotalRider = total
		accountResponse := model.AccountDB.Query(filter, 0, total, nil)
		if accountResponse.Status == common.APIStatus.Ok {
			for _, val := range accountResponse.Data.([]*model.Account) {
				if val.Status == string(enum.AccountStatus.ACTIVE) {
					dataResponse.TotalRiderActive += 1
				}
				if val.LimitAmount > 0 {
					dataResponse.TotalRiderSetLimit += 1
				}
			}
		} else {
			dataResponse.TotalRider = 0
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
		Data:    []interface{}{dataResponse}}
}

func UpdateAccount(input *model.Account) *common.APIResponse {

	getAccount := model.AccountDB.QueryOne(bson.M{
		"account_id": input.AccountID,
		"hub_code":   input.HubCode,
	})
	if getAccount.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "NOT_FOUND_EMPLOYEE",
			Message:   "Không tìm thấy nhân viên cần cập nhật",
		}
	}

	getHub := model.HubDB.QueryOne(bson.M{"code": input.HubCode})
	if getHub.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "NOT_FOUND_HUB",
			Message:   "Không tìm thấy HUB cần cập nhật",
		}
	}

	// Handle rider limit
	hub := getHub.Data.([]*model.Hub)[0]
	account := getAccount.Data.([]*model.Account)[0]

	if input.LimitAmount >= 0 && input.HubCode != "" {
		if hub.LimitStatus != "ACTIVE" {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				ErrorCode: "HUB_TURNOFF_LIMIT",
				Message:   "Không được phép cập nhật Hub chưa thiết lập hạn mức",
			}
		}

		hubLimit := getSmallestHubLimitAmount(hub.LimitAmountByWeekday, hub.LimitAmountByWeekday[time.Now().Weekday().String()])
		accountLimit := account.LimitAmount
		hubTotalAssignedAmount := hub.LimitAssignedAmount
		calculateHubAssignedAmount := hubTotalAssignedAmount - accountLimit + input.LimitAmount
		// Note: Số tiền sẽ thêm hạn mức lớn hơn hạn mức hiện tại của Hub hoặc tiền đang nhận lớn hơn số dư hạn mức còn có thể cập nhật
		if input.LimitAmount-accountLimit > hubLimit-hubTotalAssignedAmount {
			return limitErrorResponse(fmt.Sprintf("Rất tiếc, vượt quá hạn mức còn có thể cập nhật %sđ", utils.MoneyFormat(hubLimit-hubTotalAssignedAmount)))
		} else if calculateHubAssignedAmount > hubLimit {
			return limitErrorResponse(fmt.Sprintf("Rất tiếc, hạn mức cập nhật không được nhỏ hơn %sđ", utils.MoneyFormat(hubLimit)))
		} else {

			account.LimitAmount = input.LimitAmount
			afterOption := options.After
			response := model.HubDB.UpdateOne(
				bson.M{
					"code":       input.HubCode,
					"version_no": hub.VersionNo,
				},
				bson.M{
					"limit_assigned_amount": calculateHubAssignedAmount,
					"version_no":            uuid.NewString(),
				},
				&options.FindOneAndUpdateOptions{
					Upsert:         &enum.False,
					ReturnDocument: &afterOption,
				})

			if response.Status != common.APIStatus.Ok {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					ErrorCode: "UPDATE_HUB_ASSIGNED_ERROR",
					Message:   "Cập nhật hạn mức tài xế lên hub thất bại",
				}
			}
		}
	}

	updateAccount := model.AccountDB.UpdateOne(bson.M{
		"account_id": input.AccountID,
		"hub_code":   input.HubCode,
	}, account)
	if updateAccount.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "UPDATE_EMPLOYEE_ERROR",
			Message:   "Cập nhật nhân viên giao vận thất bại",
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật nhân viên giao vận thành công",
	}
}

func limitErrorResponse(message string) *common.APIResponse {

	return &common.APIResponse{
		Status:    common.APIStatus.Invalid,
		ErrorCode: "INVALID_AMOUNT",
		Message:   message,
		Data:      []interface{}{}}
}

// Sync tài khoản sang logistic: 12/1/2023
//	Đã sync role tài xế trung chuyển	  |		     LOGISTIC
//													/		\
//												   /         \
//				  								  /           \
//				  |					         Miền Bắc         Miền Nam
//	Chưa sync	  |								/   \          /   \
//				  |							   /     \        /     \
//                                            T.Bắc   Đ.Bắc  T.Nam  Đ.Nam
//                                             /  \         /   \
//    Đã sync     |                         HubA  HubB    HubC   HubD

func SyncAccountJob() {

	// Query lock avoid to multiple instance calling as same time
	isJobLockAvailable := utils.CheckJobLockAvailable(enum.SYNC_ACCOUNT_JOB, conf.Config.RepeatSyncAccountTimeJob)
	if !isJobLockAvailable {
		return
	}

	go GetTransportDriver()

	// Get LOGISTIC account
	logisticAccountQuery := model.AccountDB.QueryAll()
	mapAccount := make(map[string]*model.Account)
	if logisticAccountQuery.Status == common.APIStatus.Ok {
		logisticAccount := logisticAccountQuery.Data.([]*model.Account)
		for _, val := range logisticAccount {
			mapAccount[fmt.Sprintf("%s_%s", val.Username, val.HubCode)] = val
		}
	}

	// Get LOGISTIC department
	hubResponse := model.HubDB.Query(bson.M{"active": true}, 0, 5000, nil)
	var hubs []*model.Hub
	if hubResponse.Status == common.APIStatus.Ok {
		hubs = hubResponse.Data.([]*model.Hub)
	}

	// GET HRM account by LOGISTIC department
	var errorLogisticAccounts []*model.Account
	for _, department := range hubs {
		offset := 0
		limit := 200
		for {
			// Lấy danh sách employee từ HRM trạng thái ACTIVE/INACTIVE
			hrmEmployees, err := client.Services.HrmClient.GetAccountInfo(0, department.Code, offset, limit)
			if err != nil && err.Error() != common.APIStatus.NotFound {
				break
			}
			// Compare danh sách của LOGISTIC với danh sách của HRM
			for _, val := range hrmEmployees {
				key := fmt.Sprintf("%s_%s", val.Username, department.Code)

				// Tìm Department - Role của Account bên HRM
				hrmEmployeeRole, getRoleErr := client.Services.HrmClient.GetHrmEmployeeRoles(val.Username, department.Code)
				if getRoleErr != nil && getRoleErr.Error() != common.APIStatus.NotFound {
					errorLogisticAccounts = append(errorLogisticAccounts, val)
					continue
				}
				handleSyncDataBetweenHrmAndLogistic(mapAccount, key, hrmEmployeeRole, val, errorLogisticAccounts)
			}

			// Nếu là department cuối cùng và số lượng account lấy về < 100 hoặc không tìm thấy nữa
			if len(hrmEmployees) < limit || len(hrmEmployees) == 0 || err.Error() == common.APIStatus.NotFound {
				break
			}
			offset += limit
		}
	}

	// Những tài khoản bị remove phòng ban LOGISTIC không tìm thấy khi search theo username + department bên HRM
	for _, val := range mapAccount {
		val.Status = string(enum.AccountStatus.INACTIVE)
		updateAccountLogistic(val, errorLogisticAccounts)
	}

	// Tính toán lại hạn mức đã gắn của các Hub trong LOGISTIC
	for _, hub := range hubs {
		amount := CalculateHubAssignedLimitAmount(hub.Code)
		hubUpdateResponse := model.HubDB.UpdateOne(
			bson.M{"code": hub.Code},
			bson.M{"limit_assigned_amount": amount})
		if hubUpdateResponse.Status != common.APIStatus.Ok {
			_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
				CmdType: "UPDATE_HUB_LIMIT_ASSIGNED_AMOUNT_ERROR",
				Title:   "UPDATE HUB LIMIT ASSIGNED AMOUNT ERROR",
				Message: fmt.Sprintf("[SyncAccountJob] Không thể cập nhật hạn mức đã gắn của hub này %s với amount %f:", hub.Code, amount),
			})
		}
	}

	if len(errorLogisticAccounts) > 0 {
		_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
			CmdType: "LIST_SYNC_HRM_ACCOUNT_ERROR",
			Title:   "CANT SYNC THESE HRM ACCOUNT",
			Message: fmt.Sprintf("[SyncAccountJob] Không thể SYNC các nhân viên này: %v", &errorLogisticAccounts),
		})
	}
}

func handleSyncDataBetweenHrmAndLogistic(mapAccount map[string]*model.Account, key string, hrmEmployeeRole []*model.UserRole, val *model.Account, errorLogisticAccounts []*model.Account) {
	if account, exist := mapAccount[key]; !exist {
		// Nếu không tồn tại trong LOGISTIC và trạng thái ACTIVE thì tạo mới
		if len(hrmEmployeeRole) > 0 && val.Status == string(enum.AccountStatus.ACTIVE) {
			val.HubCode = *hrmEmployeeRole[0].DepartmentCode
			val.RoleCode = *hrmEmployeeRole[0].RoleCode
			createAccountLogistic(val, errorLogisticAccounts)
		}
	} else {
		account.Fullname = val.Fullname
		account.PhoneNumber = val.PhoneNumber
		account.Email = val.Email
		account.HashTag = val.HashTag

		// Nếu tồn tại mà trạng thái khác nhau
		if val.Status != account.Status {
			account.Status = val.Status
		}
		// Nếu role trong phòng ban đó thay đổi thì cập nhật lại role hiện tại
		if len(hrmEmployeeRole) > 0 && val.Status == string(enum.AccountStatus.ACTIVE) && *hrmEmployeeRole[0].RoleCode != account.RoleCode {
			account.RoleCode = *hrmEmployeeRole[0].RoleCode
		}
		delete(mapAccount, key)
		updateAccountLogistic(account, errorLogisticAccounts)
	}
}

func createAccountLogistic(account *model.Account, errorLogisticAccounts []*model.Account) []*model.Account {
	// Sync role tương ứng tài khoản này tại phòng ban này
	account.ReceivedAmount = CalculateHubShippingCodAmount(account.HubCode, account.AccountID, []string{string(enum.HubShippingOrderStatus.DELIVERED)})
	response := model.AccountDB.Create(account)
	if response.Status != common.APIStatus.Ok {
		errorLogisticAccounts = append(errorLogisticAccounts, account)
	}
	return errorLogisticAccounts
}

func updateAccountLogistic(account *model.Account, errorLogisticAccounts []*model.Account) []*model.Account {
	// Trừ hạn mức đã gắn của tài xế ra khỏi Hub này
	// Nếu role cập nhật mới cho user tại HUB hiện tại không nằm trong bộ role của tài xế thì trừ hạn mức ra
	_, isRiderRole := conf.Config.RiderRoleToSyncReceived[account.RoleCode]
	limitAmount := account.LimitAmount
	if limitAmount > 0 && account.Status == string(enum.AccountStatus.INACTIVE) || !isRiderRole {
		limitAmount = 0
	}

	receivedAmount := float64(0)
	if isRiderRole {
		receivedAmount = CalculateHubShippingCodAmount(account.HubCode, account.AccountID, []string{string(enum.HubShippingOrderStatus.DELIVERED)})
	}
	response := model.AccountDB.UpdateOne(bson.M{
		"username": account.Username,
		"hub_code": account.HubCode,
	}, bson.M{
		"status":          account.Status,
		"limit_amount":    limitAmount,
		"received_amount": receivedAmount,
		"role_code":       account.RoleCode,
		"fullname":        account.Fullname,
		"phone_number":    account.PhoneNumber,
		"email":           account.Email,
		"hash_tag":        account.HashTag,
	})
	if response.Status != common.APIStatus.Ok {
		errorLogisticAccounts = append(errorLogisticAccounts, account)
	}

	return errorLogisticAccounts
}

func increOneHubByHubCodeAndFieldName(hubCode string, fieldName string, limitAmount int) *common.APIResponse {

	hubUpdate := model.HubDB.IncreOne(
		bson.M{
			"code": hubCode,
		},
		fieldName,
		limitAmount,
	)

	return hubUpdate
}

func MigrateHubReceivedAmount(testHubCode []string, needUpdate bool) *common.APIResponse {
	type DataResponse struct {
		ReceivedAmount float64 `json:"receivedAmount"`
		HubCode        string  `json:"hubCode"`
		Status         bool    `json:"status"`
	}

	var response *common.APIResponse

	if len(testHubCode) > 0 {
		response = model.HubDB.Query(bson.M{
			"code": bson.M{
				"$in": testHubCode,
			},
		}, 0, 0, nil)
	} else {
		response = model.HubDB.Query(bson.M{"active": true}, 0, 0, nil)
	}

	var dataResponse []*DataResponse
	if response.Status == common.APIStatus.Ok {
		hubs := response.Data.([]*model.Hub)

		if needUpdate {
			for _, val := range hubs {
				receivedAmount := CalculateHubShippingCodAmount(val.Code, 0, []string{string(enum.HubShippingOrderStatus.DELIVERED), string(enum.HubShippingOrderStatus.COD_COLLECTED)})
				updateResponse := model.HubDB.UpdateOne(bson.M{"code": val.Code}, bson.M{"total_received_amount": receivedAmount, "version_no": uuid.NewString()})

				valResponse := &DataResponse{
					ReceivedAmount: receivedAmount,
					HubCode:        val.Code,
					Status:         true,
				}
				if updateResponse.Status != common.APIStatus.Ok {
					valResponse.Status = false
				}
				dataResponse = append(dataResponse, valResponse)
			}
		} else {
			for _, val := range hubs {
				receivedAmount := CalculateHubShippingCodAmount(val.Code, 0, []string{string(enum.HubShippingOrderStatus.DELIVERED), string(enum.HubShippingOrderStatus.COD_COLLECTED)})
				valResponse := &DataResponse{
					ReceivedAmount: receivedAmount,
					HubCode:        val.Code,
					Status:         true,
				}
				dataResponse = append(dataResponse, valResponse)
			}
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Data:    []interface{}{dataResponse},
		Message: "Successfully generated",
	}
}

func getSmallestHubLimitAmount(limitAmountByWeekDay map[string]float64, smallestAmount float64) float64 {
	if len(limitAmountByWeekDay) > 0 {
		for _, limitAmount := range limitAmountByWeekDay {
			if limitAmount < smallestAmount {
				smallestAmount = limitAmount
			}
		}
	}
	return smallestAmount
}

func GetMyDepartments(userInfo model.ActionSource) *common.APIResponse {
	departments, err := client.Services.HrmClient.GetAllUserDepartments(userInfo.Account.Username)
	if err != nil || departments == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: err.Error(),
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Lấy thông tin phòng ban thành công",
		Data:    departments,
	}
}

func GetTransportDriver() {
	storedAccountRaw := model.AccountDB.Distinct(bson.M{
		"role_code": conf.Config.TransportingDriverRole,
	}, "account_id", nil)

	// If account change their role set this value to false
	storedAccountMap := map[int64]bool{}
	if storedAccountRaw.Status == common.APIStatus.Ok {
		storedAccountBytes, err := json.Marshal(storedAccountRaw.Data)
		if err != nil {
		}
		var storedAccounts []int64
		_ = json.Unmarshal(storedAccountBytes, &storedAccounts)

		for _, a := range storedAccounts {
			storedAccountMap[a] = true
		}
	}

	offset := 0
	limit := 100
	for {
		accounts, err := client.Services.HrmClient.GetAccountsByRole(conf.Config.TransportingDriverRole, offset, limit)
		if err != nil {
			break
		}

		for _, account := range accounts {
			storedAccountMap[account.AccountID] = false
			_ = model.AccountDB.Upsert(
				bson.M{
					"account_id": account.AccountID,
					"role_code":  conf.Config.TransportingDriverRole,
				},
				account)
		}

		offset = offset + limit
	}

	// NOTE: Ở mỗi phòng ban trực thuộc phòng ban LOGISTIC sẽ tạo 1 tài khoản mới,
	// với tài khoản có role là tài xế thì hiện tại CHỈ TỒN TẠI ở phòng ban LOGISTIC
	// nên nếu tài khoản không còn là tài xế trung chuyển thì xóa tài khoản tài xế
	// do hàm này chỉ sync tài khoản tài xế nên việc xóa sẽ không ảnh hưởng đến account ở phòng ban khác
	for accountId, isChangeRole := range storedAccountMap {
		if isChangeRole {
			_ = model.AccountDB.UpdateOne(bson.M{
				"account_id": accountId,
				"role_code":  conf.Config.TransportingDriverRole,
			}, bson.M{
				"status": "INACTIVE",
			})
		}
	}
}

func CountAccountByStatus(query *request.Account) *common.APIResponse {
	type CountAccountByStatusModel struct {
		Status   string `json:"status,omitempty" bson:"_id,omitempty"`
		Quantity int64  `json:"quantity" bson:"total,omitempty"`
	}

	filter := bson.D{}

	if query.AccountID != 0 {
		filter = append(filter, bson.E{
			Key:   "account_id",
			Value: query.AccountID,
		})
	}

	if query.HubCode != "" {
		filter = append(filter, bson.E{
			Key:   "hub_code",
			Value: query.HubCode,
		})
	}

	if query.RoleCode != "" {
		filter = append(filter, bson.E{
			Key:   "role_code",
			Value: query.RoleCode,
		})
	}

	if len(query.RoleCodes) > 0 {
		filter = append(filter, bson.E{
			Key: "role_code",
			Value: bson.M{
				"$in": query.RoleCodes,
			},
		})
	}

	statuses := utils.EnumToStringSlice(enum.AccountStatus)
	if query.Status != "" {
		statuses = []string{string(query.Status)}
	}

	r := make([]*CountAccountByStatusModel, len(statuses))
	wg := new(sync.WaitGroup)
	wg.Add(len(statuses))

	for i, s := range statuses {
		go func(index int, status string, result []*CountAccountByStatusModel, waitGroup *sync.WaitGroup) {
			defer waitGroup.Done()
			copyFilter := make(bson.D, len(filter))
			copy(copyFilter, filter)
			copyFilter = append(copyFilter, bson.E{
				Key:   "status",
				Value: status,
			})
			countResult := model.AccountDB.Count(copyFilter)
			result[index] = &CountAccountByStatusModel{
				Status:   status,
				Quantity: countResult.Total,
			}
		}(i, s, r, wg)
	}

	wg.Wait()

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Thành công lấy danh sách tài khoản theo trạng thái",
		Data:    r,
	}
}

func GetProductivity(input *request.HubShippingOrderQuery) *common.APIResponse {
	var fromTime, toTime time.Time
	now := time.Now()

	if input.FromTime == 0 {
		fromTime = now.AddDate(0, 0, 1-now.Day())
	} else {
		fromTime = time.Unix(input.FromTime, 0)
	}

	if input.ToTime == 0 {
		if input.FromTime == 0 { // DEFAULT 1 MONTH
			y, m, _ := now.Date()
			toTime = time.Date(y, m+1, 0, 0, 0, 0, 0, now.Location())
		} else { // TODAY
			toTime = now
		}
	} else {
		toTime = time.Unix(input.ToTime, 0)
	}
	toYear, toMonth, d := toTime.Date()
	result := time.Date(toYear, toMonth, d, 0, 0, 0, 0, now.Location())

	type Response struct {
		SuccessDelivery int64   `json:"successDelivery"`
		TotalDelivery   int64   `json:"totalDelivery"`
		RateDelivery    float64 `json:"rateDelivery"`
		SuccessPickup   int64   `json:"successPickup"`
		TotalPickup     int64   `json:"totalPickup"`
		RatePickup      float64 `json:"ratePickup"`
		RateOverall     float64 `json:"rateOverall"`
		FromDate        int64   `json:"fromDate"`
		ToDate          int64   `json:"toDate"`
	}

	response := Response{
		SuccessDelivery: 0,
		TotalDelivery:   0,
		RateDelivery:    0.0,
		SuccessPickup:   0,
		TotalPickup:     0,
		RatePickup:      0.0,
		RateOverall:     0.0,
		FromDate:        fromTime.Unix(),
		ToDate:          result.Unix(),
	}

	var listHubOrderTypes []request.HubOrderType
	if len(input.HubOrderTypes) == 0 {
		listHubOrderTypes = listHubShippingOrderTypes()
	} else {
		listHubOrderTypes = input.HubOrderTypes
	}

	if input.ProductivityQuery == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "INVALID_PRODUCTIVITY_QUERY",
		}
	}
	hubOrderType := []string{}
	hubOrderSubType := []string{}
	for _, hoType := range listHubOrderTypes {
		if hoType.Type != "" {
			hubOrderType = append(hubOrderType, string(hoType.Type))
		}
		if hoType.SubType != "" {
			hubOrderSubType = append(hubOrderSubType, string(hoType.SubType))
		}
	}

	if *input.ProductivityQuery == enum.ProductivityQuery.DAILY {
		response.TotalDelivery = model.ShippingOrderDB.Count(bson.M{
			"driver_activities": bson.M{
				"$elemMatch": bson.M{
					"driver_id": input.DriverId,
					"hub_code":  input.HubCode,
					"hub_order_type": bson.M{
						"$in": hubOrderType,
					},
					"productivity_action": enum.ProductivityAction.ASSIGN_DELIVERY,
					"action_time": bson.M{
						"$lte": toTime.Unix(),
					},
					"$or": []bson.M{
						{
							"complete_assign_at": bson.M{
								"$gte": fromTime.Unix(),
							},
						},
						{
							"complete_assign_at": bson.M{
								"$exists": false,
							},
						},
					},
				},
			},
		}).Total
		response.SuccessDelivery = model.ShippingOrderDB.Count(bson.M{
			"driver_activities": bson.M{
				"$elemMatch": bson.M{
					"driver_id":           input.DriverId,
					"hub_code":            input.HubCode,
					"productivity_action": enum.ProductivityAction.DELIVERED,
					"hub_order_type": bson.M{
						"$in": hubOrderType,
					},
					"action_time": bson.M{
						"$gte": fromTime.Unix(),
						"$lte": toTime.Unix(),
					},
				},
			},
		}).Total
		response.TotalPickup = model.ShippingOrderDB.Count(bson.M{
			"driver_activities": bson.M{
				"$elemMatch": bson.M{
					"driver_id": input.DriverId,
					"hub_code":  input.HubCode,
					"hub_order_type": bson.M{
						"$in": hubOrderType,
					},
					"hub_order_sub_type": bson.M{
						"$in": hubOrderSubType,
					},
					"productivity_action": enum.ProductivityAction.ASSIGN_PICK,
					"action_time": bson.M{
						"$lte": toTime.Unix(),
					},
					"$or": []bson.M{
						{
							"complete_assign_at": bson.M{
								"$gte": fromTime.Unix(),
							},
						},
						{
							"complete_assign_at": bson.M{
								"$exists": false,
							},
						},
					},
				},
			},
		}).Total
		response.SuccessPickup = model.ShippingOrderDB.Count(bson.M{
			"driver_activities": bson.M{
				"$elemMatch": bson.M{
					"driver_id":           input.DriverId,
					"hub_code":            input.HubCode,
					"productivity_action": enum.ProductivityAction.PICKED,
					"hub_order_type": bson.M{
						"$in": hubOrderType,
					},
					"hub_order_sub_type": bson.M{
						"$in": hubOrderSubType,
					},
					"action_time": bson.M{
						"$gte": fromTime.Unix(),
						"$lte": toTime.Unix(),
					},
				},
			},
		}).Total
	}

	if *input.ProductivityQuery == enum.ProductivityQuery.RANGE {
		response.TotalDelivery = model.ShippingOrderDB.Count(bson.M{
			"driver_activities": bson.M{
				"$elemMatch": bson.M{
					"driver_id": input.DriverId,
					"hub_code":  input.HubCode,
					"hub_order_type": bson.M{
						"$in": hubOrderType,
					},
					"productivity_action": enum.ProductivityAction.ASSIGN_DELIVERY,
					"action_time": bson.M{
						"$gte": fromTime.Unix(),
						"$lte": toTime.Unix(),
					},
				},
			},
		}).Total
		response.SuccessDelivery = model.ShippingOrderDB.Count(bson.M{
			"driver_activities": bson.M{
				"$elemMatch": bson.M{
					"driver_id":           input.DriverId,
					"hub_code":            input.HubCode,
					"productivity_action": enum.ProductivityAction.DELIVERED,
					"hub_order_type": bson.M{
						"$in": hubOrderType,
					},
					"action_time": bson.M{
						"$gte": fromTime.Unix(),
						"$lte": toTime.Unix(),
					},
				},
			},
		}).Total
		response.TotalPickup = model.ShippingOrderDB.Count(bson.M{
			"driver_activities": bson.M{
				"$elemMatch": bson.M{
					"driver_id": input.DriverId,
					"hub_code":  input.HubCode,
					"hub_order_type": bson.M{
						"$in": hubOrderType,
					},
					"hub_order_sub_type": bson.M{
						"$in": hubOrderSubType,
					},
					"productivity_action": enum.ProductivityAction.ASSIGN_PICK,
					"action_time": bson.M{
						"$gte": fromTime.Unix(),
						"$lte": toTime.Unix(),
					},
				},
			},
		}).Total
		response.SuccessPickup = model.ShippingOrderDB.Count(bson.M{
			"driver_activities": bson.M{
				"$elemMatch": bson.M{
					"driver_id":           input.DriverId,
					"hub_code":            input.HubCode,
					"productivity_action": enum.ProductivityAction.PICKED,
					"hub_order_type": bson.M{
						"$in": hubOrderType,
					},
					"hub_order_sub_type": bson.M{
						"$in": hubOrderSubType,
					},
					"action_time": bson.M{
						"$gte": fromTime.Unix(),
						"$lte": toTime.Unix(),
					},
				},
			},
		}).Total
	}

	if response.TotalPickup+response.TotalDelivery != 0 {
		if response.TotalDelivery != 0 {
			response.RateDelivery = float64(response.SuccessDelivery) / float64(response.TotalDelivery) * 100
			response.RateDelivery = math.Round(response.RateDelivery*100) / 100
		}
		if response.TotalPickup != 0 {
			response.RatePickup = float64(response.SuccessPickup) / float64(response.TotalPickup) * 100
			response.RatePickup = math.Round(response.RatePickup*100) / 100
		}
		response.RateOverall = float64(response.SuccessDelivery+response.SuccessPickup) / float64(response.TotalDelivery+response.TotalPickup) * 100
		response.RateOverall = math.Round(response.RateOverall*100) / 100
	}

	return &common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   []interface{}{&response},
	}
}

func CrawlProductivity() *common.APIResponse {
	filter := bson.M{}
	now := time.Now()
	bound := now.AddDate(0, 1-int(now.Month()), 1-now.Day())

	// list hub order types required
	listHubOrderTypes := listHubShippingOrderTypes()
	isOutbound := false

	for {
		hubOrderRaw := model.HUBShippingOrderDB.Query(filter, 0, 1000, &bson.M{"_id": -1})
		if hubOrderRaw.Status != common.APIStatus.Ok {
			break
		}

		if isOutbound {
			break
		}

		hubShippingOrders := hubOrderRaw.Data.([]*model.HubShippingOrder)
		smallestId := hubShippingOrders[len(hubShippingOrders)-1].ID
		filter["_id"] = bson.M{
			"$lt": smallestId,
		}

		// [hubCode][driverId][year][month][day]
		listData := map[string]map[int64]map[int]map[time.Month]map[int][]*model.HubShippingOrder{}

		for _, hubOrder := range hubShippingOrders {
			if bound.Unix() > hubOrder.ID.Timestamp().Unix() {
				isOutbound = true
				break
			}

			if !ContainsHubOrderTypes(listHubOrderTypes, hubOrder.Type, hubOrder.SubType) {
				continue
			}

			if MappingDriverProductivity(listData, hubOrder, bound) {
				continue
			}

			if hubOrder.DriverID == 0 {
				continue
			}

			if hubOrder.SubType != nil &&
				*hubOrder.SubType == enum.SubType.FMPO &&
				hubOrder.Action != "PICKING" {
				continue
			}

			MappingData(listData, hubOrder)
		}

		UpdateCrawlData(listData, listHubOrderTypes)
	}

	return &common.APIResponse{
		Status: common.APIStatus.Ok,
	}
}

func MigratePickedNDeliveredLog() *common.APIResponse {
	filter := bson.M{}
	// list hub order types required
	listHubOrderTypes := listHubShippingOrderTypes()
	successPickupEnums, pickupEnums, successDeliveryEnums, deliveringEnums := listStatusProductivity()

	for {
		hubOrderRaw := model.HUBShippingOrderDB.Query(filter, 0, 1000, &bson.M{"_id": -1})
		if hubOrderRaw.Status != common.APIStatus.Ok {
			break
		}

		hubShippingOrders := hubOrderRaw.Data.([]*model.HubShippingOrder)
		smallestId := hubShippingOrders[len(hubShippingOrders)-1].ID
		filter["_id"] = bson.M{
			"$lt": smallestId,
		}
		for _, hubOrder := range hubShippingOrders {
			if !ContainsHubOrderTypes(listHubOrderTypes, hubOrder.Type, hubOrder.SubType) {
				continue
			}

			if hubOrder.DriverID == 0 {
				continue
			}

			if len(hubOrder.Logs) == 0 {
				hubOrder.Logs = []model.HubShippingOrder{}
			}

			isExistsLog := false
			needUpdate := false

			if *hubOrder.Type != enum.HubOrderType.PICKUP {
				if ContainsStatus(successDeliveryEnums, *hubOrder.Status) {
					missingAssignDriverLog := true
					missingDeliveredLog := true
					if len(hubOrder.Logs) > 0 {
						for _, log := range hubOrder.Logs {
							if log.ProductivityAction == nil {
								continue
							}

							if *log.ProductivityAction == enum.ProductivityAction.ASSIGN_DELIVERY {
								missingAssignDriverLog = false
							}

							if *log.ProductivityAction == enum.ProductivityAction.DELIVERED {
								missingDeliveredLog = false
							}
						}
					}
					if missingAssignDriverLog {
						hubOrder.Logs = append(hubOrder.Logs, model.HubShippingOrder{
							Status:             &enum.HubShippingOrderStatus.WAIT_TO_DELIVERY,
							DriverID:           hubOrder.DriverID,
							ProductivityAction: &enum.ProductivityAction.ASSIGN_DELIVERY,
							ActionTime:         hubOrder.ActionTime,
							Type:               hubOrder.Type,
							SubType:            hubOrder.SubType,
						})
					}
					if missingDeliveredLog {
						hubOrder.Logs = append(hubOrder.Logs, model.HubShippingOrder{
							Status:             &enum.HubShippingOrderStatus.DELIVERED,
							DriverID:           hubOrder.DriverID,
							ProductivityAction: &enum.ProductivityAction.DELIVERED,
							ActionTime:         hubOrder.ActionTime,
							Type:               hubOrder.Type,
							SubType:            hubOrder.SubType,
						})
					}
					if missingAssignDriverLog || missingDeliveredLog {
						needUpdate = true
					}

				} else if ContainsStatus(deliveringEnums, *hubOrder.Status) {
					for _, log := range hubOrder.Logs {
						if log.ProductivityAction != nil &&
							(*log.ProductivityAction == enum.ProductivityAction.ASSIGN_DELIVERY) {
							isExistsLog = true
							break
						}
					}
					if isExistsLog {
						continue
					}
					hubOrder.Logs = append(hubOrder.Logs, model.HubShippingOrder{
						Status:             &enum.HubShippingOrderStatus.WAIT_TO_DELIVERY,
						DriverID:           hubOrder.DriverID,
						ProductivityAction: &enum.ProductivityAction.ASSIGN_DELIVERY,
						ActionTime:         hubOrder.ActionTime,
						Type:               hubOrder.Type,
						SubType:            hubOrder.SubType,
					})
					needUpdate = true
				}
			} else {
				if ContainsStatus(successPickupEnums, *hubOrder.Status) {
					counter := 0
					if len(hubOrder.Logs) > 0 {
						for _, log := range hubOrder.Logs {
							if log.ProductivityAction != nil &&
								(*log.ProductivityAction == enum.ProductivityAction.ASSIGN_PICK ||
									*log.ProductivityAction == enum.ProductivityAction.PICKED) {
								counter++
							}
						}
					}
					if counter >= 2 {
						continue
					}

					hubOrder.Logs = append(hubOrder.Logs, model.HubShippingOrder{
						Status:             &enum.HubShippingOrderStatus.READY_TO_PICK,
						DriverID:           hubOrder.DriverID,
						ProductivityAction: &enum.ProductivityAction.ASSIGN_PICK,
						ActionTime:         hubOrder.ActionTime,
						Type:               hubOrder.Type,
						SubType:            hubOrder.SubType,
					})

					hubOrder.Logs = append(hubOrder.Logs, model.HubShippingOrder{
						Status:             &enum.HubShippingOrderStatus.WAIT_TO_STORING,
						DriverID:           hubOrder.DriverID,
						ProductivityAction: &enum.ProductivityAction.PICKED,
						ActionTime:         hubOrder.ActionTime,
						Type:               hubOrder.Type,
						SubType:            hubOrder.SubType,
					})
					needUpdate = true

				} else if ContainsStatus(pickupEnums, *hubOrder.Status) {
					for _, log := range hubOrder.Logs {
						if log.ProductivityAction != nil &&
							(*log.ProductivityAction == enum.ProductivityAction.ASSIGN_PICK) {
							isExistsLog = true
							break
						}
					}
					if isExistsLog {
						continue
					}
					hubOrder.Logs = append(hubOrder.Logs, model.HubShippingOrder{
						Status:             &enum.HubShippingOrderStatus.READY_TO_PICK,
						DriverID:           hubOrder.DriverID,
						ProductivityAction: &enum.ProductivityAction.ASSIGN_PICK,
						ActionTime:         hubOrder.ActionTime,
						Type:               hubOrder.Type,
						SubType:            hubOrder.SubType,
					})
					needUpdate = true
				}
			}

			if needUpdate {
				model.HUBShippingOrderDB.UpdateOne(bson.M{
					"_id": hubOrder.ID,
				}, bson.M{
					"logs": hubOrder.Logs,
				})
			}
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}
}

func GetHubsOfDrivers(query *request.Account, offset int64, limit int64, getTotal bool) *common.APIResponse {
	filter := bson.M{}
	hubs := []string{}

	hubRaw := model.HubDB.Distinct(&bson.M{}, "code", nil)
	if hubRaw.Status != common.APIStatus.Ok {
		fmt.Println(hubRaw)
	} else {
		if hubsByte, err := json.Marshal(hubRaw.Data); err == nil {
			_ = json.Unmarshal(hubsByte, &hubs)
		}
	}

	if query.AccountID != 0 {
		filter["account_id"] = query.AccountID
	}
	if query.HubCode != "" {
		filter["hub_code"] = query.HubCode
	} else if len(hubs) != 0 {
		filter["hub_code"] = bson.M{
			"$in": hubs,
		}
	}
	if query.HashTag != "" {
		filter["hash_tag"] = bson.M{
			"$regex": ".*" + utils.NormalizeString(query.HashTag) + ".*",
		}
	}

	filter["role_code"] = bson.M{
		"$in": []string{"WMS_SHIPPER", "WMS_HUB_COORDINATOR", "LOGISTIC_TRANSPORT_DRIVER"},
	}

	matchStage := bson.D{{"$match", filter}}
	skipStage := bson.D{{"$skip", offset}}
	limitStage := bson.D{{"$limit", limit}}

	groupStage := bson.D{{
		"$group", bson.D{
			{"_id", bson.D{
				bson.E{"account_id", "$account_id"},
				bson.E{"fullname", "$fullname"},
				bson.E{"username", "$username"}},
			},
			{"hub_codes", bson.D{
				{"$push", bson.D{
					bson.E{"hub_code", "$hub_code"},
					bson.E{"status", "$status"},
				}}},
			},
			{"statuses", bson.D{
				{"$addToSet", "$status"}},
			},
		},
	}}

	statusStage := bson.D{}
	if query.Status != "" {
		if query.Status == string(enum.AccountStatus.ACTIVE) {
			statusStage = bson.D{{"$match", &bson.M{
				"statuses": bson.M{
					"$eq": string(enum.AccountStatus.ACTIVE),
				},
			}}}
		}
		if query.Status == string(enum.AccountStatus.INACTIVE) {
			statusStage = bson.D{{"$match", &bson.M{
				"statuses": bson.M{
					"$ne": string(enum.AccountStatus.ACTIVE),
				},
			}}}
		}
	}

	var err *common.APIResponse

	var total int64 = 0
	if getTotal {

		countStage := bson.D{
			{"$count", "total"},
		}

		type getTotal struct {
			Total int `json:"total,omitempty" bson:"total,omitempty"`
		}
		var r []getTotal

		if query.Status != "" {
			err = model.AccountDB.Aggregate(mongo.Pipeline{matchStage, groupStage, statusStage, countStage}, &r)
		} else {
			err = model.AccountDB.Aggregate(mongo.Pipeline{matchStage, groupStage, countStage}, &r)
		}
		if err.Status != common.APIStatus.Ok {
			return err
		} else {
			if len(r) != 0 {
				total = int64(r[0].Total)
			}
		}

	}

	type HubStatus struct {
		HubCode string `json:"hubCode,omitempty" bson:"hub_code,omitempty"`
		Status  string `json:"status,omitempty" bson:"status,omitempty"`
	}

	type listHubOfDriver struct {
		Id       *model.Account `json:"_id,omitempty" bson:"_id,omitempty"`
		Statuses []string       `json:"statuses,omitempty" bson:"statuses,omitempty"`
		HubCodes []HubStatus    `json:"hubCodes,omitempty" bson:"hub_codes,omitempty"`
	}

	var data []listHubOfDriver
	if query.Status != "" {
		err = model.AccountDB.Aggregate(mongo.Pipeline{matchStage, groupStage, statusStage, skipStage, limitStage}, &data)

	} else {
		err = model.AccountDB.Aggregate(mongo.Pipeline{matchStage, groupStage, skipStage, limitStage}, &data)
	}

	if err.Status != common.APIStatus.Ok {
		return err
	}

	type Response struct {
		AccountID int64       `json:"accountId,omitempty"`
		Fullname  string      `json:"fullname,omitempty"`
		Username  string      `json:"username,omitempty"`
		IsActive  string      `json:"status,omitempty"`
		HubCodes  []HubStatus `json:"hubCodes,omitempty"`
	}

	response := []Response{}

	for _, driver := range data {
		status := ""
		if len(driver.Statuses) == 2 {
			status = string(enum.AccountStatus.ACTIVE)
		} else {
			status = driver.Statuses[0]
		}

		response = append(response, Response{
			AccountID: driver.Id.AccountID,
			Fullname:  driver.Id.Fullname,
			Username:  driver.Id.Username,
			IsActive:  status,
			HubCodes:  driver.HubCodes,
		})
	}

	r := common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   response,
	}

	if getTotal {
		r.Total = total
	}

	return &r
}

func DistinctCountByStatus(query *request.Account) *common.APIResponse {
	type CountAccountByStatusModel struct {
		Status   string `json:"status,omitempty" bson:"_id,omitempty"`
		Quantity int64  `json:"quantity" bson:"total,omitempty"`
	}

	filter := bson.M{}
	hubs := []string{}

	hubRaw := model.HubDB.Distinct(&bson.M{}, "code", nil)
	if hubRaw.Status != common.APIStatus.Ok {
		fmt.Println(hubRaw)
	} else {
		if hubsByte, err := json.Marshal(hubRaw.Data); err == nil {
			_ = json.Unmarshal(hubsByte, &hubs)
		}
	}

	if query.AccountID != 0 {
		filter["account_id"] = query.AccountID
	}

	if query.HubCode != "" {
		filter["hub_code"] = query.HubCode
	} else if len(hubs) != 0 {
		filter["hub_code"] = bson.M{
			"$in": hubs,
		}
	}

	if query.HashTag != "" {
		filter["hash_tag"] = bson.M{
			"$regex": ".*" + utils.NormalizeString(query.HashTag) + ".*",
		}
	}

	filter["role_code"] = bson.M{
		"$in": []string{"WMS_SHIPPER", "WMS_HUB_COORDINATOR", "LOGISTIC_TRANSPORT_DRIVER"},
	}

	matchStage := bson.D{{"$match", filter}}

	groupStage := bson.D{{
		"$group", bson.D{
			{"_id", "$status"},
			{"accounts", bson.D{
				{"$addToSet", bson.D{
					bson.E{"account_id", "$account_id"},
					bson.E{"username", "$username"},
					bson.E{"fullname", "$fullname"}}},
			}},
		},
	}}

	type AccountPerStatus struct {
		Status   string          `json:"_id,omitempty" bson:"_id,omitempty"`
		Accounts []model.Account `json:"accounts,omitempty" bson:"accounts,omitempty"`
	}

	var result []AccountPerStatus
	model.AccountDB.Aggregate(mongo.Pipeline{
		matchStage,
		groupStage,
	}, &result)

	listStatus := make(map[string][]model.Account)
	for _, s := range result {
		listStatus[s.Status] = s.Accounts
	}

	response := []CountAccountByStatusModel{}

	isActive := make(map[model.Account]bool)
	for _, account := range listStatus[string(enum.AccountStatus.ACTIVE)] {
		isActive[account] = true
	}

	statuses := utils.EnumToStringSlice(enum.AccountStatus)

	for _, status := range statuses {
		if status == string(enum.AccountStatus.ACTIVE) {
			response = append(response, CountAccountByStatusModel{
				Status:   status,
				Quantity: int64(len(isActive)),
			})

		} else if status == string(enum.AccountStatus.INACTIVE) {
			var countInactivate int64 = 0

			for _, account := range listStatus[status] {
				if _, ok := isActive[account]; !ok {
					countInactivate += 1
				}
			}

			response = append(response, CountAccountByStatusModel{
				Status:   status,
				Quantity: countInactivate,
			})
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Thành công lấy danh sách tài khoản theo trạng thái",
		Data:    response,
	}
}

func UpdateAccountsLimit(hubCode string, accountIds []int64, limitAmount float64) *common.APIResponse {
	if len(accountIds) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Không được để trống danh sách nhân viên",
		}
	}

	if limitAmount < 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Không cập nhật hạn mức âm",
		}
	}

	getHub := model.HubDB.QueryOne(bson.M{"code": hubCode})
	if getHub.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			ErrorCode: "NOT_FOUND_HUB",
			Message:   "Không tìm thấy HUB cần cập nhật",
		}
	}

	hub := getHub.Data.([]*model.Hub)[0]

	if !*hub.Active {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "HUB_INACTIVE",
			Message:   "Không được phép cập nhật Hub không hoạt động",
		}
	}

	if hub.LimitStatus != "ACTIVE" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "HUB_TURNOFF_LIMIT",
			Message:   "Không được phép cập nhật Hub chưa thiết lập hạn mức",
		}
	}

	filter := bson.M{}
	filter["hub_code"] = hubCode
	filter["account_id"] = bson.M{
		"$in": accountIds,
	}

	getAccounts := model.AccountDB.Query(filter, 0, 1000, nil)
	if getAccounts.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			ErrorCode: "NOT_FOUND_EMPLOYEE",
			Message:   "Không tìm thấy nhân viên cần cập nhật",
		}
	}

	accounts := getAccounts.Data.([]*model.Account)
	hubLimit := getSmallestHubLimitAmount(hub.LimitAmountByWeekday, hub.LimitAmountByWeekday[time.Now().Weekday().String()])
	hubTotalAssignedAmount := hub.LimitAssignedAmount

	totalLimit := limitAmount * float64(len(accountIds))
	totalAcountsLimit := 0.0

	for _, account := range accounts {
		if account.Status != string(enum.AccountStatus.ACTIVE) {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				ErrorCode: "EMPLOYEE_INACTIVE",
				Message:   "Không được phép cập nhật hạn mức cho nhân viên không hoạt động",
			}
		}
		totalAcountsLimit += account.LimitAmount
	}

	// hạn mức cập nhật * số nhân viên > số tiền hạn mức còn lại
	if totalLimit-totalAcountsLimit > hubLimit-hubTotalAssignedAmount {
		return limitErrorResponse(fmt.Sprintf("Rất tiếc, vượt quá hạn mức còn có thể cập nhật %sđ", utils.MoneyFormat(hubLimit-hubTotalAssignedAmount)))
	} else {
		calculateHubAssignedAmount := hubTotalAssignedAmount + totalLimit - totalAcountsLimit
		updateHub := model.HubDB.UpdateOne(
			bson.M{
				"code":       hubCode,
				"version_no": hub.VersionNo,
			},
			bson.M{
				"limit_assigned_amount": calculateHubAssignedAmount,
				"version_no":            uuid.NewString(),
			})
		if updateHub.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Cập nhật hạn mức tài xế lên hub thất bại",
			}
		}

		updateAccount := model.AccountDB.UpdateMany(bson.M{
			"hub_code": hubCode,
			"account_id": bson.M{
				"$in": accountIds,
			},
		}, bson.M{
			"limit_amount": limitAmount,
		})
		if updateAccount.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Cập nhật hạn mức cho nhân viên thất bại",
			}
		}
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật nhân viên giao vận thành công",
	}
}
