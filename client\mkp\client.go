package mkp

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/golang/configuration"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathGetListMkpAccount = "/marketplace/customer/v1/account/list"
)

type MkpClient struct {
	svc     *client.RestClient
	headers map[string]string
}

func NewServiceClient(serviceConfig configuration.Service, logName string, session *mongo.Database) *MkpClient {
	if serviceConfig.Host == "" {
		return nil
	}
	mkpClient := &MkpClient{
		svc: client.NewRESTClient(
			serviceConfig.Host,
			logName,
			300*time.Second,
			5,
			3*time.Second,
		),
		headers: map[string]string{
			"Authorization": serviceConfig.Authorization,
		},
	}
	mkpClient.svc.SetDBLog(session)
	return mkpClient
}
