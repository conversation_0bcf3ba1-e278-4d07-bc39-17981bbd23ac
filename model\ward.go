package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Ward struct {
	CreatedTime     *time.Time `json:"createdTime,omitempty"`
	LastUpdatedTime *time.Time `json:"lastUpdatedTime,omitempty"`

	Code         string `json:"code"`
	Name         string `json:"name"`
	Level        string `json:"level"`
	IsSupport    bool   `json:"isSupport"`
	ProvinceCode string `json:"provinceCode"`
	ProvinceName string `json:"provinceName"`
	DistrictCode string `json:"districtCode"`
	DistrictName string `json:"districtName"`

	Dictionary []string `json:"dictionary"`
}

type WardMapping struct {
	Code         string   `json:"code" bson:"code,omitempty"`
	Name         string   `json:"name" bson:"name,omitempty"`
	DistrictCode string   `json:"districtCode" bson:"district_code,omitempty"`
	VTPId        int64    `json:"vtpId" bson:"vtp_id,omitempty"`
	NhatTinId    int64    `json:"nhattinId" bson:"nhattin_id,omitempty"`
	VNPId        string   `json:"vnpId" bson:"vnp_id,omitempty"`
	GHNId        string   `json:"ghnId" bson:"ghn_id,omitempty"`
	Dictionary   []string `json:"dictionary" bson:"dictionary,omitempty"`
}

type WardGHN struct {
	WardCode      string   `json:"WardCode"`
	DistrictId    int64    `json:"DistrictId"`
	WardName      string   `json:"WardName"`
	NameExtension []string `json:"NameExtension"`
}

type WardNhatTin struct {
	Id           int64  `json:"id"`
	WardName     string `json:"ward_name"`
	DistrictName string `json:"district_name"`
}

type WardVTP struct {
	WardId     int64  `json:"WARDS_ID"`
	WardName   string `json:"WARDS_NAME"`
	DistrictId int64  `json:"DISTRICT_ID"`
}

type WardVNP struct {
	Code         string `json:"MaPhuongXa"`
	Name         string `json:"TenPhuongXa"`
	DistrictCode string `json:"MaQuanHuyen"`
}

// WardMappingDB is an instance db
var WardMappingDB = &db.Instance{
	ColName:        "ward_mapping",
	TemplateObject: &WardMapping{},
}

// InitWardMappingModel is func init model ward
func InitWardMappingModel(s *mongo.Database) {
	WardMappingDB.ApplyDatabase(s)

	t := true
	_ = WardMappingDB.CreateIndex(bson.D{
		primitive.E{Key: "name", Value: 1},
		primitive.E{Key: "code", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
		Unique:     &t,
	})

	WardMappingDB.CreateIndex(bson.D{
		{"code", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	WardMappingDB.CreateIndex(bson.D{
		{"dictionary", 1},
		{"district_code", 1},
	}, &options.IndexOptions{
		Background: &t,
	})
}
