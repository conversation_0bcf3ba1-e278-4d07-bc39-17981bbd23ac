package action

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/response"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	shippingOrderQueue "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action/consumer/shipping-order"
	sync_data "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action/consumer/tpl-callback"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var (
	// Cached shipping order statuses. WARNING: DO NOT MODIFY THIS SLICE
	ShippingOrderSnapshotStatuses = []string{
		"COMPLETED",
		"LOST",
		"RETURNED",
		"CANCEL",
	}
)

// GetShippingOrder func
func GetShippingOrder(referenceCode string) *common.APIResponse {
	if referenceCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Parameter reference_code must not empty.",
			ErrorCode: "REFERENCE_CODE_REQUIRED",
		}
	}

	return model.ShippingOrderDB.QueryOne(&model.ShippingOrder{
		ReferenceCode: referenceCode,
	})
}

// GetListShippingOrder func
func GetListShippingOrder(query request.ShippingOrderQuery, sort map[string]interface{}, offset, limit int64, getTotal bool) *common.APIResponse {
	filter := bson.M{}
	now := time.Now()
	if query.ReferenceCode != "" {
		filter["reference_code"] = query.ReferenceCode
	}

	if len(query.References) > 0 {
		filter["references"] = bson.M{
			"$in": query.References,
		}
	}

	if query.WarehouseCode != "" {
		hubRaw := model.HubDB.QueryOne(bson.M{
			"warehouse_reference_code": query.WarehouseCode,
		})
		if hubRaw.Status != common.APIStatus.Ok {
			return hubRaw
		}
		hub := hubRaw.Data.([]*model.Hub)[0]
		filter["current_hub"] = hub.Code
	}

	if len(query.Tags) > 0 {
		if len(query.Tags) == 1 {
			filter["tags"] = query.Tags[0]
		} else {
			filter["tags"] = bson.M{
				"$in": query.Tags,
			}
		}
	}

	//if query.NeedMerge != nil {
	//	filter["need_merge"] = *query.NeedMerge
	//}

	if query.MergeStatus != nil {
		filter["merge_status"] = *query.MergeStatus
	}

	if len(query.MergeStatuses) > 0 {
		filter["$or"] = []bson.M{}
		for _, status := range query.MergeStatuses {
			if *status == enum.MergeStatus.DEFAULT {
				filter["$or"] = append(filter["$or"].([]bson.M),
					bson.M{"merge_status": bson.M{
						"$exists": false,
					}})
				continue
			}
			filter["$or"] = append(filter["$or"].([]bson.M), bson.M{"merge_status": *status})
		}
	}

	if len(query.ListReferenceCode) > 0 {
		if len(query.ListReferenceCode) == 1 {
			filter["reference_code"] = query.ListReferenceCode[0]
		} else {
			filter["reference_code"] = bson.M{
				"$in": query.ListReferenceCode,
			}
		}
	}

	if query.ParentReceiveSessionCode != "" {
		filter["parent_receive_session_code"] = query.ParentReceiveSessionCode
	}

	if len(query.ListTplServiceId) > 0 {
		if len(query.ListTplServiceId) == 1 {
			filter["tpl_service_id"] = query.ListTplServiceId[0]
		} else {
			filter["tpl_service_id"] = bson.M{
				"$in": query.ListTplServiceId,
			}
		}
	}

	if query.Status != nil && *query.Status != "" {
		filter["status"] = *query.Status
	}

	if len(query.Statuses) > 0 {
		if len(query.Statuses) == 1 {
			filter["status"] = query.Statuses[0]
		} else {
			filter["status"] = bson.M{
				"$in": query.Statuses,
			}
		}
	}

	if query.FilterEstimatePickingTime {
		filter["estimate_picking_time"] = bson.M{
			"$lte": query.EstimateTime,
		}
	}

	if query.FilterEstimateDeliveringTime {
		filter["estimate_delivering_time"] = bson.M{
			"$lte": query.EstimateTime,
		}
	}

	if query.FilterEstimateReturningTime {
		filter["estimate_returning_time"] = bson.M{
			"$lte": query.EstimateTime,
		}
	}

	if query.ProvinceCode != "" {
		filter["customer_province_code"] = query.ProvinceCode
	}

	if query.DistrictCode != "" {
		filter["customer_district_code"] = query.DistrictCode
	}

	if query.WardCode != "" {
		filter["customer_ward_code"] = query.WardCode
	}

	if len(query.ProvinceCodes) > 0 {
		filter["customer_province_code"] = bson.M{"$in": query.ProvinceCodes}
	}

	if len(query.DistrictCodes) > 0 {
		filter["customer_district_code"] = bson.M{"$in": query.DistrictCodes}
	}

	if len(query.WardCodes) > 0 {
		filter["customer_ward_code"] = bson.M{"$in": query.WardCodes}
	}

	if len(query.FromProvinceCodes) > 0 {
		filter["from_province_code"] = bson.M{"$in": query.FromProvinceCodes}
	}

	if len(query.FromDistrictCodes) > 0 {
		filter["from_district_code"] = bson.M{"$in": query.FromDistrictCodes}
	}

	if len(query.FromWardCodes) > 0 {
		filter["from_ward_code"] = bson.M{"$in": query.FromWardCodes}
	}

	if query.DriverId > 0 {
		filter["driver_id"] = query.DriverId
	}

	if query.TplCode != nil && *query.TplCode != "" {
		filter["tpl_code"] = *query.TplCode
	}

	if len(query.ListTplCode) > 0 {
		filter["tpl_code"] = bson.M{
			"$in": query.ListTplCode,
		}
	}

	if query.TrackingCode != "" {
		filter["tracking_code"] = query.TrackingCode
	}

	if len(query.ListTrackingCode) > 0 {
		if len(query.ListTrackingCode) == 1 {
			filter["tracking_code"] = query.ListTrackingCode[0]
		} else {
			filter["tracking_code"] = bson.M{
				"$in": query.ListTrackingCode,
			}
		}
	}

	if len(query.ShippingTypes) > 0 {
		if len(query.ShippingTypes) == 1 {
			filter["type"] = query.ShippingTypes[0]
		} else {
			filter["type"] = bson.M{
				"$in": query.ShippingTypes,
			}
		}
	}

	if len(query.Skus) > 0 {
		if len(query.Skus) == 1 {
			filter["products.sku"] = query.Skus[0]
		} else {
			filter["products.sku"] = bson.M{
				"$in": query.Skus,
			}
		}
	}

	if query.ParentReferenceCode != "" {
		filter["parent_reference_code"] = query.ParentReferenceCode
	}

	if query.FromTime > 0 && query.ToTime > 0 {
		filter["action_time"] = bson.M{
			"$gte": query.FromTime,
			"$lte": query.ToTime,
		}
	} else if query.FromTime > 0 && query.ToTime == 0 {
		filter["action_time"] = bson.M{
			"$gte": query.FromTime,
		}
	} else if query.FromTime == 0 && query.ToTime > 0 {
		filter["action_time"] = bson.M{
			"$lte": query.ToTime,
		}
	}

	if len(query.Departments) > 0 {
		filter["scope"] = bson.M{
			"$in": query.Departments,
		}
	}

	if query.IsBookDropOff != nil {
		filter["$or"] = []bson.M{}
		if len(query.ShippingTypes) > 0 && (utils.StringSliceContain(query.ShippingTypes, "DELIVERY") ||
			utils.StringSliceContain(query.ShippingTypes, "RETURN") ||
			utils.StringSliceContain(query.ShippingTypes, "PICKUP") ||
			utils.StringSliceContain(query.ShippingTypes, "INTERNAL_TRANS")) {
			filter["$or"] = append(filter["$or"].([]bson.M), bson.M{
				"is_book_drop_off": bson.M{
					"$exists": false,
				},
			})
		}

		filter["$or"] = append(filter["$or"].([]bson.M), bson.M{
			"is_book_drop_off": *query.IsBookDropOff,
		})
	}

	if query.LeadTimeQuery != nil {
		//Còn hạn: now <  deliveryLeadtime
		//Còn hạn 6h: now < deliveryLeadtime - 6h => deliveryLeadtime > now + 6h
		//Qúa hạn dưới 48h: deliveryLeadtime < now < deliveryLeadtime + 48h
		//Quá hạn 48h: deliveryLeadtime + 48h < now => deliveryLeadtime < now - 48h
		//Quá hạn 7 ngày: deliveryLeadtime  + 7d < now => deliveryLeadtime < now - 7d

		leadTimeFilter := bson.M{}
		if query.LeadTimeQuery.ToMultiplier > 0 {
			leadTimeFilter["$gte"] = now.Add(time.Second * time.Duration(-query.LeadTimeQuery.ToAddition))
		}
		if query.LeadTimeQuery.FromMultiplier > 0 {
			leadTimeFilter["$lte"] = now.Add(time.Second * time.Duration(-query.LeadTimeQuery.FromAddition))
		}
		filter["delivery_lead_time"] = leadTimeFilter
	}

	sortField := primitive.M{}

	if sort == nil {
		sortField["_id"] = -1
	} else {
		sortField = sort
	}

	var result *common.APIResponse
	if query.ReadPreference != nil && *query.ReadPreference == enum.ReadPreference.SECONDARY {
		result = model.ShippingOrderDB.SecondaryInstance.Query(
			filter,
			offset,
			limit,
			&sortField)
		if getTotal {
			countResult := model.ShippingOrderDB.SecondaryInstance.Count(filter)
			result.Total = countResult.Total
		}
	} else {
		result = model.ShippingOrderDB.Query(
			filter,
			offset,
			limit,
			&sortField)
		if getTotal {
			countResult := model.ShippingOrderDB.Count(filter)
			result.Total = countResult.Total
		}
	}

	return result
}

// CountShippingOrderGroupByStatus count shipping-order group by status
func CountShippingOrderGroupByStatus(query *request.ShippingOrderQuery) *common.APIResponse {
	type CountShippingOrderByStatusModel struct {
		Status   string `json:"status,omitempty" bson:"_id,omitempty"`
		Quantity int64  `json:"quantity" bson:"total,omitempty"`
	}

	filter := bson.D{}
	now := time.Now()
	if len(query.Tags) > 0 {
		filter = append(filter, bson.E{
			Key: "tags",
			Value: bson.M{
				"$in": query.Tags,
			},
		})
	}

	if query.ReferenceCode != "" {
		filter = append(filter, bson.E{
			Key:   "reference_code",
			Value: query.ReferenceCode,
		})
	}

	if len(query.References) > 0 {
		filter = append(filter, bson.E{
			Key: "references",
			Value: bson.M{
				"$in": query.References,
			},
		})
	}

	if len(query.ListReferenceCode) > 0 {
		filter = append(filter, bson.E{
			Key: "reference_code",
			Value: bson.M{
				"$in": query.ListReferenceCode,
			},
		})
	}

	if query.MergeStatus != nil {
		filter = append(filter, bson.E{
			Key:   "merge_status",
			Value: *query.MergeStatus,
		})
	}

	if len(query.MergeStatuses) > 0 {
		var t []bson.M
		for _, status := range query.MergeStatuses {
			if *status == enum.MergeStatus.DEFAULT {
				t = append(t, bson.M{
					"merge_status": bson.M{
						"$exists": false,
					},
				})
			} else {
				t = append(t, bson.M{
					"merge_status": *status,
				})
			}
		}
		filter = append(filter, bson.E{
			Key:   "$or",
			Value: t,
		})
	}

	if len(query.ListTplServiceId) > 0 {
		filter = append(filter, bson.E{
			Key: "tpl_service_id",
			Value: bson.M{
				"$in": query.ListTplServiceId,
			},
		})
	}

	if query.FilterEstimatePickingTime {
		filter = append(filter, bson.E{
			Key: "estimate_picking_time",
			Value: bson.M{
				"$lte": query.EstimateTime,
			},
		})
	}

	if query.FilterEstimateDeliveringTime {
		filter = append(filter, bson.E{
			Key: "estimate_delivering_time",
			Value: bson.M{
				"$lte": query.EstimateTime,
			},
		})
	}

	if query.FilterEstimateReturningTime {
		filter = append(filter, bson.E{
			Key: "estimate_returning_time",
			Value: bson.M{
				"$lte": query.EstimateTime,
			},
		})
	}

	if query.ProvinceCode != "" {
		filter = append(filter, bson.E{
			Key:   "customer_province_code",
			Value: query.ProvinceCode,
		})
	}

	if query.DistrictCode != "" {
		filter = append(filter, bson.E{
			Key:   "customer_district_code",
			Value: query.DistrictCode,
		})
	}

	if query.WardCode != "" {
		filter = append(filter, bson.E{
			Key:   "customer_ward_code",
			Value: query.WardCode,
		})
	}

	if len(query.ProvinceCodes) > 0 {
		filter = append(filter, bson.E{
			Key: "customer_province_code",
			Value: bson.M{
				"$in": query.ProvinceCodes,
			},
		})
	}

	if len(query.DistrictCodes) > 0 {
		filter = append(filter, bson.E{
			Key: "customer_district_code",
			Value: bson.M{
				"$in": query.DistrictCodes,
			},
		})
	}

	if len(query.WardCodes) > 0 {
		filter = append(filter, bson.E{
			Key: "customer_ward_code",
			Value: bson.M{
				"$in": query.WardCodes,
			},
		})
	}

	if len(query.FromProvinceCodes) > 0 {
		filter = append(filter, bson.E{
			Key: "from_province_code",
			Value: bson.M{
				"$in": query.FromProvinceCodes,
			},
		})
	}

	if len(query.FromDistrictCodes) > 0 {
		filter = append(filter, bson.E{
			Key: "from_district_code",
			Value: bson.M{
				"$in": query.FromDistrictCodes,
			},
		})
	}

	if len(query.FromWardCodes) > 0 {
		filter = append(filter, bson.E{
			Key: "from_ward_code",
			Value: bson.M{
				"$in": query.FromWardCodes,
			},
		})
	}

	if query.DriverId > 0 {
		filter = append(filter, bson.E{
			Key:   "driver_id",
			Value: query.DriverId,
		})
	}

	if query.TplCode != nil && *query.TplCode != "" {
		filter = append(filter, bson.E{
			Key:   "tpl_code",
			Value: *query.TplCode,
		})
	}

	if len(query.ListTplCode) > 0 {
		filter = append(filter, bson.E{
			Key: "tpl_code",
			Value: bson.M{
				"$in": query.ListTplCode,
			},
		})
	}

	if query.TrackingCode != "" {
		filter = append(filter, bson.E{
			Key:   "tracking_code",
			Value: query.TrackingCode,
		})
	}

	if len(query.ListTrackingCode) > 0 {
		filter = append(filter, bson.E{
			Key: "tracking_code",
			Value: bson.M{
				"$in": query.ListTrackingCode,
			},
		})
	}

	if len(query.ShippingTypes) > 0 {
		filter = append(filter, bson.E{
			Key: "type",
			Value: bson.M{
				"$in": query.ShippingTypes,
			},
		})
	}

	if query.FromTime > 0 && query.ToTime > 0 {
		filter = append(filter, bson.E{
			Key: "action_time",
			Value: bson.M{
				"$gte": query.FromTime,
				"$lte": query.ToTime,
			},
		})

	} else if query.FromTime > 0 && query.ToTime == 0 {
		filter = append(filter, bson.E{
			Key: "action_time",
			Value: bson.M{
				"$gte": query.FromTime,
			},
		})
	} else if query.FromTime == 0 && query.ToTime > 0 {
		filter = append(filter, bson.E{
			Key: "action_time",
			Value: bson.M{
				"$lte": query.ToTime,
			},
		})
	}

	if len(query.Departments) > 0 {
		filter = append(filter, bson.E{
			Key: "scope",
			Value: bson.M{
				"$in": query.Departments,
			},
		})
	}

	if query.IsBookDropOff != nil {
		orArr := []bson.M{}
		if len(query.ShippingTypes) > 0 && (utils.StringSliceContain(query.ShippingTypes, "DELIVERY") ||
			utils.StringSliceContain(query.ShippingTypes, "RETURN") ||
			utils.StringSliceContain(query.ShippingTypes, "PICKUP") ||
			utils.StringSliceContain(query.ShippingTypes, "INTERNAL_TRANS")) {
			orArr = append(orArr, bson.M{
				"is_book_drop_off": bson.M{
					"$exists": false,
				},
			})
		}
		orArr = append(orArr, bson.M{
			"is_book_drop_off": *query.IsBookDropOff,
		})

		filter = append(filter, bson.E{
			Key:   "$or",
			Value: orArr,
		})
	}

	if query.LeadTimeQuery != nil {
		//Còn hạn: now <  deliveryLeadtime
		//Còn hạn 6h: now < deliveryLeadtime - 6h => deliveryLeadtime > now + 6h
		//Qúa hạn dưới 48h: deliveryLeadtime < now < deliveryLeadtime + 48h
		//Quá hạn 48h: deliveryLeadtime + 48h < now => deliveryLeadtime < now - 48h
		//Quá hạn 7 ngày: deliveryLeadtime  + 7d < now => deliveryLeadtime < now - 7d

		leadTimeFilter := bson.M{}
		if query.LeadTimeQuery.ToMultiplier > 0 {
			leadTimeFilter["$gte"] = now.Add(time.Second * time.Duration(-query.LeadTimeQuery.ToAddition))
		}
		if query.LeadTimeQuery.FromMultiplier > 0 {
			leadTimeFilter["$lte"] = now.Add(time.Second * time.Duration(-query.LeadTimeQuery.FromAddition))
		}
		filter = append(filter, bson.E{
			Key:   "delivery_lead_time",
			Value: leadTimeFilter,
		})
	}

	statuses := utils.EnumToStringSlice(*enum.TPLCallbackStatus)

	if query.Status != nil {
		statuses = []string{string(*query.Status)}
	}

	if len(query.Statuses) > 0 {
		statuses = []string{}
		for _, status := range query.Statuses {
			statuses = append(statuses, string(*status))
		}
	}

	result := make([]*CountShippingOrderByStatusModel, len(statuses))
	wg := new(sync.WaitGroup)
	wg.Add(len(statuses))
	for i, status := range statuses {
		go func(
			index int,
			status string,
			result []*CountShippingOrderByStatusModel,
			wg *sync.WaitGroup) {
			defer wg.Done()
			copyFilter := make(bson.D, len(filter))
			copy(copyFilter, filter)
			copyFilter = append(copyFilter, bson.E{
				Key:   "status",
				Value: status,
			})
			// If filter satisfies the condition, we can use snapshot to get the count
			if utils.StringSliceContain(ShippingOrderSnapshotStatuses, status) &&
				len(copyFilter) == 1 {
				snapshotResp := model.SnapshotDB.QueryOne(bson.M{
					"type": enum.SnapshotType.CACHE_ORDER_STATUS,
					"key":  "shipping_order_status_" + status,
				})
				if snapshotResp.Status == common.APIStatus.Ok {
					snapshot := snapshotResp.Data.([]*model.Snapshot)[0]
					lastSnapshotTime := snapshot.SnapshotAt
					obj := primitive.NewObjectIDFromTimestamp(lastSnapshotTime)
					countResult := model.ShippingOrderDB.Count(bson.M{
						"status": status,
						"_id": bson.M{
							"$gt": obj,
						},
					})
					result[index] = &CountShippingOrderByStatusModel{
						Status:   status,
						Quantity: countResult.Total + snapshot.Quantity,
					}
					return
				}
			}

			if query.ReadPreference != nil && *query.ReadPreference == enum.ReadPreference.SECONDARY {
				countResult := model.ShippingOrderDB.SecondaryInstance.Count(copyFilter)
				result[index] = &CountShippingOrderByStatusModel{
					Status:   status,
					Quantity: countResult.Total,
				}
			} else {
				countResult := model.ShippingOrderDB.Count(copyFilter)
				result[index] = &CountShippingOrderByStatusModel{
					Status:   status,
					Quantity: countResult.Total,
				}
			}

		}(i, status, result, wg)
	}

	wg.Wait()

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Thông tin số lượng phiếu theo trạng thái.",
		Data:    result,
	}
}

// UpdateShippingOrder func
func UpdateShippingOrder(input *model.ShippingOrder) *common.APIResponse {
	if input.ReferenceCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Mã đơn hàng không được để trống",
		}
	}

	afterOption := options.After
	updateShipping := model.ShippingOrderDB.UpdateOne(
		bson.M{
			"reference_code": input.ReferenceCode,
		},
		input,
		&options.FindOneAndUpdateOptions{
			Upsert:         &enum.False,
			ReturnDocument: &afterOption,
		})

	if updateShipping.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Bad request when Update shipping_order",
		}
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật thành công",
	}
}

// Assign func
func Assign(listReferenceCode []string, driverName string, driverId int, accountId int64) *common.APIResponse {
	if len(listReferenceCode) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Danh sách phiếu không được để trống.",
		}
	}

	if driverId <= 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Nhân viên không được để trống.",
		}
	}

	updateResult := model.ShippingOrderDB.UpdateMany(
		bson.M{
			"reference_code": bson.M{"$in": listReferenceCode},
		},
		bson.M{
			"driver_id":         driverId,
			"driver_name":       driverName,
			"updated_by":        accountId,
			"last_updated_time": time.Now(),
		})
	return updateResult
}

// UpdateStatusShippingOrder func
func UpdateStatusShippingOrder(listReferenceCode []string, status *enum.TPLCallbackStatusValue, accountId int64) *common.APIResponse {
	if len(listReferenceCode) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Danh sách phiếu không được để trống.",
		}
	}

	if status == nil || *status == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Trạng thái không hợp lệ.",
		}
	}

	shippingOrders := model.ShippingOrderDB.Query(bson.M{
		"reference_code": bson.M{
			"$in": listReferenceCode,
		},
	}, 0, 100, nil)

	if shippingOrders.Status != common.APIStatus.Ok || shippingOrders.Data == nil {
		return shippingOrders
	}

	var listCode []string

	listShippingOrderInfo := shippingOrders.Data.([]*model.ShippingOrder)

	for _, shippingOrder := range listShippingOrderInfo {
		listCode = append(listCode, shippingOrder.ReferenceCode)
	}

	var listShippingError []string

	for _, referenceCode := range listReferenceCode {
		if !CheckItemInArray(referenceCode, listCode) {
			listShippingError = append(listShippingError, referenceCode)
		}
	}

	if len(listShippingError) > 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không tìm thấy thông tin đơn :" + strings.Join(listShippingError, ","),
		}
	}

	updateResult := model.ShippingOrderDB.UpdateMany(
		bson.M{
			"reference_code": bson.M{"$in": listReferenceCode},
		},
		bson.M{
			"status":            string(*status),
			"updated_by":        accountId,
			"last_updated_time": time.Now(),
		})

	if updateResult.Status == common.APIStatus.Ok {
		for _, shippingOrder := range listShippingOrderInfo {
			t := time.Unix(shippingOrder.ActionTime, 10)
			callback := request.Callback{
				SO:         shippingOrder.ReferenceCode,
				ActionTime: &t,
				Status:     status,
				TPLStatus:  string(*status),
			}
			switch *status {
			case enum.TPLCallbackStatus.TRANSPORTING:
				callback.TPLStatusName = "Đã bàn giao hàng cho đơn vị trung chuyển."
				callback.StatusName = "Đã bàn giao hàng cho đơn vị trung chuyển."
				break
			case enum.TPLCallbackStatus.DELIVERING:
				callback.StatusName = "Đang giao hàng."
				callback.TPLStatusName = "Đang giao hàng."
				break
			case enum.TPLCallbackStatus.DELIVERED:
				callback.StatusName = "Đã giao hàng thành công."
				callback.TPLStatusName = "Đã giao hàng thành công."
				break
			case enum.TPLCallbackStatus.STORING:
				callback.StatusName = "Lưu kho/Nhập kho, " + shippingOrder.TplName
				callback.TPLStatusName = "Lưu kho/Nhập kho, " + shippingOrder.TplName
				break
			case enum.TPLCallbackStatus.PICKED:
				callback.StatusName = "Đã lấy hàng."
				callback.TPLStatusName = "Đã lấy hàng."
				break
			case enum.TPLCallbackStatus.CANCEL:
				callback.StatusName = "Hủy giao hàng."
				callback.TPLStatusName = "Hủy giao hàng."
				break
			case enum.TPLCallbackStatus.DELIVERY_FAIL:
				callback.StatusName = "Giao hàng thất bại."
				callback.TPLStatusName = "Giao hàng thất bại."
				break
			case enum.TPLCallbackStatus.LOST:
				callback.StatusName = "Đã mất hàng."
				callback.TPLStatusName = "Đã mất hàng."
			default:
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "SYNC_STATUS_CALLBACK",
					Title:   "Status update: " + string(*status),
					Message: shippingOrder.ReferenceCode,
				})
				continue
			}

			sync_data.PushSyncStatusTPLCallbackQueue(callback, shippingOrder.ReferenceCode)
		}
	}
	return updateResult
}

// SyncShippingOrder func
func SyncShippingOrder(input *request.Callback) {
	sync_data.PushCallbackQueue(input, input.SO)
}

// SyncSomeShippingOrder func
func SyncSomeShippingOrder(listReferenceCode []string) *common.APIResponse {
	query := model.CallbackQuery{
		ListSO: listReferenceCode,
	}
	listCallback, err := client.Services.TplCallbackClient.GetListCallback(len(listReferenceCode), 0, &query)

	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Get tpl_callback error ",
		}
	}

	for _, callback := range listCallback {
		sync_data.PushCallbackQueue(callback, callback.SO)
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật thành công",
	}
}

// MigrateShippingOrder func
func MigrateShippingOrder() *common.APIResponse {
	var offset int64
LOAD_MORE:
	listCallback, err := client.Services.TplCallbackClient.GetListCallback(1000, int(offset), nil)

	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Get tpl_callback error ",
		}
	}

	for i, callback := range listCallback {
		sync_data.PushCallbackQueue(callback, callback.SO)
		if i == 999 {
			offset = offset + 1000
			goto LOAD_MORE
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật thành công",
	}
}

// MassUpdateTPLServiceID func
func MassUpdateTPLServiceID(referenceCodes []string, tplServiceId int) *common.APIResponse {
	if len(referenceCodes) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "referenceCodes is not empty",
		}
	}

	if tplServiceId <= 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "tplServiceId is not empty",
		}
	}

	return model.ShippingOrderDB.UpdateMany(
		bson.M{
			"reference_code": bson.M{
				"$in": referenceCodes,
			},
		},
		bson.M{
			"tpl_service_id": tplServiceId,
		})
}

// AdditionNoteShippingOrder func
func AdditionNoteShippingOrder(note, referenceCode string, accountId int64) *common.APIResponse {
	if note == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Nội dung ghi chú không được để trống.",
		}
	}

	if referenceCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã phiếu giao hàng không được để trống.",
		}
	}

	shippingOrders := model.ShippingOrderDB.QueryOne(bson.M{
		"reference_code": referenceCode,
	})

	if shippingOrders.Status != common.APIStatus.Ok || shippingOrders.Data == nil {
		return shippingOrders
	}

	shippingOrder := shippingOrders.Data.([]*model.ShippingOrder)[0]
	current := time.Now()

	shippingOrder.DeliveryNote = append(shippingOrder.DeliveryNote, &model.DeliveryNote{
		Description: note,
		ActionTime:  current.Unix(),
		CreatedBy:   accountId,
	})

	updateResult := model.ShippingOrderDB.UpdateOne(
		bson.M{
			"reference_code": referenceCode,
		},
		shippingOrder)

	if updateResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: updateResult.Message,
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Thêm ghi chú thành công.",
	}
}

// AdditionNoteHubOrder func
func AdditionNoteHubOrder(note, referenceCode string, accountId int64) *common.APIResponse {
	if note == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Nội dung ghi chú không được để trống.",
		}
	}

	if referenceCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã phiếu giao hàng không được để trống.",
		}
	}

	hubOrders := model.HUBShippingOrderDB.QueryOne(bson.M{
		"reference_code": referenceCode,
	})

	if hubOrders.Status != common.APIStatus.Ok || hubOrders.Data == nil {
		return hubOrders
	}

	hubOrder := hubOrders.Data.([]*model.ShippingOrder)[0]
	current := time.Now()

	hubOrder.DeliveryNote = append(hubOrder.DeliveryNote, &model.DeliveryNote{
		Description: note,
		ActionTime:  current.Unix(),
		CreatedBy:   accountId,
	})

	updateResult := model.HUBShippingOrderDB.UpdateOne(
		bson.M{
			"reference_code": referenceCode,
		},
		hubOrder)

	if updateResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: updateResult.Message,
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Thêm ghi chú thành công.",
	}
}

// CheckinPackage func
func CheckinPackage(input *request.CheckinPackageRequest) *common.APIResponse {
	if input.ReferenceCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã đơn hàng không được để trống",
			ErrorCode: "REFERENCE_CODE_REQUIRED",
		}
	}

	if input.WarehouseCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã kho không được để trống",
			ErrorCode: "WAREHOUSE_CODE_REQUIRED",
		}
	}

	resultQueryHub := model.HubDB.QueryOne(bson.M{
		"warehouse_reference_code": input.WarehouseCode,
	})

	if resultQueryHub.Status != common.APIStatus.Ok {
		return resultQueryHub
	}

	hub := resultQueryHub.Data.([]*model.Hub)[0]

	resultQueryHandover := model.HandoverTicketDB.Query(bson.M{
		"so_list.so":         input.ReferenceCode,
		"to_department_code": hub.Code,
	}, 0, 100, nil)

	if resultQueryHandover.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy thông tin phiếu bàn giao",
			ErrorCode: "HANDOVER_TICKET_INVALID",
		}
	}

	existedTicket := resultQueryHandover.Data.([]*model.HandoverTicket)[0]

	isOverPackage := false
	autoDoneHandover := true

	// If exist, increase numPackage
	for i, existedSO := range existedTicket.SOList {
		if existedSO.SO == input.ReferenceCode {
			existedSO.ReceivedQuantity = input.ReceivePackage + existedSO.ReceivedQuantity
			if existedSO.ReceivedQuantity == existedSO.ScannedQuantity {
				existedSO.Status = &enum.HandoverItemStatus.DONE
			}

			if existedSO.ReceivedQuantity > existedSO.ScannedQuantity {
				isOverPackage = true
				break
			}

			existedTicket.SOList[i] = existedSO
		}

		if existedTicket.SOList[i].Status != nil && *existedTicket.SOList[i].Status != enum.HandoverItemStatus.DONE {
			autoDoneHandover = false
		}
	}

	if isOverPackage {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Quá số kiện",
			ErrorCode: "OVER_PACKAGE",
		}
	}

	if autoDoneHandover {
		existedTicket.Status = &enum.HandoverStatus.COMPLETED
	}

	now := time.Now()
	existedTicket.LastUpdatedTime = &now
	afterOption := options.After
	updateResult := model.HandoverTicketDB.UpdateOne(bson.M{
		"ticket_id": existedTicket.TicketID,
	},
		existedTicket,
		&options.FindOneAndUpdateOptions{
			Upsert:         &enum.False,
			ReturnDocument: &afterOption,
		})

	if updateResult.Status != common.APIStatus.Ok {
		return updateResult
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Checkin thành công",
	}
}

// CheckHandover func
func CheckHandover(input *request.CheckinPackageRequest) *common.APIResponse {
	if input.ReferenceCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã đơn hàng không được để trống",
			ErrorCode: "REFERENCE_CODE_REQUIRED",
		}
	}

	if input.WarehouseCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã kho không được để trống",
			ErrorCode: "WAREHOUSE_CODE_REQUIRED",
		}
	}

	resultQueryHub := model.HubDB.QueryOne(bson.M{
		"warehouse_reference_code": input.WarehouseCode,
	})

	if resultQueryHub.Status != common.APIStatus.Ok {
		return resultQueryHub
	}

	hub := resultQueryHub.Data.([]*model.Hub)[0]

	resultQueryHandover := model.HandoverTicketDB.Query(bson.M{
		"so_list.so":         input.ReferenceCode,
		"to_department_code": hub.Code,
	}, 0, 100, nil)

	if resultQueryHandover.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy thông tin phiếu bàn giao",
			ErrorCode: "HANDOVER_TICKET_INVALID",
		}
	}

	handoverTickets := resultQueryHandover.Data.([]*model.HandoverTicket)

	isAcceptReturn := false
	insideHandover := 0
	for _, handoverTicket := range handoverTickets {
		if handoverTicket.Status != nil && (*handoverTicket.Status == enum.HandoverStatus.TRANSPORTING) {
			isAcceptReturn = true
			insideHandover++
		}
	}

	if !isAcceptReturn || insideHandover > 1 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Checkin đơn hàng không hợp lệ",
			ErrorCode: "HANDOVER_TICKET_STATUS_ERROR",
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Hàng được bàn giao về kho",
	}
}

func DeleteShippingOrder(input *model.ShippingOrder) *common.APIResponse {
	return model.ShippingOrderDB.Delete(bson.M{
		"reference_code": input.ReferenceCode,
	})
}

func CreateHubOrderByShippingOrder(input *request.CreateHubOrderRequest) *common.APIResponse {
	if input.ReferenceCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã đơn không được để trống",
			ErrorCode: "REFERENCE_CODE_EMPTY",
		}
	}

	queryShippingOrder := model.ShippingOrderDB.QueryOne(&model.ShippingOrder{ReferenceCode: input.ReferenceCode})

	if queryShippingOrder.Status != common.APIStatus.Ok || queryShippingOrder.Data == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   queryShippingOrder.Message,
			ErrorCode: queryShippingOrder.ErrorCode,
		}
	}

	shippingOrder := queryShippingOrder.Data.([]*model.ShippingOrder)[0]
	hubShippingOrder := utils.BuildHubOrderByShippingOrder(shippingOrder)

	defer model.ShippingOrderDB.UpdateOne(&bson.M{
		"reference_code": shippingOrder.ReferenceCode,
	}, &bson.M{
		"stored_at_lm_hub_time": hubShippingOrder.StoredTime,
	})

	return model.HUBShippingOrderDB.Create(hubShippingOrder)
}

func MigrateShippingOrderType() *common.APIResponse {
	var offset int64 = 0
	for {
		shippingOrderRaw := model.ShippingOrderDB.Query(bson.M{
			"type": bson.M{
				"$exists": false,
			},
		}, offset, 200, &bson.M{"_id": -1})

		if shippingOrderRaw.Status != common.APIStatus.Ok {
			break
		}
		var referenceCodes []string
		shippingOrders := shippingOrderRaw.Data.([]*model.ShippingOrder)

		for _, shippingOrder := range shippingOrders {
			if strings.HasPrefix(shippingOrder.ReferenceCode, "SO") {
				referenceCodes = append(referenceCodes, shippingOrder.ReferenceCode)
			}
		}

		if len(referenceCodes) > 0 {
			_ = model.ShippingOrderDB.UpdateMany(bson.M{
				"reference_code": bson.M{
					"$in": referenceCodes,
				},
			}, bson.M{
				"type": &enum.ShippingOrderType.DELIVERY,
			})
		}
		offset += 200
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Ok",
	}
}

func CompleteShippingOrder(referenceCodes []string, updateBy int64) *common.APIResponse {
	if len(referenceCodes) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Danh sách đơn trống",
		}
	}

	var errors []*common.Error
	current := time.Now()

	for _, referenceCode := range referenceCodes {
		shippingOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
			"reference_code": referenceCode,
		})

		if shippingOrderRaw.Status != common.APIStatus.Ok {
			errors = append(errors, &common.Error{
				Message: "Đơn giao hàng" + referenceCode + " không tồn tại",
			})
			continue
		}

		shippingOrder := shippingOrderRaw.Data.([]*model.ShippingOrder)[0]
		if shippingOrder.ShippingType != nil &&
			*shippingOrder.ShippingType == enum.ShippingOrderType.OPM {
			callback := request.Callback{
				ActionTime:    &current,
				SO:            referenceCode,
				Status:        &enum.TPLCallbackStatus.COMPLETED,
				StatusName:    "Đã hoàn thành.",
				TPLStatus:     string(enum.TPLCallbackStatus.COMPLETED),
				TPLStatusName: "Đã hoàn thành.",
				Type:          shippingOrder.ShippingType,
			}
			_ = sync_data.PushCreateTPLCallbackQueue(callback, callback.SO)
			continue
		}

		// Nếu thu người nhận của đơn FMPO mà hoàn thành đối soát:
		// + Nếu đơn đã checkin vô kho thì mới complete đơn
		// + Nếu đơn chưa checkin vào kho thì chỉ lật biến IsReconciled lên true tính cả những đơn tách
		if shippingOrder.ShippingType != nil &&
			*shippingOrder.ShippingType == enum.ShippingOrderType.FMPO &&
			shippingOrder.FeeCollectMethod != nil &&
			(*shippingOrder.FeeCollectMethod == enum.FeeCollectMethod.SENDER_PAY ||
				*shippingOrder.FeeCollectMethod == enum.FeeCollectMethod.DEBT) {
			// Lấy tất cả những đơn tách hàng
			checkShippingOrdersRaw := model.ShippingOrderDB.Query(bson.M{
				"receive_session_code": shippingOrder.ReceiveSessionCode,
			}, 0, 1000, nil)

			if checkShippingOrdersRaw.Status != common.APIStatus.Ok {
				continue
			}

			for _, order := range checkShippingOrdersRaw.Data.([]*model.ShippingOrder) {
				// Nếu đơn đã nhập kho đích thì hoàn thành đơn
				if order.Status != nil && *order.Status == enum.TPLCallbackStatus.DELIVERED {
					model.ShippingOrderDB.UpdateOne(bson.M{
						"reference_code": order.ReferenceCode,
					}, bson.M{
						"status":            &enum.TPLCallbackStatus.COMPLETED,
						"last_updated_by":   strconv.FormatInt(updateBy, 10),
						"last_updated_time": &current,
						"action_time":       current.Unix(),
						"is_reconciled":     true,
					})

					model.HUBShippingOrderDB.UpdateOne(bson.M{
						"reference_code": order.ReferenceCode,
						"hub_code":       order.CurrentHub,
					}, bson.M{
						"status":            &enum.HubShippingOrderStatus.COMPLETED,
						"last_updated_by":   strconv.FormatInt(updateBy, 10),
						"last_updated_time": &current,
						"action_time":       current.Unix(),
						"reconciled_time":   current,
					})

					callback := request.Callback{
						ActionTime:    &current,
						SO:            referenceCode,
						Status:        &enum.TPLCallbackStatus.COMPLETED,
						StatusName:    "Đã hoàn thành.",
						TPLStatus:     string(enum.TPLCallbackStatus.COMPLETED),
						TPLStatusName: "Đã hoàn thành.",
						Type:          shippingOrder.ShippingType,
					}
					_ = sync_data.PushCreateTPLCallbackQueue(callback, callback.SO)

					if shippingOrder.CallbackUrl != "" {
						callbackPartner := request.Callback{
							Status:        &enum.TPLCallbackStatus.COMPLETED,
							ReferenceCode: shippingOrder.ReferenceCode,
							PoCode:        shippingOrder.ParentReferenceCode,
							TrackingCode:  shippingOrder.TrackingCode,
							ActionTime:    &current,
							PartnerCode:   "INTERNAL",
							HubCode:       shippingOrder.CurrentHub,
							WarehouseCode: shippingOrder.CustomerCode,
							CallbackUrl:   shippingOrder.CallbackUrl,
							NumPackage:    shippingOrder.NumPackage,
						}
						if shippingOrder.CheckinNumPack != 0 {
							callbackPartner.NumPackage = shippingOrder.CheckinNumPack
						}
						partner := client.GetWebhookClient("INTERNAL")
						_ = partner.SendCallbackToPartner(callbackPartner)
					}
				} else {
					// Nếu đơn chưa nhập kho thì chỉ lật IsReconcile lên true
					model.ShippingOrderDB.UpdateOne(bson.M{
						"reference_code": order.ReferenceCode,
					}, bson.M{
						"is_reconciled":   true,
						"reconciled_time": current,
					})
				}
			}
			continue
		}

		hubOrderRaw := model.HUBShippingOrderDB.QueryOne(bson.M{
			"reference_code": referenceCode,
			"hub_code":       shippingOrder.CurrentHub,
		})

		if hubOrderRaw.Status != common.APIStatus.Ok {
			errors = append(errors, &common.Error{
				Message: "Đơn giao hàng" + referenceCode + " tại hub " + shippingOrder.CurrentHub + " không tồn tại",
			})
			continue
		}

		updateShippingOrderResult := model.ShippingOrderDB.UpdateOne(bson.M{
			"reference_code": referenceCode,
		}, bson.M{
			"status":            &enum.TPLCallbackStatus.COMPLETED,
			"last_updated_by":   strconv.FormatInt(updateBy, 10),
			"last_updated_time": &current,
			"action_time":       current.Unix(),
		})

		if updateShippingOrderResult.Status != common.APIStatus.Ok {
			errors = append(errors, &common.Error{
				Message: "Không thể cập nhật trạng thái đơn " + referenceCode,
			})
			continue
		}

		hubOrder := hubOrderRaw.Data.([]*model.HubShippingOrder)[0]
		bins := []string{}
		for _, product := range hubOrder.Products {
			if product.Status != nil && (*product.Status == enum.ProductStatus.LOST || *product.Status == enum.ProductStatus.REMOVED) {
				bins = append(bins, product.SKU)
			} else {
				product.Status = &enum.ProductStatus.STORING
			}
		}

		parentReferenceCode := hubOrder.ParentReferenceCode
		if parentReferenceCode != "" && len(bins) > 0 {
			handOverRes := model.HandoverTicketDB.QueryOne(bson.M{
				"so_list": bson.M{
					"$elemMatch": bson.M{
						"so": parentReferenceCode,
						"status": bson.M{
							"$ne": "CANCEL",
						},
					},
				},
			})
			if handOverRes.Status == common.APIStatus.Ok {
				handOver := handOverRes.Data.([]*model.HandoverTicket)[0]
				for _, so := range handOver.SOList {
					for _, product := range so.Products {
						if utils.CheckContainValue(bins, product.SKU) {
							product.Status = &enum.ProductStatus.STORING
							so.ReceivedQuantity = so.ReceivedQuantity + 1
						}
					}

				}

				_ = model.HandoverTicketDB.UpdateOne(bson.M{
					"ticket_id": handOver.TicketID,
				}, bson.M{
					"so_list": handOver.SOList,
				})
			}
		}

		lastestHubOrderResp := model.HUBShippingOrderDB.Query(bson.M{
			"reference_code": referenceCode,
		}, 0, 100, &primitive.M{"_id": -1})

		if lastestHubOrderResp.Status != common.APIStatus.Ok {
			errors = append(errors, &common.Error{
				Message: "Không thể lấy thông tin đơn " + referenceCode,
			})
			continue
		}
		lastestHubOrder := lastestHubOrderResp.Data.([]*model.HubShippingOrder)[0]

		updateHubOrderResult := model.HUBShippingOrderDB.UpdateOne(bson.M{
			"reference_code": referenceCode,
			"hub_code":       lastestHubOrder.HUBCode,
		}, bson.M{
			"status":            &enum.HubShippingOrderStatus.COMPLETED,
			"last_updated_by":   strconv.FormatInt(updateBy, 10),
			"last_updated_time": &current,
			"action_time":       current.Unix(),
			"products":          hubOrder.Products,
		})

		if updateHubOrderResult.Status != common.APIStatus.Ok {
			errors = append(errors, &common.Error{
				Message: "Không thể cập nhật trạng thái đơn " + referenceCode + " tại hub " + shippingOrder.CurrentHub,
			})
			continue
		}

		callback := request.Callback{
			ActionTime:    &current,
			SO:            referenceCode,
			Status:        &enum.TPLCallbackStatus.COMPLETED,
			StatusName:    "Đã hoàn thành.",
			TPLStatus:     string(enum.TPLCallbackStatus.COMPLETED),
			TPLStatusName: "Đã hoàn thành.",
			Type:          shippingOrder.ShippingType,
		}
		_ = sync_data.PushCreateTPLCallbackQueue(callback, callback.SO)

		if shippingOrder.CallbackUrl != "" {
			callbackPartner := request.Callback{
				Status:        &enum.TPLCallbackStatus.COMPLETED,
				ReferenceCode: shippingOrder.ReferenceCode,
				PoCode:        shippingOrder.ParentReferenceCode,
				TrackingCode:  shippingOrder.TrackingCode,
				ActionTime:    &current,
				PartnerCode:   "INTERNAL",
				HubCode:       shippingOrder.CurrentHub,
				WarehouseCode: shippingOrder.CustomerCode,
				CallbackUrl:   shippingOrder.CallbackUrl,
				NumPackage:    shippingOrder.NumPackage,
			}
			if shippingOrder.CheckinNumPack != 0 {
				callbackPartner.NumPackage = shippingOrder.CheckinNumPack
			}

			partner := client.GetWebhookClient("INTERNAL")
			_ = partner.SendCallbackToPartner(callbackPartner)
		}
	}

	if len(errors) > 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Đã có lỗi xuất hiện khi hoàn thành giao hàng",
			Data:    errors,
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Hoàn tất hoàn thành đơn",
	}
}

func MigrateProductForPickupOrder(referenceCodes []string) *common.APIResponse {
	allPickupOrder := model.ShippingOrderDB.Query(bson.M{
		"tracking_code": bson.M{"$in": referenceCodes},
	}, 0, 5000, nil)

	if allPickupOrder.Status != common.APIStatus.Ok {
		return allPickupOrder
	}

	listOrder := allPickupOrder.Data.([]*model.ShippingOrder)
	for _, order := range listOrder {
		// Call pick ticket get delivery order
		ticket, err := client.Services.PickingClient.GetPickTicket(order.ReferenceCode)
		if err != nil || ticket == nil {
			continue
		}

		Height := 30.0
		Width := 10.0
		Length := 60.0
		Weight := 0.5
		var binCodes []string
		var products []*model.Product
		for _, code := range ticket.Baskets {
			if code.Type == "DELIVERY" {
				for _, binCode := range code.Codes {
					if !strings.HasPrefix(binCode, "TG") {
						binCodes = append(binCodes, binCode)
						products = append(products, &model.Product{
							Name:     "BIN " + binCode,
							Category: "BIN",
							Quantity: 1,
							SKU:      binCode,
							Height:   Height,
							Width:    Width,
							Weight:   Weight,
							Length:   Length,
							Price:    0,
						})
					}
				}
			}
		}

		_ = model.ShippingOrderDB.UpdateOne(bson.M{
			"reference_code": order.TrackingCode,
			"type":           "PICKUP",
		}, bson.M{
			"products": products,
		})

		_ = model.HUBShippingOrderDB.UpdateOne(bson.M{
			"reference_code": order.TrackingCode,
			"type":           "PICKUP",
		}, bson.M{
			"products": products,
		})

	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Hoàn tất hoàn thành đơn",
	}
}

func MigrateShippingOrderScope() *common.APIResponse {
	var limit int64 = 1000
	filter := bson.M{}
	errCount := 0
	// Full table scan
	for {
		shippingOrderRaw := model.ShippingOrderDB.Query(filter, 0, limit, &bson.M{"_id": -1})
		if shippingOrderRaw.Status != common.APIStatus.Ok {
			break
		}
		shippingOrders := shippingOrderRaw.Data.([]*model.ShippingOrder)
		smallestId := shippingOrders[len(shippingOrders)-1].ID
		filter["_id"] = bson.M{
			"$lt": smallestId,
		}

		for _, shippingOrder := range shippingOrders {
			hubOrdersRaw := model.HUBShippingOrderDB.Distinct(bson.M{
				"reference_code": shippingOrder.ReferenceCode,
			}, "hub_code")

			if hubOrdersRaw.Status != common.APIStatus.Ok {
				errCount++
				continue
			}

			hubOrdersByte, err := json.Marshal(hubOrdersRaw.Data)
			if err != nil {
				errCount++
				continue
			}
			var hubOrdersCode []string
			_ = json.Unmarshal(hubOrdersByte, &hubOrdersCode)
			if len(hubOrdersCode) == 0 {
				errCount++
				continue
			}

			model.ShippingOrderDB.UpdateOne(bson.M{
				"reference_code": shippingOrder.ReferenceCode,
			}, bson.M{
				"scope": hubOrdersCode,
			})
		}
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
		Data:    []int{errCount},
	}
}

func CompleteBinOrder(binCodes []request.CompleteBinCode, updateBy int64) *common.APIResponse {
	var errors []*common.Error
	var completeParentCodes []string
	var completeChildCodes []request.CompleteBinCode
	var removeTransportCodes []request.CompleteBinCode

	for _, binCode := range binCodes {
		if binCode.BinCode == "" || binCode.ParentReferenceCode == "" {
			errors = append(errors, &common.Error{
				Message: "Bin code AND parent reference code must not be empty.",
			})
		}

		parentOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
			"reference_code": binCode.ParentReferenceCode,
		})
		// If parent not found we dont need to keep checking child
		if parentOrderRaw.Status != common.APIStatus.Ok {
			errors = append(errors, &common.Error{
				Message: "Not found parent order",
			})
			continue
		}

		parentOrder := parentOrderRaw.Data.([]*model.ShippingOrder)[0]
		parentHubOrderRaw := model.HUBShippingOrderDB.QueryOne(bson.M{
			"reference_code": binCode.ParentReferenceCode,
			"hub_code":       parentOrder.CurrentHub,
		})
		// If parent not found we dont need to keep checking child
		if parentHubOrderRaw.Status != common.APIStatus.Ok {
			errors = append(errors, &common.Error{
				Message: "Not found parent hub order",
			})
			continue
		}

		parentHubOrder := parentHubOrderRaw.Data.([]*model.HubShippingOrder)[0]

		needCompleteParent := false
		needRemoveTransport := false
		needCheckChild := false
		if len(parentHubOrder.Products) == 1 {
			needCompleteParent = true
		} else if parentOrder.Status != nil && *parentOrder.Status == enum.TPLCallbackStatus.TRANSPORTING {
			needRemoveTransport = true
		} else {
			isAllFinished := true
			// Nếu đơn lấy hàng cha có bin chưa bị REMOVED thì complete thẳng đơn cha luôn
			for _, prd := range parentHubOrder.Products {
				if prd.SKU == binCode.BinCode {
					if prd.Status != nil && *prd.Status == enum.ProductStatus.REMOVED {
						needCheckChild = true
					}
					continue
				}
				if prd.Status == nil {
					isAllFinished = false
					break
				}
				if *prd.Status == enum.ProductStatus.REMOVED ||
					*prd.Status == enum.ProductStatus.LOST {
					continue
				}
				if parentOrder.Status != nil &&
					*parentOrder.Status == enum.TPLCallbackStatus.STORING &&
					*prd.Status == enum.ProductStatus.STORING {
					continue
				}
				isAllFinished = false
			}

			if isAllFinished {
				needCompleteParent = true
			} else if needCheckChild {
			} else {
				needRemoveTransport = true
			}
		}
		if needCompleteParent {
			completeParentCodes = append(completeParentCodes, binCode.ParentReferenceCode)
		} else if needRemoveTransport {
			removeTransportCodes = append(removeTransportCodes, binCode)
		} else {
			// Nếu đơn lấy hàng cha đã tách bin con thì tìm bin con để complete
			completeChildCodes = append(completeChildCodes, binCode)
		}
	}

	if len(completeParentCodes) > 0 {
		return CompleteShippingOrder(completeParentCodes, updateBy)
	}

	if len(completeChildCodes) > 0 {
		return CompleteChildShippingOrder(completeChildCodes, updateBy)
	}

	if len(removeTransportCodes) > 0 {
		return RemoveTransportBin(removeTransportCodes, updateBy)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Hoàn tất hoàn thành đơn",
	}
}

func CompleteChildShippingOrder(parents []request.CompleteBinCode, updateBy int64) *common.APIResponse {
	var needCompleteChildren []string
	var parentCodes []string
	var parentBinCodes []string

	for _, p := range parents {
		parentCodes = append(parentCodes, p.ParentReferenceCode)
		parentBinCodes = append(parentBinCodes, p.BinCode)
	}

	// Đề phòng trường hợp loop vô tận
	maxLoopCount := 0

	for {
		maxLoopCount++
		if maxLoopCount > 20 {
			_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
				CmdType: "INFINITE_LOOP_DETECTED",
				Title:   "INFINITE_LOOP_DETECTED",
				Message: fmt.Sprintf("%v", parents),
			})
			break
		}

		// Tìm mã đơn con mà mã bin chưa bị xóa
		filter := bson.M{
			"parent_reference_code": bson.M{
				"$in": parentCodes,
			},
			"status": bson.M{
				"$nin": []string{
					string(enum.HubShippingOrderStatus.DONE_TRANSPORTING),
				},
			},
			"products": bson.M{
				"$elemMatch": bson.M{
					"sku": bson.M{
						"$in": parentBinCodes,
					},
				},
			},
		}

		hubOrderRaw := model.HUBShippingOrderDB.Query(filter, 0, 20, nil)
		if hubOrderRaw.Status != common.APIStatus.Ok {
			break
		}

		hubOrders := hubOrderRaw.Data.([]*model.HubShippingOrder)
		parentCodes = nil
		for _, hubOrder := range hubOrders {
			parentCodes = append(parentCodes, hubOrder.ReferenceCode)
			for _, p := range hubOrder.Products {
				if CheckItemInArray(p.SKU, parentBinCodes) && p.Status != nil && *p.Status != enum.ProductStatus.REMOVED {
					needCompleteChildren = append(needCompleteChildren, hubOrder.ReferenceCode)
				}
			}
		}
	}

	if len(needCompleteChildren) > 0 {
		return CompleteShippingOrder(needCompleteChildren, updateBy)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Hoàn tất hoàn thành đơn",
	}

}

func RemoveTransportBin(parents []request.CompleteBinCode, updateBy int64) *common.APIResponse {
	for _, p := range parents {
		orderRaw := model.ShippingOrderDB.QueryOne(bson.M{
			"reference_code": p.ParentReferenceCode,
		})
		if orderRaw.Status != common.APIStatus.Ok {
			continue
		}

		hubOrderRaw := model.HUBShippingOrderDB.Query(bson.M{
			"reference_code": p.ParentReferenceCode,
		}, 0, 100, nil)
		if hubOrderRaw.Status != common.APIStatus.Ok {
			continue
		}
		hubOrders := hubOrderRaw.Data.([]*model.HubShippingOrder)
		toDepartmentCode := ""
		mapHubShippingOrder := map[string]*model.HubShippingOrder{}
		for _, hubOrder := range hubOrders {
			mapHubShippingOrder[hubOrder.ReferenceCode] = hubOrder
			if hubOrder.Status != nil &&
				*hubOrder.Status == enum.HubShippingOrderStatus.WAIT_TO_STORING {
				isRemovedAll := true
				newHubOrderStatus := enum.HubShippingOrderStatus.WAIT_TO_STORING
				for _, product := range hubOrder.Products {
					if product.SKU == p.BinCode {
						product.Status = &enum.ProductStatus.REMOVED
					}
					if product.Status != nil && *product.Status != enum.ProductStatus.REMOVED {
						isRemovedAll = false
					}
				}
				if isRemovedAll {
					newHubOrderStatus = enum.HubShippingOrderStatus.COMPLETED
				}
				toDepartmentCode = hubOrder.HUBCode
				model.HUBShippingOrderDB.UpdateOne(bson.M{
					"reference_code": p.ParentReferenceCode,
					"hub_code":       hubOrder.HUBCode,
				}, bson.M{
					"products": hubOrder.Products,
					"status":   &newHubOrderStatus,
				})
			}
		}
		if toDepartmentCode != "" {
			handoverTicketRaw := model.HandoverTicketDB.QueryOne(bson.M{
				"so_list.so":         p.ParentReferenceCode,
				"to_department_code": toDepartmentCode,
				"status": bson.M{
					"$in": []string{
						string(enum.HandoverStatus.TRANSPORTING),
						string(enum.HandoverStatus.WAIT_TO_CHECK),
						string(enum.HandoverStatus.CHECKING),
					},
				},
			})
			if handoverTicketRaw.Status != common.APIStatus.Ok {
				continue
			}
			handoverTicket := handoverTicketRaw.Data.([]*model.HandoverTicket)[0]
			for i, so := range handoverTicket.SOList {
				removedBin := []*model.Product{}
				if so.SO == p.ParentReferenceCode {
					for _, item := range so.Products {
						if item.SKU == p.BinCode {
							item.Status = &enum.ProductStatus.STORING
							removedBin = append(removedBin, item)
						}
					}
					handoverTicket.SOList[i] = so
				}
				if len(removedBin) > 0 {
					_ = shippingOrderQueue.PushCreateHubShippingOrder(shippingOrderQueue.RequestBodyHandover{
						HubOrder:       *mapHubShippingOrder[so.SO],
						RemovedBinList: removedBin,
						ShouldComplete: true,
					}, so.SO)
				}
			}
			model.HandoverTicketDB.UpdateOne(bson.M{
				"ticket_id": handoverTicket.TicketID,
			}, handoverTicket)
		}
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Hoàn tất hoàn thành đơn",
	}
}

func IsReturnOrder(shippingOrder model.ShippingOrder) bool {
	return (shippingOrder.ShippingType != nil &&
		*shippingOrder.ShippingType == enum.ShippingOrderType.RETURN) ||
		(shippingOrder.Status != nil &&
			(*shippingOrder.Status == enum.TPLCallbackStatus.RETURN ||
				*shippingOrder.Status == enum.TPLCallbackStatus.RETURNING ||
				*shippingOrder.Status == enum.TPLCallbackStatus.RETURN_FAIL ||
				*shippingOrder.Status == enum.TPLCallbackStatus.RETURNED))
}

func UpdateFMPackage(referenceCode string, numPack int64) *common.APIResponse {
	if referenceCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã liên kết không được để trống",
		}
	}

	if numPack <= 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Số kiện phải lớn hơn 0",
		}
	}

	shippingOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
		"reference_code": referenceCode,
	})
	if shippingOrderRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không tìm thấy đơn hàng",
		}
	}
	shippingOrder := shippingOrderRaw.Data.([]*model.ShippingOrder)[0]

	if shippingOrder.IsBookDropOff == nil || *shippingOrder.ShippingType != enum.ShippingOrderType.FMPO {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không phải đơn PO/PGH",
		}
	}

	hubShippingOrderRaw := model.HUBShippingOrderDB.QueryOne(bson.M{
		"reference_code": referenceCode,
		"hub_code":       shippingOrder.CurrentHub,
	})
	if hubShippingOrderRaw.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không tìm thấy đơn hàng",
		}
	}

	hubShippingOrder := hubShippingOrderRaw.Data.([]*model.HubShippingOrder)[0]

	updater := bson.M{
		"num_package": numPack,
	}
	var feeAmount = 0.0
	if (!*shippingOrder.IsBookDropOff &&
		*shippingOrder.Status == enum.TPLCallbackStatus.READY_TO_PICK &&
		*hubShippingOrder.Status == enum.HubShippingOrderStatus.READY_TO_PICK) ||
		(*shippingOrder.IsBookDropOff &&
			*shippingOrder.Status == enum.TPLCallbackStatus.PICKED &&
			*hubShippingOrder.Status == enum.HubShippingOrderStatus.WAIT_TO_STORING) {
		if shippingOrder.FeeCollectMethod != nil &&
			*shippingOrder.FeeCollectMethod == enum.FeeCollectMethod.SENDER_PAY {
			query := bson.M{
				"code": shippingOrder.FromCustomerCode,
			}
			if *shippingOrder.IsBookDropOff {
				query["applied_fees.config_fee_type"] = enum.ConfigFeeType.TRANSPORT
			} else {
				query["applied_fees.config_fee_type"] = enum.ConfigFeeType.PICK_N_TRANSPORT
			}

			var configFee *model.ConfigFee
			customerRaw := model.CustomerDB.QueryOne(query)
			if customerRaw.Status == common.APIStatus.Ok {
				customer := customerRaw.Data.([]*model.Customer)[0]
				var configFeeId int64
				for _, fee := range *customer.AppliedFees {
					if *shippingOrder.IsBookDropOff {
						if fee.ConfigFeeType == enum.ConfigFeeType.TRANSPORT {
							configFeeId = fee.ConfigFeeId
						}
					} else {
						if fee.ConfigFeeType == enum.ConfigFeeType.PICK_N_TRANSPORT {
							configFeeId = fee.ConfigFeeId
						}
					}
				}
				configFeeRaw := model.ConfigFeeDB.QueryOne(bson.M{
					"config_id": configFeeId,
				})
				if configFeeRaw.Status != common.APIStatus.Ok {
					return &common.APIResponse{
						Status:  common.APIStatus.Invalid,
						Message: "Không tìm thấy cấu hình phí vận chuyển",
					}
				}
				configFee = configFeeRaw.Data.([]*model.ConfigFee)[0]
			}
			if configFee == nil {
				var configCode string
				if *shippingOrder.IsBookDropOff {
					configCode = conf.Config.DefaultFMDropOffFeeCode
				} else {
					configCode = conf.Config.DefaultFMPickUpFeeCode
				}

				configFeeRaw := model.ConfigFeeDB.QueryOne(bson.M{
					"code": configCode,
				})

				if configFeeRaw.Status != common.APIStatus.Ok {
					return &common.APIResponse{
						Status:  common.APIStatus.Invalid,
						Message: "Không tìm thấy cấu hình phí vận chuyển",
					}
				}
				configFee = configFeeRaw.Data.([]*model.ConfigFee)[0]
			}
			shippingOrder.NumPackage = numPack
			bookRequest := shippingOrderToBookShippingReq(shippingOrder)
			var err error
			feeAmount, err = CalculateOrderFee(bookRequest, configFee)

			if err != nil {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   "Không thể tính phí",
					ErrorCode: "CANT_COMPUTE_FEE",
				}
			}
			if feeAmount != shippingOrder.FeeAmount {
				updater["fee_amount"] = feeAmount
				updater["total_collect_sender_amount"] = feeAmount
				updater["cod_amount"] = feeAmount
				updater["fee_sender_amount"] = feeAmount
			}

		}
		updateShippingOrder := model.ShippingOrderDB.UpdateOne(bson.M{
			"reference_code": referenceCode,
		}, updater)

		if updateShippingOrder.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không thể cập nhật số kiện",
			}
		}

		updateHubOrder := model.HUBShippingOrderDB.UpdateOne(bson.M{
			"reference_code": referenceCode,
			"hub_code":       shippingOrder.CurrentHub,
		}, updater)

		if updateHubOrder.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không thể cập nhật số kiện",
			}
		}

		type FeeResponse struct {
			ReferenceCode string  `json:"referenceCode"`
			FeeAmount     float64 `json:"feeAmount"`
		}

		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Cập nhật số kiện thành công",
			Data: []FeeResponse{
				{
					ReferenceCode: referenceCode,
					FeeAmount:     feeAmount,
				},
			},
		}

	} else {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Status không phù hợp để cập nhật số kiện",
		}
	}
}

func CompleteRSO(rsos []request.ReconcileSessionOrder, updateBy int64) *common.APIResponse {
	var errors []*common.Error
	current := time.Now()

	for _, rso := range rsos {
		shippingOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{
			"reference_code": rso.ReferenceCode,
		})
		shippingOrder := shippingOrderRaw.Data.([]*model.ShippingOrder)[0]
		// Nếu đơn chưa gộp mà hoàn thành đơn thì check là đang đố soát cod hay fee
		if rso.IsMerged == nil ||
			(rso.IsMerged != nil && !*rso.IsMerged) {
			isReconcileCod := false
			isReconcileFee := false
			// Nếu đơn đã đs cod rồi thì check xem đơn này đối soát fee chưa
			if shippingOrder.IsReconciledCod != nil && *shippingOrder.IsReconciledCod {
				isReconcileCod = true
				if rso.DeliveryFeeAmount != nil {
					isReconcileFee = true
				}
			}
			// Nếu đơn đã đs fee rồi thì check xem đơn này đối soát cod chưa
			if shippingOrder.IsReconciledFee != nil && *shippingOrder.IsReconciledFee {
				isReconcileFee = true
				if rso.DeliveryFeeAmount == nil {
					isReconcileCod = true
				}
			}

			// Nếu chưa đối soát cod và fee
			if !isReconcileCod && !isReconcileFee {
				if rso.DeliveryFeeAmount == nil {
					isReconcileCod = true
				}
				if rso.DeliveryFeeAmount != nil {
					isReconcileFee = true
				}
			}

			// 1 trong 2 chưa đối soát thì không được hoàn thành đơn
			if !isReconcileCod || !isReconcileFee {
				updateShippingOrderResult := model.ShippingOrderDB.UpdateOne(bson.M{
					"reference_code": shippingOrder.ReferenceCode,
				}, bson.M{
					"is_reconcile_fee": isReconcileFee,
					"is_reconcile_cod": isReconcileCod,
				})
				if updateShippingOrderResult.Status != common.APIStatus.Ok {
					errors = append(errors, &common.Error{
						Message: "Không thể cập nhật trạng thái đơn " + shippingOrder.ReferenceCode,
					})
				}
				continue
			}
		}

		updateShippingOrderResult := model.ShippingOrderDB.UpdateOne(bson.M{
			"reference_code": shippingOrder.ReferenceCode,
		}, bson.M{
			"status":            &enum.TPLCallbackStatus.COMPLETED,
			"last_updated_by":   strconv.FormatInt(updateBy, 10),
			"last_updated_time": &current,
			"action_time":       current.Unix(),
			"is_reconcile_fee":  true,
			"is_reconcile_cod":  true,
		})

		if updateShippingOrderResult.Status != common.APIStatus.Ok {
			errors = append(errors, &common.Error{
				Message: "Không thể cập nhật trạng thái đơn " + shippingOrder.ReferenceCode,
			})
			continue
		}

		updateHubOrderResult := model.HUBShippingOrderDB.UpdateOne(bson.M{
			"reference_code": shippingOrder.ReferenceCode,
			"hub_code":       shippingOrder.CurrentHub,
		}, bson.M{
			"status":            &enum.HubShippingOrderStatus.COMPLETED,
			"last_updated_by":   strconv.FormatInt(updateBy, 10),
			"last_updated_time": &current,
			"action_time":       current.Unix(),
		})

		if updateHubOrderResult.Status != common.APIStatus.Ok {
			errors = append(errors, &common.Error{
				Message: "Không thể cập nhật trạng thái đơn " + shippingOrder.ReferenceCode + " tại hub " + shippingOrder.CurrentHub,
			})
			continue
		}

		callback := request.Callback{
			ActionTime:    &current,
			SO:            shippingOrder.ReferenceCode,
			Status:        &enum.TPLCallbackStatus.COMPLETED,
			StatusName:    "Đã hoàn thành.",
			TPLStatus:     string(enum.TPLCallbackStatus.COMPLETED),
			TPLStatusName: "Đã hoàn thành.",
			Type:          shippingOrder.ShippingType,
		}
		_ = sync_data.PushCreateTPLCallbackQueue(callback, callback.SO)
	}

	if len(errors) > 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Đã có lỗi xuất hiện khi hoàn thành giao hàng",
			Data:    errors,
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Hoàn tất hoàn thành đơn",
	}
}

func CountReadyToTransport(req request.CountReadyToTransferRequest) *common.APIResponse {
	toHubResp := model.HubDB.QueryOne(
		bson.M{
			"code": req.ToHubCode,
		})
	if toHubResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không tìm thấy hub",
		}
	}
	tohub := toHubResp.Data.([]*model.Hub)[0]
	defaultCarrier := tohub.DefaultCarrierId
	filter := bson.M{
		"status": bson.M{
			"$in": req.Statuses,
		},
		"current_hub":    req.FromHubCode,
		"tpl_service_id": defaultCarrier,
		"type":           req.Type,
	}
	var limit int64 = 200
	totalOrder := 0
	totalPackage := int64(0)
	for {
		shippingOrdersRaw := model.ShippingOrderDB.Query(
			filter, 0,
			limit,
			&bson.M{"_id": -1},
		)
		if shippingOrdersRaw.Status != common.APIStatus.Ok {
			break
		}
		shippingOrders := shippingOrdersRaw.Data.([]*model.ShippingOrder)
		smallestId := shippingOrders[len(shippingOrders)-1].ID
		filter["_id"] = bson.M{
			"$lt": smallestId,
		}
		for _, order := range shippingOrders {
			totalOrder++
			totalPackage += order.NumPackage
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
		Data: []response.CountReadyToTransferResponse{
			{
				TotalOrder:   totalOrder,
				TotalPackage: int(totalPackage),
			},
		},
	}
}
