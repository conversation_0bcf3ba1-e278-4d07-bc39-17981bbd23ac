package model

import (
	"time"

	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
)

type GHNService struct {
	ServiceId     int64  `json:"service_id"`
	ShortName     string `json:"short_name"`
	ServiceTypeId int64  `json:"service_type_id"`
}

type GHNOrder struct {
	ShopId             int64                `json:"shop_id"`
	ClientId           int64                `json:"client_id"`
	ReturnName         string               `json:"return_name"`
	ReturnPhone        string               `json:"return_phone"`
	ReturnAddress      string               `json:"return_address"`
	ReturnWardCode     string               `json:"return_ward_code"`
	ReturnDistrict     int64                `json:"return_district"`
	FromName           string               `json:"from_name"`
	FromPhone          string               `json:"from_phone"`
	FromAddress        string               `json:"from_address"`
	FromWardCode       string               `json:"from_ward_code"`
	FromDistrictId     string               `json:"from_district_id"`
	DeliveryStationId  int64                `json:"delivery_station_id"`
	ToName             string               `json:"to_name"`
	ToPhone            string               `json:"to_phone"`
	ToAddress          string               `json:"to_address"`
	ToWardCode         string               `json:"to_ward_code"`
	ToDistrictId       int64                `json:"to_district_id"`
	Weight             float64              `json:"weight"`
	Height             float64              `json:"height"`
	Length             float64              `json:"length"`
	Width              float64              `json:"width"`
	ConvertedWeight    float64              `json:"converted_weight"`
	ServiceTypeId      int                  `json:"service_type_id"`
	ServiceId          int                  `json:"service_id"`
	PaymentMethod      int                  `json:"payment_method"`
	CustomerServiceFee float64              `json:"customer_service_fee"`
	CodAmount          float64              `json:"cod_amount"`
	OrderCode          string               `json:"order_code"`
	Status             *enum.GHNStatusValue `json:"status"`
	ClientOrderCode    string               `json:"client_order_code"`
	UpdateDate         *time.Time           `json:"update_date"`
	CurrentWarehouse   string               `json:"current_warehouse"`
}
