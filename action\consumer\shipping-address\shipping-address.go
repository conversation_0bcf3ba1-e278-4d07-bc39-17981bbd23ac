package shipping_address

import (
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/utils"
	"go.mongodb.org/mongo-driver/bson"
)

func (j *ExecutorJob) createShippingAddress() {
	j.Job.SetTopicConsumer(defaultTopic, func(item *job.JobItem) (err error) {
		defer utils.RecoverFromPanic(item.Data)

		var dataByte []byte
		dataByte, err = bson.Marshal(item.Data)
		if err != nil {
			return
		}

		var address model.ShippingAddress
		err = bson.Unmarshal(dataByte, &address)
		if err != nil {
			return
		}

		addressResult := model.ShippingAddressDB.Query(&bson.M{
			"customer_code": address.CustomerCode,
			"ward_code":     address.WardCode,
		}, 0, 100, nil)

		if addressResult.Status != common.APIStatus.Ok {
			address.ID = model.GenId("SHIPPING_ADDRESS_ID")
			address.CreatedTime = utils.Now()

			createResult := model.ShippingAddressDB.Create(address)
			if createResult.Status == common.APIStatus.Ok {
				if address.NearestOrder != "" {
					// Old data may not contain hub order type
					if address.HubOrderType == nil ||
						*address.HubOrderType == enum.HubOrderType.TRANSPORTING ||
						*address.HubOrderType == enum.HubOrderType.DELIVERY {
						_ = model.HUBShippingOrderDB.UpdateMany(bson.M{
							"reference_code": address.NearestOrder,
						}, bson.M{
							"to_address_id": address.ID,
						})

						return nil
					}

					if *address.HubOrderType == enum.HubOrderType.RETURN || *address.HubOrderType == enum.HubOrderType.PICKUP {
						_ = model.HUBShippingOrderDB.UpdateMany(bson.M{
							"reference_code": address.NearestOrder,
						}, bson.M{
							"from_address_id": address.ID,
						})

						return nil
					}

				}
				return nil
			}

			return fmt.Errorf("Create new shipping address failed: %v", createResult)
		} else {
			addressExisted := false
			storedAddress := addressResult.Data.([]*model.ShippingAddress)
			// Check if not match create new shipping address
			for _, ele := range storedAddress {
				if utils.ConvertToRawText(address.CustomerAddress) ==
					utils.ConvertToRawText(ele.CustomerAddress) {
					addressExisted = true
					break
				}
			}

			if !addressExisted {
				address.ID = model.GenId("SHIPPING_ADDRESS_ID")
				address.CreatedTime = utils.Now()

				createResult := model.ShippingAddressDB.Create(address)
				if createResult.Status == common.APIStatus.Ok {
					if address.NearestOrder != "" {
						if address.HubOrderType == nil ||
							*address.HubOrderType == enum.HubOrderType.TRANSPORTING ||
							*address.HubOrderType == enum.HubOrderType.DELIVERY {
							_ = model.HUBShippingOrderDB.UpdateMany(bson.M{
								"reference_code": address.NearestOrder,
							}, bson.M{
								"to_address_id": address.ID,
							})

							return nil
						}

						if *address.HubOrderType == enum.HubOrderType.RETURN || *address.HubOrderType == enum.HubOrderType.PICKUP {
							_ = model.HUBShippingOrderDB.UpdateMany(bson.M{
								"reference_code": address.NearestOrder,
							}, bson.M{
								"from_address_id": address.ID,
							})

							return nil
						}
					}

					return nil
				}

				return fmt.Errorf("Create new shipping address failed: %v", createResult)
			}
		}

		return err
	})
}
