package model

import "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"

type GHTKOrder struct {
	PartnerId  string                `json:"partner_id"`
	LabelId    string                `json:"label_id"`
	Status     *enum.GHTKStatusValue `json:"status"`
	ReasonCode string                `json:"reason_code"`
	Reason     string                `json:"reason"`
	Weight     float64               `json:"weight"`
	Fee        float64               `json:"fee"`
	Modified   string                `json:"modified"`
	PickMoney  int64                 `json:"pick_money"`
}

type GHTKShippingFee struct {
	PartnerId  string                `json:"partner_id"`
	LabelId    string                `json:"label_id"`
	Status     *enum.GHTKStatusValue `json:"status"`
	ReasonCode string                `json:"reason_code"`
	Reason     string                `json:"reason"`
	Weight     float64               `json:"weight"`
	Fee        float64               `json:"fee"`
	Modified   string                `json:"modified"`
	PickMoney  int64                 `json:"pick_money"`
}
