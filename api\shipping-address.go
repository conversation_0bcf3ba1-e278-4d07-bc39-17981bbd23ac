package api

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
)

// UpdateShippingAddress api
func UpdateShippingAddress(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.ShippingAddress
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	return resp.Respond(action.UpdateShippingAddress(&input))
}

func MigrateShippingAddress(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.MigrateShippingAddress())
}
