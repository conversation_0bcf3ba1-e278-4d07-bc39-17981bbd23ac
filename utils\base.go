package utils

import (
	"fmt"
	"hash/fnv"
	"log"
	"net/url"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"go.mongodb.org/mongo-driver/bson"
)

func Hash(s string) uint32 {
	h := fnv.New32a()
	h.Write([]byte(s))
	return h.Sum32()
}

func Now() *time.Time {
	now := time.Now()
	return &now
}

func ConvertToRawText(s string) string {
	s = strings.TrimSpace(s)
	s = strings.ToLower(s)
	s = strings.ReplaceAll(s, "  ", " ")

	// Remove all special characters

	return s
}

func StringSliceContain(s []string, e string) bool {
	for _, a := range s {
		if a == e {
			return true
		}
	}
	return false
}

func Subslice(s1 []string, s2 []string) bool {
	if len(s1) > len(s2) {
		return false
	}
	for _, e := range s1 {
		if !StringSliceContain(s2, e) {
			return false
		}
	}
	return true
}

// s1 = [1,2,3] cu
// s2 = [3,4,5] moi
// => [1,2]

func GetRemovedElement(s1 []string, s2 []string) (removed []string) {
	hash := make(map[string]bool)
	for _, e := range s2 {
		hash[e] = true
	}

	for _, e := range s1 {
		if !hash[e] {
			removed = append(removed, e)
		}
	}
	return
}

func RemoveStringFromStrings(removeString string, arr []string) (newString []string) {
	for _, s := range arr {
		if s != removeString {
			newString = append(newString, s)
		}
	}
	return newString
}
func CommonStringsFromStringSlices(s1 []string, s2 []string) []string {
	common := []string{}
	hash := make(map[string]bool)
	for _, s := range s1 {
		hash[s] = true
	}

	for _, s := range s2 {
		if hash[s] {
			common = append(common, s)
		}
	}
	return common
}

func CheckExistInEnum(input interface{}, baseObj interface{}) bool {
	v := reflect.ValueOf(baseObj)
	for i := 0; i < v.NumField(); i++ {
		if input == v.Field(i).Interface() {
			return true
		}
	}

	return false
}

func IsUrl(str string) bool {
	u, err := url.Parse(str)
	return err == nil && u.Scheme != "" && u.Host != ""
}

func RecoverFromPanic(data interface{}) {
	if r := recover(); r != nil {
		errStr := fmt.Sprintf("%v", data)
		_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
			CmdType: "RECOVER_FROM_PANIC",
			Title:   errStr,
			Message: fmt.Sprintf("%v", r),
		})
	}
}

func IsEmptyString(s string) bool {
	return strings.TrimSpace(s) == ""
}

func RemoveDuplicateStr(strSlice []string) []string {
	allKeys := make(map[string]bool)
	list := []string{}
	for _, item := range strSlice {
		if _, value := allKeys[item]; !value {
			allKeys[item] = true
			list = append(list, item)
		}
	}
	return list
}

func MoneyFormat(amount float64) string {
	var value string
	amountStr := strconv.FormatFloat(amount, 'f', -1, 64)
	if len(amountStr) < 3 {
		return amountStr
	}
	commaIndex := len(amountStr) % 3
	if commaIndex == 0 {
		commaIndex = 3
	}
	value += amountStr[0:commaIndex]
	indexValue := len(value) + 1
	for index := indexValue; index <= len(amountStr); index++ {
		if index%3 == 0 {
			value += fmt.Sprintf(",%s", amountStr[commaIndex:commaIndex+3])
			commaIndex += 3
		}
	}
	return value
}

func EnumToStringSlice(baseObj interface{}) []string {
	var result []string
	v := reflect.ValueOf(baseObj)
	for i := 0; i < v.NumField(); i++ {
		result = append(result, v.Field(i).String())
	}
	return result
}

// Query lock avoid to multiple instance calling as same time
func CheckJobLockAvailable(key string, repeatTimeJob int) bool {
	now := time.Now()
	lockQuery := model.SyncLockDB.QueryOne(bson.M{"key": key})
	if lockQuery.Status == common.APIStatus.Ok {
		lock := lockQuery.Data.([]*model.SyncLock)[0]
		nextTimeRun := now.Sub(lock.NextUpdatedTime).Seconds()
		if nextTimeRun >= 0 {
			nextUpdatedTime := now.Add(time.Duration(repeatTimeJob) * time.Second)
			update := model.SyncLockDB.UpdateOne(
				bson.M{
					"key":        key,
					"version_no": lock.VersionNo},
				bson.M{
					"version_no":        uuid.NewString(),
					"next_updated_time": nextUpdatedTime,
				})
			if update.Status != common.APIStatus.Ok {
				return false
			}
		} else {
			return false
		}
	} else {
		log.Println("[CheckJobLockAvailable] Không tìm thấy dữ liệu DB SyncLock theo key", key, lockQuery.ErrorCode, lockQuery.Message)
		return false
	}
	return true
}

func CheckApiLockAvailable(key string, lockType string) bool {
	isAvailable := model.CacheDB.Create(model.Cache{
		Key:  key,
		Type: lockType,
	})

	if isAvailable.Status != common.APIStatus.Ok {
		return false
	}
	return true
}

func MergeMaps(maps ...map[string]interface{}) (result map[string]interface{}) {
	result = make(map[string]interface{})
	for _, m := range maps {
		for k, v := range m {
			result[k] = v
		}
	}
	return result
}

func CeilFloat(val float64) float64 {
	// round
	x := int64(val * 1000)
	y := x % 10
	if y > 4 {
		x = x - y + 10
	} else {
		x = x - y
	}

	return float64(x) / 1000
}

func MergeUniqueStringSlice(slices ...[]string) (result []string) {
	hash := make(map[string]bool)
	for _, slice := range slices {
		for _, s := range slice {
			if !hash[s] {
				hash[s] = true
				result = append(result, s)
			}
		}
	}
	return
}

func CheckContainValue(data interface{}, value interface{}) bool {
	if data == nil || value == nil {
		return false
	}
	if reflect.TypeOf(data).String() != "[]"+reflect.TypeOf(value).String() {
		return false
	}

	switch data.(type) {
	case []int:
		for _, v := range data.([]int) {
			if v == value {
				return true
			}
		}
		break
	case []int32:
		for _, v := range data.([]int32) {
			if v == value {
				return true
			}
		}
		break
	case []int64:
		for _, v := range data.([]int64) {
			if v == value {
				return true
			}
		}
		break
	case []float32:
		for _, v := range data.([]float32) {
			if v == value {
				return true
			}
		}
		break
	case []float64:
		for _, v := range data.([]float64) {
			if v == value {
				return true
			}
		}
		break
	case []string:
		for _, v := range data.([]string) {
			if v == value {
				return true
			}
		}
		break
	default:
		return false
	}
	return false
}

func AppendStrIfNotExists(slice []string, item ...string) []string {
	for _, i := range item {
		if !StringSliceContain(slice, i) {
			slice = append(slice, i)
		}
	}
	return slice
}

func NotEmptyStrArr(item ...string) []string {
	var slice []string
	for _, i := range item {
		if i != "" {
			slice = append(slice, i)
		}
	}
	return slice
}
func BsonDContains(bsonD bson.D, key string) bool {
	for _, e := range bsonD {
		if e.Key == key {
			return true
		}
	}
	return false
}

func AppendIfUnique(slice []string, str string) []string {
	if !StringSliceContain(slice, str) {
		return append(slice, str)
	}
	return slice
}
