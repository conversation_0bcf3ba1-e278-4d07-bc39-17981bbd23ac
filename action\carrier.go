package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/shipping"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// CreateCarrier action
func CreateCarrier(input *model.Carrier) *common.APIResponse {
	if input.CarrierId == 0 {
		input.CarrierId = model.GenId("CARRIER_ID")
	} else {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "ID nhà vận chuyển không hợp lệ",
		}
	}

	if input.CarrierName == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Tên nhà vận chuyển không được để trống",
		}
	}

	if input.CarrierInternalName == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Tên nhà vận chuyển không được để trống",
		}
	}

	if input.CarrierCode == nil || *input.CarrierCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Mã nhà vận chuyển không được để trống",
		}
	}

	// Check CarrierCode exists
	existCarrier := model.CarrierDB.QueryOne(&bson.M{
		"carrier_code": *input.CarrierCode,
	})

	if existCarrier.Status == common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Existed,
			Message:   "Mã nhà vận chuyển đã tồn tại",
			ErrorCode: "CARRIER_CODE_EXISTED",
		}
	}

	newKeyword, err := utils.GenKeyword(string(*input.CarrierCode), input.CarrierName, input.CarrierInternalName, input.CarrierId)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không thể tạo từ khóa cho carrier",
		}
	}
	input.Keyword = newKeyword

	createCallbackResult := model.CarrierDB.Create(input)

	if createCallbackResult.Status != common.APIStatus.Ok {
		return createCallbackResult
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Tạo nhà vận chuyển thành công",
	}
}

// GetCarrier action
func GetCarrier(query *request.GetCarrierRequest, offset int64, limit int64, getTotal bool) *common.APIResponse {
	filter := bson.M{}

	if query.CarrierName != "" {
		filter["carrier_name"] = bson.M{
			"$regex": query.CarrierName,
		}
	}

	if query.IsChild {
		filter["parent_code"] = bson.M{
			"$ne": "",
		}
	}

	if query.Keyword != "" {
		filter["keyword"] = bson.M{
			"$regex": utils.ToRawText(query.Keyword),
		}
	}

	if query.CarrierCode != nil {
		filter["carrier_code"] = query.CarrierCode

	}

	if query.CarrierId != 0 {
		filter["carrier_id"] = query.CarrierId
	}

	if query.CarrierInternalName != "" {
		filter["carrier_internal_name"] = query.CarrierInternalName
	}

	if query.Active != nil {
		filter["active"] = query.Active
	}

	if query.IsInternal != nil {
		filter["is_internal"] = query.IsInternal
	}

	if query.ParentCode != nil {
		filter["parent_code"] = query.ParentCode
	}

	if query.Service != "" {
		filter["service"] = query.Service
	}

	if query.PickupCode != "" {
		filter["pickup_code"] = query.PickupCode
	}

	if query.PaymentMethod != "" {
		filter["payment_method"] = query.PaymentMethod
	}

	if query.PickupType != 0 {
		filter["pickup_type"] = query.PickupType
	}

	result := model.CarrierDB.Query(filter, offset, limit, &primitive.M{"_id": -1})
	if getTotal {
		countResult := model.CarrierDB.Count(filter)
		result.Total = countResult.Total
	}
	return result
}

// UpdateCarrier action
func UpdateCarrier(input *model.Carrier) *common.APIResponse {
	if input.CarrierId == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Mã nhà vận chuyển không được để trống",
		}
	}

	if input.CarrierName != "" {
		carrierRaw := model.CarrierDB.QueryOne(bson.M{
			"carrier_id": input.CarrierId,
		})
		if carrierRaw.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Carrier id không hợp lệ",
			}
		}
		carrier := carrierRaw.Data.([]*model.Carrier)[0]

		newKeyword, err := utils.GenKeyword(string(*carrier.CarrierCode), input.CarrierName, carrier.CarrierInternalName, carrier.CarrierId)
		if err != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không thể tạo từ khóa cho nhà vận chuyển",
			}
		}
		input.Keyword = newKeyword
	}

	afterOption := options.After
	createCallbackResult := model.CarrierDB.UpdateOne(
		bson.M{
			"carrier_id": input.CarrierId,
		},
		input,
		&options.FindOneAndUpdateOptions{
			Upsert:         &enum.True,
			ReturnDocument: &afterOption,
		})

	if createCallbackResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Bad request when Update carrier",
		}
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật nhà vận chuyển thành công",
	}
}

// DeleteCarrier action
func DeleteCarrier(carrierId int64) *common.APIResponse {
	return model.CarrierDB.Delete(
		bson.M{
			"carrier_id": carrierId,
		})
}

func LoadCarrierInfo() {
	carriers := model.CarrierDB.Query(bson.M{
		"parent_code": "",
	}, 0, 1000, nil)

	if carriers.Status != common.APIStatus.Ok || carriers.Data == nil {
		return
	}

	carrierInfo := carriers.Data.([]*model.Carrier)

	if conf.Config.ConfigCarrier == nil {
		conf.Config.ConfigCarrier = &conf.ConfigCarrier{
			GHTKConfig:     &conf.GHTKConfig{},
			VTPConfig:      &conf.VTPConfig{},
			SnappyConfig:   &conf.SnappyConfig{},
			AhamoveConfig:  &conf.AhamoveConfig{},
			GHNConfig:      &conf.GHNConfig{},
			NhatTinConfig:  &conf.NhatTinConfig{},
			VNPostConfig:   &conf.VNPostConfig{},
			NinjavanConfig: &conf.NinjavanConfig{},
			BeConfig:       &conf.BeConfig{},
		}
	}

	for _, carrier := range carrierInfo {
		if carrier.ExtraData == nil {
			continue
		}
		switch *carrier.CarrierCode {
		case enum.Partner.VIETTEL_POST:
			conf.Config.ConfigCarrier.VTPConfig = &conf.VTPConfig{
				Domain: carrier.ExtraData.Domain,
			}
			break
		case enum.Partner.GHN:
			conf.Config.ConfigCarrier.GHNConfig = &conf.GHNConfig{
				Domain: carrier.ExtraData.Domain,
			}
			break
		case enum.Partner.GHTK:
			conf.Config.ConfigCarrier.GHTKConfig = &conf.GHTKConfig{
				Domain: carrier.ExtraData.Domain,
			}
			break
		case enum.Partner.SNAPPY:
			conf.Config.ConfigCarrier.SnappyConfig = &conf.SnappyConfig{
				Domain: carrier.ExtraData.Domain,
			}
			break
		case enum.Partner.AHAMOVE:
			conf.Config.ConfigCarrier.AhamoveConfig = &conf.AhamoveConfig{
				Domain: carrier.ExtraData.Domain,
			}
			break
		case enum.Partner.NHAT_TIN:
			conf.Config.ConfigCarrier.NhatTinConfig = &conf.NhatTinConfig{
				Domain: carrier.ExtraData.Domain,
			}
			break
		case enum.Partner.VNPOST:
			conf.Config.ConfigCarrier.VNPostConfig = &conf.VNPostConfig{
				Domain: carrier.ExtraData.Domain,
			}
			break
		case enum.Partner.NINJAVAN:
			conf.Config.ConfigCarrier.NinjavanConfig = &conf.NinjavanConfig{
				Domain: carrier.ExtraData.Domain,
			}
			break
		case enum.Partner.BE:
			conf.Config.ConfigCarrier.BeConfig = &conf.BeConfig{
				Domain: carrier.ExtraData.Domain,
				Proxy:  carrier.ExtraData.Proxy,
			}
		}
	}
	shipping.Init()
}

func GenCarrierKeyword() *common.APIResponse {
	carriersRaw := model.CarrierDB.QueryAll()
	carriers := carriersRaw.Data.([]*model.Carrier)

	for _, carrier := range carriers {
		carrier.Keyword, _ = utils.GenKeyword(string(*carrier.CarrierCode), carrier.CarrierName, carrier.CarrierInternalName, carrier.CarrierId)
		model.CarrierDB.UpdateOne(
			bson.M{
				"carrier_id": carrier.CarrierId,
			},
			bson.M{
				"keyword": carrier.Keyword,
			},
		)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Successfully generated",
	}
}

func UpdateCarrierAccessToken(carrierId int64, accessToken string) *common.APIResponse {
	if carrierId == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Mã nhà vận chuyển không được để trống",
		}
	}

	if accessToken == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Access token không được để trống",
		}
	}

	afterOption := options.After
	createCallbackResult := model.CarrierDB.UpdateOne(
		bson.M{
			"carrier_id": carrierId,
		},
		bson.M{
			"extra_data.access_token": accessToken,
		},
		&options.FindOneAndUpdateOptions{
			ReturnDocument: &afterOption,
		})

	return createCallbackResult
}
