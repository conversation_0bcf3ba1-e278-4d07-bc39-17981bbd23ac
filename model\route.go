package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Route struct {
	// base info
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedBy       string             `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedBy   string             `json:"lastUpdatedBy,omitempty" bson:"last_updated_by,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	VersionNo       string             `json:"versionNo,omitempty" bson:"version_no,omitempty"`

	// Main fields
	RouteCode             string                `json:"routeCode,omitempty" bson:"route_code,omitempty"`
	RouteName             string                `json:"routeName,omitempty" bson:"route_name,omitempty"`
	DropOffPoints         []DropOffPoint        `json:"dropOffPoints,omitempty" bson:"drop_off_points,omitempty"`
	TotalDropOffPoint     int64                 `json:"totalDropOffPoint,omitempty" bson:"total_drop_off_point,omitempty"`
	Distance              float64               `json:"distance,omitempty" bson:"distance,omitempty"`
	Status                enum.RouteStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	Keyword               string                `json:"keyword,omitempty" bson:"keyword,omitempty"`
	EstimatedHandoverTime int64                 `json:"estimatedStartHandoverTime,omitempty"  bson:"estimated_start_handover_time,omitempty"`
	TotalTransportTime    int64                 `json:"totalTransportTime,omitempty" bson:"total_transport_time,omitempty"`
}

type RouteQuery struct {
	Route
	FromEstimatedHandoverTime int64    `json:"fromEstimatedHandoverTime,omitempty"`
	ToEstimatedHandoverTime   int64    `json:"toEstimatedHandoverTime,omitempty"`
	DropOffCodes              []string `json:"dropOffCodes,omitempty"`
}

type DropOffPoint struct {
	Code                      string                      `json:"code,omitempty" bson:"code,omitempty"`
	Location                  Coordinates                 `json:"location,omitempty" bson:"location,omitempty"`
	EstimatedDropOffTime      int                         `json:"estimatedDropOffTime" bson:"estimated_drop_off_time,omitempty"`
	Type                      *enum.DropOffPointTypeValue `json:"type,omitempty" bson:"type,omitempty"`
	Index                     int64                       `json:"index,omitempty" bson:"index,omitempty"`
	DistanceWithNearestPoints []DistanceWithNearestPoint  `json:"distanceWithNearestPoints,omitempty" bson:"distance_with_nearest_points,omitempty"`
}

type DistanceWithNearestPoint struct {
	PointCode string  `json:"pointCode" bson:"point_code,omitempty"`
	Distance  float64 `json:"distance,omitempty" bson:"distance,omitempty"`
}

type Coordinates struct {
	Latitude  float64 `json:"latitude,omitempty" bson:"latitude,omitempty"`
	Longitude float64 `json:"longitude,omitempty" bson:"longitude,omitempty"`
}

var RouteDB = &db.Instance{
	ColName:        "route",
	TemplateObject: &Route{},
}

func InitRouteModel(s *mongo.Database) {
	RouteDB.ApplyDatabase(s)

	t := true
	RouteDB.CreateIndex(bson.D{
		{"drop_off_points.code", 1},
	}, &options.IndexOptions{
		Background: &t,
	})
}
