package model

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Customer struct {
	Id                      primitive.ObjectID             `json:"_id,omitempty" bson:"_id,omitempty"`
	Name                    string                         `json:"name,omitempty" bson:"name,omitempty"`
	Code                    string                         `json:"code,omitempty" bson:"code,omitempty"`
	Phone                   string                         `json:"phone,omitempty" bson:"phone,omitempty"`
	CustomerType            *enum.CustomerTypeValue        `json:"customerType,omitempty" bson:"customer_type,omitempty"`
	CutOffReconcileDay      int                            `json:"cutOffReconcileDay,omitempty" bson:"cut_off_reconcile_day,omitempty"`
	CustomerId              int64                          `json:"customerId,omitempty" bson:"customer_id,omitempty"`
	ExternalId              int64                          `json:"externalId,omitempty" bson:"external_id,omitempty"`
	AppliedFees             *[]AppliedFee                  `json:"appliedFees,omitempty" bson:"applied_fees,omitempty"`
	PreferPaymentMethod     *enum.PreferPaymentMethodValue `json:"preferPaymentMethod,omitempty" bson:"prefer_payment_method,omitempty"`
	Keyword                 string                         `json:"keyword,omitempty" bson:"keyword,omitempty"`
	TotalReadyToPickOrds    int64                          `json:"totalReadyToPickOrds,omitempty" bson:"-"`
	TotalPickedOrds         int64                          `json:"totalPickedOrds,omitempty" bson:"-"`
	TotalWaitToDeliveryOrds int64                          `json:"totalWaitToDeliveryOrds,omitempty" bson:"-"`
	TotalDeliveredOrds      int64                          `json:"totalDeliveredOrds,omitempty" bson:"-"`
	CitizenIdCode           string                         `json:"citizenIdCode,omitempty" bson:"citizen_id_code,omitempty"`
	CustomerBankInfo        *BankInfo                      `json:"customerBankInfo,omitempty" bson:"customer_bank_info,omitempty"`
	Tags                    *[]string                      `json:"tags,omitempty" bson:"tags,omitempty"`
	Note                    *string                        `json:"note,omitempty" bson:"note,omitempty"`
}

type BankInfo struct {
	BankName        string `json:"bankName,omitempty" bson:"bank_name,omitempty"`
	BankBranch      string `json:"bankBranch,omitempty" bson:"bank_branch,omitempty"`
	BankAccountName string `json:"bankAccountName,omitempty" bson:"bank_account_name,omitempty"`
	BankAccountID   string `json:"bankAccountID,omitempty" bson:"bank_account_id,omitempty"`
}

type AppliedFee struct {
	ConfigFeeType enum.ConfigFeeValue `json:"configFeeType,omitempty" bson:"config_fee_type,omitempty"`
	ConfigFeeId   int64               `json:"configFeeId,omitempty" bson:"config_fee_id,omitempty"`
}

// DeliveryTruckDB is an instance db
var CustomerDB = &db.Instance{
	ColName:        "customer",
	TemplateObject: &Customer{},
}

// InitDeliveryTruckModel is func init model deliveryTruck
func InitCustomerModel(s *mongo.Database) {
	CustomerDB.ApplyDatabase(s)
	t := true

	CustomerDB.CreateIndex(bson.D{
		{"code", 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	CustomerDB.CreateIndex(bson.D{
		{"customer_type", 1},
	}, &options.IndexOptions{
		Background: &t,
	})
}
