package api

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
)

// CreateHub api
func CreateHub(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Hub
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.CreateHub(&input))
}

// GetHub api
func GetHub(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input request.HubQuery
	str := req.GetParam("q")
	if str == "" {
		str = "{}"
	}
	var offset = int64(sdk.ParseInt(req.GetParam("offset"), 0))
	var limit = int64(sdk.ParseInt(req.GetParam("limit"), 100))
	var getTotal = req.GetParam("getTotal") == "true"
	err := json.Unmarshal([]byte(str), &input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.GetHub(&input, offset, limit, getTotal))
}

// UpdateHub api
func UpdateHub(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Hub
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.UpdateHub(&input))
}

// DeleteHub api
func DeleteHub(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Hub
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.DeleteHub(input.HubId))
}

func GenHubKeyword(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.GenHubKeyword())
}

func MigrateDefaultCarrier(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.MigrateDefaultCarrier())
}

func MigrateHubLatLong(req sdk.APIRequest, resp sdk.APIResponder) error {
	type reqMigrateHub struct {
		IsMigrateAll *bool    `json:"isMigrateAll,omitempty"`
		ListHub      []string `json:"listHub,omitempty"`
	}

	var input reqMigrateHub
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.MigrateHubLatLong(input.IsMigrateAll, input.ListHub))
}

func GetRelatedTripOfHub(req sdk.APIRequest, resp sdk.APIResponder) error {

	str := req.GetParam("q")
	if str == "" {
		str = "{}"
	}
	var input model.Hub
	err := json.Unmarshal([]byte(str), &input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.GetRelatedTripOfHub(input.Code))
}

func GetReportProductivity(req sdk.APIRequest, resp sdk.APIResponder) error {
	type Report struct {
		HubCode  string              `json:"hubCode,omitempty"`
		DriverId int64               `json:"driverId,omitempty"`
		ViewMode *enum.ViewModeValue `json:"viewMode,omitempty"`
	}
	var input Report
	str := req.GetParam("q")

	err := json.Unmarshal([]byte(str), &input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.GetReportProductivity(input.HubCode, input.DriverId, input.ViewMode))
}
