package retry_queue

import (
	"encoding/json"
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"go.mongodb.org/mongo-driver/bson"
)

func (j *ExecutorJob) retryReconcile() {
	j.Job.SetTopicConsumer(defaultTopic, func(item *job.JobItem) (err error) {
		var dataByte []byte
		dataByte, err = bson.Marshal(item.Data)
		if err != nil {
			return
		}

		var reqData RetryReconcile
		err = bson.Unmarshal(dataByte, &reqData)
		if err != nil {
			return
		}

		if reqData.ReconcileType == string(enum.ReconcileTypeRequest.RIDER_HUB) {
			err = retryReconcileRiderHub(reqData)
		}

		if err != nil {
			if item.FailCount%200 == 0 {
				syncDataByte, _ := json.Marshal(reqData)
				_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
					CmdType: "Cann't create reconcile",
					Title:   string(syncDataByte),
					Message: "================================ " + err.Error(),
				})
			}

			if item.FailCount > 500 {
				return nil
			}

			sleepTime := item.FailCount * 2
			time.Sleep(time.Duration(sleepTime) * time.Second)
			return
		}

		return nil
	})
}

func retryReconcileRiderHub(reqData RetryReconcile) error {
	_, isNotOk := CreateReconcile(reqData.HubOrder, string(enum.ReconcileTypeRequest.RIDER_HUB))
	if isNotOk {
		return fmt.Errorf("Can not create reconcile session order")
	}
	return nil
}

func CreateReconcile(hubShippingOrder *model.HubShippingOrder, reconcileType string) (*common.APIResponse, bool) {
	carrierCode := string(*hubShippingOrder.TplCode)
	// Lấy carrier cha nếu có
	response := model.CarrierDB.QueryOne(&bson.M{"carrier_code": carrierCode})
	if response.Status == common.APIStatus.Ok {
		carrier := response.Data.([]*model.Carrier)[0]
		if carrier.ParentCode != nil && *carrier.ParentCode != "" {
			carrierCode = string(*carrier.ParentCode)
		}
	}

	createReconcileSession := &request.CreateReconcileSessionRequest{
		CarrierCode:   carrierCode,
		ReconcileType: reconcileType,
		HubCode:       hubShippingOrder.HUBCode,
		UserId:        int(hubShippingOrder.DriverID),
		Fullname:      hubShippingOrder.DriverName,
	}

	response = client.Services.AccountingClient.CreateReconcileSession(createReconcileSession)
	if response.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Tạo đối soát thất bại.",
		}, true
	} else {
		deliveredTime := time.Unix(hubShippingOrder.DeliveredTime, 0)

		paymentMethod := "COD"
		if hubShippingOrder.PaymentMethod != nil {
			paymentMethod = string(*hubShippingOrder.PaymentMethod)
		}
		createReconcileSessionOrder := &request.CreateReconcileSessionOrderRequest{
			HubCode:             hubShippingOrder.HUBCode,
			UserId:              int(hubShippingOrder.DriverID),
			ReferenceCode:       hubShippingOrder.ReferenceCode,
			TrackingCode:        hubShippingOrder.TrackingCode,
			ReconcileType:       reconcileType,
			CODAmount:           hubShippingOrder.CODAmount,
			TplCode:             hubShippingOrder.TplCode,
			PaymentMethod:       paymentMethod,
			CarrierCode:         string(*hubShippingOrder.TplCode),
			DeliveredTime:       &deliveredTime,
			DeliveryAmount:      hubShippingOrder.DeliveryAmount,
			ParentReferenceCode: hubShippingOrder.ParentReferenceCode,
			OrderType:           hubShippingOrder.SubType,
		}

		if hubShippingOrder.SubType != nil &&
			*hubShippingOrder.SubType == enum.SubType.EO {
			// Nếu là đơn lấy hàng VÀ thu phí người gửi thì tạo đối soát với phí lấy hàng
			// COD CHỈ thu ở nguời nhận nên COD sẽ bằng 0
			if hubShippingOrder.Status != nil &&
				(*hubShippingOrder.Status == enum.HubShippingOrderStatus.WAIT_TO_STORING ||
					*hubShippingOrder.Status == enum.HubShippingOrderStatus.STORING) &&
				hubShippingOrder.FeeSenderAmount != nil {
				if hubShippingOrder.FeeSenderAmount != nil {
					temp := int64(*hubShippingOrder.FeeSenderAmount)
					createReconcileSessionOrder.DeliveryFeeAmount = &temp
				}
				createReconcileSessionOrder.CODAmount = 0
			}

			if hubShippingOrder.Status != nil &&
				*hubShippingOrder.Status == enum.HubShippingOrderStatus.DELIVERED {
				createReconcileSessionOrder.CODAmount = hubShippingOrder.CODAmount
				if hubShippingOrder.FeeReceiverAmount != nil {
					if hubShippingOrder.FeeReceiverAmount != nil {
						temp := int64(*hubShippingOrder.FeeReceiverAmount)
						createReconcileSessionOrder.DeliveryFeeAmount = &temp

					}
				}
			}
		}

		response = client.Services.AccountingClient.CreateReconcileSessionOrder(createReconcileSessionOrder)
		if response.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Ok,
				Message: "Tạo đối soát thất bại.",
			}, true
		}
	}

	return nil, false
}
