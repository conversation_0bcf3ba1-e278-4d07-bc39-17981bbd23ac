package ghn

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathBookGHNService   = "/shiip/public-api/v2/shipping-order/create"
	pathCancelGHNService = "/shiip/public-api/v2/switch-status/cancel"
	pathGetProvince      = "/shiip/public-api/master-data/province"
	pathGetDistrict      = "/shiip/public-api/master-data/district"
	pathGetWard          = "/shiip/public-api/master-data/ward"
	pathGetOrder         = "/shiip/public-api/v2/shipping-order/detail"
	pathCalculateFee     = "/shiip/public-api/v2/shipping-order/fee"
	pathGetListService   = "/shiip/public-api/v2/shipping-order/available-services"
)

type Client struct {
	svc     *client.RestClient
	headers map[string]string
}

func NewServiceClient(apiHost, logName string, session *mongo.Database) *Client {
	if apiHost == "" {
		return nil
	}
	newClient := &Client{
		svc: client.NewRESTClient(
			apiHost,
			logName,
			180*time.Second,
			0,
			180*time.Second,
		),
		headers: map[string]string{},
	}
	newClient.svc.SetDBLog(session)
	return newClient
}

func (cli *Client) ChangeInformation(carrierInfo *model.Carrier) {
	cli.headers = map[string]string{
		"Token":  carrierInfo.ExtraData.AccessToken,
		"ShopId": strconv.Itoa(int(carrierInfo.ExtraData.PartnerId)),
	}
}

func (cli *Client) CreateTrackingGHN(body request.BookGHNRequest) (trackingInfo *model.ShippingInfo, err error) {
	type myResponse struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    struct {
			TotalFee  float64 `json:"total_fee"`
			OrderCode string  `json:"order_code"`
		} `json:"data"`
	}

	query := map[string]string{}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, query, body, pathBookGHNService, nil)
	if err != nil {
		return
	}

	resBody := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return
	}

	if resBody.Code != 200 {
		err = fmt.Errorf("%v", resBody)
		return
	}

	trackingInfo = &model.ShippingInfo{
		TrackingNumber: resBody.Data.OrderCode,
		FeeAmount:      resBody.Data.TotalFee,
	}

	return
}

func (cli *Client) CancelGHN(orderCodes []string) (err error) {
	type myResponse struct {
		Message string `json:"message"`
		Code    int    `json:"code"`
	}

	type myRequest struct {
		OrderCodes []string `json:"order_codes"`
	}

	body := myRequest{
		OrderCodes: orderCodes,
	}

	query := map[string]string{}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, query, body, pathCancelGHNService, nil)
	if err != nil {
		return
	}
	resBody := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return
	}

	if resBody.Code != 200 {
		err = fmt.Errorf("%v-%v", resBody, resBody.Message)
		return
	}

	return
}

func (cli *Client) GetProvince() (result []*model.ProvinceGHN, err error) {
	type myResponse struct {
		Message string               `json:"message"`
		Code    int                  `json:"code"`
		Data    []*model.ProvinceGHN `json:"data"`
	}

	query := map[string]string{}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, query, nil, pathGetProvince, nil)
	if err != nil {
		return
	}
	resBody := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return
	}

	if resBody.Code != 200 {
		err = fmt.Errorf("%v-%v", resBody, resBody.Message)
		return
	}

	if len(resBody.Data) > 0 {
		result = resBody.Data
	}

	return
}

func (cli *Client) GetDistrict(provinceId int64) (result []*model.DistrictGHN, err error) {
	type myResponse struct {
		Message string               `json:"message"`
		Code    int                  `json:"code"`
		Data    []*model.DistrictGHN `json:"data"`
	}

	type myRequest struct {
		ProvinceId int64 `json:"province_id"`
	}

	body := myRequest{
		ProvinceId: provinceId,
	}

	query := map[string]string{}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, query, body, pathGetDistrict, nil)
	if err != nil {
		return
	}
	resBody := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return
	}

	if resBody.Code != 200 {
		err = fmt.Errorf("%v-%v", resBody, resBody.Message)
		return
	}

	if len(resBody.Data) > 0 {
		result = resBody.Data
	}

	return
}

func (cli *Client) GetWard(districtId int64) (result []*model.WardGHN, err error) {
	type myResponse struct {
		Message string           `json:"message"`
		Code    int              `json:"code"`
		Data    []*model.WardGHN `json:"data"`
	}

	type myRequest struct {
		DistrictId int64 `json:"district_id"`
	}

	body := myRequest{
		DistrictId: districtId,
	}

	query := map[string]string{}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, query, body, pathGetWard, nil)
	if err != nil {
		return
	}
	resBody := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return
	}

	if resBody.Code != 200 {
		err = fmt.Errorf("%v-%v", resBody, resBody.Message)
		return
	}

	if len(resBody.Data) > 0 {
		result = resBody.Data
	}

	return
}

func (cli *Client) GetOrderInfo(orderCode string) (result *model.GHNOrder, err error) {
	type myResponse struct {
		Message string            `json:"message"`
		Code    int               `json:"code"`
		Data    []*model.GHNOrder `json:"data"`
	}

	type myRequest struct {
		OrderCodes string `json:"order_codes"`
	}

	body := myRequest{
		OrderCodes: orderCode,
	}

	query := map[string]string{}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, query, body, pathGetOrder, nil)
	if err != nil {
		return
	}
	resBody := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		return
	}

	if resBody.Code != 200 || resBody.Data == nil {
		err = fmt.Errorf("%v-%v", resBody, resBody.Message)
		return
	}

	result = resBody.Data[0]

	return
}
