package api

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
)

// GetShippingOrder api
func GetShippingOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var str = req.GetParam("referenceCode")
	if str != "" {
		return resp.Respond(action.GetShippingOrder(str))
	}

	var (
		q        = req.GetParam("q")
		offset   = int64(sdk.ParseInt(req.GetParam("offset"), 0))
		limit    = int64(sdk.ParseInt(req.GetParam("limit"), 100))
		getTotal = req.GetParam("getTotal") == "true"
	)
	if q == "" {
		q = "{}"
	}
	var query request.ShippingOrderQuery
	err := json.Unmarshal([]byte(q), &query)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data." + err.Error(),
			ErrorCode: "JSON_INVALID",
		})
	}

	var sort map[string]interface{}
	sortStr := req.GetParam("sort")
	if sortStr == "" {
		sortStr = "{}"
	}

	err = json.Unmarshal([]byte(sortStr), &sort)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data: " + err.Error(),
			ErrorCode: "JSON_INVALID",
		})
	}

	return resp.Respond(action.GetListShippingOrder(query, sort, offset, limit, getTotal))
}

// GetMyShippingOrder api
func GetMyShippingOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q        = req.GetParam("q")
		offset   = int64(sdk.ParseInt(req.GetParam("offset"), 0))
		limit    = int64(sdk.ParseInt(req.GetParam("limit"), 100))
		getTotal = req.GetParam("getTotal") == "true"
	)
	if q == "" {
		q = "{}"
	}
	var query request.ShippingOrderQuery
	err := json.Unmarshal([]byte(q), &query)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	var sort map[string]interface{}
	sortStr := req.GetParam("sort")
	if sortStr == "" {
		sortStr = "{}"
	}

	err = json.Unmarshal([]byte(sortStr), &sort)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data: " + err.Error(),
			ErrorCode: "JSON_INVALID",
		})
	}

	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}
	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}
	query.DriverId = UserInfo.Account.AccountID
	return resp.Respond(action.GetListShippingOrder(query, sort, offset, limit, getTotal))
}

// CountShippingOrderByStatus api
func CountShippingOrderByStatus(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q = req.GetParam("q")
	)

	var query *request.ShippingOrderQuery
	if q == "" {
		q = "{}"
	}
	err := json.Unmarshal([]byte(q), &query)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.CountShippingOrderGroupByStatus(query))
}

// CountMyShippingOrderByStatus api
func CountMyShippingOrderByStatus(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q = req.GetParam("q")
	)

	var query *request.ShippingOrderQuery
	if q == "" {
		q = "{}"
	}
	err := json.Unmarshal([]byte(q), &query)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}
	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}
	query.DriverId = UserInfo.Account.AccountID
	return resp.Respond(action.CountShippingOrderGroupByStatus(query))
}

// UpdateShippingOrder api
func UpdateShippingOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.ShippingOrder
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.UpdateShippingOrder(&input))
}

// SyncDetailOrder api
func SyncDetailOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	type myRequest struct {
		ListReferenceCode []string `json:"listReferenceCode"`
	}
	var input myRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.GetDetailOrder(input.ListReferenceCode))
}

// AssignManualShippingOrder api
func AssignManualShippingOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	type myRequest struct {
		ListReferenceCode []string `json:"listReferenceCode"`
		DriverName        string   `json:"driverName"`
		DriverId          int      `json:"driverId"`
	}
	var input myRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	// Check UpdatedBy
	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}
	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}
	return resp.Respond(action.Assign(input.ListReferenceCode, input.DriverName, input.DriverId, UserInfo.Account.AccountID))
}

// UpdateStatusShippingOrder api
func UpdateStatusShippingOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	type myRequest struct {
		ListReferenceCode []string                     `json:"listReferenceCode"`
		Status            *enum.TPLCallbackStatusValue `json:"status"`
	}
	var input myRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	// Check UpdatedBy
	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}
	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}
	return resp.Respond(action.UpdateStatusShippingOrder(input.ListReferenceCode, input.Status, UserInfo.Account.AccountID))
}

// AdditionNoteShippingOrder api
func AdditionNoteShippingOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	type myRequest struct {
		Note          string `json:"note"`
		ReferenceCode string `json:"referenceCode"`
	}
	var input myRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	// Check UpdatedBy
	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}
	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}
	return resp.Respond(action.AdditionNoteShippingOrder(input.Note, input.ReferenceCode, UserInfo.Account.AccountID))
}

// SyncShippingOrder api
func SyncShippingOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var callback request.Callback
	err := req.GetContent(&callback)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	action.SyncShippingOrder(&callback)
	return resp.Respond(
		&common.APIResponse{
			Status: common.APIStatus.Ok,
		},
	)
}

// MigrateShippingOrder api
func MigrateShippingOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	type myRequest struct {
		ListReferenceCode []string `json:"listReferenceCode"`
	}
	var input myRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	if len(input.ListReferenceCode) > 0 {
		return resp.Respond(action.SyncSomeShippingOrder(input.ListReferenceCode))
	}
	return resp.Respond(action.MigrateShippingOrder())
}

// MassUpdateTPLServiceID api
func MassUpdateTPLServiceID(req sdk.APIRequest, resp sdk.APIResponder) error {
	type myRequest struct {
		ReferenceCodes []string `json:"referenceCodes"`
		TPLServiceID   int      `json:"tplServiceID"`
	}
	var input myRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	return resp.Respond(action.MassUpdateTPLServiceID(input.ReferenceCodes, input.TPLServiceID))
}

// DeleteShippingOrder api
func DeleteShippingOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.ShippingOrder
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	return resp.Respond(action.DeleteShippingOrder(&input))
}

// CheckinPackage api
func CheckinPackage(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input request.CheckinPackageRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	return resp.Respond(action.CheckinPackage(&input))
}

// CheckHandover api
func CheckHandover(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input request.CheckinPackageRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	return resp.Respond(action.CheckHandover(&input))
}

func BookShippingOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input request.BookShippingOrder
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}
	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}
	return resp.Respond(action.BookShippingOrder(&input, UserInfo.Account.AccountID))
}

func MigrateShippingOrderType(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.MigrateShippingOrderType())
}

func CompleteShippingOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	type referenceCodes struct {
		ReferenceCodes         []string                        `json:"referenceCodes"`
		BinCodes               []request.CompleteBinCode       `json:"binCodes"`
		ReconcileSessionOrders []request.ReconcileSessionOrder `json:"reconcileSessionOrders"`
	}

	var input referenceCodes

	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}
	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	if len(input.ReferenceCodes) > 0 {
		return resp.Respond(action.CompleteShippingOrder(input.ReferenceCodes, UserInfo.Account.AccountID))
	}

	if len(input.BinCodes) > 0 {
		return resp.Respond(action.CompleteBinOrder(input.BinCodes, UserInfo.Account.AccountID))
	}

	if len(input.ReconcileSessionOrders) > 0 {
		return resp.Respond(action.CompleteRSO(input.ReconcileSessionOrders, UserInfo.Account.AccountID))
	}

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Invalid,
		Message: "Mã đơn không được để trống",
	})
}

func MigrateProductForPickupOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	type referenceCodes struct {
		ReferenceCodes []string `json:"referenceCodes"`
	}

	var input referenceCodes

	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.MigrateProductForPickupOrder(input.ReferenceCodes))
}

func MigrateShippingOrderScope(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.MigrateShippingOrderScope())
}

func CancelShippingOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input request.CancelShippingService
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}
	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	return resp.Respond(action.CancelShippingOrder(&input, UserInfo.Account.AccountID))
}

func CancelShippingOrders(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input request.CancelShippingService
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}
	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	return resp.Respond(action.CancelShippingOrders(&input, UserInfo.Account.AccountID))
}

func MigrateProduct(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.MigrateProduct())
}

func ChangeCarrier(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input request.ChangeCarrier

	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}
	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	return resp.Respond(action.ChangeCarrier(&input, UserInfo.Account.AccountID))
}

func MigrateTotalAmount(req sdk.APIRequest, resp sdk.APIResponder) error {
	type referenceCodes struct {
		TestOrder  []string `json:"referenceCodes"`
		MassUpdate bool     `json:"massUpdate"`
	}

	var input referenceCodes
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}
	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	return resp.Respond(action.MigrateTotalAmount(input.TestOrder, input.MassUpdate))
}

func ImportFee(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input request.ImportFeeRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}
	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	return resp.Respond(action.ImportFee(input, UserInfo.Account.AccountID))
}

func CheckinInbound(req sdk.APIRequest, resp sdk.APIResponder) error {
	type order struct {
		ReferenceCode string                      `json:"referenceCode"`
		Type          enum.ShippingOrderTypeValue `json:"type"`
		CheckInBy     string                      `json:"checkInBy"`
	}

	var input order
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.CheckinInbound(input.ReferenceCode, input.Type, input.CheckInBy))
}

func MigrateDropOffFM(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.MigrateDropOffFM())
}

func UpdateOrderValue(req sdk.APIRequest, resp sdk.APIResponder) error {
	type updateOrderValue struct {
		ParentReferenceCode string  `json:"parentReferenceCode"`
		OrderValue          float64 `json:"orderValue"`
	}
	var input updateOrderValue
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}
	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}
	return resp.Respond(action.UpdateOrderValue(input.ParentReferenceCode, input.OrderValue))
}

func EstimateShippingOrderFee(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input request.BookShippingOrder
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	//if input.FeeCollectedOn != nil && *input.FeeCollectedOn == enum.FeeCollectMethod.DEBT {
	//	return resp.Respond(&common.APIResponse{
	//		Status:  common.APIStatus.Ok,
	//		Message: "Lấy phí vận chuyển thành công",
	//		Data: []response.Fee{
	//			{FeeAmount: 0},
	//		},
	//	})
	//}

	return resp.Respond(action.EstimateShippingOrderFee(input))
}

func MergeShippingOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.ShippingOrder
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.MergeShippingOrder(input))
}

func ReleaseNeedMergeShippingOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.ShippingOrder
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.ReleaseNeedMergeShippingOrder(input))
}

func ImportOrders(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input request.ImportOrdersRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}
	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	return resp.Respond(action.ImportOrders(input, UserInfo.Account.AccountID))
}

func MigrateCustomerInfo(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.MigrateCustomerInfo())
}

func GetCustomerOrders(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q        = req.GetParam("q")
		offset   = int64(sdk.ParseInt(req.GetParam("offset"), 0))
		limit    = int64(sdk.ParseInt(req.GetParam("limit"), 100))
		getTotal = req.GetParam("getTotal") == "true"
	)
	if q == "" {
		q = "{}"
	}
	var query request.ShippingOrderQuery
	err := json.Unmarshal([]byte(q), &query)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data." + err.Error(),
			ErrorCode: "JSON_INVALID",
		})
	}

	var sort map[string]interface{}
	sortStr := req.GetParam("sort")
	if sortStr == "" {
		sortStr = "{}"
	}

	err = json.Unmarshal([]byte(sortStr), &sort)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data: " + err.Error(),
			ErrorCode: "JSON_INVALID",
		})
	}

	return resp.Respond(action.GetCustomerOrders(query, sort, offset, limit, getTotal))
}

func CountCustomerOrders(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q = req.GetParam("q")
	)
	if q == "" {
		q = "{}"
	}
	var query request.ShippingOrderQuery
	err := json.Unmarshal([]byte(q), &query)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data." + err.Error(),
			ErrorCode: "JSON_INVALID",
		})
	}

	return resp.Respond(action.CountCustomerOrders(query))
}

func UpdateFMPackage(req sdk.APIRequest, resp sdk.APIResponder) error {
	type myRequest struct {
		ReferenceCode string `json:"referenceCode"`
		NumPack       int64  `json:"numPack"`
	}

	var input myRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}
	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	return resp.Respond(action.UpdateFMPackage(input.ReferenceCode, input.NumPack))
}

func MigrateSellerInfo(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.MigrateSellerInfo())
}

func FillMissingCustomerType(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.FillMissingCustomerType())
}

func UpdateOrdersNote(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input []request.UpdateOrderNoteRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data." + err.Error(),
			ErrorCode: "JSON_INVALID",
		})
	}

	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}
	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	return resp.Respond(action.UpdateOrdersNote(input, UserInfo.Account.AccountID))
}

func RemoveRequireVerify(req sdk.APIRequest, resp sdk.APIResponder) error {
	type referenceCodes struct {
		ReferenceCodes []string `json:"referenceCodes"`
	}

	var input referenceCodes

	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.RemoveRequireVerify(input.ReferenceCodes))
}

func PrepareCheckin(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input request.PrepareCheckinRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	if err = input.Validate(); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Error(),
		})
	}

	return resp.Respond(action.PrepareCheckin(input))
}

func CheckinShippingOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input request.CheckinShippingOrderRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	if err = input.Validate(); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Error(),
		})
	}

	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}
	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	return resp.Respond(action.CheckinShippingOrder(input, *UserInfo.Account))
}
func CountReadyToTransport(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q = req.GetParam("q")
	)

	var query *request.CountReadyToTransferRequest
	if q == "" {
		q = "{}"
	}
	err := json.Unmarshal([]byte(q), &query)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.CountReadyToTransport(*query))
}
