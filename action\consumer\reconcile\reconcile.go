package reconcile

import (
	"errors"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/utils"
	"go.mongodb.org/mongo-driver/bson"
)

func (j *ExecutorJob) addOrderToReconcileSession() {
	j.Job.SetTopicConsumer(defaultTopic, func(item *job.JobItem) (err error) {
		var dataByte []byte
		dataByte, err = bson.Marshal(item.Data)
		if err != nil {
			return
		}

		var reqData AddOrderToReconcileSession
		err = bson.Unmarshal(dataByte, &reqData)
		if err != nil {
			return
		}

		customerRaw := model.CustomerDB.QueryOne(bson.M{"code": reqData.CustomerCode})
		if customerRaw.Status != common.APIStatus.Ok {
			return
		}
		customer := customerRaw.Data.([]*model.Customer)[0]

		shippingOrderRaw := model.ShippingOrderDB.QueryOne(bson.M{"reference_code": reqData.ReferenceCode})
		if shippingOrderRaw.Status != common.APIStatus.Ok {
			return
		}
		shippingOrder := shippingOrderRaw.Data.([]*model.ShippingOrder)[0]
		feeId := 0
		if customer.AppliedFees != nil {
			for _, fee := range *customer.AppliedFees {
				if shippingOrder.IsBookDropOff != nil &&
					*shippingOrder.IsBookDropOff &&
					fee.ConfigFeeType == enum.ConfigFeeType.TRANSPORT {
					feeId = int(fee.ConfigFeeId)
					break
				}

				if (shippingOrder.IsBookDropOff == nil ||
					*shippingOrder.IsBookDropOff == false) &&
					fee.ConfigFeeType == enum.ConfigFeeType.PICK_N_TRANSPORT {
					feeId = int(fee.ConfigFeeId)
					break
				}
			}
		}

		if shippingOrder.PickedTime == 0 {
			shippingOrder.PickedTime = time.Now().Unix()
			model.ShippingOrderDB.UpdateOne(bson.M{"reference_code": reqData.ReferenceCode}, bson.M{"picked_time": shippingOrder.PickedTime})
		}
		parsePickedTime := time.Unix(shippingOrder.PickedTime, 0)
		// Nếu chưa config fee thì khỏi tính
		if feeId != 0 {
			configFeeRaw := model.ConfigFeeDB.QueryOne(bson.M{"config_id": feeId})
			if configFeeRaw.Status != common.APIStatus.Ok {
				return
			}
			configFee := configFeeRaw.Data.([]*model.ConfigFee)[0]
			// nếu có config fee có quota thì phải query tất cả đơn đã giao trong tháng để tính lại xem đạt quota chưa
			if DoesConfigFeeContainsQuota(configFee) {
				// Tự hiểu là group tất cả các đơn theo tháng
				currentDate := time.Unix(shippingOrder.PickedTime, 0)
				firstDayOfMonth, endDayOfMonth := monthInterval(currentDate)
				firstDayOfMonthUnix := firstDayOfMonth.Unix()
				endDayOfMonthUnix := endDayOfMonth.Unix()
				shippingOrdersRaw := model.ShippingOrderDB.Query(bson.M{
					"picked_time":        bson.M{"$gte": firstDayOfMonthUnix, "$lte": endDayOfMonthUnix},
					"type":               shippingOrder.ShippingType,
					"from_customer_code": customer.Code,
					"is_book_drop_off":   shippingOrder.IsBookDropOff,
				}, 0, 1000, nil)

				// Luôn có ít nhất 1 đơn vừa insert ở trên, nếu vào đây thì có nghĩa là có lỗi !!!
				if shippingOrdersRaw.Status != common.APIStatus.Ok {
					return
				}

				shippingOrders := shippingOrdersRaw.Data.([]*model.ShippingOrder)
				needUpdateOrders, needUpdateOrdersErr := CalculateOrdersFee(shippingOrders, configFee)
				if needUpdateOrdersErr != nil || len(needUpdateOrders) == 0 {
					// Send noti telegram

					// Send create reconcile session order for a single order
					newReconcileOrder := []*request.CreateReconcileSessionOrderRequest{
						{
							ReferenceCode:       shippingOrder.ReferenceCode,
							PickedTime:          &parsePickedTime,
							CustomerCode:        shippingOrder.FromCustomerCode,
							CarrierCode:         string(*shippingOrder.TplCode),
							CODAmount:           shippingOrder.FeeAmount,
							IsFeeBeenCalculated: &enum.False,
							OrderValue:          shippingOrder.OrderValue,
							IsBookDropOff:       shippingOrder.IsBookDropOff,
							TrackingCode:        shippingOrder.TrackingCode,
							NumPackage:          shippingOrder.NumPackage,
						},
					}
					_ = client.Services.AccountingClient.AddReconcileFeeSession(
						newReconcileOrder,
						nil,
						shippingOrder.Tags)

					return nil
				}
				newReconcileOrder := []*request.CreateReconcileSessionOrderRequest{
					{
						ReferenceCode:       shippingOrder.ReferenceCode,
						PickedTime:          &parsePickedTime,
						CustomerCode:        shippingOrder.FromCustomerCode,
						CarrierCode:         string(*shippingOrder.TplCode),
						CODAmount:           shippingOrder.FeeAmount,
						IsFeeBeenCalculated: &enum.False,
						OrderValue:          shippingOrder.OrderValue,
						IsBookDropOff:       shippingOrder.IsBookDropOff,
						TrackingCode:        shippingOrder.TrackingCode,
						NumPackage:          shippingOrder.NumPackage,
					},
				}

				var updateReconcileOrders []*request.CreateReconcileSessionOrderRequest
				for _, needUpdateOrder := range needUpdateOrders {
					if needUpdateOrder.ReferenceCode == shippingOrder.ReferenceCode {
						newReconcileOrder[0].IsFeeBeenCalculated = &enum.True
						newReconcileOrder[0].CODAmount = needUpdateOrder.FeeAmount
						continue
					}
					updateReconcileOrder := &request.CreateReconcileSessionOrderRequest{
						ReferenceCode:       needUpdateOrder.ReferenceCode,
						PickedTime:          &parsePickedTime,
						CustomerCode:        needUpdateOrder.FromCustomerCode,
						CarrierCode:         string(*needUpdateOrder.TplCode),
						CODAmount:           needUpdateOrder.FeeAmount,
						IsFeeBeenCalculated: &enum.True,
						NumPackage:          needUpdateOrder.NumPackage,
					}
					updateReconcileOrders = append(updateReconcileOrders, updateReconcileOrder)
				}

				// TODO: catch error here
				_ = client.Services.AccountingClient.AddReconcileFeeSession(
					newReconcileOrder,
					updateReconcileOrders,
					shippingOrder.Tags)

				return nil
			}

			// Nếu không có quota thì tính fee cho đơn đó rồi thêm vào phiên đối soát ở kế toán
			needUpdateOrders, needUpdateOrdersErr := CalculateOrdersFee([]*model.ShippingOrder{shippingOrder}, configFee)
			isFeeBeenCalculated := enum.True
			// Cũng có thể đơn không thỏa điều kiện nào của biểu phí
			if needUpdateOrdersErr != nil || len(needUpdateOrders) == 0 {
				isFeeBeenCalculated = enum.False
			}
			// Thêm đơn vô phiên đối soát
			newReconcileOrder := []*request.CreateReconcileSessionOrderRequest{
				{
					ReferenceCode:       shippingOrder.ReferenceCode,
					PickedTime:          &parsePickedTime,
					CustomerCode:        shippingOrder.FromCustomerCode,
					CarrierCode:         string(*shippingOrder.TplCode),
					CODAmount:           shippingOrder.FeeAmount,
					TrackingCode:        shippingOrder.TrackingCode,
					IsFeeBeenCalculated: &isFeeBeenCalculated,
					OrderValue:          shippingOrder.OrderValue,
					IsBookDropOff:       shippingOrder.IsBookDropOff,
					NumPackage:          shippingOrder.NumPackage,
				},
			}
			_ = client.Services.AccountingClient.AddReconcileFeeSession(
				newReconcileOrder,
				nil,
				shippingOrder.Tags)

			return nil
		}

		// Thêm đơn vô phiên đối soát
		newReconcileOrder := []*request.CreateReconcileSessionOrderRequest{
			{
				ReferenceCode:       shippingOrder.ReferenceCode,
				PickedTime:          &parsePickedTime,
				CustomerCode:        shippingOrder.FromCustomerCode,
				CarrierCode:         string(*shippingOrder.TplCode),
				CODAmount:           shippingOrder.FeeAmount,
				IsFeeBeenCalculated: &enum.False,
				OrderValue:          shippingOrder.OrderValue,
				IsBookDropOff:       shippingOrder.IsBookDropOff,
				TrackingCode:        shippingOrder.TrackingCode,
				NumPackage:          shippingOrder.NumPackage,
			},
		}

		// TODO: catch error here
		_ = client.Services.AccountingClient.AddReconcileFeeSession(
			newReconcileOrder,
			nil,
			shippingOrder.Tags)
		return nil
	})
}

func (j *ExecutorJob) updateOrderFeeToReconcileSession() {
	j.Job.SetTopicConsumer(updateOrderFeeReconcileTopic, func(item *job.JobItem) (err error) {
		var dataByte []byte
		dataByte, err = bson.Marshal(item.Data)
		if err != nil {
			return
		}

		var reqData UpdateOrderFeeSession
		err = bson.Unmarshal(dataByte, &reqData)
		if err != nil {
			return
		}
		customerRaw := model.CustomerDB.QueryOne(bson.M{"code": reqData.StoredShippingOrder.FromCustomerCode})
		if customerRaw.Status != common.APIStatus.Ok {
			return
		}
		customer := customerRaw.Data.([]*model.Customer)[0]
		shippingOrder := reqData.StoredShippingOrder
		feeId := 0
		if customer.AppliedFees != nil {
			for _, fee := range *customer.AppliedFees {
				if shippingOrder.IsBookDropOff != nil &&
					*shippingOrder.IsBookDropOff &&
					fee.ConfigFeeType == enum.ConfigFeeType.TRANSPORT {
					feeId = int(fee.ConfigFeeId)
					break
				}

				if (shippingOrder.IsBookDropOff == nil ||
					*shippingOrder.IsBookDropOff == false) &&
					fee.ConfigFeeType == enum.ConfigFeeType.PICK_N_TRANSPORT {
					feeId = int(fee.ConfigFeeId)
					break
				}
			}
		}
		if shippingOrder.PickedTime == 0 {
			shippingOrder.PickedTime = time.Now().Unix()
			model.ShippingOrderDB.UpdateOne(bson.M{"reference_code": reqData.StoredShippingOrder.ReferenceCode}, bson.M{"picked_time": shippingOrder.PickedTime})
		}
		parsePickedTime := time.Unix(shippingOrder.PickedTime, 0)
		if feeId != 0 {
			configFeeRaw := model.ConfigFeeDB.QueryOne(bson.M{"config_id": feeId})
			if configFeeRaw.Status != common.APIStatus.Ok {
				return
			}
			configFee := configFeeRaw.Data.([]*model.ConfigFee)[0]
			if DoesConfigFeeContainsQuota(configFee) {
				// Tự hiểu là group tất cả các đơn theo tháng
				currentDate := time.Unix(shippingOrder.PickedTime, 0)
				firstDayOfMonth, endDayOfMonth := monthInterval(currentDate)
				firstDayOfMonthUnix := firstDayOfMonth.Unix()
				endDayOfMonthUnix := endDayOfMonth.Unix()
				shippingOrdersRaw := model.ShippingOrderDB.Query(bson.M{
					"picked_time":        bson.M{"$gte": firstDayOfMonthUnix, "$lte": endDayOfMonthUnix},
					"type":               shippingOrder.ShippingType,
					"from_customer_code": customer.Code,
					"is_book_drop_off":   shippingOrder.IsBookDropOff,
				}, 0, 1000, nil)

				// Luôn có ít nhất 1 đơn vừa insert ở trên, nếu vào đây thì có nghĩa là có lỗi !!!
				if shippingOrdersRaw.Status != common.APIStatus.Ok {
					return nil
				}

				shippingOrders := shippingOrdersRaw.Data.([]*model.ShippingOrder)
				needUpdateOrders, needUpdateOrdersErr := CalculateOrdersFee(shippingOrders, configFee)
				if needUpdateOrdersErr != nil || len(needUpdateOrders) == 0 {
					return nil
				}
				var updateReconcileOrders []*request.CreateReconcileSessionOrderRequest
				for _, needUpdateOrder := range needUpdateOrders {
					updateReconcileOrder := &request.CreateReconcileSessionOrderRequest{
						ReferenceCode:       needUpdateOrder.ReferenceCode,
						PickedTime:          &parsePickedTime,
						CustomerCode:        needUpdateOrder.FromCustomerCode,
						CarrierCode:         string(*needUpdateOrder.TplCode),
						CODAmount:           needUpdateOrder.FeeAmount,
						IsFeeBeenCalculated: &enum.True,
						NumPackage:          needUpdateOrder.NumPackage,
					}
					updateReconcileOrders = append(updateReconcileOrders, updateReconcileOrder)
				}
				// TODO: catch error here
				_ = client.Services.AccountingClient.AddReconcileFeeSession(
					nil,
					updateReconcileOrders,
					shippingOrder.Tags)

				return nil
			}

			// Nếu không có quota thì tính fee cho đơn đó rồi cập nhật vào phiên đối soát ở kế toán
			needUpdateOrders, needUpdateOrdersErr := CalculateOrdersFee([]*model.ShippingOrder{&shippingOrder}, configFee)
			// Cũng có thể đơn không thỏa điều kiện nào của biểu phí
			if needUpdateOrdersErr != nil || len(needUpdateOrders) == 0 {
				return nil
			}
			updateReconcileOrders := []*request.CreateReconcileSessionOrderRequest{
				{
					ReferenceCode:       needUpdateOrders[0].ReferenceCode,
					PickedTime:          &parsePickedTime,
					CustomerCode:        needUpdateOrders[0].FromCustomerCode,
					CarrierCode:         string(*needUpdateOrders[0].TplCode),
					CODAmount:           needUpdateOrders[0].FeeAmount,
					IsFeeBeenCalculated: &enum.True,
					NumPackage:          needUpdateOrders[0].NumPackage,
				},
			}
			// TODO: catch error here
			_ = client.Services.AccountingClient.AddReconcileFeeSession(
				nil,
				updateReconcileOrders,
				nil)

			return nil

		}
		return nil
	})

}

func DoesConfigFeeContainsQuota(configFee *model.ConfigFee) bool {
	for _, freeStruct := range configFee.FeeStructures {
		for _, condition := range freeStruct.FeeConditions {
			if condition.ConditionType == enum.FeeConditionType.TOTAL_ORDER ||
				condition.ConditionType == enum.FeeConditionType.TOTAL_PACKAGE {
				return true
			}
		}
	}
	return false
}

func CalculateOrdersFee(orders []*model.ShippingOrder, configFee *model.ConfigFee) ([]*model.ShippingOrder, error) {
	if len(orders) == 0 {
		return nil, errors.New("no order to calculate fee")
	}

	if configFee.IsApplyToArea != nil &&
		*configFee.IsApplyToArea {
		return CalculateAreaOrdersFee(orders, configFee)
	}

	var totalOrders int64 = 0
	var totalPackages int64 = 0
	for _, order := range orders {
		// Chi tinh tien don dau tien chu khong tinh tien don tach
		if order.SplitFromCode != "" {
			continue
		}
		totalOrders += 1
		if order.CheckinNumPack != 0 {
			order.NumPackage = order.CheckinNumPack
		}
		totalPackages += order.NumPackage
	}

	var needUpdateOrderFee []*model.ShippingOrder

	for _, order := range orders {
		if order.SplitFromCode != "" {
			continue
		}
		for _, freeStruct := range configFee.FeeStructures {
			isMatchFeeStructure := false
			for _, condition := range freeStruct.FeeConditions {
				switch condition.ConditionType {
				case enum.FeeConditionType.PACKAGE:
					isMatchFeeStructure = isMatchNumPack(order.NumPackage, condition)
				case enum.FeeConditionType.WEIGHT:
					isMatchFeeStructure = isMatchWeight(order.Weight, condition)
				case enum.FeeConditionType.TOTAL_ORDER:
					isMatchFeeStructure = isMatchTotalOrder(totalOrders, condition)
				case enum.FeeConditionType.TOTAL_PACKAGE:
					isMatchFeeStructure = isMatchTotalNumPackages(totalPackages, condition)
				case enum.FeeConditionType.ORDER_VALUE:
					if order.OrderValue == nil {
						break
					}
					isMatchFeeStructure = isMatchOrderValue(*order.OrderValue, condition)
				}
				// TODO: Nếu có loại mới thêm vào cụm switch case này để mở rộng
				if !isMatchFeeStructure {
					break
				}
			}

			if isMatchFeeStructure {
				var feePerPackage float64 = 0
				var feePercentOrderValue float64 = 0
				var additionalFee float64 = 0
				if fee, ok := freeStruct.Fees[enum.FeeType.FEE_PER_PACKAGE]; ok {
					feePerPackage = fee
				}

				if fee, ok := freeStruct.Fees[enum.FeeType.PERCENT_ORDER_VALUE]; ok {
					feePercentOrderValue = fee / 100
				}

				if fee, ok := freeStruct.Fees[enum.FeeType.ADDITIONAL_FEE]; ok {
					additionalFee = fee
				}
				var zero float64 = 0
				if order.OrderValue == nil {
					order.OrderValue = &zero
				}
				// Công thức tính phí tổng quát:
				// Phí = Số lượng kiện * Phí kiện + Giá trị đơn hàng * phần trăm phí trên giá trị đơn hàng + Phí thêm
				calculatedFee := float64(order.NumPackage)*feePerPackage + *order.OrderValue*feePercentOrderValue + additionalFee
				// TODO: Add voucher validation and discount here, for now if voucher is not empty, we will not calculate fee
				if order.VoucherCode != "" {
					calculatedFee = 0
				}
				if calculatedFee != order.FeeAmount ||
					order.VoucherCode != "" ||
					calculatedFee == 0 {
					order.FeeAmount = calculatedFee
					needUpdateOrderFee = append(needUpdateOrderFee, order)
				}
			}
		}
	}

	return needUpdateOrderFee, nil
}

func isMatchNumPack(value int64, condition model.FeeCondition) bool {
	compareValue := int64(condition.Value.(float64))
	switch condition.Method {
	case enum.ComparableMethod.EQ:
		return compareValue == value
	case enum.ComparableMethod.LT:
		return value < compareValue
	case enum.ComparableMethod.GT:
		return value > compareValue
	case enum.ComparableMethod.LTE:
		return value <= compareValue
	case enum.ComparableMethod.GTE:
		return value >= compareValue
	}
	return false
}

func isMatchWeight(value float64, condition model.FeeCondition) bool {
	compareValue := utils.CeilFloat(condition.Value.(float64))
	switch condition.Method {
	case enum.ComparableMethod.EQ:
		return compareValue == value
	case enum.ComparableMethod.LT:
		return value < compareValue
	case enum.ComparableMethod.GT:
		return value > compareValue
	case enum.ComparableMethod.LTE:
		return value <= compareValue
	case enum.ComparableMethod.GTE:
		return value >= compareValue
	}
	return false
}

func isMatchTotalOrder(numOfOrder int64, condition model.FeeCondition) bool {
	compareValue := int64(condition.Value.(float64))
	switch condition.Method {
	case enum.ComparableMethod.EQ:
		return compareValue == numOfOrder
	case enum.ComparableMethod.LT:
		return numOfOrder < compareValue
	case enum.ComparableMethod.GT:
		return numOfOrder > compareValue
	case enum.ComparableMethod.LTE:
		return numOfOrder <= compareValue
	case enum.ComparableMethod.GTE:
		return numOfOrder >= compareValue
	}
	return false
}

func isMatchTotalNumPackages(numPack int64, condition model.FeeCondition) bool {
	compareValue := int64(condition.Value.(float64))
	switch condition.Method {
	case enum.ComparableMethod.EQ:
		return compareValue == numPack
	case enum.ComparableMethod.LT:
		return numPack < compareValue
	case enum.ComparableMethod.GT:
		return numPack > compareValue
	case enum.ComparableMethod.LTE:
		return numPack <= compareValue
	case enum.ComparableMethod.GTE:
		return numPack >= compareValue
	}
	return false
}

func isMatchOrderValue(value float64, condition model.FeeCondition) bool {
	compareValue := utils.CeilFloat(condition.Value.(float64))
	switch condition.Method {
	case enum.ComparableMethod.EQ:
		return compareValue == value
	case enum.ComparableMethod.LT:
		return value < compareValue
	case enum.ComparableMethod.GT:
		return value > compareValue
	case enum.ComparableMethod.LTE:
		return value <= compareValue
	case enum.ComparableMethod.GTE:
		return value >= compareValue
	}
	return false
}

func monthInterval(t time.Time) (firstDay, lastDay time.Time) {
	y, m, _ := t.Date()
	loc := t.Location()
	firstDay = time.Date(y, m, 1, 0, 0, 0, 0, loc)
	lastDay = time.Date(y, m+1, 1, 0, 0, 0, -1, loc)
	return firstDay, lastDay
}

func isMatchFeeArea(bookReq request.BookShippingOrder, configFee model.ConfigFee) bool {
	queryFromZoneResult := model.ZoneDB.QueryOne(bson.M{
		"provinces.districts.wards.ward_code": bookReq.From.WardCode,
		"parent_code": bson.M{
			"$exists": false,
		},
		"is_deleted": false,
	})

	queryToZoneResult := model.ZoneDB.QueryOne(bson.M{
		"provinces.districts.wards.ward_code": bookReq.To.WardCode,
		"parent_code": bson.M{
			"$exists": false,
		},
		"is_deleted": false,
	})

	if queryFromZoneResult.Status != common.APIStatus.Ok {
		return false
	}
	if queryToZoneResult.Status != common.APIStatus.Ok {
		return false
	}

	fromZone := queryFromZoneResult.Data.([]*model.Zone)[0]
	toZone := queryToZoneResult.Data.([]*model.Zone)[0]

	switch configFee.FeeArea.Type {
	case enum.FeeArea.HUB_TO_WH:
		return isContainInAreaIdentifier(configFee.FeeArea.FromAreaIdentifiers, bookReq.From.Code) &&
			isContainInAreaIdentifier(configFee.FeeArea.ToAreaIdentifiers, bookReq.To.Code)
	case enum.FeeArea.PROVINCE_TO_WH:
		return isContainInAreaIdentifier(configFee.FeeArea.FromAreaIdentifiers, bookReq.From.ProvinceCode) &&
			bookReq.To.Code == configFee.FeeArea.WarehouseCode
	case enum.FeeArea.ZONE_TO_WH:
		return isContainInAreaIdentifier(configFee.FeeArea.FromAreaIdentifiers, fromZone.Code) &&
			bookReq.To.Code == configFee.FeeArea.WarehouseCode
	case enum.FeeArea.WH_TO_HUB:
		return isContainInAreaIdentifier(configFee.FeeArea.ToAreaIdentifiers, bookReq.To.Code) &&
			isContainInAreaIdentifier(configFee.FeeArea.FromAreaIdentifiers, bookReq.From.Code)
	case enum.FeeArea.WH_TO_PROVINCE:
		return isContainInAreaIdentifier(configFee.FeeArea.ToAreaIdentifiers, bookReq.To.ProvinceCode) &&
			bookReq.From.Code == configFee.FeeArea.WarehouseCode
	case enum.FeeArea.WH_TO_ZONE:
		return isContainInAreaIdentifier(configFee.FeeArea.ToAreaIdentifiers, toZone.Code) &&
			bookReq.From.Code == configFee.FeeArea.WarehouseCode
	case enum.FeeArea.INTERNAL_PROVINCE:
		return isContainInAreaIdentifier(configFee.FeeArea.FromAreaIdentifiers, bookReq.From.ProvinceCode) && isContainInAreaIdentifier(configFee.FeeArea.ToAreaIdentifiers, bookReq.To.ProvinceCode)
	case enum.FeeArea.INTERNAL_ZONE:
		return isContainInAreaIdentifier(configFee.FeeArea.FromAreaIdentifiers, fromZone.Code) && isContainInAreaIdentifier(configFee.FeeArea.ToAreaIdentifiers, toZone.Code)
	case enum.FeeArea.NEAR_ZONE:
		return isContainInAreaIdentifier(configFee.FeeArea.FromAreaIdentifiers, fromZone.Code) && isContainInAreaIdentifier(configFee.FeeArea.ToAreaIdentifiers, toZone.Code) ||
			isContainInAreaIdentifier(configFee.FeeArea.FromAreaIdentifiers, toZone.Code) && isContainInAreaIdentifier(configFee.FeeArea.ToAreaIdentifiers, fromZone.Code)
	case enum.FeeArea.DISTANT_ZONE:
		return isContainInAreaIdentifier(configFee.FeeArea.FromAreaIdentifiers, fromZone.Code) && isContainInAreaIdentifier(configFee.FeeArea.ToAreaIdentifiers, toZone.Code) ||
			isContainInAreaIdentifier(configFee.FeeArea.FromAreaIdentifiers, toZone.Code) && isContainInAreaIdentifier(configFee.FeeArea.ToAreaIdentifiers, fromZone.Code)
	}
	return false
}

func shippingOrderToBookShippingReq(shippingOrder *model.ShippingOrder) request.BookShippingOrder {
	req := request.BookShippingOrder{
		From: &model.Address{
			Address:      shippingOrder.FromCustomerAddress,
			Code:         shippingOrder.FromCustomerCode,
			Name:         shippingOrder.FromCustomerName,
			Phone:        shippingOrder.FromCustomerPhone,
			ProvinceCode: shippingOrder.FromProvinceCode,
			DistrictCode: shippingOrder.FromDistrictCode,
			WardCode:     shippingOrder.FromWardCode,
			ProvinceName: shippingOrder.FromProvinceName,
			DistrictName: shippingOrder.FromDistrictName,
			WardName:     shippingOrder.FromWardName,
		},
		To: &model.Address{
			Address:      shippingOrder.CustomerShippingAddress,
			Code:         shippingOrder.CustomerCode,
			Name:         shippingOrder.CustomerName,
			Phone:        shippingOrder.CustomerPhone,
			ProvinceCode: shippingOrder.CustomerProvinceCode,
			DistrictCode: shippingOrder.CustomerDistrictCode,
			WardCode:     shippingOrder.CustomerWardCode,
			ProvinceName: shippingOrder.CustomerProvinceName,
			DistrictName: shippingOrder.CustomerDistrictName,
			WardName:     shippingOrder.CustomerWardName,
		},
		ShippingType:        shippingOrder.ShippingType,
		ParentReferenceCode: shippingOrder.ParentReferenceCode,
		FeeCollectedOn:      shippingOrder.FeeCollectMethod,
		NumPackage:          shippingOrder.NumPackage,
		Weight:              shippingOrder.Weight,
		VoucherCode:         shippingOrder.VoucherCode,
		References:          shippingOrder.References,
	}

	if shippingOrder.OrderValue != nil {
		req.OrderValue = *shippingOrder.OrderValue
	}

	if shippingOrder.IsBookDropOff == nil || !*shippingOrder.IsBookDropOff {
		req.IsBookDropOff = false
	} else {
		req.IsBookDropOff = true
		if shippingOrder.ShippingType != nil &&
			*shippingOrder.ShippingType == enum.ShippingOrderType.FMPO {
			fmHub := shippingOrder.Scope[0]
			fmHubRaw := model.HubDB.QueryOne(bson.M{
				"code": fmHub,
			})
			if fmHubRaw.Status == common.APIStatus.Ok {
				hub := fmHubRaw.Data.([]*model.Hub)[0]
				req.From.Address = hub.Address.Address
				req.From.Code = hub.Code
				req.From.ProvinceCode = hub.Address.ProvinceCode
				req.From.DistrictCode = hub.Address.DistrictCode
				req.From.WardCode = hub.Address.WardCode
				req.From.ProvinceName = hub.Address.ProvinceName
				req.From.DistrictName = hub.Address.DistrictName
				req.From.WardName = hub.Address.WardName
			}

			lmHub := shippingOrder.CustomerCode
			lmHubRaw := model.HubDB.QueryOne(bson.M{
				"warehouse_reference_code": lmHub,
			})
			if lmHubRaw.Status == common.APIStatus.Ok {
				hub := lmHubRaw.Data.([]*model.Hub)[0]
				req.To.Address = hub.Address.Address
				req.To.Code = hub.Code
				req.To.ProvinceCode = hub.Address.ProvinceCode
				req.To.DistrictCode = hub.Address.DistrictCode
				req.To.WardCode = hub.Address.WardCode
				req.To.ProvinceName = hub.Address.ProvinceName
				req.To.DistrictName = hub.Address.DistrictName
				req.To.WardName = hub.Address.WardName
			}
		}
	}

	if shippingOrder.CheckinNumPack != 0 {
		req.NumPackage = shippingOrder.CheckinNumPack
	}

	return req
}

func CalculateAreaOrdersFee(orders []*model.ShippingOrder, configFee *model.ConfigFee) ([]*model.ShippingOrder, error) {
	applyFeeAtIndexConfigFee := map[int][]*model.ShippingOrder{}
	splitOrderFilter := []*model.ShippingOrder{}
	for _, order := range orders {
		// Chi tinh tien don dau tien chu khong tinh tien don tach
		if order.SplitFromCode != "" {
			continue
		}
		splitOrderFilter = append(splitOrderFilter, order)
	}
	for _, order := range splitOrderFilter {
		for index, feeArea := range configFee.FeeAreaList {
			bookReq := shippingOrderToBookShippingReq(order)
			if isMatchFeeArea(bookReq, feeArea) {
				applyFeeAtIndexConfigFee[index] = append(applyFeeAtIndexConfigFee[index], order)
			}
		}
	}

	var needUpdateOrderFee []*model.ShippingOrder
	for atIndexFee, groupOrders := range applyFeeAtIndexConfigFee {
		for _, order := range groupOrders {
			for _, freeStruct := range configFee.FeeAreaList[atIndexFee].FeeStructures {
				isMatchFeeStructure := false
				for _, condition := range freeStruct.FeeConditions {
					switch condition.ConditionType {
					case enum.FeeConditionType.PACKAGE:
						isMatchFeeStructure = isMatchNumPack(order.NumPackage, condition)
					case enum.FeeConditionType.WEIGHT:
						isMatchFeeStructure = isMatchWeight(order.Weight, condition)
					case enum.FeeConditionType.TOTAL_ORDER:
						isMatchFeeStructure = isMatchTotalOrder(int64(len(groupOrders)), condition)
					case enum.FeeConditionType.TOTAL_PACKAGE:
						isMatchFeeStructure = isMatchTotalNumPackages(int64(len(groupOrders)), condition)
					case enum.FeeConditionType.ORDER_VALUE:
						if order.OrderValue == nil {
							break
						}
						isMatchFeeStructure = isMatchOrderValue(*order.OrderValue, condition)
					}
					// TODO: Nếu có loại mới thêm vào cụm switch case này để mở rộng
					if !isMatchFeeStructure {
						break
					}
				}
				if isMatchFeeStructure {
					var feePerPackage float64 = 0
					var feePercentOrderValue float64 = 0
					var additionalFee float64 = 0
					if fee, ok := freeStruct.Fees[enum.FeeType.FEE_PER_PACKAGE]; ok {
						feePerPackage = fee
					}

					if fee, ok := freeStruct.Fees[enum.FeeType.PERCENT_ORDER_VALUE]; ok {
						feePercentOrderValue = fee / 100
					}

					if fee, ok := freeStruct.Fees[enum.FeeType.ADDITIONAL_FEE]; ok {
						additionalFee = fee
					}
					var zero float64 = 0
					if order.OrderValue == nil {
						order.OrderValue = &zero
					}
					// Công thức tính phí tổng quát:
					// Phí = Số lượng kiện * Phí kiện + Giá trị đơn hàng * phần trăm phí trên giá trị đơn hàng + Phí thêm
					calculatedFee := float64(order.NumPackage)*feePerPackage + *order.OrderValue*feePercentOrderValue + additionalFee
					// TODO: Add voucher validation and discount here, for now if voucher is not empty, we will not calculate fee
					if order.VoucherCode != "" {
						calculatedFee = 0
					}
					if calculatedFee != order.FeeAmount ||
						order.VoucherCode != "" ||
						calculatedFee == 0 {
						order.FeeAmount = calculatedFee
						needUpdateOrderFee = append(needUpdateOrderFee, order)
					}
				}
			}
		}
	}
	return needUpdateOrderFee, nil
}

func isContainInAreaIdentifier(areaIdentifier []model.AreaIdentifier, code string) bool {
	for _, a := range areaIdentifier {
		if a.Code == code {
			return true
		}
	}
	return false
}
