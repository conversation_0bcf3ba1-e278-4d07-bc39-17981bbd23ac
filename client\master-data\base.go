package master_data

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/golang/configuration"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathGetProvinceByCode = "/core/master-data/v1/province"
	pathGetProvinces      = "/core/master-data/v1/province/list"
	pathGetDistrictByCode = "/core/master-data/v1/district"
	pathGetDistrictAll    = "/core/master-data/v1/district/list"
	pathGetWardByCode     = "/core/master-data/v1/ward/list"
)

// MasterDataClient
type MasterDataClient struct {
	svc     *client.RestClient
	headers map[string]string
}

func NewServiceClient(serviceConfig configuration.Service, logName string, session *mongo.Database) *MasterDataClient {
	if serviceConfig.Host == "" {
		return nil
	}

	masterDataClient := &MasterDataClient{
		svc: client.NewRESTClient(
			serviceConfig.Host,
			logName,
			3*time.Second,
			1,
			3*time.Second,
		),
		headers: map[string]string{
			"Authorization": serviceConfig.Authorization,
		},
	}

	masterDataClient.svc.SetDBLog(session)
	return masterDataClient
}

func (cli *MasterDataClient) GetAllProvinces() (results []model.Province, err error) {

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, nil, nil, pathGetProvinces, nil)
	if err != nil {
		log.Println("Call marketplace ", err)
		return nil, err
	}

	type myResponse struct {
		Status string           `json:"status"`
		Code   string           `json:"code"`
		Data   []model.Province `json:"data"`
	}

	response := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		log.Println(err)
		return nil, err
	}

	if response.Status != common.APIStatus.Ok || response.Data == nil {
		return nil, fmt.Errorf("%v", response.Code)
	}

	results = response.Data
	return
}

func (cli *MasterDataClient) GetDistrictByProvinceCode(provinceCode string) (result []*model.District, err error) {
	params := map[string]string{
		"provinceCode": provinceCode,
	}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetDistrictByCode, nil)
	if err != nil {
		log.Println("Call marketplace prd", err)
		return nil, err
	}

	type myResponse struct {
		Status  string            `json:"status"`
		Code    string            `json:"code"`
		Message string            `json:"message"`
		Data    []*model.District `json:"data"`
	}

	response := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return nil, err
	}

	if response.Status != common.APIStatus.Ok || response.Data == nil {
		return nil, fmt.Errorf("%v-%v", response.Code, response.Message)
	}

	result = response.Data
	return
}

func (cli *MasterDataClient) GetWardByDistrictCode(code, districtCode, provinceCode string) (result []*model.Ward, err error) {
	params := map[string]string{
		"wardCode":     code,
		"districtCode": districtCode,
		"provinceCode": provinceCode,
	}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetWardByCode, nil)
	if err != nil {
		log.Println("Call marketplace prd", err)
		return nil, err
	}

	type myResponse struct {
		Status  string        `json:"status"`
		Code    string        `json:"code"`
		Message string        `json:"message"`
		Data    []*model.Ward `json:"data"`
	}

	response := new(myResponse)

	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		log.Println(err)
		return nil, err
	}

	if response.Status != common.APIStatus.Ok || response.Data == nil {
		return nil, fmt.Errorf("%v-%v", response.Code, response.Message)
	}

	if len(response.Data) > 0 {
		result = response.Data
	}
	return
}
