package warehouse_core

import (
	"time"

	"go.mongodb.org/mongo-driver/mongo"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/golang/configuration"
)

const (
	pathGetWarehouse                = "/warehouse/core/v1/warehouse"
	pathSaleOrder                   = "/warehouse/core/v1/sale-orders"
	pathGetSaleOrder                = "/warehouse/core/v1/sale-orders/list"
	pathGetProvince                 = "/warehouse/core/v1/master-data/province"
	pathGetDistrict                 = "/warehouse/core/v1/master-data/district"
	pathGetWard                     = "/warehouse/core/v1/master-data/ward"
	pathSendTelegramMessage         = "/warehouse/core/v1/telegram/send"
	pathInboundWarehouse            = "/warehouse/inbound/v1/receipt-session/transport"
	pathDoneTransportWarehouse      = "/warehouse/inbound/v1/return-ticket/marketplace/check-in"
	pathDoneTOWarehouse             = "/warehouse/inbound/v1/receipt-session/to/transport"
	pathResetLocationWarehouse      = "/warehouse/inventory/v1/location/reset"
	padthGetLocationDetailWarehouse = "/warehouse/inventory/v1/location"
	pathDeliveryOrder               = "/warehouse/core/v1/delivery-order"
	pathDeliveryOrderItem           = "/warehouse/core/v1/delivery-order-item"
	pathDoneReturnRO                = "/warehouse/inbound/v1/return-order/check-in"
	pathDoneReceive                 = "/warehouse/inbound/v1/receipt-session/po"
	pathUpdateFMCode                = "/warehouse/inbound/v1/inbound-ticket/fm-code"
)

type Client struct {
	svc     *client.RestClient
	headers map[string]string
}

func NewServiceClient(serviceConfig configuration.Service, logName string, session *mongo.Database) *Client {
	if serviceConfig.Host == "" {
		return nil
	}
	warehouseClient := &Client{
		svc: client.NewRESTClient(
			serviceConfig.Host,
			logName,
			30*time.Second,
			0,
			30*time.Second,
		),
		headers: map[string]string{
			"Authorization": serviceConfig.Authorization,
		},
	}
	warehouseClient.svc.SetDBLog(session)
	return warehouseClient
}
