package response

import (
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
)

type PrepareCheckinResponse struct {
	BinCode       string                         `json:"binCode"`
	Status        enum.PrepareCheckinStatusValue `json:"status"`
	TransportCode string                         `json:"transportCode"`
	ReferenceCode string                         `json:"referenceCode"`
	TotalBin      int                            `json:"totalBin"`
}
type CountReadyToTransferResponse struct {
	TotalOrder   int `json:"totalOrder"`
	TotalPackage int `json:"totalPackage"`
}
