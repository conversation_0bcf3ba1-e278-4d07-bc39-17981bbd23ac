package transporting

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
)

const (
	pathCompleteShippingOrder = "/delivery/transporting/v1/shipping-order/complete"
)

func (cli *Client) CompleteShippingOrder(referenceCodes []string) (err error) {
	body := map[string][]string{
		"referenceCodes": referenceCodes,
	}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, nil, body, pathCompleteShippingOrder, nil)
	if err != nil {
		return err
	}

	type myResponse struct {
		Status  string `json:"status"`
		Message string `json:"message"`
	}

	response := new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return err
	}

	if response.Status != common.APIStatus.Ok {
		return err
	}

	return
}
