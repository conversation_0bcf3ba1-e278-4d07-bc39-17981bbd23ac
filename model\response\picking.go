package response

type PickTicket struct {
	TicketID      int64     `json:"ticketId,omitempty"`
	WarehouseCode string    `json:"warehouseCode,omitempty"`
	SO            string    `json:"so,omitempty"`
	Baskets       []*Basket `json:"baskets,omitempty"`
	EndTime       int64     `json:"endTime,omitempty" bson:"end_time,omitempty"`
}

type Basket struct {
	Codes []string `json:"codes,omitempty"`
	Type  string   `json:"type,omitempty"`
}
