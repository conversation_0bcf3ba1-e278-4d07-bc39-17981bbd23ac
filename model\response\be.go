package response

type BookBeOrderResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		BeSessionId       string  `json:"be_session_id"`
		EstimatedDistance float64 `json:"estimated_distance"`
		EstimatedTime     float64 `json:"estimated_time"`
		TotalPrice        float64 `json:"total_price"`
		Discount          float64 `json:"discount"`
		TrackingUrl       string  `json:"tracking_url"`
		Status            string  `json:"status"`
		OrderId           string  `json:"order_id"`
		Fare              struct {
			DeliveryCharges float64 `json:"delivery_charges"`
			EtaTime         float64 `json:"eta_time"`
			CodFee          float64 `json:"cod_fee"`
			FactorSurge     float64 `json:"factor_surge"`
			BulkyFee        float64 `json:"bulky_fee"`
			SmsFee          float64 `json:"sms_fee"`
			DoorToDoorFee   float64 `json:"door_to_door_fee"`
		} `json:"fare"`
	} `json:"data"`
}
type CalculateBeFeeResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    []struct {
		ServiceType       string  `json:"service_type"`
		Name              string  `json:"name"`
		Description       string  `json:"description"`
		IsActive          bool    `json:"is_active"`
		FinalFare         float64 `json:"final_fare"`
		BaseFare          float64 `json:"base_fare"`
		InsuranceFee      float64 `json:"insurance_fee"`
		SurgeFactor       float64 `json:"surge_factor"`
		ServiceFee        float64 `json:"service_fee"`
		Discount          float64 `json:"discount"`
		EstimatedDistance float64 `json:"estimated_distance"`
		EstimatedTime     float64 `json:"estimated_time"`
		AddonFees         []struct {
			Key string `json:"key"`
			Fee int    `json:"fee"`
		} `json:"addon_fees"`
	} `json:"data"`
}

type BeCancelOrderResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

type BeAuth struct {
	AccessToken string `json:"access_token"`
	ExpireAt    string `json:"expire_at"`
	PartnerName string `json:"partner_name"`
}

type BeAuthData struct {
	Be BeAuth `json:"auth"`
}

type BeAuthResponse struct {
	Code    int        `json:"code"`
	Message string     `json:"message"`
	Data    BeAuthData `json:"data"`
}
