package model

import (
	"time"

	"github.com/google/uuid"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type SyncLock struct {
	Key             string    `json:"key" bson:"key"`
	VersionNo       string    `json:"versionNo" bson:"version_no"`
	NextUpdatedTime time.Time `json:"nextUpdatedTime" bson:"next_updated_time"`
}

// SyncLockDB represent DB repo of this model
var SyncLockDB = &db.Instance{
	ColName:        "sync_lock",
	TemplateObject: &SyncLock{},
}

// InitLockDBModel ...
func InitLockDBModel(s *mongo.Database) {
	SyncLockDB.ApplyDatabase(s)

	t := true
	SyncLockDB.CreateIndex(bson.D{
		{"key", 1},
	}, &options.IndexOptions{
		Unique: &t,
	})

	syncAccountKeyGen := &SyncLock{
		Key:       enum.SYNC_ACCOUNT_JOB,
		VersionNo: uuid.NewString(),
	}

	syncLeadTimeKeyGen := &SyncLock{
		Key:       enum.SYNC_LEADTIME_JOB,
		VersionNo: uuid.NewString(),
	}

	syncCrawlProductivity := &SyncLock{
		Key:       enum.CRAWL_PRODUCTIVITY,
		VersionNo: uuid.NewString(),
	}

	syncCalculateLeadTime := &SyncLock{
		Key:       enum.CALCULATE_SUGGEST_LEAD_TIME_JOB,
		VersionNo: uuid.NewString(),
	}

	autoCancelOrderJob := &SyncLock{
		Key:       enum.AUTO_CANCEL_ORDER_JOB,
		VersionNo: uuid.NewString(),
	}

	SyncLockDB.Create(syncAccountKeyGen)
	SyncLockDB.Create(syncLeadTimeKeyGen)
	SyncLockDB.Create(syncCrawlProductivity)
	SyncLockDB.Create(syncCalculateLeadTime)
	SyncLockDB.Create(autoCancelOrderJob)
}
