package warehouse_core

import (
	"encoding/json"
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
)

func (cli *Client) GetDistrictByProvinceCode(provinceCode string, name string) (result []*model.District, err error) {
	params := map[string]string{
		"provinceCode": provinceCode,
		"name":         name,
	}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetDistrict, nil)
	if err != nil {
		return nil, err
	}

	type myResponse struct {
		Status string            `json:"status"`
		Code   string            `json:"code"`
		Data   []*model.District `json:"data"`
	}

	response := new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return nil, err
	}

	if response.Status != common.APIStatus.Ok || response.Data == nil {
		return nil, fmt.Errorf("%v", response.Code)
	}

	if len(response.Data) > 0 {
		result = response.Data
	}
	return
}

func (cli *Client) GetProvince(name string) (results []*model.Province, err error) {
	params := map[string]string{
		"name": name,
	}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetProvince, nil)
	if err != nil {
		return nil, err
	}

	type myResponse struct {
		Status  string            `json:"status"`
		Code    string            `json:"code"`
		Message string            `json:"message"`
		Data    []*model.Province `json:"data"`
	}

	response := new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return nil, err
	}

	if response.Status != common.APIStatus.Ok || response.Data == nil {
		return nil, fmt.Errorf("%v", response.Message)
	}

	results = response.Data
	return
}

func (cli *Client) GetWard(name, wardCode, districtCode, provinceCode string) (results []*model.Ward, err error) {
	params := map[string]string{
		"name":         name,
		"code":         wardCode,
		"districtCode": districtCode,
		"provinceCode": provinceCode,
	}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetWard, nil)
	if err != nil {
		return nil, err
	}

	type myResponse struct {
		Status string        `json:"status"`
		Code   string        `json:"code"`
		Data   []*model.Ward `json:"data"`
	}

	response := new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return nil, err
	}

	if response.Status != common.APIStatus.Ok || response.Data == nil {
		return nil, fmt.Errorf("%v", response.Code)
	}

	results = response.Data
	return
}

func (cli *Client) GetProvinceByCode(code string) (results []*model.Province, err error) {
	params := map[string]string{
		"provinceCode": code,
	}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetProvince, nil)
	if err != nil {
		return nil, err
	}

	type myResponse struct {
		Status  string            `json:"status"`
		Code    string            `json:"code"`
		Message string            `json:"message"`
		Data    []*model.Province `json:"data"`
	}

	response := new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return nil, err
	}

	if response.Status != common.APIStatus.Ok || response.Data == nil {
		return nil, fmt.Errorf("%v", response.Message)
	}

	results = response.Data
	return
}

func (cli *Client) GetDistrictByCode(provinceCode string, districtCode string) (result []*model.District, err error) {
	params := map[string]string{
		"provinceCode": provinceCode,
		"districtCode": districtCode,
	}

	var res *client.RestResult
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetDistrict, nil)
	if err != nil {
		return nil, err
	}

	type myResponse struct {
		Status string            `json:"status"`
		Code   string            `json:"code"`
		Data   []*model.District `json:"data"`
	}

	response := new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &response)
	if err != nil {
		return nil, err
	}

	if response.Status != common.APIStatus.Ok || response.Data == nil {
		return nil, fmt.Errorf("%v", response.Code)
	}

	if len(response.Data) > 0 {
		result = response.Data
	}
	return
}
