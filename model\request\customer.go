package request

import (
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
)

type GetCustomerRequest struct {
	model.Customer
	NeedCountOrder *bool `json:"needCountOrder,omitempty"`
}

type ImportCustomerRequest struct {
	Code         string                 `json:"code,omitempty"`
	CustomerType enum.CustomerTypeValue `json:"customerType,omitempty"`
	Note         string                 `json:"note,omitempty"`
	Tags         []string               `json:"tags,omitempty"`
}
