package response

import (
	"time"

	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
)

type Callback struct {
	CreatedSource   *enum.PartnerValue `json:"createdSource,omitempty"`
	CreatedBy       int64              `json:"createdBy,omitempty"`
	UpdatedBy       int64              `json:"updatedBy,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty"`

	HubCode         string                 `json:"hubCode"`
	WarehouseCode   string                 `json:"warehouseCode,omitempty"`
	CurrentLocation string                 `json:"currentLocation,omitempty"`
	SO              string                 `json:"so,omitempty"`
	TPLCode         string                 `json:"tplCode,omitempty"`
	Status          *enum.StatusValue      `json:"status,omitempty"`
	Action          string                 `json:"action"`
	ActionName      string                 `json:"actionName"`
	TPLStatus       string                 `json:"tplStatus,omitempty"`
	StatusName      string                 `json:"statusName,omitempty"`
	TPLStatusName   string                 `json:"tplStatusName,omitempty"`
	Weight          float64                `json:"weight,omitempty"`
	Reason          string                 `json:"reason,omitempty"`
	ReasonCode      string                 `json:"reasonCode,omitempty"`
	TotalFee        float64                `json:"totalFee,omitempty"`
	Height          int                    `json:"height,omitempty"`
	Width           int                    `json:"width,omitempty"`
	Length          int                    `json:"length,omitempty"`
	COD             float64                `json:"cod,omitempty"`
	ActionTime      *time.Time             `json:"actionTime,omitempty"`
	Logs            []*Callback            `json:"logs,omitempty"`
	ExternalTPLName string                 `json:"externalTPLName"`
	ExternalTPLId   int64                  `json:"externalTplId"`
	NumPackage      int64                  `json:"numPackage,omitempty"`
	ExtraCallback   map[string]interface{} `json:"extraCallback,omitempty"`
	DeliverID       int64                  `json:"deliverId"`
	DeliverName     string                 `json:"deliverName"`
}
