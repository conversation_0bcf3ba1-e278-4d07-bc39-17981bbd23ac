package seller

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/golang/configuration"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathGetSellers = "/seller/core/v1/account/list"
)

type SellerClient struct {
	svc     *client.RestClient
	headers map[string]string
}

func NewServiceClient(serviceConfig configuration.Service, logName string, session *mongo.Database) *SellerClient {
	if serviceConfig.Host == "" {
		return nil
	}
	sellerClient := &SellerClient{
		svc: client.NewRESTClient(
			serviceConfig.Host,
			logName,
			300*time.Second,
			5,
			3*time.Second,
		),
		headers: map[string]string{
			"Authorization": serviceConfig.Authorization,
		},
	}
	sellerClient.svc.SetDBLog(session)
	return sellerClient
}
