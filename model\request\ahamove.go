package request

import (
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
)

type BookAhamoveRequest struct {
	Token             string            `json:"token"`
	OrderTime         int64             `json:"order_time"`
	Path              []*AhamovePath    `json:"path"`
	ServiceId         string            `json:"service_id"`
	Requests          []*AhamoveRequest `json:"requests"`
	PaymentMethod     string            `json:"payment_method"`
	PromoCode         string            `json:"promo_code"`
	Remarks           string            `json:"remarks"`
	IdleUntil         int64             `json:"idle_until"`
	Type              string            `json:"type"`
	NeedOptimizeRoute bool              `json:"need_optimize_route"`
}

type AhamoveRequest struct {
	Id        string `json:"_id"`
	ServiceId string `json:"service_id"`
}

type AhamovePath struct {
	PartnerField    *PartnerAhamove                  `json:"partner_fields"`
	Address         string                           `json:"address"`
	Name            string                           `json:"name"`
	Mobile          string                           `json:"mobile"`
	FailTime        float64                          `json:"fail_time"`
	FailComment     string                           `json:"fail_comment"`
	CompleteTime    float64                          `json:"complete_time"`
	CompleteComment string                           `json:"complete_comment"`
	TrackingNumber  string                           `json:"tracking_number"`
	Remarks         string                           `json:"remarks"`
	COD             int64                            `json:"cod"`
	ItemValue       int64                            `json:"item_value"`
	ReturnTime      float64                          `json:"return_time"`
	Status          *enum.AhamoveDeliveryStatusValue `json:"status"`
	Building        string                           `json:"building"`
	Apt_Number      string                           `json:"apt_number"`
	RedeliveryNote  *RedeliveryNote                  `json:"redelivery_note"`
	RequirePod      bool                             `json:"require_pod"`
	PodType         string                           `json:"pod_type"`
}

type PartnerAhamove struct {
	StoreId string `json:"store_id"`
}

type RedeliveryNote struct {
	FromTime float64
	ToTime   float64
	Address  string
	Lat      float64
	Lng      float64
}

type CancelAhamoveRequest struct {
	Token   string `json:"token"`
	OrderId string `json:"order_id"`
	Comment string `json:"comment"`
}

type AhamoveCallback struct {
	Id                  string                       `json:"_id"`
	Status              *enum.AhamoveMainStatusValue `json:"status"`
	ServiceId           string                       `json:"service_id"`
	CityId              string                       `json:"city_id"`
	UserId              string                       `json:"user_id"`
	UserName            string                       `json:"user_name"`
	Partner             string                       `json:"partner"`
	SupplierId          string                       `json:"supplier_id"`
	SupplierName        string                       `json:"supplier_name"`
	Path                []*AhamovePath               `json:"path"`
	CreateTime          float64                      `json:"create_time"`
	OrderTime           float64                      `json:"order_time"`
	AcceptTime          float64                      `json:"accept_time"`
	BoardTime           float64                      `json:"board_time"`
	PickupTime          float64                      `json:"pickup_time"`
	CancelTime          float64                      `json:"cancel_time"`
	CompleteTime        float64                      `json:"complete_time"`
	CancelComment       string                       `json:"cancel_comment"`
	Currency            string                       `json:"currency"`
	CancelByUser        bool                         `json:"cancel_by_user"`
	PromoCode           string                       `json:"promo_code"`
	PaymentMethod       string                       `json:"payment_method"`
	StopFee             int64                        `json:"stop_fee"`
	RequestFee          int64                        `json:"request_fee"`
	DistanceFee         int64                        `json:"distance_fee"`
	Discount            int64                        `json:"discount"`
	TotalFee            int64                        `json:"total_fee"`
	UserBonusAccount    int64                        `json:"user_bonus_account"`
	UserMainAccount     int64                        `json:"user_main_account"`
	TotalPay            int64                        `json:"total_pay"`
	DistancePrice       int64                        `json:"distance_price"`
	SpecialRequestPrice int64                        `json:"special_request_price"`
	StoppointPrice      int64                        `json:"stoppoint_price"`
	VoucherDiscount     int64                        `json:"voucher_discount"`
	SubStatus           string                       `json:"sub_status"`
	SubtotalPrice       int64                        `json:"subtotal_price"`
	TotalPrice          int64                        `json:"total_price"`
}

type LatLng struct {
	Lat float64 `json:"lat"`
	Lng float64 `json:"lng"`
}

// Structure of On Wheel service
// Format of request to Distance optimization
type AhamoveRouteOptimizationRequest struct {
	Depots           [][]float64           `json:"depots,omitempty"`
	Points           [][]float64           `json:"points,omitempty"`
	VehicleNumber    int                   `json:"vehicle_num,omitempty"`
	MaxParcels       int                   `json:"max_parcels,omitempty"`
	MinParcels       int                   `json:"min_parcels,omitempty"`
	MaxDistance      int                   `json:"max_distance,omitempty"`
	PointMaxDistance int                   `json:"point_max_distance,omitempty"`
	MinVehicles      bool                  `json:"min_vehicles,omitempty"`
	ResultMode       string                `json:"result_mode,omitempty"` // COORDINATES OR INDEX
	Dimensions       []*model.VRPDimension `json:"dimensions,omitempty"`
	TransportMode    string                `json:"transport_mode,omitempty"`
}

type AhamoveRouteOptimizationDimension struct {
	Values      []int  `json:"values,omitempty"`
	Name        string `json:"name,omitempty"`
	MaxCapacity int    `json:"max_capacity,omitempty"`
}
