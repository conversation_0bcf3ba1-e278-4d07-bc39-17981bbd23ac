package request

type ZoneQuery struct {
	ZoneCode      []string `json:"codes,omitempty"`
	ListWardCode  []string `json:"wards,omitempty"`
	ListProvince  []string `json:"provinces,omitempty"`
	ListDistrict  []string `json:"districts,omitempty"`
	FromTime      int64    `json:"fromTime,omitempty"`
	ToTime        int64    `json:"toTime,omitempty"`
	IsAssignToHub *bool    `json:"isAssignToHub"`
	Keyword       string   `json:"keyword"`
	ParentCode    *bool    `json:"parentCode"`
	IsParentZone  *bool    `json:"isParentZone"`
}
