package api

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
)

// CreateCarrier api
func CreateCarrier(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Carrier
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.CreateCarrier(&input))
}

// GetCarrier api
func GetCarrier(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input request.GetCarrierRequest
	str := req.GetParam("q")
	if str == "" {
		str = "{}"
	}
	var offset = int64(sdk.ParseInt(req.GetParam("offset"), 0))
	var limit = int64(sdk.ParseInt(req.GetParam("limit"), 100))
	var getTotal = req.GetParam("getTotal") == "true"
	err := json.Unmarshal([]byte(str), &input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.GetCarrier(&input, offset, limit, getTotal))
}

// GetCarrierActive api
func GetCarrierActive(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input request.GetCarrierRequest
	str := req.GetParam("q")
	if str == "" {
		str = "{}"
	}
	var offset = int64(sdk.ParseInt(req.GetParam("offset"), 0))
	var limit = int64(sdk.ParseInt(req.GetParam("limit"), 100))
	var getTotal = req.GetParam("getTotal") == "true"
	err := json.Unmarshal([]byte(str), &input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	t := true
	input.Active = &t
	return resp.Respond(action.GetCarrier(&input, offset, limit, getTotal))
}

// UpdateCarrier api
func UpdateCarrier(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Carrier
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.UpdateCarrier(&input))
}

// TransferPackageCarrier api
func TransferPackageCarrier(req sdk.APIRequest, resp sdk.APIResponder) error {

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Successfully",
	})
}

// DeleteCarrier api
func DeleteCarrier(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Carrier
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	return resp.Respond(action.DeleteCarrier(input.CarrierId))
}

func GenCarrierKeyword(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.GenCarrierKeyword())
}

func UpdateCarrierAccessToken(req sdk.APIRequest, resp sdk.APIResponder) error {
	type myRequest struct {
		CarrierId   int64  `json:"carrierId"`
		AccessToken string `json:"accessToken"`
	}

	var input myRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data: " + err.Error(),
			ErrorCode: "JSON_INVALID",
		})
	}

	return resp.Respond(action.UpdateCarrierAccessToken(input.CarrierId, input.AccessToken))
}
