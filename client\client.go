package client

import (
	"fmt"

	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/geocode"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/mkp"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/notification"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/picking"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/seller"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/transporting"
	"go.mongodb.org/mongo-driver/mongo"

	accounting "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/accounting"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/hrm"
	masterData "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/master-data"
	tplCallback "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/tpl-callback"
	warehouseCore "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/warehouse-core"
	webHook "gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/web-hook"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
)

type gatewayClient struct {
	WarehouseCoreClient *warehouseCore.Client
	TplCallbackClient   *tplCallback.TPLCallbackClient
	PickingClient       *picking.PickingClient
	GeocodeClient       *geocode.GeoCodeClient
	HrmClient           *hrm.HrmClient
	AccountingClient    *accounting.AccountingClient
	NotificationClient  *notification.NotificationClient
	TransportingClient  *transporting.Client
	SellerClient        *seller.SellerClient
	MasterDataClient    *masterData.MasterDataClient
	MkpClient           *mkp.MkpClient
	WebHookClients      map[string]*webHook.WebHookClient
}

var (
	Services              *gatewayClient
	ErrUnAvailableService = fmt.Errorf("%s", "Service invoice unavailable")
)

func Init(connection *mongo.Database) {
	Services = &gatewayClient{
		WarehouseCoreClient: warehouseCore.NewServiceClient(conf.Config.ThuocsiSvcConf, conf.Config.LogDBConf.DatabaseName, connection),
		TplCallbackClient:   tplCallback.NewServiceClient(conf.Config.ThuocsiSvcConf, conf.Config.LogDBConf.DatabaseName, connection),
		PickingClient:       picking.NewServiceClient(conf.Config.ThuocsiSvcConf, conf.Config.LogDBConf.DatabaseName, connection),
		GeocodeClient:       geocode.NewServiceClient(conf.Config.MapGoogleHost, conf.Config.GeoCodeApiKey, conf.Config.LogDBConf.DatabaseName, connection),
		HrmClient:           hrm.NewServiceClient(conf.Config.ThuocsiSvcConf, conf.Config.LogDBConf.DatabaseName, connection),
		NotificationClient:  notification.NewServiceClient(conf.Config.ThuocsiSvcConf.Host, conf.Config.LogDBConf.DatabaseName, connection),
		AccountingClient:    accounting.NewServiceClient(conf.Config.ThuocsiSvcConf, "accounting", connection),
		TransportingClient:  transporting.NewServiceClient(conf.Config.ThuocsiSvcConf, "transporting", connection),
		SellerClient:        seller.NewServiceClient(conf.Config.ThuocsiSvcConf, conf.Config.LogDBConf.DatabaseName, connection),
		MasterDataClient:    masterData.NewServiceClient(conf.Config.ThuocsiSvcConf, conf.Config.LogDBConf.DatabaseName, connection),
		MkpClient:           mkp.NewServiceClient(conf.Config.ThuocsiSvcConf, conf.Config.LogDBConf.DatabaseName, connection),
		WebHookClients:      map[string]*webHook.WebHookClient{},
	}

	Services.InitWebhookClient(conf.Config.WebHookConfig, connection)
}

func (s *gatewayClient) InitWebhookClient(configs map[string]*conf.WebHookConfig, connection *mongo.Database) {
	for _, cfg := range configs {
		s.WebHookClients[cfg.PartnerCode] = webHook.NewServiceClient(cfg.Url, conf.Config.LogDBConf.DatabaseName, cfg, connection)
	}
}

func GetWebhookClient(code string) *webHook.WebHookClient {
	return Services.WebHookClients[code]
}
