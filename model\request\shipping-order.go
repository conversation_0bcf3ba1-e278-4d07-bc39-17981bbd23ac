package request

import (
	"fmt"

	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
)

type ShippingOrderQuery struct {
	WarehouseCode                string                         `json:"warehouseCode,omitempty"`
	Status                       *enum.StatusValue              `json:"status,omitempty"`
	Statuses                     []*enum.StatusValue            `json:"statuses,omitempty"`
	ReferenceCode                string                         `json:"referenceCode,omitempty"`
	ParentReferenceCode          string                         `json:"parentReferenceCode,omitempty"`
	ListReferenceCode            []string                       `json:"listReferenceCode,omitempty"`
	TrackingCode                 string                         `json:"trackingCode,omitempty"`
	ListTrackingCode             []string                       `json:"listTrackingCode,omitempty"`
	TplCode                      *enum.PartnerValue             `json:"tplCode,omitempty"`
	ListTplCode                  []*enum.PartnerValue           `json:"listTplCode,omitempty"`
	DriverId                     int64                          `json:"driverId,omitempty"`
	FromTime                     int64                          `json:"fromDate"`
	ToTime                       int64                          `json:"toDate"`
	ListTplServiceId             []int64                        `json:"listTplServiceId"`
	ProvinceCode                 string                         `json:"provinceCode"`
	DistrictCode                 string                         `json:"districtCode"`
	WardCode                     string                         `json:"wardCode"`
	ProvinceCodes                []string                       `json:"provinceCodes"`
	DistrictCodes                []string                       `json:"districtCodes"`
	WardCodes                    []string                       `json:"wardCodes"`
	EstimateTime                 int64                          `json:"estimateTime"`
	FilterEstimatePickingTime    bool                           `json:"filterEstimatePickingTime"`
	FilterEstimateDeliveringTime bool                           `json:"filterEstimateDeliveringTime"`
	FilterEstimateReturningTime  bool                           `json:"filterEstimateReturningTime"`
	ShippingTypes                []string                       `json:"types"`
	Skus                         []string                       `json:"skus"`
	Departments                  []string                       `json:"departments"`
	FromProvinceCodes            []string                       `json:"fromProvinceCodes"`
	FromDistrictCodes            []string                       `json:"fromDistrictCodes"`
	FromWardCodes                []string                       `json:"fromWardCodes"`
	Tags                         []string                       `json:"tags"`
	IsBookDropOff                *bool                          `json:"isBookDropOff"`
	ParentReceiveSessionCode     string                         `json:"parentReceiveSessionCode"`
	ReadPreference               *enum.ReadPreferenceValue      `json:"readPreference"`
	MergeStatus                  *enum.MergeStatusValue         `json:"mergeStatus,omitempty"`
	MergeStatuses                []*enum.MergeStatusValue       `json:"mergeStatuses,omitempty"`
	CustomerCode                 string                         `json:"customerCode,omitempty"`
	CustomerOrderStatus          *enum.CustomerOrderStatusValue `json:"customerOrderStatus,omitempty"`
	FromCreatedTime              int64                          `json:"fromCreatedTime"`
	ToCreatedTime                int64                          `json:"toCreatedTime"`
	LeadTimeQuery                *LeadTimeQuery                 `json:"leadTimeQuery,omitempty"`
	References                   []string                       `json:"references,omitempty"`
}

type UpdateShippingOrderRequest struct {
	ReferenceCode      string `json:"referenceCode,omitempty"`
	CurrentHub         string `json:"currentHub"`
	HandoverTime       int64  `json:"handoverTime"`
	LeadTimePicking    int64  `json:"leadTimePicking"`
	LeadTimeDelivering int64  `json:"leadTimeDelivering"`
	LeadTimeReturning  int64  `json:"leadTimeReturning"`
}

type CheckinPackageRequest struct {
	ReferenceCode  string `json:"referenceCode"`
	WarehouseCode  string `json:"warehouseCode"`
	ReceivePackage int64  `json:"receivePackage"`
}

type CreateHubOrderRequest struct {
	ReferenceCode string `json:"referenceCode"`
}

type UpdateAddressRequest struct {
	ReferenceCode string         `json:"referenceCode,omitempty"`
	HubCode       string         `json:"hubCode,omitempty"`
	From          *model.Address `json:"from,omitempty"`
	To            *model.Address `json:"to,omitempty"`
}

type CompleteBinCode struct {
	ParentReferenceCode string `json:"parentReferenceCode"`
	BinCode             string `json:"binCode"`
}

type ImportFeeRequest struct {
	Lines []ImportFeeLine `json:"lines,omitempty"`
}

type ImportFeeLine struct {
	ReferenceCode string  `json:"referenceCode,omitempty"`
	FeeAmount     float64 `json:"feeAmount,omitempty"`
}

type UpdateOrderFee struct {
	TotalAmount                *float64 `json:"totalAmount"`
	FeeAmount                  *float64 `json:"feeAmount"`
	CodAmount                  *float64 `json:"codAmount"`
	TotalCollectSenderAmount   *float64 `json:"totalCollectSenderAmount,omitempty" bson:"total_collect_sender_amount,omitempty"`
	TotalCollectReceiverAmount *float64 `json:"totalCollectReceiverAmount,omitempty" bson:"total_collect_receiver_amount,omitempty"`
	FeeDebtAmount              *float64 `json:"feeDebtAmount,omitempty" bson:"fee_debt_amount,omitempty"`
	TotalDebtAmount            *float64 `json:"totalDebtAmount,omitempty" bson:"total_debt_amount,omitempty"`
	FeeSenderAmount            *float64 `json:"feeSenderAmount"`
	FeeReceiverAmount          *float64 `json:"feeReceiverAmount"`
	ReferenceCode              string   `json:"referenceCode"`
}

type ImportOrdersRequest struct {
	ShippingOrderType *enum.ShippingOrderTypeValue `json:"shippingOrderType,omitempty"`
	HubCode           string                       `json:"hubCode,omitempty"`
	Orders            []*BookShippingOrder         `json:"orders,omitempty"`
}

type UpdateOrderNoteRequest struct {
	ReferenceCode string `json:"referenceCode"`
	Note          string `json:"note"`
}

type PrepareCheckinRequest struct {
	CheckinAt     enum.CheckInAtValue `json:"checkInAt"`
	WarehouseCode string              `json:"warehouseCode"`
	BinCode       string              `json:"binCode"`
}

func (p *PrepareCheckinRequest) Validate() error {
	if p.BinCode == "" {
		return fmt.Errorf("binCode is required")
	}
	if p.WarehouseCode == "" {
		return fmt.Errorf("locationCode is required")
	}
	if p.CheckinAt == "" {
		return fmt.Errorf("checkInAt is required")
	}
	if p.CheckinAt != enum.CheckInAt.WAREHOUSE {
		return fmt.Errorf("checkInAt must be WAREHOUSE")
	}
	return nil
}

type CheckinTransportItem struct {
	ReferenceCode string `json:"referenceCode"`
	Quantity      int    `json:"quantity"`
}

type CheckinTransport struct {
	TransportCode string                 `json:"transportCode"`
	Items         []CheckinTransportItem `json:"items"`
	TotalBin      int                    `json:"totalBin"`
}

func (c *CheckinTransport) FindBinFromCheckinTransportItems(refCode string) *CheckinTransportItem {
	for _, item := range c.Items {
		if item.ReferenceCode == refCode {
			return &item
		}
	}
	return nil
}

type CheckinShippingOrderRequest struct {
	CheckinAt                  enum.CheckInAtValue         `json:"checkInAt"`
	WarehouseCode              string                      `json:"warehouseCode"`
	Transports                 []CheckinTransport          `json:"transports"`
	ReceiveAtWarehouse         []*model.ReceiveAtWarehouse `json:"receiveAtWarehouse"`
	Delivering                 []string                    `json:"delivering"`
	MissingTransferToWarehouse []string                    `json:"missingTransferToWarehouse"`
	Lost                       []string                    `json:"lost"`
	Unmapping                  []string                    `json:"unmapping"`
	NotUsed                    []string                    `json:"notUsed"`
	Other                      []*model.OtherReason        `json:"other"`
}

func (p *CheckinShippingOrderRequest) Validate() error {
	if p.WarehouseCode == "" {
		return fmt.Errorf("locationCode is required")
	}
	if p.CheckinAt == "" {
		return fmt.Errorf("checkInAt is required")
	}
	if p.CheckinAt != enum.CheckInAt.WAREHOUSE {
		return fmt.Errorf("checkInAt must be WAREHOUSE")
	}
	if len(p.Transports) == 0 {
		return fmt.Errorf("transports is required")
	}
	transportMap := make(map[string]bool)
	binMap := make(map[string]bool)
	for _, transport := range p.Transports {
		if transport.TransportCode == "" {
			return fmt.Errorf("transportCode is required")
		}
		if len(transport.Items) == 0 {
			return fmt.Errorf("items is required")
		}
		if transportMap[transport.TransportCode] {
			return fmt.Errorf("transportCode is duplicated")
		}
		if transport.TotalBin <= 0 {
			return fmt.Errorf("totalBin must be greater than 0")
		}
		if transport.TotalBin < len(transport.Items) {
			return fmt.Errorf("totalBin must be greater than or equal to len(items)")
		}
		transportMap[transport.TransportCode] = true
		for _, item := range transport.Items {
			if item.ReferenceCode == "" {
				return fmt.Errorf("referenceCode is required")
			}
			if item.Quantity <= 0 {
				return fmt.Errorf("quantity must be greater than 0")
			}
			if binMap[item.ReferenceCode] {
				return fmt.Errorf("referenceCode is duplicated")
			}
			binMap[item.ReferenceCode] = true
		}
	}
	return nil
}

type CountReadyToTransferRequest struct {
	FromHubCode string   `json:"fromHubCode"`
	ToHubCode   string   `json:"toHubCode"`
	Statuses    []string `json:"statuses"`
	Type        string   `json:"type"`
}
