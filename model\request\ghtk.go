package request

import (
	"time"

	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
)

type BookGHTKRequest struct {
	Products []*GHTKProduct `json:"products"`
	Order    *GHTKOrder     `json:"order"`
	Details  []*GHTKDetails `json:"details"`
}

type GHTKOrder struct {
	Id                   string  `json:"id"`
	PickName             string  `json:"pick_name"`
	PickMoney            float64 `json:"pick_money"`
	PickAddress          string  `json:"pick_address"`
	PickProvince         string  `json:"pick_province"`
	PickDistrict         string  `json:"pick_district"`
	PickWard             string  `json:"pick_ward"`
	PickStreet           string  `json:"pick_street"`
	PickTel              string  `json:"pick_tel"`
	PickEmail            string  `json:"pick_email"`
	Name                 string  `json:"name"`
	Address              string  `json:"address"`
	Province             string  `json:"province"`
	District             string  `json:"district"`
	Ward                 string  `json:"ward"`
	Street               string  `json:"street"`
	Hamlet               string  `json:"hamlet"`
	Tel                  string  `json:"tel"`
	Note                 string  `json:"note"`
	Email                string  `json:"email"`
	UseReturnAddress     string  `json:"use_return_address"`
	ReturnName           string  `json:"return_name"`
	ReturnAddress        string  `json:"return_address"`
	ReturnProvince       string  `json:"return_province"`
	ReturnDistrict       string  `json:"return_district"`
	ReturnWard           string  `json:"return_ward"`
	ReturnStreet         string  `json:"return_street"`
	ReturnTel            string  `json:"return_tel"`
	ReturnEmail          string  `json:"return_email"`
	IsFreeShip           int64   `json:"is_freeship"`
	PickWorkShift        int64   `json:"pick_work_shift"`
	PickDate             string  `json:"pick_date"`
	DeliverDate          string  `json:"deliver_date"`
	Expired              string  `json:"expired"`
	Value                int64   `json:"value"`
	PickOption           string  `json:"pick_option"`
	TotalWeight          float64 `json:"total_weight"`
	TotalBox             int64   `json:"total_box"`
	ActualTransferMethod string  `json:"actual_transfer_method"`
	Transport            string  `json:"transport"`
	DeliverOption        string  `json:"deliver_option"`
	PickSession          string  `json:"pick_session"`
	OPM                  int     `json:"opm"`
}

type GHTKCallback struct {
	PartnerId         string                `json:"partner_id"`
	LabelId           string                `json:"label_id"`
	StatusId          *enum.GHTKStatusValue `json:"status_id"`
	ActionTime        *time.Time            `json:"action_time"`
	ReasonCode        string                `json:"reason_code"`
	Reason            string                `json:"reason"`
	Weight            float64               `json:"weight"`
	Fee               float64               `json:"fee"`
	PickMoney         int64                 `json:"pick_money"`
	ReturnPartPackage int64                 `json:"return_part_package"` // Nếu bằng 1 là đơn giao hàng một phần
}

type GHTKProduct struct {
	Name        string  `json:"name"`
	Price       float64 `json:"price"`
	Weight      float64 `json:"weight"`
	Quantity    int64   `json:"quantity"`
	ProductCode string  `json:"product_code"`
}

type GHTKDetails struct {
	Date  string  `json:"date"`
	Value float64 `json:"value"`
	Note  string  `json:"note"`
	Type  string  `json:"type"`
}
