package picking

import (
	"encoding/json"
	"fmt"
	"log"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/response"
)

func (cli *PickingClient) CompleteHandOverTransfer(syncData *request.CompleteHandOverRequest) (err error) {

	params := map[string]string{}

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, params, syncData, pathCompleteHandOver, nil)
	if err != nil {
		log.Printf("[ERROR] Call API %s: %v\n", pathCompleteHandOver, err)
		return
	}

	resBody := new(response.BaseResponse)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		log.Printf("[ERROR] Call API %s: %v\n", pathCompleteHandOver, err)
		return
	}

	if resBody.Status != common.APIStatus.Ok {
		log.Printf("[ERROR] Call API %s : %s\n", pathCompleteHandOver, res.Body)
		err = fmt.Errorf("complete handover at pick app %v", resBody.ErrorCode)
		return
	}

	return
}

func (cli *PickingClient) GetPickTicket(so string) (result *response.PickTicket, err error) {

	params := map[string]string{
		"so": so,
	}

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetTicket, nil)
	if err != nil {
		log.Printf("[ERROR] Call API %s: %v\n", pathGetTicket, err)
		return
	}

	type responseData struct {
		response.BaseResponse
		Data []*response.PickTicket `json:"data"`
	}
	resBody := new(responseData)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		log.Printf("[ERROR] Call API %s: %v\n", pathGetTicket, err)
		return
	}

	if resBody.Status != common.APIStatus.Ok {
		log.Printf("[ERROR] Call API %s : %s\n", pathGetTicket, res.Body)
		err = fmt.Errorf("pathGetTicket at pick app %v", resBody.ErrorCode)
		return
	}

	result = resBody.Data[0]
	return
}

func (cli *PickingClient) GetPickTicketById(id int64) (result *response.PickTicket, err error) {

	params := map[string]string{
		"ticketId": fmt.Sprintf("%d", id),
	}

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetTicket, nil)
	if err != nil {
		log.Printf("[ERROR] Call API %s: %v\n", pathGetTicket, err)
		return
	}

	type responseData struct {
		response.BaseResponse
		Data []*response.PickTicket `json:"data"`
	}
	resBody := new(responseData)

	err = json.Unmarshal([]byte(res.Body), &resBody)
	if err != nil {
		log.Printf("[ERROR] Call API %s: %v\n", pathGetTicket, err)
		return
	}

	if resBody.Status != common.APIStatus.Ok {
		log.Printf("[ERROR] Call API %s : %s\n", pathGetTicket, res.Body)
		err = fmt.Errorf("pathGetTicket at pick app %v", resBody.ErrorCode)
		return
	}

	result = resBody.Data[0]
	return
}
