package action

import (
	"strconv"
	"sync"
	"time"

	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func CreateZone(input *model.Zone, accountId int64) *common.APIResponse {
	if input.Name == "" || input.Code == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Tên và mã khu vực không được để trống",
		}
	}
	if !utils.CheckValidCodeFormat(input.Code) {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Code không hợp lệ",
		}
	}
	key, genErr := utils.GenKeyword(input.Name, input.Code)
	if genErr != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không thể tạo từ khóa cho khu vực",
		}
	}
	input.Keyword = key

	zoneCodeNName := bson.M{
		"$or": []interface{}{
			bson.M{"name": input.Name},
			bson.M{"code": input.Code},
		},
		"is_deleted": false,
	}
	zones := model.ZoneDB.QueryOne(zoneCodeNName)
	if zones.Status != common.APIStatus.NotFound {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Tên hoặc mã khu vực đã được sử dụng",
		}
	}

	// Check dup province
	isDupProvince := utils.IsDupProvince(input)
	if isDupProvince {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã tỉnh không được để trùng",
		}
	}

	err := buildAreaTree(input)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Message,
		}
	}

	input.IsLastChild = &enum.True
	input.IsAssignToHub = &enum.False
	// handle parent case
	if input.ParentCode != "" {
		isValidZone := isValidZoneInParentZone(input)
		if isValidZone != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: isValidZone.Message,
			}
		}

		if len(input.NearZone) > 0 {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không thể thiết lập cận miền với khu vực con",
			}
		}

		parentZone := model.ZoneDB.UpdateOne(bson.M{
			"code": input.ParentCode,
		}, bson.M{
			"is_last_child": false,
		})

		if parentZone.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không thể cập nhật khu vực cha",
			}
		}

	} else {
		isValidZone := isParentWardsAvailable(input.WardIndexes)
		if isValidZone != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: isValidZone.Message,
			}
		}
	}

	input.CreatedBy = strconv.FormatInt(accountId, 10)
	input.LastUpdatedBy = strconv.FormatInt(accountId, 10)
	now := time.Now()
	input.LastUpdatedTime = &now
	input.CreatedTime = &now
	createZoneResult := model.ZoneDB.Create(input)
	if createZoneResult.Status != common.APIStatus.Ok {
		return createZoneResult
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Tạo khu vực thành công",
	}
}

func GetZones(query request.ZoneQuery, offset, limit int64, getTotal bool) *common.APIResponse {
	filter := bson.M{}

	if len(query.ZoneCode) > 0 {
		filter["code"] = bson.M{
			"$in": query.ZoneCode,
		}
	}

	if query.Keyword != "" {
		filter["keyword"] = bson.M{
			"$regex": utils.ToRawText(query.Keyword),
		}
	}

	if len(query.ListWardCode) > 0 {
		filter["ward_indexes"] = bson.M{
			"$in": query.ListWardCode,
		}
	}

	if len(query.ListProvince) > 0 {
		filter["provinces.province_code"] = bson.M{
			"$in": query.ListProvince,
		}
	}

	if len(query.ListDistrict) > 0 {
		filter["provinces.districts.district_code"] = bson.M{
			"$in": query.ListDistrict,
		}
	}

	if query.IsAssignToHub != nil {
		filter["is_assign_to_hub"] = *query.IsAssignToHub
	}

	if query.IsParentZone != nil && *query.IsParentZone {
		filter["$or"] = bson.A{
			bson.M{
				"parent_code": "",
			},
			bson.M{
				"parent_code": bson.M{
					"$exists": false,
				},
			},
		}
	}
	if query.FromTime > 0 && query.ToTime > 0 {
		fromTime := time.Unix(query.FromTime, 0)
		toTime := time.Unix(query.ToTime, 0)
		filter["created_time"] = bson.M{
			"$gte": primitive.NewDateTimeFromTime(fromTime),
			"$lte": primitive.NewDateTimeFromTime(toTime),
		}
	} else if query.FromTime > 0 && query.ToTime == 0 {
		fromTime := time.Unix(query.FromTime, 0)
		filter["created_time"] = bson.M{
			"$gte": primitive.NewDateTimeFromTime(fromTime),
		}
	} else if query.FromTime == 0 && query.ToTime > 0 {
		toTime := time.Unix(query.ToTime, 0)
		filter["created_time"] = bson.M{
			"$lte": primitive.NewDateTimeFromTime(toTime),
		}
	}

	filter["is_deleted"] = false
	result := model.ZoneDB.Query(
		filter,
		offset,
		limit,
		nil)

	if result.Status != common.APIStatus.Ok {
		return result
	}

	zones := result.Data.([]*model.Zone)
	for _, zone := range zones {
		if zone.IsAssignToHub != nil && *zone.IsAssignToHub {
			hubRaw := model.HubDB.QueryOne(bson.M{
				"zones": zone.Code,
			})
			if hubRaw.Status != common.APIStatus.Ok {
				continue
			}
			hub := hubRaw.Data.([]*model.Hub)[0]
			zone.HubName = hub.Name
		}
	}
	result.Data = zones

	if getTotal {
		total := model.ZoneDB.Count(filter)
		result.Total = total.Total
	}

	return result
}

func GetNearZones(offset, limit int64, getTotal bool) *common.APIResponse {
	type resNearZoneStruct struct {
		FromZoneCode string `json:"fromZoneCode,omitempty" bson:"from_zone_code,omitempty"`
		FromZoneName string `json:"fromZoneName,omitempty" bson:"from_zone_name,omitempty"`
		ToZoneCode   string `json:"toZoneCode,omitempty" bson:"to_zone_code,omitempty"`
		ToZoneName   string `json:"toZoneName,omitempty" bson:"to_zone_name,omitempty"`
	}
	var resNearZone []resNearZoneStruct

	result := model.ZoneDB.Query(
		bson.M{
			"is_deleted": false,
			"parent_code": bson.M{
				"$exists": false,
			},
			"near_zone": bson.M{
				"$exists": true,
			},
		},
		offset,
		limit,
		nil)

	if result.Status != common.APIStatus.Ok {
		return result
	}
	zones := result.Data.([]*model.Zone)

	for _, zone := range zones {
		if len(zone.NearZone) > 0 {
			for _, nearZoneValue := range zone.NearZone {
				isExist := false
				for _, resZone := range resNearZone {
					if resZone.FromZoneCode == zone.Code && resZone.ToZoneCode == nearZoneValue.Code || resZone.FromZoneCode == nearZoneValue.Code && resZone.ToZoneCode == zone.Code {
						isExist = true
						break
					}
				}
				if !isExist {
					resNearZone = append(resNearZone, resNearZoneStruct{
						zone.Code,
						zone.Name,
						nearZoneValue.Code,
						nearZoneValue.Name,
					})
				}
			}
		}
	}

	return &common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   resNearZone,
	}
}

func UpdateZoneArea(input *model.Zone, accountId int64) *common.APIResponse {
	filter := bson.M{
		"code":       input.Code,
		"is_deleted": false,
	}

	zoneResult := model.ZoneDB.QueryOne(filter)

	if zoneResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Khu vực không tồn tại",
		}
	}

	// Check dup province
	isDupProvince := utils.IsDupProvince(input)
	if isDupProvince {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã tỉnh không được để trùng",
		}
	}
	zone := zoneResult.Data.([]*model.Zone)[0]
	err := buildAreaTree(input)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Message,
		}
	}

	if zone.ParentCode != "" {
		input.ParentCode = zone.ParentCode
		// If there is a parent check if area tree is valid or not
		err := isValidChildToUpdate(input)
		if err != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: err.Message,
			}
		}

		if len(input.NearZone) > 0 {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không thể thiết lập cận miền với khu vực con",
			}
		}
	} else {
		// No dup from parent
		isValidZone := isValidParentToUpdate(input)
		if isValidZone != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: isValidZone.Message,
			}
		}
	}
	updater := bson.M{
		"ward_indexes":    input.WardIndexes,
		"provinces":       input.Provinces,
		"last_updated_by": strconv.FormatInt(accountId, 10),
		"near_zone":       input.NearZone,
	}

	if input.Name != "" {
		isDupName := model.ZoneDB.QueryOne(bson.M{
			"name":       input.Name,
			"is_deleted": false,
			"code": bson.M{
				"$ne": input.Code,
			},
		})

		if isDupName.Status != common.APIStatus.NotFound {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Tên khu vực đã được sử dụng",
			}
		}
		updater["name"] = input.Name

		key, genErr := utils.GenKeyword(input.Name, input.Code)
		if genErr != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không thể tạo từ khóa cho khu vực",
			}
		}
		updater["keyword"] = key
	}

	oldWardIndex := zone.WardIndexes
	newWardIndex := input.WardIndexes
	removedWard := utils.GetRemovedElement(oldWardIndex, newWardIndex)

	// update children
	rebuildZone := queryMatchWardChild(removedWard, zone.Code)

	if len(rebuildZone) > 0 {
		for _, child := range rebuildZone {
			deleteBranchFromWardIndex(child, removedWard)
			updateWard := model.ZoneDB.UpdateOne(
				bson.M{
					"code":       child.Code,
					"is_deleted": false,
				},
				bson.M{
					"ward_indexes":    child.WardIndexes,
					"provinces":       child.Provinces,
					"last_updated_by": strconv.FormatInt(accountId, 10),
				},
			)
			if updateWard.Status != common.APIStatus.Ok {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: updateWard.Message,
				}
			}
		}
	}

	if input.Description != "" {
		updater["description"] = input.Description
	}

	updateWard := model.ZoneDB.UpdateOne(
		bson.M{
			"code":       input.Code,
			"is_deleted": false,
		},
		updater,
	)

	if updateWard.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: updateWard.Message,
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật thông tin khu vực thành công",
	}
}

func DeleteZone(zoneCode string, accountId int64) *common.APIResponse {
	if zoneCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã khuc vực không được đế trống",
		}
	}

	queryZone := bson.M{
		"code":       zoneCode,
		"is_deleted": false,
	}

	zoneResult := model.ZoneDB.QueryOne(queryZone)

	if zoneResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Khu vực không tồn tại",
		}
	}
	zone := zoneResult.Data.([]*model.Zone)[0]
	// update chu k xoa
	visitQueue := make([]string, 0)
	visitQueue = append(visitQueue, zone.Code)
	deleteZones := make([]string, 0)
	deleteZones = append(deleteZones, zone.Code)

	for len(visitQueue) != 0 {
		visitor := visitQueue[0]
		visitQueue = visitQueue[1:]
		filter := bson.M{
			"parent_code": visitor,
			"is_deleted":  false,
		}
		childrenRaw := model.ZoneDB.Query(filter, 0, 100, nil)
		if childrenRaw.Status == common.APIStatus.Ok {
			children := childrenRaw.Data.([]*model.Zone)
			for _, child := range children {
				deleteZones = append(deleteZones, child.Code)
				visitQueue = append(visitQueue, child.Code)
			}
		}
	}

	if len(deleteZones) > 0 {
		query := bson.M{
			"zones": bson.M{
				"$in": deleteZones,
			},
		}

		isValidChildrenRaw := model.HubDB.Query(query, 0, 10, nil)
		if isValidChildrenRaw.Status == common.APIStatus.Ok {
			isValidChildren := isValidChildrenRaw.Data.([]*model.Hub)

			type dupZoneDetail struct {
				HubCode     string   `json:"hubCode,omitempty"`
				DupZoneCode []string `json:"dupZoneCode,omitempty"`
			}

			response := []dupZoneDetail{}

			for _, child := range isValidChildren {
				dupZones := utils.CommonStringsFromStringSlices(deleteZones, child.Zones)
				response = append(response, dupZoneDetail{
					HubCode:     child.Code,
					DupZoneCode: dupZones,
				})
			}
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Có khu vực bị xóa nhưng đã gán vào hub",
				Data:    response,
			}
		}

		if isValidChildrenRaw.Status != common.APIStatus.NotFound {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: isValidChildrenRaw.Message,
			}
		}
	}
	result := model.ZoneDB.UpdateMany(
		bson.M{
			"code": bson.M{
				"$in": deleteZones,
			},
		},
		bson.M{
			"is_deleted":      true,
			"last_updated_by": strconv.FormatInt(accountId, 10),
		},
	)

	if result.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: result.Message,
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Xóa khu vực thành công",
	}
}

func PrepareUpdate(input *model.Zone) *common.APIResponse {
	// Check dup province
	isDupProvince := utils.IsDupProvince(input)
	if isDupProvince {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Mã tỉnh không được để trùng",
		}
	}

	filter := bson.M{
		"code":       input.Code,
		"is_deleted": false,
	}

	zoneResult := model.ZoneDB.QueryOne(filter)

	if zoneResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Khu vực không tồn tại",
		}
	}

	zone := zoneResult.Data.([]*model.Zone)[0]

	err := buildAreaTree(input)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Message,
		}
	}

	oldWardIndex := zone.WardIndexes
	newWardIndex := input.WardIndexes
	removedWard := utils.GetRemovedElement(oldWardIndex, newWardIndex)

	children := queryMatchWardChild(removedWard, zone.Code)
	if len(children) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Cập nhật này không làm ảnh hưởng đến những khu vực khác",
		}
	}

	childrenCodes := make([]string, 0, len(children))
	for _, child := range children {
		childrenCodes = append(childrenCodes, child.Name)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật này sẽ gây ảnh hưởng",
		Data:    childrenCodes,
	}
}
func isParentWardsAvailable(wards []string) *common.Error {
	queryStage := bson.M{
		"ward_indexes": bson.M{
			"$in": wards,
		},
		"parent_code": nil,
		"is_deleted":  false,
	}
	zones := model.ZoneDB.QueryOne(queryStage)
	if zones.Status == common.APIStatus.Ok {
		return &common.Error{
			Message: "Khu vực này đã được sử dụng bởi: " + zones.Data.([]*model.Zone)[0].Code,
		}
	}

	return nil
}

func isValidZoneInParentZone(request *model.Zone) *common.Error {
	parentZoneRaw := model.ZoneDB.QueryOne(bson.M{
		"code":       request.ParentCode,
		"is_deleted": false,
	})

	if parentZoneRaw.Status != common.APIStatus.Ok {
		return &common.Error{
			Message: "Mã khu vực cha không hợp lệ",
		}
	}
	parentZone := parentZoneRaw.Data.([]*model.Zone)
	if !utils.Subslice(request.WardIndexes, parentZone[0].WardIndexes) {
		return &common.Error{
			Message: "Khu vực con không nằm trong khu vực cha",
		}
	}

	query := bson.M{
		"parent_code": request.ParentCode,
		"is_deleted":  false,
		"ward_indexes": bson.M{
			"$in": request.WardIndexes,
		},
	}

	intersectSiblingRaw := model.ZoneDB.QueryOne(query)
	if intersectSiblingRaw.Status == common.APIStatus.Ok {
		intersectSibling := intersectSiblingRaw.Data.([]*model.Zone)[0]
		return &common.Error{
			Message: "Khu vực này đang được sử dụng bởi: " + intersectSibling.Code,
		}
	}

	return nil
}

func buildAreaTree(zone *model.Zone) *common.Error {
	if zone.Provinces == nil {
		return &common.Error{
			Message: "Tỉnh không được để trống",
		}
	}
	zoneWg := sync.WaitGroup{}
	zoneMut := sync.Mutex{}
	errs := make(chan common.Error, len(zone.Provinces))
	for _, province := range zone.Provinces {
		zoneWg.Add(1)
		go buildProvinceTree(zone, province, &zoneWg, &zoneMut, errs)
	}
	zoneWg.Wait()
	close(errs)
	for err := range errs {
		return &common.Error{
			Message: err.Message,
		}
	}
	return nil
}

func buildProvinceTree(zone *model.Zone, province *model.ProvinceArea, zoneWg *sync.WaitGroup, zoneMut *sync.Mutex, errs chan<- common.Error) {
	defer zoneWg.Done()
	// Check province
	if province.ProvinceCode == "" {
		errs <- common.Error{
			Message: "Mã quận huyện không được để trống",
		}
		return
	}

	provinceRaw, err := client.Services.WarehouseCoreClient.GetProvinceByCode(province.ProvinceCode)
	if err != nil {
		errs <- common.Error{
			Message: err.Error(),
		}
		return
	}
	province.ProvinceName = provinceRaw[0].Name

	districtWg := sync.WaitGroup{}
	if province.DistrictArea == nil {
		districts, err := client.Services.WarehouseCoreClient.GetDistrictByProvinceCode(province.ProvinceCode, "")
		if err != nil {
			errs <- common.Error{
				Message: "Mã tỉnh không hợp lệ",
			}
			return
		}
		province.DistrictArea = utils.DistrictsToDistrictArea(districts)
	}

	districtErrs := make(chan common.Error, len(province.DistrictArea))
	for _, district := range province.DistrictArea {
		districtWg.Add(1)
		go slowBuildDistrictTree(zone, province.ProvinceCode, district, &districtWg, zoneMut, districtErrs)
	}
	districtWg.Wait()
	close(districtErrs)

	for districtErr := range districtErrs {
		errs <- districtErr
		return
	}

}

func buildDistrictTree(zone *model.Zone, provinceCode string, district *model.DistrictArea, districtWg *sync.WaitGroup, zoneMut *sync.Mutex, districtErrs chan<- common.Error) {
	defer districtWg.Done()

	if district.DistrictCode == "" {
		districtErrs <- common.Error{
			Message: "Mã quận huyện không được để trống",
		}
		return
	}

	if district.WardArea == nil {
		wards, err := client.Services.WarehouseCoreClient.GetWard("", "", district.DistrictCode, provinceCode)
		if err != nil {
			districtErrs <- common.Error{
				Message: "Mã quận huyện không hợp lệ",
			}
			return
		}
		wardArea, wardIndexes := utils.WardsToWardArea(wards)
		zoneMut.Lock()
		district.WardArea = wardArea
		zone.WardIndexes = append(zone.WardIndexes, wardIndexes...)
		zoneMut.Unlock()
		return
	}

	for _, ward := range district.WardArea {
		if ward.WardCode == "" {
			districtErrs <- common.Error{
				Message: "Mã phường xã không được để trống",
			}
			return
		}
		_, err := client.Services.WarehouseCoreClient.GetWard("", ward.WardCode, district.DistrictCode, provinceCode)
		if err != nil {
			districtErrs <- common.Error{
				Message: "Mã tỉnh hoặc mã quận hoặc mã phường không hợp lệ",
			}
			return
		}
		zoneMut.Lock()
		zone.WardIndexes = append(zone.WardIndexes, ward.WardCode)
		zoneMut.Unlock()
	}
}

func slowBuildDistrictTree(zone *model.Zone, provinceCode string, district *model.DistrictArea, districtWg *sync.WaitGroup, zoneMut *sync.Mutex, districtErrs chan<- common.Error) {
	defer districtWg.Done()

	if district.DistrictCode == "" {
		districtErrs <- common.Error{
			Message: "Mã quận huyện không được để trống",
		}
		return
	}

	if zone.ParentCode != "" {
		if district.AreaLevel != nil {
			districtErrs <- common.Error{
				Message: "Không thể thiết lập mức độ khu vực quận huyện với khu vực con",
			}
			return
		}
	}

	wards, err := client.Services.WarehouseCoreClient.GetWard("", "", district.DistrictCode, provinceCode)
	if err != nil {
		_ = client.Services.WarehouseCoreClient.SendMessage(&request.SendMessageTelegram{
			CmdType: "EMPTY_WARD_OF_DISTRICT",
			Title:   "Not found any ward",
			Message: "Không tìm thấy phường xã cho quận: " + district.DistrictCode,
		})
		return
	}
	wardArea, wardIndexes := utils.WardsToWardArea(wards)

	if district.WardArea == nil {
		zoneMut.Lock()
		district.WardArea = wardArea
		zone.WardIndexes = append(zone.WardIndexes, wardIndexes...)
		zoneMut.Unlock()
		return
	}

	for _, ward := range district.WardArea {
		if ward.WardCode == "" {
			districtErrs <- common.Error{
				Message: "Mã phường xã không được để trống",
			}
			return
		}

		if !utils.StringSliceContain(wardIndexes, ward.WardCode) {
			districtErrs <- common.Error{
				Message: "Mã tỉnh hoặc mã quận hoặc mã phường không hợp lệ: " + ward.WardCode,
			}
			return
		}

		zoneMut.Lock()
		zone.WardIndexes = append(zone.WardIndexes, ward.WardCode)
		zoneMut.Unlock()
	}
}

func deleteBranchFromWardIndex(zone *model.Zone, removedWardIndexes []string) {
	hash := make(map[string]bool)
	for _, e := range removedWardIndexes {
		hash[e] = true
	}

	for _, province := range zone.Provinces {
		for _, district := range province.DistrictArea {
			for _, ward := range district.WardArea {
				if hash[ward.WardCode] {
					district.WardArea = utils.RemoveWardFromWards(ward.WardCode, district.WardArea)
					zone.WardIndexes = utils.RemoveStringFromStrings(ward.WardCode, zone.WardIndexes)
				}
			}
			if len(district.WardArea) == 0 {
				province.DistrictArea = utils.RemoveDistrictFromDistricts(district.DistrictCode, province.DistrictArea)
			}
		}
		if len(province.DistrictArea) == 0 {
			zone.Provinces = utils.RemoveProvinceFromProvinces(province.ProvinceCode, zone.Provinces)
		}
	}
}

func queryMatchWardChild(wards []string, parentCode string) (zones []*model.Zone) {
	if parentCode == "" {
		return nil
	}
	if len(wards) == 0 {
		return nil
	}

	visitQueue := make([]string, 0)
	visitQueue = append(visitQueue, parentCode)

	for len(visitQueue) != 0 {
		visitor := visitQueue[0]
		visitQueue = visitQueue[1:]
		filter := bson.M{
			"parent_code": visitor,
			"ward_indexes": bson.M{
				"$in": wards,
			},
			"is_deleted": false,
		}
		childrenRaw := model.ZoneDB.Query(filter, 0, 100, nil)
		if childrenRaw.Status == common.APIStatus.Ok {
			children := childrenRaw.Data.([]*model.Zone)
			for _, child := range children {
				zones = append(zones, child)
				visitQueue = append(visitQueue, child.Code)
			}
		}
	}

	return
}

func isValidParentToUpdate(zone *model.Zone) *common.Error {
	queryStage := bson.M{
		"ward_indexes": bson.M{
			"$in": zone.WardIndexes,
		},
		"parent_code": nil,
		"is_deleted":  false,
		"code": bson.M{
			"$ne": zone.Code,
		},
	}
	zones := model.ZoneDB.QueryOne(queryStage)
	if zones.Status == common.APIStatus.Ok {
		return &common.Error{
			Message: "Khu vực này đã được sử dụng bởi: " + zones.Data.([]*model.Zone)[0].Code,
		}
	}

	return nil
}

func isValidChildToUpdate(zone *model.Zone) *common.Error {
	parentZoneRaw := model.ZoneDB.QueryOne(bson.M{
		"code":       zone.ParentCode,
		"is_deleted": false,
	})

	if parentZoneRaw.Status != common.APIStatus.Ok {
		return &common.Error{
			Message: "Mã khu vực cha không hợp lệ",
		}
	}
	parentZone := parentZoneRaw.Data.([]*model.Zone)
	if !utils.Subslice(zone.WardIndexes, parentZone[0].WardIndexes) {
		return &common.Error{
			Message: "Khu vực con không nằm trong khu vực cha",
		}
	}

	childRaw := model.ZoneDB.QueryOne(
		bson.M{
			"parent_code": zone.ParentCode,
			"ward_indexes": bson.M{
				"$in": zone.WardIndexes,
			},
			"code": bson.M{
				"$ne": zone.Code,
			},
			"is_deleted": false,
		})

	if childRaw.Status == common.APIStatus.Ok {
		child := childRaw.Data.([]*model.Zone)[0]
		return &common.Error{
			Message: "Khu vực này đang được sử dụng bởi: " + child.Code,
		}
	}
	return nil
}

func MigrateLeafZone() *common.APIResponse {
	zonesRaw := model.ZoneDB.Query(
		bson.M{
			"is_deleted": false,
			"parent_code": bson.M{
				"$exists": false,
			},
		}, 0, 100, nil)

	zones := zonesRaw.Data.([]*model.Zone)
	visitQueue := make([]string, 0)

	var lastChildCode []string
	var parentCode []string
	for _, zone := range zones {
		visitQueue = append(visitQueue, zone.Code)
	}

	for len(visitQueue) != 0 {
		visitor := visitQueue[0]
		visitQueue = visitQueue[1:]

		filter := bson.M{
			"parent_code": visitor,
			"is_deleted":  false,
		}

		childrenRaw := model.ZoneDB.Query(filter, 0, 100, nil)

		if childrenRaw.Status == common.APIStatus.NotFound {
			lastChildCode = append(lastChildCode, visitor)
		}

		if childrenRaw.Status == common.APIStatus.Ok {
			parentCode = append(parentCode, visitor)
			children := childrenRaw.Data.([]*model.Zone)

			for _, child := range children {
				visitQueue = append(visitQueue, child.Code)
			}
		}
	}

	_ = model.ZoneDB.UpdateMany(bson.M{
		"code": bson.M{
			"$in": lastChildCode,
		},
	}, bson.M{
		"is_last_child": true,
	})

	_ = model.ZoneDB.UpdateMany(bson.M{
		"code": bson.M{
			"$in": parentCode,
		},
	}, bson.M{
		"is_last_child": false,
	})

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Migrate thành công",
	}

}

func MigrateZoneLevel() *common.APIResponse {
	zonesRaw := model.ZoneDB.Query(
		bson.M{
			"parent_code": bson.M{
				"$exists": false,
			},
			"is_deleted": false,
		}, 0, 120, nil)

	zones := zonesRaw.Data.([]*model.Zone)

	for _, zone := range zones {
		for _, province := range zone.Provinces {
			for _, district := range province.DistrictArea {
				if district.AreaLevel == nil {
					district.AreaLevel = &enum.AreaLevel.URBAN
				}
			}
		}
		_ = model.ZoneDB.UpdateOne(bson.M{
			"code": zone.Code,
		}, zone)

	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Migrate thành công",
	}

}

func MigrateZoneKeyword() *common.APIResponse {
	zonesRaw := model.ZoneDB.QueryAll()
	zones := zonesRaw.Data.([]*model.Zone)
	for _, zone := range zones {
		zone.Keyword, _ = utils.GenKeyword(zone.Name, zone.Code)
		model.ZoneDB.UpdateOne(
			bson.M{
				"code":       zone.Code,
				"is_deleted": false,
			},
			bson.M{
				"keyword": zone.Keyword,
			},
		)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}
}

func MigrateWardIndexes(inputZones []*model.Zone) *common.APIResponse {
	for _, inputZone := range inputZones {
		getZoneResp := model.ZoneDB.QueryOne(bson.M{
			"code": inputZone.Code,
		})
		if getZoneResp.Status != common.APIStatus.Ok {
			continue
		}
		zone := getZoneResp.Data.([]*model.Zone)[0]
		wardIndexes := make([]string, 0)

		if len(zone.Provinces) > 0 {
			for _, province := range zone.Provinces {
				for _, district := range province.DistrictArea {
					for _, ward := range district.WardArea {
						wardIndexes = append(wardIndexes, ward.WardCode)
					}
				}
			}
		} else {
			wardIndexes = inputZone.WardIndexes
		}

		model.ZoneDB.UpdateOne(
			bson.M{
				"code": zone.Code,
			},
			bson.M{
				"ward_indexes": wardIndexes,
			},
		)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}
}
