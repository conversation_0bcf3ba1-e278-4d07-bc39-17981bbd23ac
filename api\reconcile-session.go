package api

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/action"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
)

func GetReconcileSession(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input request.GetReconcileSession
	str := req.GetParam("q")
	if str == "" {
		str = "{}"
	}

	err := json.Unmarshal([]byte(str), &input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data.",
			ErrorCode: "invalid_input",
		})
	}

	return resp.Respond(action.GetReconcileSession(&input))
}
