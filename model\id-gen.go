package model

import (
	"math"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/mongo"
)

// IdGen DB entity for gen code
type IdGen struct {
	ID    string `json:"id,omitempty" bson:"_id,omitempty"`
	Value int64  `json:"value,omitempty" bson:"value,omitempty"`
}

// IdGenDB DB model for gen code
var IdGenDB = &db.Instance{
	ColName:        "_id_gen",
	TemplateObject: &IdGen{},
}

func InitIdGenModel(s *mongo.Database) {

	IdGenDB.ApplyDatabase(s)

	handoverTicketGen := IdGen{
		ID:    "HANDOVER_TICKET",
		Value: 1,
	}

	trackingGen := IdGen{
		ID:    "TRACKING_CODE",
		Value: 13175,
	}

	carrierGen := IdGen{
		ID:    "CARRIER_ID",
		Value: 1,
	}

	HubGen := IdGen{
		ID:    "HUB_ID",
		Value: 1,
	}

	tripGen := IdGen{
		ID:    "TRIP_ID",
		Value: 1,
	}

	feeConfigId := IdGen{
		ID:    "FEE_CONFIG_ID",
		Value: 1,
	}

	truckGen := IdGen{
		ID:    "TRUCK_ID",
		Value: 1,
	}

	shippingAddressGen := IdGen{
		ID:    "SHIPPING_ADDRESS_ID",
		Value: 1,
	}

	customerId := IdGen{
		ID:    "CUSTOMER_ID",
		Value: 1,
	}

	reconcileCode := IdGen{
		ID:    "RECONCILE_CODE",
		Value: 13175,
	}

	customerSupportCode := IdGen{
		ID:    "CUSTOMER_SUPPORT_CODE",
		Value: 1000,
	}

	externalOrderCode := IdGen{
		ID:    "EXTERNAL_ORDER_CODE",
		Value: 1000,
	}

	CheckinMultipleBinCode:= IdGen{
		ID:    "CHECKIN-BINS",
		Value: 1,
	}

	IdGenDB.Create(handoverTicketGen)
	IdGenDB.Create(trackingGen)
	IdGenDB.Create(carrierGen)
	IdGenDB.Create(HubGen)
	IdGenDB.Create(tripGen)
	IdGenDB.Create(feeConfigId)
	IdGenDB.Create(truckGen)
	IdGenDB.Create(shippingAddressGen)
	IdGenDB.Create(customerId)
	IdGenDB.Create(reconcileCode)
	IdGenDB.Create(customerSupportCode)
	IdGenDB.Create(externalOrderCode)
	IdGenDB.Create(CheckinMultipleBinCode)
}

// convertToCode convert id from int to string
func convertToCode(number int64, length int64, template string) string {
	var result = ""
	var i = int64(0)
	var ln = int64(len(template))
	var capacity = int64(math.Pow(float64(ln), float64(length)))
	number = number % capacity
	for i < length {
		var cur = number % ln
		if i > 0 {
			cur = (cur + int64(result[i-1])) % ln
		}
		result = result + string(template[cur])
		number = number / ln
		i++
	}
	return result
}

// GenCode gen unique code in range, map from id
func GenCode(objectType string) string {
	increResult := IdGenDB.IncreOne(IdGen{
		ID: objectType,
	}, "value", 1)
	val := increResult.Data.([]*IdGen)[0]
	return convertToCode(val.Value, 10, "12346789QWERTYUPASDFGHJKLXCVBNM")
}

func GenId(objectType string) int64 {
	increResult := IdGenDB.IncreOne(IdGen{
		ID: objectType,
	}, "value", 1)
	val := increResult.Data.([]*IdGen)[0]
	return val.Value
}
