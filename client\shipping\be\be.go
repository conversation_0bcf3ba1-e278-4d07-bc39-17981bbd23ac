package be

import (
	"encoding/json"
	"errors"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/response"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

const (
	pathBookService = "/delivery/place_order"
	pathCancel      = "/delivery/cancel"
	pathEstimateFee = "/delivery/calculate_fare"
	pathAuth        = "/auth"
)

type Client struct {
	svc     *client.RestClient
	headers map[string]string
}

func NewServiceClient(apiHost, logName, proxyUrl string, session *mongo.Database) *Client {
	if apiHost == "" {
		return nil
	}
	if proxyUrl == "" {
		newClient := &Client{
			svc: client.NewRESTClient(
				apiHost,
				logName,
				180*time.Second,
				0,
				180*time.Second,
			),
			headers: map[string]string{},
		}
		newClient.svc.SetDBLog(session)
		return newClient
	}
	newClient := &Client{
		svc: client.NewRESTClientWithProxy(
			apiHost,
			logName,
			proxyUrl,
			180*time.Second,
			0,
			180*time.Second,
		),
		headers: map[string]string{},
	}
	if newClient.svc != nil {
		newClient.svc.SetDBLog(session)
	}
	return newClient
}

func (cli *Client) ChangeInformation(carrierInfo *model.Carrier) error {
	if carrierInfo.ExtraData == nil {
		return errors.New("missing extra data")
	}
	if carrierInfo.ExtraData.AccessToken == "" {
		return errors.New("missing access token")
	}
	if carrierInfo.ExtraData.ClientId == "" {
		return errors.New("missing access id")
	}
	cli.headers = map[string]string{
		"access-id":    carrierInfo.ExtraData.ClientId,
		"access-token": carrierInfo.ExtraData.AccessToken,
	}
	return nil
}

func (cli *Client) CreateTrackingBe(
	body request.BookBeRequest,
	carrier *model.Carrier,
) (*model.ShippingInfo, error) {
	err := cli.ChangeInformationBe(carrier)
	if err != nil {
		return nil, err
	}

	_, err = cli.EstimateFeeBe(body, carrier)
	if err != nil {
		return nil, err
	}

	header := map[string]string{
		"client_id":    carrier.ExtraData.ClientId,
		"access_token": carrier.ExtraData.AccessToken,
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, header, nil, body, pathBookService, &[]string{
		body.OrderInfo.ReferenceId,
	})
	if err != nil {
		return nil, err
	}
	resBody := new(response.BookBeOrderResponse)
	if err := json.Unmarshal([]byte(res.Body), &resBody); err != nil {
		return nil, err
	}

	if res.Code != 200 {
		err = errors.New(resBody.Message)
		return nil, err
	}

	// Number order id to string tracking number
	trackingNum := resBody.Data.OrderId
	trackingInfo := &model.ShippingInfo{
		TrackingNumber: trackingNum,
		FeeAmount:      resBody.Data.TotalPrice,
		Distance:       resBody.Data.EstimatedDistance,
	}

	return trackingInfo, nil
}

func (cli *Client) CancelBe(beOrderId string, fromUserId, reason string, carrier *model.Carrier) (err error) {
	header := map[string]string{
		"client-id":    carrier.ExtraData.ClientId,
		"access-token": carrier.ExtraData.AccessToken,
	}
	body := map[string]string{
		"be_order_id":     beOrderId,
		"partner_user_id": fromUserId,
		"reason":          reason,
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, header, nil, body, pathCancel, &[]string{
		beOrderId,
	})
	if err != nil {
		return err
	}
	resBody := new(response.BeCancelOrderResponse)
	if err := json.Unmarshal([]byte(res.Body), &resBody); err != nil {
		return err
	}
	if res.Code != 0 {
		return errors.New(resBody.Message)
	}
	return nil
}

func (cli *Client) ChangeInformationBe(carrier *model.Carrier) error {
	header := map[string]string{
		"client_id":     carrier.ExtraData.ClientId,
		"client_secret": carrier.ExtraData.SecretKey,
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, header, nil, nil, pathAuth, nil)
	if err != nil {
		return err
	}
	resBody := new(response.BeAuthResponse)

	if err := json.Unmarshal([]byte(res.Body), &resBody); err != nil {
		return err
	}
	if res.Code != 200 || resBody.Code != 0 {
		return errors.New(resBody.Message)
	}
	carrier.ExtraData.AccessToken = resBody.Data.Be.AccessToken
	return nil
}

func (cli *Client) EstimateFeeBe(
	body request.BookBeRequest,
	carrier *model.Carrier) (*model.ShippingInfo, error) {
	header := map[string]string{
		"client_id":    carrier.ExtraData.ClientId,
		"access_token": carrier.ExtraData.AccessToken,
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, header, nil, body, pathEstimateFee, &[]string{
		body.OrderInfo.ReferenceId,
	})
	if err != nil {
		return nil, err
	}
	resBody := new(response.CalculateBeFeeResponse)
	if err := json.Unmarshal([]byte(res.Body), &resBody); err != nil {
		return nil, err
	}

	if res.Code != 200 {
		err = errors.New(resBody.Message)
		return nil, err
	}

	for _, v := range resBody.Data {
		if v.ServiceType == carrier.Service {
			return &model.ShippingInfo{
				FeeAmount: v.FinalFare,
			}, nil
		}
	}

	errorMes := "Không tìm thấy giá vận chuyển"
	if resBody.Message != "" {
		errorMes = resBody.Message
	}
	return nil, errors.New(errorMes)
}

func (cli *Client) GetDetailOrderBe() (trackingInfo *model.ShippingInfo, err error) {
	return nil, nil
}
