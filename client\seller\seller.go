package seller

import (
	"encoding/json"
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/request"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/response"
)

func (cli *SellerClient) GetSellersInformation(request request.GetSellerRequest) ([]*response.Seller, error) {
	params := map[string]string{
		"sellerCodes": request.SellerCodes,
		"offset":      fmt.Sprintf("%v", request.Offset),
		"limit":       fmt.Sprintf("%v", request.Limit),
		"getTotal":    fmt.Sprintf("%v", request.GetTotal),
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetSellers, nil)
	if err != nil {
		return nil, err
	}
	type myResponse struct {
		Status string             `json:"status"`
		Code   string             `json:"code"`
		Data   []*response.Seller `json:"data"`
	}

	resp := new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &resp)
	if err != nil {
		return nil, err
	}
	if resp.Status != common.APIStatus.Ok || resp.Data == nil {
		return nil, fmt.Errorf("%v", resp.Code)
	}

	return resp.Data, nil
}

func (cli *SellerClient) GetSellerById(sellerId int64) (*response.Seller, error) {
	params := map[string]string{
		"q": fmt.Sprintf("{\"sellerID\": %d}", sellerId),
	}

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetSellers, nil)
	if err != nil {
		return nil, err
	}
	type myResponse struct {
		Status string             `json:"status"`
		Code   string             `json:"code"`
		Data   []*response.Seller `json:"data"`
	}

	resp := new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &resp)
	if err != nil {
		return nil, err
	}
	if resp.Status != common.APIStatus.Ok || resp.Data == nil {
		return nil, fmt.Errorf("%v", resp.Code)
	}

	return resp.Data[0], nil
}
