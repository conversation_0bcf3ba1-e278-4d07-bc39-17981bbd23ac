package hrm

import (
	"time"

	"go.mongodb.org/mongo-driver/mongo"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/golang/configuration"
)

const (
	pathDepartment            = "/core/account/v1/department"
	pathGetAccount            = "/core/account/v1/employee/all-v2"
	pathGetAccountRole        = "/core/account/v1/account/employee"
	pathGetHrmEmployeeRoles   = "/core/account/v1/user-role"
	pathGetAllUserDepartments = "/core/account/v1/department/my-all-list"
)

type HrmClient struct {
	svc     *client.RestClient
	headers map[string]string
}

func NewServiceClient(serviceConfig configuration.Service, logName string, session *mongo.Database) *HrmClient {
	if serviceConfig.Host == "" {
		return nil
	}
	hrmClient := &HrmClient{
		svc: client.NewRESTClient(
			serviceConfig.Host,
			logName,
			300*time.Second,
			1,
			3*time.Second,
		),
		headers: map[string]string{
			"Authorization": serviceConfig.Authorization,
		},
	}
	hrmClient.svc.SetDBLog(session)
	return hrmClient
}
