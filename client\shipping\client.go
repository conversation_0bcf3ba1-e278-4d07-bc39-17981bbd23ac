package shipping

import (
	"fmt"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/shipping/be"

	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/shipping/ahamove"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/shipping/ghn"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/shipping/ghtk"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/shipping/ninjavan"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/shipping/nt"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/shipping/snappy"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/shipping/vnp"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/client/shipping/vtp"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/conf"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model"
	"gitlab.buymed.tech/thuocsi.vn/delivery/transporting/model/enum"
	"go.mongodb.org/mongo-driver/mongo"
)

type shippingClient struct {
	SnappyClient         *snappy.Client
	NhatTinClient        *nt.Client
	GHTKClient           *ghtk.Client
	GHNClient            *ghn.Client
	VTPClient            *vtp.Client
	AhamoveClient        *ahamove.Client
	VNPClient            *vnp.Client
	NinjavanClient       *ninjavan.Client
	AhamoveOnWheelClient *ahamove.OnWheelClient
	BeClient             *be.Client
}

var (
	TplShippingClient *shippingClient
	logConn           *mongo.Database
)

func InitLog(log *mongo.Database) {
	if logConn == nil {
		logConn = log
	}
}

func Init() {
	TplShippingClient = &shippingClient{
		SnappyClient:  snappy.NewServiceClient(conf.Config.ConfigCarrier.SnappyConfig.Domain, "tpl_api", logConn),
		NhatTinClient: nt.NewServiceClient(conf.Config.ConfigCarrier.NhatTinConfig.Domain, "tpl_api", logConn),
		GHTKClient:    ghtk.NewServiceClient(conf.Config.ConfigCarrier.GHTKConfig.Domain, "tpl_api", logConn),
		GHNClient:     ghn.NewServiceClient(conf.Config.ConfigCarrier.GHNConfig.Domain, "tpl_api", logConn),
		VTPClient:     vtp.NewServiceClient(conf.Config.ConfigCarrier.VTPConfig.Domain, "tpl_api", logConn),
		AhamoveClient: ahamove.NewServiceClient(conf.Config.ConfigCarrier.AhamoveConfig.Domain, "tpl_api", logConn),
		VNPClient:     vnp.NewServiceClient(conf.Config.ConfigCarrier.VNPostConfig.Domain, "tpl_api", logConn),
		//NinjavanClient: ninjavan.NewServiceClient(conf.Config.ConfigCarrier.NinjavanConfig.Domain, "tpl_api", logConn),
		AhamoveOnWheelClient: ahamove.NewOnWheelServiceClient(conf.Config.AhamoveOnWheelHost,
			conf.Config.AhamoveOnWheelApiKey,
			conf.Config.LogDBConf.DatabaseName,
			logConn),
		BeClient: be.NewServiceClient(
			conf.Config.ConfigCarrier.BeConfig.Domain,
			"tpl_api",
			conf.Config.ConfigCarrier.BeConfig.Proxy,
			logConn),
	}
}

func ChangeInfoCarrier(carrier *model.Carrier) error {
	if carrier.IsInternal == nil || *carrier.IsInternal {
		return nil
	}

	if TplShippingClient == nil {
		return nil
	}

	if carrier == nil {
		return nil
	}

	if carrier.ExtraData == nil {
		return fmt.Errorf("Không có thông tin nhà vận chuyển")
	}

	switch *carrier.ParentCode {
	case enum.Partner.SNAPPY:
		return nil
	case enum.Partner.GHTK:
		TplShippingClient.GHTKClient.ChangeInformation(carrier)
		break
	case enum.Partner.AHAMOVE:
		TplShippingClient.AhamoveClient.ChangeInformation(carrier)
		break
	case enum.Partner.NHAT_TIN:
		TplShippingClient.NhatTinClient.ChangeInformation(carrier)
		break
	case enum.Partner.GHN:
		TplShippingClient.GHNClient.ChangeInformation(carrier)
		break
	case enum.Partner.VIETTEL_POST:
		TplShippingClient.VTPClient.ChangeInformation(carrier)
		break
	case enum.Partner.VNPOST:
		TplShippingClient.VNPClient.ChangeInformation(carrier)
		break
	default:
		return fmt.Errorf("Không tìm thấy thông tin nhà vận chuyển")
	}
	return nil
}
